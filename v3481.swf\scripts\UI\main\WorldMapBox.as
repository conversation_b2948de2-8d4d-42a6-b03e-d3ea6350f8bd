package UI.main
{
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.task.TaskGotoPan;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.task.TaskData;
   import dataAll._app.worldMap.WorldMapData;
   import dataAll._app.worldMap.WorldMapDataGroup;
   import dataAll._app.worldMap.define.BigMapName;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._app.worldMap.save.WorldMapSave;
   import dataAll.level.define.LevelDefine;
   import flash.events.MouseEvent;
   import gameAll.level.LevelGetting;
   import gameAll.level.endless.EndlessModelCtrl;
   
   public class WorldMapBox extends NormalUI
   {
      private static var tempId:String = "";
      
      public var nowSp:WorldMapSprite = null;
      
      public var mapSpObj:Object = {};
      
      private var nowBigMap:String = "sunglow";
      
      public function WorldMapBox()
      {
         super();
      }
      
      public static function gotoLevelByWorldMapId(id0:String, mustWinB0:Boolean = false) : void
      {
         if(mustWinB0)
         {
            if(Gaming.PG.da.worldMap.saveGroup.getWinB(id0) == false)
            {
               return;
            }
         }
         var da0:WorldMapData = Gaming.PG.da.worldMap.getDataByName(id0);
         if(da0.canDiffB())
         {
            Gaming.uiGroup.alertBox.diff.showDiff(da0,gotoLevel);
         }
         else
         {
            tempId = id0;
            if(Boolean(da0.taskData))
            {
               TaskGotoPan.gotoMap(da0.taskData,gotoLevelTemp);
            }
            else
            {
               gotoLevelTemp();
            }
         }
      }
      
      public static function gotoLevelTemp() : void
      {
         gotoLevel(tempId,0);
      }
      
      public static function gotoLevel(worldMapId0:String, diff0:int, mapModel0:String = "regular", sweepingB0:Boolean = false) : void
      {
         if(mapModel0 == MapMode.ENDLESS)
         {
            EndlessModelCtrl.chooseLevel(worldMapId0,diff0);
         }
         else
         {
            Gaming.LG.chooseLevel(worldMapId0,diff0,"normal",mapModel0,null,sweepingB0 ? "sweeping" : "",true);
         }
      }
      
      public function affterDefineInit() : void
      {
         var n:* = undefined;
         var d0:WorldMapDefine = null;
         var sp0:WorldMapSprite = null;
         var obj0:Object = Gaming.defineGroup.worldMap.bigObj;
         for(n in obj0)
         {
            d0 = obj0[n];
            sp0 = new WorldMapSprite();
            sp0.bigDefine = d0;
            this.mapSpObj[d0.name] = sp0;
            sp0.btnClickFun = this.btnClick;
            sp0.btnOutFun = this.btnOut;
            sp0.btnOverFun = this.btnOver;
            sp0.btnMoveFun = this.btnMove;
            sp0.mapClickFun = this.mapClick;
            addChild(sp0);
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      public function fleshData() : void
      {
         var dg0:WorldMapDataGroup = Gaming.PG.da.worldMap;
         dg0.uiFlesh();
         this.inData(dg0);
         var bb0:Boolean = Boolean(dg0.saveGroup.getSave("LvSen"));
         if(!bb0)
         {
            this.setBigMap(BigMapName.sunglow);
         }
         this.nowSp.setDesertVisible(false);
         var youLing_s0:WorldMapSave = dg0.saveGroup.getSave("YouLing");
         if(youLing_s0 is WorldMapSave)
         {
            if(youLing_s0.levelName == "YouLing_4")
            {
               this.nowSp.setDesertVisible(true);
            }
         }
      }
      
      public function inData(dg0:WorldMapDataGroup) : void
      {
         if(Boolean(this.nowSp) && Boolean(dg0.saveGroup))
         {
            this.nowSp.inData(dg0);
         }
      }
      
      public function showMap(name0:String) : void
      {
         var n:* = undefined;
         var sp0:WorldMapSprite = null;
         for(n in this.mapSpObj)
         {
            sp0 = this.mapSpObj[n];
            sp0.visible = false;
         }
         this.nowSp = this.mapSpObj[name0];
         this.nowSp.visible = true;
         if(!this.nowSp.setImgB())
         {
            this.nowSp.setImg(Gaming.swfLoaderManager.getResourceFull(this.nowSp.bigDefine.swfUrl));
         }
      }
      
      public function getBtnByName(name0:String) : NormalBtn
      {
         return this.nowSp.getBtnByName(name0);
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var btn0:WorldMapGrip = e.target as WorldMapGrip;
         if(btn0.actived)
         {
            gotoLevelByWorldMapId(btn0.label);
         }
      }
      
      private function btnOver(e:MouseEvent) : void
      {
         var d0:LevelDefine = null;
         var str0:String = null;
         var btn0:WorldMapGrip = e.target as WorldMapGrip;
         var da0:WorldMapData = LevelGetting.getWorldMapData(btn0.label);
         var s0:WorldMapSave = da0.getSaveNull();
         if(Boolean(s0))
         {
            d0 = da0.levelDef;
            str0 = "";
            if(Boolean(d0) && Gaming.testCtrl.cheating.enabled)
            {
               str0 += d0.name + "\n";
            }
            if(btn0.taskData is TaskData)
            {
               str0 += "任务：" + btn0.taskData.def.getAlertTitleText();
               str0 += "\n等级：" + ComMethod.color(da0.getEnemyLv() + "级","#FFFF00");
            }
            else
            {
               str0 += da0.getBtnTip(Gaming.PG.da.level);
            }
            if(str0 != "")
            {
               Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
            }
         }
         else
         {
            this.btnOut();
         }
      }
      
      private function btnMove(e:MouseEvent = null) : void
      {
         Gaming.uiGroup.tipBox.setPositionByMouse();
      }
      
      private function btnOut(e:MouseEvent = null) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function mapClick(e:MouseEvent) : void
      {
      }
      
      public function setBigMap(name0:String) : void
      {
         var f0:int = int(BigMapName.arr.indexOf(name0));
         if(f0 >= 0)
         {
            this.nowBigMap = name0;
            this.setMapPositionIndex(f0);
            Gaming.uiGroup.mainUI.smallMap.fleshData();
         }
      }
      
      public function getNowBigMap() : String
      {
         return this.nowBigMap;
      }
      
      private function setMapPositionIndex(n0:int) : void
      {
         var n:* = undefined;
         var sp0:WorldMapSprite = null;
         var x0:int = n0 % 4;
         var y0:int = int(n0 / 4);
         for(n in this.mapSpObj)
         {
            sp0 = this.mapSpObj[n];
            sp0.x = -x0 * Gaming.WIDTH;
            sp0.y = -y0 * (Gaming.HEIGHT + 100);
         }
      }
   }
}

