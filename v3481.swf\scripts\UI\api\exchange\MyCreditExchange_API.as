package UI.api.exchange
{
   import UI.test.SaveTestBox;
   import com.adobe.crypto.MD5;
   import com.adobe.serialization.json.JSON2;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   
   public class MyCreditExchange_API
   {
      public var yesFun:Function;
      
      public var noFun:Function;
      
      internal var loader:URLLoader = new URLLoader();
      
      internal var url:URLRequest = new URLRequest("https://my.4399.com/credit/sn-use");
      
      public var app_id:int = 9;
      
      public var $key:String = "854873cba9d1ff8f1bf691a4e0b537e0";
      
      public function MyCreditExchange_API()
      {
         super();
         this.loader.addEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this.loader.addEventListener(Event.COMPLETE,this.loaderCompleteHandler);
      }
      
      public function startExchange(uid0:String, activation0:String, product_id0:String, _yesFun:Function = null, _noFun:Function = null) : *
      {
         var n:* = undefined;
         this.yesFun = _yesFun;
         this.noFun = _noFun;
         if(Gaming.api.save.isLocal())
         {
            uid0 = "1057653076";
         }
         var data0:URLVariables = new URLVariables();
         data0.uid = Number(uid0);
         data0.activation = activation0;
         data0.product_id = int(product_id0);
         data0.time = int(Gaming.api.save.getNowServerDate().getDateClass().getTime() / 1000);
         data0.app_id = this.app_id;
         data0.token = MD5.hash(activation0 + "||" + this.app_id + "||" + product_id0 + "||" + 0 + "||" + data0.time + "||" + uid0 + "||" + "/credit/sn-use" + "||" + this.$key);
         this.url.data = data0;
         this.url.method = URLRequestMethod.POST;
         this.loader.load(this.url);
         SaveTestBox.addText("$key：" + this.$key);
         var arr0:Array = ["uid","activation","product_id","time","app_id","token"];
         for(n in arr0)
         {
            SaveTestBox.addText(arr0[n] + "：" + this.url.data[arr0[n]]);
         }
      }
      
      private function loaderCompleteHandler(e:Event) : void
      {
         var json0:String = this.loader.data;
         var obj0:Object = JSON2.decode(json0);
         var code0:int = int(obj0["code"]);
         var result0:String = obj0["result"];
         var msg0:String = obj0["msg"];
         if(code0 == 100)
         {
            if(this.yesFun is Function)
            {
               this.yesFun(result0);
            }
         }
         else if(this.noFun is Function)
         {
            this.noFun(msg0);
         }
      }
      
      private function errorHandler(e:IOErrorEvent) : void
      {
         if(this.noFun is Function)
         {
            this.noFun("网络连接错误");
         }
      }
   }
}

