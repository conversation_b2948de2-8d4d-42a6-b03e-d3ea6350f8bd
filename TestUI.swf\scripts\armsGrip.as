package
{
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol717")]
   public dynamic class armsGrip extends MovieClip
   {
      public var smallIconMc:MovieClip;
      
      public var lockSp:MovieClip;
      
      public var levelTxt:TextField;
      
      public var secMc:MovieClip;
      
      public var backMc:MovieClip;
      
      public var iconCon:MovieClip;
      
      public var starMc:MovieClip;
      
      public var levelTxtBack:MovieClip;
      
      public var chooseMc:MovieClip;
      
      public var btnMc:MovieClip;
      
      public var shopBtnBackMc:MovieClip;
      
      public function armsGrip()
      {
         super();
      }
   }
}

