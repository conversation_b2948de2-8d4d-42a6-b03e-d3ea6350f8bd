package UI.gameWorld.tip
{
   import UI.base.AutoNormalUI;
   import UI.base.box.NormalBox;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.union.building.geology.UnionGeologyData;
   import dataAll._app.union.building.geology.UnionGeologyThingsData;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import gameAll.level._diy.GeologyLevelDiy;
   
   public class GameWorldThingsBox extends AutoNormalUI
   {
      private var box:NormalBox = new NormalBox();
      
      public var con:Sprite;
      
      public var autoBtn:NormalBtn;
      
      private var boxTag:Sprite;
      
      public function GameWorldThingsBox()
      {
         super();
         mcTypeArr = ["tag","txt","btnSp"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         addChild(this.box);
         this.box.x = this.boxTag.x;
         this.box.y = this.boxTag.y;
         this.box.arg.init(5,2,0,9);
         this.box.setIconPro("GameWorldUI/thingsTipBox");
         this.box.evt.setWantEvent(true,false,false,false,false);
         this.box.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.box.setNowGripNum(10);
         this.mouseEnabled = false;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
         if(Boolean(this.con) && parent != this.con)
         {
            this.con.addChild(this);
         }
      }
      
      override public function hide() : void
      {
         super.hide();
         if(Boolean(this.con) && Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
      
      public function fleshData() : void
      {
         var grip0:NormalBtn = null;
         var autoB0:Boolean = false;
         var da0:UnionGeologyThingsData = null;
         var oneChooseB0:Boolean = false;
         this.show();
         var gda0:UnionGeologyData = Gaming.PG.da.union.building.geology;
         var chooseVisibleB0:Boolean = gda0.def.hangingChooseB;
         for each(grip0 in this.box.gripArr)
         {
            da0 = gda0.countArr[grip0.index];
            if(Boolean(da0))
            {
               oneChooseB0 = chooseVisibleB0 && da0.def.orePro > 0;
               grip0.actived = oneChooseB0;
               grip0.itemsData = da0;
               grip0.setName(ComMethod.colorMustNum(da0.save.now,da0.def.max));
               grip0.setIconName(da0.thingsDefine.iconUrl);
               grip0.setChooseVisible(oneChooseB0);
               if(oneChooseB0)
               {
                  grip0.isChosen = da0.save.hangingB;
               }
               else
               {
                  grip0.isChosen = false;
               }
            }
            else
            {
               grip0.actived = false;
               grip0.itemsData = null;
               grip0.setName("");
               grip0.setIconName("");
               grip0.setChooseVisible(false);
            }
         }
         autoB0 = GeologyLevelDiy.autoB;
         this.autoBtn.setName(autoB0 ? "暂停挖矿" : "自动挖矿");
         this.autoBtn.actived = gda0.def.hangingB;
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var da0:UnionGeologyThingsData = e.childData as UnionGeologyThingsData;
         if(Boolean(da0))
         {
            da0.save.hangingB = !da0.save.hangingB;
            Gaming.PG.ctrlHero.ai.geologyAI.reFindTarget();
            this.fleshData();
         }
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var funName0:String = btn0.label + "BtnClick";
         if(this.hasOwnProperty(funName0))
         {
            this[funName0](e);
         }
      }
      
      public function autoBtnClick(e:MouseEvent) : void
      {
         var autoB0:Boolean = false;
         if(this.autoBtn.actived)
         {
            autoB0 = GeologyLevelDiy.autoB;
            GeologyLevelDiy.setAuto(!autoB0);
            this.fleshData();
         }
      }
   }
}

