package UI.gameWorld
{
   import UI.base.button.NormalBtn;
   import UI.base.grid.NormalGrid;
   import com.greensock.TweenLite;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class GameWorldIconBtn extends NormalGrid
   {
      private var nameTxt_y:int = 0;
      
      public var tweenAddY:int = 15;
      
      public var tweenB:Boolean = true;
      
      public function GameWorldIconBtn()
      {
         super();
         activedAndTextGray = true;
      }
      
      override public function setImg(img0:MovieClip) : void
      {
         super.setImg(img0);
         if(Bo<PERSON>an(nameTxt))
         {
            this.nameTxt_y = nameTxt.y;
            if(this.tweenB)
            {
               nameTxt.alpha = 0;
            }
         }
      }
      
      override protected function MOver(event:MouseEvent) : *
      {
         this.setBtnBack("over");
      }
      
      override protected function MOut(event:MouseEvent) : *
      {
         this.setBtnBack("normal");
      }
      
      override public function setBtnBack(str0:String) : void
      {
         nowBtnLabel = str0;
         if(btnMc is Sprite)
         {
            btnMc.gotoAndStop(str0);
         }
         else if(img.totalFrames > 1)
         {
            img.gotoAndStop(str0);
         }
         if(mouseIconEffectB && icon is Sprite)
         {
            if(str0 == "no" || _actived)
            {
               icon.transform.colorTransform = NormalBtn[nowBtnLabel + "_CF"];
            }
         }
         if(Boolean(nameTxt) && this.tweenB)
         {
            if(nowBtnLabel == "over")
            {
               if(nameTxt.alpha < 1)
               {
                  nameTxt.y = this.nameTxt_y + this.tweenAddY;
                  TweenLite.to(nameTxt,0.2,{
                     "y":this.nameTxt_y,
                     "alpha":1
                  });
               }
            }
            else if(nowBtnLabel == "normal")
            {
               TweenLite.to(nameTxt,0.2,{
                  "y":this.nameTxt_y + this.tweenAddY,
                  "alpha":0
               });
            }
         }
      }
   }
}

