package UI.pet.strengthen
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.must.NormalMustBox;
   import UI.base.oneKey.OneKeyCtrl;
   import UI.pet.PetUI;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.NumberMethod;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.pet.PetData;
   import dataAll.pet.base.PetBaseData;
   import dataAll.pro.PropertyArrayDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class PetStrengthenBoard extends NormalUI
   {
      private var gripBox:ItemsGripBox = new ItemsGripBox();
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var boxTag:Sprite;
      
      private var mustTag:Sprite;
      
      private var btnSp:MovieClip;
      
      private var beforeTxt:TextField;
      
      private var nextTxt:TextField;
      
      private var nameTxt:TextField;
      
      private var numKey:OneKeyCtrl = new OneKeyCtrl();
      
      private var numKeyBtnSp:MovieClip;
      
      private var numKeyBtn:NormalBtn = new NormalBtn();
      
      private var tempMust:MustDefine = null;
      
      public function PetStrengthenBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["nameTxt","boxTag","mustTag","btnSp","beforeTxt","nextTxt","numKeyBtnSp"];
         super.setImg(img0);
         FontDeal.dealOne(this.nameTxt);
         addChild(this.gripBox);
         NormalUICtrl.setTag(this.gripBox,this.boxTag);
         this.gripBox.arg.init(1,7,1,1);
         this.gripBox.imgType = "PetUI/proBar";
         this.gripBox.evt.setWantEvent(true,false,false,true,true);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         addChild(this.mustBox);
         this.mustBox.setNormalImg();
         NormalUICtrl.setTag(this.mustBox,this.mustTag);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("强化");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         addChild(this.numKeyBtn);
         this.numKeyBtn.setImg(this.numKeyBtnSp);
         this.numKeyBtn.activedAndEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.numKeyBtn);
         this.numKeyBtn.addEventListener(MouseEvent.CLICK,this.numKeyBtnClick);
         this.numKeyBtn.tipString = "一键强化";
         this.numKey.startFun = this.numKeyStart;
         this.numKey.endFun = this.numKeyEnd;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function get nowData() : PetData
      {
         return PetUI.getNowData();
      }
      
      private function getNowLv() : int
      {
         var da0:PetData = this.nowData;
         var label0:String = this.gripBox.nowLabel;
         if(Boolean(da0) && label0 != "")
         {
            return da0.base.save.getAddLv(label0);
         }
         return 0;
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      public function fleshData() : void
      {
         var label0:String = null;
         if(Boolean(PetUI.nowGrip))
         {
            label0 = this.gripBox.nowLabel;
            this.fleshList();
            this.chooseLabel(label0);
         }
         else
         {
            this.clearAll();
         }
      }
      
      public function clearAll() : void
      {
         this.gripBox.clearAllData();
      }
      
      private function fleshList() : void
      {
         var da0:PetData = PetUI.getNowData();
         var arr0:Array = da0.base.getBarDataArr();
         this.gripBox.inData_byArr(arr0,"inData_barData");
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         this.chooseLabel(e.label);
      }
      
      public function setNowLabel(label0:String) : void
      {
         this.gripBox.nowLabel = label0;
      }
      
      private function chooseLabel(name0:String) : void
      {
         if(name0 == "")
         {
            name0 = this.gripBox.gripArr[0].label;
         }
         var da0:PetData = PetUI.getNowData();
         this.gripBox.setChoose(name0);
         var proD0:PropertyArrayDefine = Gaming.defineGroup.gene.strengthenPro.getDefine(name0);
         this.nameTxt.htmlText = "强化" + proD0.cnName;
         this.showStrengthen(da0.base,name0);
         this.showMust(da0.base,name0);
      }
      
      private function showStrengthen(base0:PetBaseData, label0:String) : void
      {
         var proD0:PropertyArrayDefine = Gaming.defineGroup.gene.strengthenPro.getDefine(label0);
         var affterDa0:PetBaseData = base0.simpleClone();
         affterDa0.save.addAddLv(label0,1);
         var beforeStr0:String = this.getValueStringBy(base0,label0,"强化前");
         var affterStr0:String = this.getValueStringBy(affterDa0,label0,"强化后");
         this.beforeTxt.htmlText = beforeStr0;
         this.nextTxt.htmlText = affterStr0;
      }
      
      private function getValueStringBy(base0:PetBaseData, label0:String, titleStr0:String) : String
      {
         var str0:String = "";
         var proD0:PropertyArrayDefine = Gaming.defineGroup.gene.strengthenPro.getDefine(label0);
         var v0:Number = base0.getBaseValueByLabel(label0) + base0.getStrengthenValue(label0);
         var lv0:int = base0.save.getAddLv(label0);
         var all0:Number = base0.getValueByLabel(label0);
         str0 += ComMethod.color("<b>" + titleStr0 + (lv0 > 0 ? lv0 + "级" : "") + "</b>","#00FF00");
         str0 += "\n基础" + proD0.cnName + "：" + ComMethod.color(NumberMethod.toWan(v0),"#FFFF00");
         return str0 + ("\n总" + proD0.cnName + "：" + ComMethod.color(NumberMethod.toWan(all0) + "","#FFFF00"));
      }
      
      private function showMust(base0:PetBaseData, label0:String) : void
      {
         var da0:PetData = PetUI.getNowData();
         var nowLv0:int = base0.save.getAddLv(label0);
         var affterLv0:int = nowLv0 + 1;
         var d0:MustDefine = Gaming.defineGroup.gene.getStrengthenMust(label0,affterLv0);
         var bb0:Boolean = this.mustBox.inData(d0,da0.base.save.level,"尸宠");
         this.btn.actived = bb0;
         this.numKeyBtn.actived = this.btn.actived;
      }
      
      private function btnClick(e:MouseEvent = null) : void
      {
         var label0:String = this.gripBox.nowLabel;
         var da0:PetData = PetUI.getNowData();
         var nowLv0:int = da0.base.save.getAddLv(label0);
         var affterLv0:int = nowLv0 + 1;
         var d0:MustDefine = Gaming.defineGroup.gene.getStrengthenMust(label0,affterLv0);
         this.tempMust = d0;
         PlayerMustCtrl.deductMust(d0,this.affter_btnClick);
      }
      
      private function affter_btnClick() : void
      {
         var label0:String = this.gripBox.nowLabel;
         var da0:PetData = PetUI.getNowData();
         da0.base.save.addAddLv(label0,1);
         if(this.numKey.ingB)
         {
            this.numKey.useMust(this.tempMust);
            this.tempMust = null;
            this.nextNumKey(false);
         }
         else
         {
            Gaming.uiGroup.alertBox.showSuccess("强化成功！");
            this.fleshData();
         }
      }
      
      private function numKeyBtnClick(e:MouseEvent) : void
      {
         if(this.numKeyBtn.actived)
         {
            if(Boolean(this.nowData))
            {
               Gaming.uiGroup.alertBox.showNumChoose("选择强化次数",1,100,1,1,this.yesNumKey);
            }
         }
      }
      
      private function yesNumKey(num0:int) : void
      {
         if(Boolean(this.nowData))
         {
            Gaming.uiGroup.connectUI.show("数据处理中……",1);
            this.numKey.start(num0,this.getNowLv());
         }
      }
      
      private function numKeyStart() : void
      {
         if(this.btn.actived)
         {
            this.btnClick();
         }
         else
         {
            this.stopNumKey();
         }
      }
      
      private function numKeyEnd() : void
      {
         var tip0:String = null;
         Gaming.uiGroup.connectUI.hide();
         if(this.numKey.getNum() > 0)
         {
            this.fleshData();
            tip0 = "强化等级变化：" + this.numKey.getLevelChangeStr();
            tip0 += "\n强化次数：" + this.numKey.getNum() + "次";
            if(Boolean(this.numKey.getGift()))
            {
               tip0 += "\n消耗材料：" + this.numKey.getGift().getDescription();
            }
            Gaming.uiGroup.alertBox.showSuccess(tip0);
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("当前无法一键强化。");
         }
         this.numKey.clear();
      }
      
      private function nextNumKey(saveB0:Boolean) : void
      {
         this.fleshData();
         this.numKey.next(this.getNowLv(),saveB0);
      }
      
      private function stopNumKey(v0:* = null) : void
      {
         this.numKey.doErrorIng("强化中止！");
      }
   }
}

