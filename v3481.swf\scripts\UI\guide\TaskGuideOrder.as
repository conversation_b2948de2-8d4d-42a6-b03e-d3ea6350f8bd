package UI.guide
{
   public class TaskGuideOrder extends NormalGuideOrder
   {
      public function TaskGuideOrder()
      {
         super();
         arr = [this.mainBtn,this.taskBtn];
      }
      
      private function mainBtn() : OneGuideData
      {
         return new OneGuideData(Gaming.uiGroup.mainUI.getBtn("task"),"你可以接受任务了！");
      }
      
      private function taskBtn() : OneGuideData
      {
         Gaming.uiGroup.taskUI.showBox("day");
         return new OneGuideData(Gaming.uiGroup.taskUI.getBtn,"点击即可接受任务");
      }
      
      private function targetBtn() : OneGuideData
      {
         return new OneGuideData(Gaming.uiGroup.taskUI.mapBtn,"点击入目的地");
      }
   }
}

