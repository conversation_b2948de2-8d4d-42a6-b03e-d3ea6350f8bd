package UI.bag.wear
{
   import UI.UIOrder;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import com.common.text.TextWay;
   import dataAll._player.base.IO_NameChangeGetter;
   import dataAll._player.define.KeywordShield;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.pet.PetData;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class NameChangeBox extends NormalUI
   {
      private static var tempGetter:IO_NameChangeGetter = null;
      
      private static var tempGetterName:String = "";
      
      private static var tempWeekB:Boolean = true;
      
      private static var getterNamePan_yesFun:Function = null;
      
      private var textTxt:TextField;
      
      private var btnSp:MovieClip;
      
      private var noBtnSp:MovieClip;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var noBtn:NormalBtn = new NormalBtn();
      
      private var state:String = "no";
      
      private var tempName:String = "";
      
      private var nowData:Object = null;
      
      private var fleshFun:Function = null;
      
      private var weekOneB:Boolean = false;
      
      public function NameChangeBox()
      {
         super();
      }
      
      private static function getWeekChangeNameB() : Boolean
      {
         if(Boolean(Gaming.PG.da))
         {
            return Gaming.PG.da.main.save.changeNameB;
         }
         return false;
      }
      
      private static function setWeekChangeNameB(bb0:Boolean) : void
      {
         if(Boolean(Gaming.PG.da))
         {
            Gaming.PG.da.main.save.changeNameB = bb0;
         }
      }
      
      public static function getterNamePan(g0:IO_NameChangeGetter, yesFun0:Function, weekLimitB0:Boolean = true) : void
      {
         tempWeekB = weekLimitB0;
         tempGetter = g0;
         getterNamePan_yesFun = yesFun0;
         Gaming.uiGroup.alertBox.textInput.showTextInput("修改名称",g0.getUIName(),yes_getterNamePan,"yesAndNo",g0.getChangeNameMaxChar());
      }
      
      private static function yes_getterNamePan(str0:String) : void
      {
         tempGetterName = str0;
         Gaming.uiGroup.connectUI.show("语法判断中……");
         allNamePan(str0,tempGetter.getUIName(),must_getterNamePan,no_getterNamePan);
      }
      
      private static function no_getterNamePan(str0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         UIOrder.alertError(str0);
      }
      
      private static function must_getterNamePan() : void
      {
         Gaming.uiGroup.connectUI.hide();
         tempGetterName = dealName(tempGetterName);
         PlayerMustCtrl.deductMustAlert(tempGetter.getChangeNameMust(),"修改名称如下：" + tempGetterName + "\n需要消耗：",last_getterNamePan);
      }
      
      private static function last_getterNamePan() : void
      {
         if(Boolean(tempGetter))
         {
            tempGetter.setUIName(tempGetterName);
         }
         if(tempWeekB)
         {
            Gaming.PG.da.main.save.changeNameB = true;
         }
         if(getterNamePan_yesFun is Function)
         {
            getterNamePan_yesFun();
         }
         UIOrder.saveCanStop();
      }
      
      private static function dealName(str0:String) : String
      {
         str0 = TextWay.toHanSpace(str0);
         return TextWay.toHan2(str0);
      }
      
      private static function allNamePan(name0:String, old0:String, yesFun0:Function, noFun0:Function) : void
      {
         var str0:String = dealName(name0);
         if(str0 == old0)
         {
            noFun0("昵称未修改");
         }
         else if(KeywordShield.haveKey(str0))
         {
            noFun0("昵称包含敏感词");
         }
         else
         {
            namePan(str0,yesFun0,noFun0);
         }
      }
      
      public static function namePan(name0:String, yesFun0:Function, noFun0:Function) : void
      {
         var htmlNoStr0:String = null;
         var str0:String = dealName(name0);
         var errorStr0:String = "";
         if(str0 == "" || !str0)
         {
            errorStr0 = "昵称不能为空";
         }
         else
         {
            htmlNoStr0 = TextWay.haveHtmlNoB(str0);
            if(htmlNoStr0 != "")
            {
               errorStr0 = "昵称不能包含" + htmlNoStr0 + "字符";
            }
         }
         if(errorStr0 == "")
         {
            Gaming.api.badWorld.check(str0,yesFun0,noFun0);
         }
         else if(noFun0 is Function)
         {
            noFun0(errorStr0);
         }
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["textTxt","btnSp","noBtnSp"];
         super.setImg(img0);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.btn.addEventListener(MouseEvent.MOUSE_OVER,this.btnOver);
         this.btn.addEventListener(MouseEvent.MOUSE_OUT,this.btnOut);
         this.btn.activedAndEnabled = false;
         addChild(this.noBtn);
         this.noBtn.setImg(this.noBtnSp);
         this.noBtn.setName("取消");
         this.noBtn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.showState("no");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function outLoginEvent() : void
      {
         this.showState("no");
         this.nowData = null;
      }
      
      public function setWeekOneB(bb0:Boolean) : void
      {
         this.weekOneB = bb0;
      }
      
      public function setNowData(obj0:Object, fun0:Function) : void
      {
         this.nowData = obj0;
         this.fleshFun = fun0;
      }
      
      private function setName(name0:String) : void
      {
         if(this.weekOneB)
         {
            setWeekChangeNameB(true);
         }
         if(this.nowData is NormalPlayerData)
         {
            (this.nowData as NormalPlayerData).base.save.playerName = name0;
         }
         else if(this.nowData is PetData)
         {
            (this.nowData as PetData).base.save.playerName = name0;
         }
      }
      
      private function getName() : String
      {
         if(this.nowData is NormalPlayerData)
         {
            return (this.nowData as NormalPlayerData).base.save.playerName;
         }
         if(this.nowData is PetData)
         {
            return (this.nowData as PetData).base.save.playerName;
         }
         return "";
      }
      
      public function fleshChangeBtn() : void
      {
         if(this.state == "change")
         {
            this.btn.actived = true;
         }
         else if(this.weekOneB)
         {
            this.btn.actived = !getWeekChangeNameB();
         }
         else
         {
            this.btn.actived = true;
         }
      }
      
      private function showState(str0:String) : void
      {
         this.state = str0;
         this.textTxt.visible = false;
         this.noBtn.visible = false;
         this.btn.setName("修改");
         if(str0 == "change")
         {
            this.noBtn.visible = true;
            this.textTxt.visible = true;
            this.btn.setName("确定");
         }
         this.fleshChangeBtn();
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         if(e.target == this.noBtn)
         {
            this.stopChangeFun();
         }
         else
         {
            if(Bug.panParty())
            {
               return;
            }
            if(this.state == "change")
            {
               this.meNamePan(this.textTxt.text);
            }
            else if(this.btn.actived)
            {
               this.showState("change");
               this.textTxt.text = this.getName();
            }
         }
      }
      
      private function btnOver(e:MouseEvent) : void
      {
         var s2:String = null;
         var must_d0:MustDefine = Gaming.defineGroup.must.changeName;
         var str0:String = "修改一次昵称，需要：\n" + must_d0.getText();
         if(this.weekOneB)
         {
            s2 = "";
            if(getWeekChangeNameB())
            {
               s2 = "下周一你才能再次修改昵称。";
            }
            else
            {
               s2 = "一周只能修改一次昵称(包括人物昵称、尸宠昵称)。";
            }
            str0 = s2 + "\n\n" + str0;
         }
         Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
      }
      
      private function btnOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function meNamePan(name0:String) : void
      {
         var str0:String = TextWay.toHanSpace(name0);
         str0 = TextWay.toHan2(str0);
         this.tempName = str0;
         if(str0 == this.getName())
         {
            this.showError("昵称未修改");
         }
         else if(KeywordShield.haveKey(str0))
         {
            this.showError("昵称包含敏感词");
         }
         else
         {
            Gaming.uiGroup.connectUI.show("语法判断中……");
            namePan(str0,this.yes_badWorld,this.no_badWorld);
         }
      }
      
      private function showError(str0:String) : void
      {
         Gaming.uiGroup.alertBox.showNormal(str0 + "，是否要重新输入？","yesAndNo",this.continueChangeFun,this.stopChangeFun,"no");
      }
      
      private function continueChangeFun() : void
      {
      }
      
      private function stopChangeFun() : void
      {
         this.showState("no");
      }
      
      private function no_badWorld(str0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         this.showError(str0);
      }
      
      private function yes_badWorld() : void
      {
         Gaming.uiGroup.connectUI.hide();
         var must_d0:MustDefine = Gaming.defineGroup.must.changeName;
         PlayerMustCtrl.deductMustAlert(must_d0,"修改一次昵称，需要：",this.yes_change);
      }
      
      private function yes_change() : void
      {
         this.setName(this.tempName);
         this.showState("no");
         if(this.fleshFun is Function)
         {
            this.fleshFun();
         }
         UIOrder.save(true,false,false);
      }
   }
}

