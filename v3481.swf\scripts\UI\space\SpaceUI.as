package UI.space
{
   import UI.UIGroup;
   import UI.UIOrder;
   import UI.base.AppNewUI;
   import flash.text.TextField;
   
   public class SpaceUI extends AppNewUI
   {
      private var titleTxt:TextField;
      
      private var mapBoard:SpaceMapBoard = new SpaceMapBoard();
      
      private var craftBoard:SpaceCraftBoard = new SpaceCraftBoard();
      
      private var achieveBoard:SpaceAchieveBoard = new SpaceAchieveBoard();
      
      public function SpaceUI()
      {
         super();
         UICn = "太空探索";
         UILabel = "space";
         swfLabel = "SpaceUI";
         labelArr = ["map","craft","achieve"];
         labelCnArr = ["地图","飞船","成就"];
      }
      
      override protected function firstLoad() : void
      {
         elementNameArr = elementNameArr.concat(["titleTxt"]);
         setImgUrl(swfLabel + "/" + UILabel + "UI");
         init_addLabel();
         init_addBox();
         init_other();
         UIGroup.setUIMiddle(this,true);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         var bb0:Boolean = UIOrder.taskPan("MadmanPlot");
         if(bb0)
         {
            Gaming.PG.da.bossEdit.openUI();
            super.show();
         }
      }
      
      public function outLoginEvent() : void
      {
         this.craftBoard.outLoginEvent();
         this.mapBoard.outLoginEvent();
      }
      
      override protected function getTipText() : String
      {
         var str0:String = "";
         if(this.mapBoard.visible)
         {
            str0 += "1、完成主线任务后，开启指定太空地图。";
            str0 += "\n\n2、在地图中摧毁敌人或者物体将获得飞船经验。";
            str0 += "\n\n3、每周每张地图有经验获取上限。";
         }
         else if(this.craftBoard.visible)
         {
            str0 += "飞船每升1级，就会提高飞船耐久，同时获得1点技能点数，用于升级飞船技能。";
         }
         return str0;
      }
   }
}

