package UI.gameWorld.bodyLifeBar
{
   import flash.display.Sprite;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.data.NormalBodyData;
   
   public class BodyLifeBarGroup extends Sprite
   {
      private var arr:Array = [];
      
      private var weBar:BodyLifeBar = new BodyLifeBar();
      
      private var enemyBar:BodyLifeBar = new BodyLifeBar();
      
      private var spaceB:Boolean = false;
      
      public function BodyLifeBarGroup()
      {
         super();
         addChild(this.weBar);
         this.weBar.visible = false;
         addChild(this.enemyBar);
         this.enemyBar.visible = false;
      }
      
      public function imgInit() : void
      {
         this.weBar.setToCamp("we");
         this.weBar.skillShowB = true;
         this.enemyBar.setToCamp("enemy");
      }
      
      public function startLevel() : void
      {
         Gaming.gameSprite.L_text.addChild(this);
         this.spaceB = Gaming.LG.isSpaceMapB();
      }
      
      public function addLifeBar(b0:IO_NormalBody) : void
      {
         var bar0:BodyLifeBar = null;
         var data0:NormalBodyData = b0.getData();
         if(!data0.haveLifeBarB)
         {
            bar0 = new BodyLifeBar();
            bar0.setToCamp(data0.camp,data0.define.doubleLifeBarB);
            bar0.setTarget(b0);
            data0.haveLifeBarB = true;
            addChild(bar0);
            this.arr.push(bar0);
         }
      }
      
      public function setOtherBar(b0:IO_NormalBody) : void
      {
         var data0:NormalBodyData = b0.getData();
         if(data0.camp == "we")
         {
            this.weBar.clearData();
            this.weBar.setTarget(b0);
         }
         else
         {
            this.enemyBar.clearData();
            this.enemyBar.setTarget(b0);
         }
      }
      
      public function fleshEach() : void
      {
         var n:* = undefined;
         var mouseB0:IO_NormalBody = null;
         var bar0:BodyLifeBar = null;
         var arr2:Array = [];
         for(n in this.arr)
         {
            bar0 = this.arr[n];
            bar0.flesh();
            if(Boolean(bar0.targetBody) && !bar0.targetBody.getDieCtrl().canClearLifeBar())
            {
               arr2.push(bar0);
            }
            else
            {
               bar0.targetBody = null;
               removeChild(bar0);
            }
         }
         this.arr = arr2;
         mouseB0 = Gaming.BGHit.nowMouseBody;
         if(Boolean(mouseB0))
         {
            if(!this.spaceB && !mouseB0.getData().haveLifeBarB && mouseB0.getDie() == 0)
            {
               this.setOtherBar(mouseB0);
            }
         }
         this.weBar.flesh();
         this.enemyBar.flesh();
      }
      
      public function clearAll() : void
      {
         var n:* = undefined;
         var bar0:BodyLifeBar = null;
         for(n in this.arr)
         {
            bar0 = this.arr[n];
            bar0.clearData();
            removeChild(bar0);
         }
         this.arr.length = 0;
         this.weBar.visible = false;
         this.weBar.clearData();
         this.enemyBar.visible = false;
         this.enemyBar.clearData();
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
   }
}

