package UI.achieve
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.label.MoreLabelBox;
   import UI.base.scroll.NormalScrollBar;
   import dataAll._app.achieve.AchieveData;
   import dataAll._app.achieve.AchieveLabelShowData;
   import dataAll._app.achieve.define.AchieveDefine;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.ui.label.LabelAddData;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class AchieveListBoard extends NormalUI
   {
      private var onlyCompleteBtn:NormalBtn = new NormalBtn();
      
      private var leftBox:MoreLabelBox = new MoreLabelBox();
      
      private var itemsBox:AchieveListGripBox = new AchieveListGripBox();
      
      private var scrollBar:NormalScrollBar;
      
      private var leftTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var barTag:Sprite;
      
      private var onlyCompleteBtnSp:MovieClip;
      
      private var tipBtn:SimpleButton;
      
      public var nowType:String = "";
      
      public var nowChildType:String = "";
      
      public function AchieveListBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["tipBtn","leftTag","barTag","pageTag","onlyCompleteBtnSp"];
         super.setImg(img0);
         img0.addChild(this.leftBox);
         NormalUICtrl.setTag(this.leftBox,this.leftTag);
         this.addLeftLabel();
         addChild(this.onlyCompleteBtn);
         this.onlyCompleteBtn.setImg(this.onlyCompleteBtnSp);
         this.onlyCompleteBtn.addEventListener(MouseEvent.CLICK,this.onlyBtnClick);
         addChild(this.itemsBox);
         this.itemsBox.arg.init(1,4,0,0);
         this.itemsBox.setIconPro("AchieveUI/itemsBar",60,60);
         this.itemsBox.evt.setWantEvent(true,false,false,true,true,false);
         this.itemsBox.addEventListener(ClickEvent.ON_CLICK,this.barClick);
         NormalUICtrl.setTag(this.itemsBox,this.barTag);
         this.itemsBox.pageBox.setToNormalBtn();
         this.itemsBox.pageBox.setXY_bySp(this.pageTag,this.itemsBox);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.itemsBox);
         this.scrollBar = new NormalScrollBar(this.leftBox,img0["maskTargetSp"],img0["scrollBarSp"],img0["scrollLineSp"],1,false,true,true);
         this.scrollBar.speed = 30;
         this.scrollBar.refresh();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function addLeftLabel() : void
      {
         var da0:LabelAddData = Gaming.defineGroup.achieve.getLabelAddData();
         this.leftBox.inDataByLabelAddData(da0);
         this.leftBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
      }
      
      private function fleshLabelNum() : void
      {
         var label0:MoreLabelBox = null;
         var obj0:Object = Gaming.PG.da.achieve.getLabelShowData();
         this.fleshLabelNumByOne(this.leftBox,obj0);
         var gatherObj0:Object = this.leftBox.childObj;
         for each(label0 in gatherObj0)
         {
            this.fleshLabelNumByOne(label0,obj0);
         }
      }
      
      private function fleshLabelNumByOne(box0:MoreLabelBox, obj0:Object) : void
      {
         var btn0:NormalBtn = null;
         var show0:AchieveLabelShowData = null;
         for each(btn0 in box0.gripArr)
         {
            show0 = obj0[btn0.label];
            btn0.setNumText(show0.text);
            btn0.setNew(show0.newB);
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshLabelNum();
         this.showLabel(this.nowType,this.nowChildType);
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      public function gotoName(name0:String) : void
      {
         var d0:AchieveDefine = Gaming.defineGroup.achieve.getDefine(name0);
         if(Boolean(d0))
         {
            this.leftBox.clearAllChoose();
            this.leftBox.hideAllChild();
            this.leftBox.showChildVisibleByName(d0.gather,d0.father);
            this.showLabel(d0.gather,d0.father);
            this.scrollBar.refresh();
         }
      }
      
      private function barClick(e:ClickEvent) : void
      {
         var g0:GiftAddDefineGroup = null;
         var bb0:Boolean = false;
         var grip0:AchieveListGrip = null;
         var da0:AchieveData = e.childData as AchieveData;
         if(da0.isCompleteB())
         {
            g0 = da0.getGift();
            if(Boolean(g0))
            {
               if(da0.giftGettedB() == false)
               {
                  bb0 = GiftAddit.addAndAutoBagSpacePan(g0,"获得：\n" + g0.getDescription());
                  if(bb0)
                  {
                     da0.giftEvent();
                     grip0 = e.child as AchieveListGrip;
                     grip0.inData(da0);
                  }
               }
            }
         }
      }
      
      private function onlyBtnClick(e:MouseEvent) : void
      {
         Gaming.PG.save.achieve.changeOnlyNoComplete();
         this.showLabel(this.nowType,this.nowChildType);
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         var btn0:NormalBtn = null;
         var fatherBtn0:NormalBtn = null;
         var strArr0:Array = e.fatherUrl.split("/");
         var type0:String = strArr0[strArr0.length - 2];
         var childType0:String = e.label;
         if(e.father != e.target)
         {
            btn0 = e.child as NormalBtn;
            btn0.setNew(false);
            fatherBtn0 = this.leftBox.getBtnByLabel(type0);
            fatherBtn0.setNew(false);
            this.showLabel(type0,childType0);
         }
         this.scrollBar.refresh();
      }
      
      private function showLabel(type0:String, childType0:String) : void
      {
         this.onlyCompleteBtn.isChosen = Gaming.PG.save.achieve.onlyNoCompleteB;
         if(type0 == "")
         {
            type0 = "level";
            childType0 = "killNum";
            this.leftBox.showChildVisibleByName(type0,childType0);
         }
         this.nowType = type0;
         this.nowChildType = childType0;
         var defineArr0:Array = Gaming.PG.da.achieve.getArrByFather(this.nowChildType,true);
         this.itemsBox.inData(defineArr0);
      }
   }
}

