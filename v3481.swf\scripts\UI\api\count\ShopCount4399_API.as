package UI.api.count
{
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   
   public class ShopCount4399_API
   {
      private var loader:URLLoader = new URLLoader();
      
      private var urlStr:String = "https://stat.api.4399.com/statistics_item/log.js";
      
      private var game_id:int = 100027788;
      
      public function ShopCount4399_API()
      {
         super();
         this.loader.addEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this.loader.addEventListener(Event.COMPLETE,this.loaderCompleteHandler);
      }
      
      public function start(flag0:int, count0:ShopCountObj, uid0:String) : *
      {
         var n:* = undefined;
         var url0:URLRequest = null;
         if(Gaming.api.save.isLocal())
         {
            uid0 = "607876138";
         }
         count0.u = uid0;
         count0.g = this.game_id;
         count0.f = flag0;
         var obj0:Object = count0.getObj();
         var str0:String = this.urlStr;
         var i0:int = 0;
         for(n in obj0)
         {
            str0 += (i0 > 0 ? "&" : "?") + n + "=" + obj0[n];
            i0++;
         }
         url0 = new URLRequest(str0);
         url0.method = URLRequestMethod.GET;
         this.loader.load(url0);
      }
      
      private function loaderCompleteHandler(e:Event) : void
      {
      }
      
      private function errorHandler(e:IOErrorEvent) : void
      {
      }
   }
}

