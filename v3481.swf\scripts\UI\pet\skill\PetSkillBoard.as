package UI.pet.skill
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.pet.PetUI;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.pet.PetData;
   import dataAll.skill.define.SkillDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class PetSkillBoard extends NormalUI
   {
      private var boxTag:Sprite;
      
      private var ctrlSp:Sprite;
      
      private var gripBox:ItemsGripBox = new ItemsGripBox();
      
      private var ctrl:PetSkillCtrlBox = new PetSkillCtrlBox();
      
      private var nowChooseIndex:int = 0;
      
      private var resetBtnSp:MovieClip;
      
      private var resetBtn:NormalBtn = new NormalBtn();
      
      public function PetSkillBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["boxTag","ctrlSp","resetBtnSp"];
         super.setImg(img0);
         addChild(this.gripBox);
         NormalUICtrl.setTag(this.gripBox,this.boxTag);
         this.gripBox.arg.init(2,5,12,12);
         this.gripBox.imgType = "equipGrip";
         this.gripBox.evt.setWantEvent(true,false,false,true,true);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.gripBox);
         addChild(this.resetBtn);
         this.resetBtn.setImg(this.resetBtnSp);
         this.resetBtn.setName("重置");
         this.resetBtn.addEventListener(MouseEvent.CLICK,this.resetBtnClick);
         Gaming.uiGroup.tipBox.textTip.addOverBody(this.resetBtn,this.resetBtnTip);
         addChild(this.ctrl);
         this.ctrl.setImg(this.ctrlSp);
         this.ctrl.visible = false;
         this.ctrl.father = this;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      public function fleshData() : void
      {
         var arr0:Array = null;
         var da0:PetData = PetUI.getNowData();
         if(Boolean(da0))
         {
            arr0 = da0.getUISkillArr();
            this.gripBox.inData_byArr(arr0,"inData_UIPetSkill");
         }
         else
         {
            this.gripBox.clearAllData();
         }
         this.chooseOneByIndex(this.nowChooseIndex);
      }
      
      public function setNowChooseIndex(i0:int) : void
      {
         this.nowChooseIndex = i0;
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         this.chooseOneByIndex(e.index);
      }
      
      private function chooseOneByIndex(index0:int) : void
      {
         var pda0:PetData = null;
         var label0:String = null;
         if(index0 > this.gripBox.gripArr.length - 1)
         {
            index0 = 0;
         }
         this.gripBox.setChoose_byIndex(index0);
         this.nowChooseIndex = index0;
         var grip0:ItemsGrid = this.gripBox.gripArr[index0];
         if(grip0 is ItemsGrid)
         {
            this.ctrl.visible = true;
            this.ctrl.showObj(grip0.itemsData);
            pda0 = PetUI.getNowData();
            label0 = this.ctrl.getNowBaseLabel();
            this.resetBtn.visible = pda0.gene.save.talentSkillArr.indexOf(label0) == -1;
         }
         else
         {
            this.ctrl.visible = false;
            this.resetBtn.visible = false;
         }
      }
      
      private function resetBtnClick(e:MouseEvent) : void
      {
         Gaming.uiGroup.alertBox.showChoose("重置会删除当前技能，然后获得1个随机技能。\n确定要继续吗？",this.doReset2,null,this.ctrl.getSkillGrip().icon.getIconName());
      }
      
      private function doReset2() : void
      {
         var must_d0:MustDefine = this.getResetMust();
         PlayerMustCtrl.simpleBuy(must_d0,"重置宠物技能需要：",this.yes_resetBtn);
      }
      
      private function getResetMust() : MustDefine
      {
         var m0:MustDefine = new MustDefine();
         m0.name = "resetPetSkill";
         m0.cnName = "重置当前宠物技能";
         m0.money = 50;
         m0.propId = "3645";
         return m0;
      }
      
      private function yes_resetBtn() : void
      {
         Gaming.soundGroup.playSound("uiSound","getItems");
         var before0:String = this.ctrl.getNowBaseLabel();
         var pda0:PetData = PetUI.getNowData();
         var new0:String = pda0.resetSkill(before0);
         var newD0:SkillDefine = Gaming.defineGroup.skill.getDefine(new0);
         this.fleshData();
         var tip0:String = "重置成了技能" + ComMethod.color(newD0.cnName,"#FFFF00") + "！";
         UIOrder.yesSaveTip = tip0;
      }
      
      private function resetBtnTip(e:MouseEvent) : void
      {
         var m0:MustDefine = this.getResetMust();
         var str0:String = "需求：\n" + m0.getText();
         str0 += "\n\n1、重置技能会删除当前技能，然后随机获得1个新技能。";
         str0 += "\n2、新技能需要重新学习。";
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
      }
   }
}

