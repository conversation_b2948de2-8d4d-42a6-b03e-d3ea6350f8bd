package UI.gameWorld.bodyLifeBar
{
   import UI.base.loadBar.LoadBar;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.geom.Point;
   import flash.text.TextField;
   import flash.text.TextFormatAlign;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.data.NormalBodyData;
   import gameAll.body.motion.NormalGroundMotion;
   
   public class BodyLifeBar extends LoadBar
   {
      private var skillTxt:TextField;
      
      private var effecMc:MovieClip;
      
      private var secBarSp:Sprite;
      
      private var _secPer:Number = 0;
      
      private var _skillShowB:Boolean = false;
      
      public var camp:String = "";
      
      public var targetBody:IO_NormalBody = null;
      
      private var beforeBody:IO_NormalBody = null;
      
      public var randomY:int = -int(Math.random() * 10);
      
      public function BodyLifeBar()
      {
         super();
      }
      
      public function set skillShowB(bb0:Boolean) : void
      {
         this._skillShowB = bb0;
         if(Boolean(this.skillTxt))
         {
            if(bb0)
            {
               if(!this.skillTxt.parent)
               {
                  addChild(this.skillTxt);
               }
            }
            else if(Boolean(this.skillTxt.parent))
            {
               this.skillTxt.parent.removeChild(this.skillTxt);
            }
         }
      }
      
      public function get skillShowB() : Boolean
      {
         return this._skillShowB;
      }
      
      override public function setImg(img0:Sprite, followXYB0:Boolean = true) : void
      {
         super.setImg(img0);
         this.skillTxt = img0["skillTxt"];
         this.secBarSp = img0["secBarSp"];
         this.effecMc = img0["effecMc"];
         if(Boolean(this.skillTxt))
         {
            this.skillTxt.autoSize = TextFormatAlign.CENTER;
            this.skillTxt.wordWrap = false;
         }
         this.skillShowB = false;
      }
      
      public function setTarget(b0:IO_NormalBody) : void
      {
         this.clearData();
         this.targetBody = b0;
         this.targetBody.getData().lifeBar = this;
      }
      
      public function playerEffect() : void
      {
         if(Boolean(this.effecMc))
         {
            this.effecMc.gotoAndPlay(2);
         }
      }
      
      public function setToCamp(type0:String, doubleB0:Boolean = false) : void
      {
         if(doubleB0)
         {
            this.setImg(Gaming.swfLoaderManager.getResource("GameWorldUI","doubleLifeBar"));
         }
         else if(type0 == "enemy")
         {
            this.setImg(Gaming.swfLoaderManager.getResource("GameWorldUI","enemyLifeBar"));
         }
         else
         {
            this.setImg(Gaming.swfLoaderManager.getResource("GameWorldUI","weLifeBar"));
         }
         this.camp = type0;
      }
      
      public function setSecPer(per0:Number) : void
      {
         if(this._secPer != per0 && this.secBarSp is Sprite)
         {
            if(per0 < 0)
            {
               per0 = 0;
            }
            else if(per0 > 1)
            {
               per0 = 1;
            }
            this.secBarSp.scaleX = per0;
            this._secPer = per0;
         }
      }
      
      public function setSkillTxt(skillNameArr0:Array) : void
      {
         var y0:int = 0;
         var str0:String = null;
         var n:* = undefined;
         if(this._skillShowB)
         {
            if(Boolean(this.skillTxt))
            {
               y0 = -skillNameArr0.length * 15 - 4;
               this.skillTxt.y = y0;
               str0 = "";
               for(n in skillNameArr0)
               {
                  str0 += skillNameArr0[n];
                  if(n < skillNameArr0.length - 1)
                  {
                     str0 += "\n";
                  }
               }
               this.skillTxt.text = str0;
            }
         }
      }
      
      public function flesh() : void
      {
         var data0:NormalBodyData = null;
         var mot0:NormalGroundMotion = null;
         var p0:Point = null;
         if(this.targetBody is IO_NormalBody)
         {
            data0 = this.targetBody.getData();
            if(this.camp != data0.camp || data0.camp == "we" && this.targetBody.getDieCtrl().canClearLifeBar())
            {
               this.targetBody = null;
               visible = false;
            }
            else
            {
               mot0 = this.targetBody.getMot();
               setPer(data0.getLifePer());
               if(data0.define.lifeBarHaveLvB())
               {
                  setText(data0.getLifeBarLeveText());
                  setBackVisible(true);
               }
               else
               {
                  setText("");
                  setBackVisible(false);
               }
               p0 = this.targetBody.getAttack().getLifeBarPoint();
               this.x = int(p0.x);
               this.y = int(p0.y + this.randomY);
               if(this.beforeBody != this.targetBody)
               {
                  if(this._skillShowB)
                  {
                     this.setSkillTxt(this.targetBody.getSkill().getNowSkillCnNameArr());
                  }
                  this.beforeBody = this.targetBody;
               }
               if(this.targetBody.getDie() == 0 && this.targetBody.getData().existB)
               {
                  if(data0.camp == "enemy" && !this.targetBody.getAiFindB())
                  {
                     this.visible = false;
                  }
                  else
                  {
                     this.visible = true;
                  }
               }
               else
               {
                  this.visible = false;
               }
            }
         }
         else
         {
            this.visible = false;
         }
      }
      
      override public function clearData() : void
      {
         super.clearData();
         if(Boolean(this.targetBody))
         {
            this.targetBody.getData().lifeBar = null;
         }
         this.targetBody = null;
         this.beforeBody = null;
      }
   }
}

