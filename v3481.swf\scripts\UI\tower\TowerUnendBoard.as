package UI.tower
{
   import UI.UIOrder;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.box.NormalBox;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.label.LabelBox;
   import UI.peak.PeakProBar;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.DisplayMethod;
   import com.sounto.utils.NumberMethod;
   import dataAll._app.peak.PeakProDefine;
   import dataAll._app.tower.UnendAgent;
   import dataAll._app.tower.UnendData;
   import dataAll._app.tower.UnendDataCtrl;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll.body.attack.ElementShell;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.level.TempLevel;
   import dataAll.level.define.LevelDefine;
   import dataAll.level.define.unit.UnitType;
   import dataAll.level.modeDiy.ModeDiyDefine;
   import dataAll.skill.define.SkillDescrip;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class TowerUnendBoard extends AutoNormalUI
   {
      private var scoreTxt:TextField;
      
      private var infoTxt:TextField;
      
      private var lvMc:MovieClip;
      
      private var lvTxt:TextField;
      
      private var prevBtn:NormalBtn;
      
      private var nextBtn:NormalBtn;
      
      private var bossTxt:TextField;
      
      private var bossTag:Sprite;
      
      private var bossBox:NormalBox = new NormalBox();
      
      private var bossProTxt:TextField;
      
      private var enemyTxt:TextField;
      
      private var enemyTag:Sprite;
      
      private var enemyBox:NormalBox = new NormalBox();
      
      private var enemyProTxt:TextField;
      
      private var giftTag:Sprite;
      
      private var giftTxt:TextField;
      
      private var giftBox:ItemsGripBox = new ItemsGripBox();
      
      private var limitTxt:TextField;
      
      private var limitInfoTxt:TextField;
      
      private var fightBtn:NormalBtn;
      
      private var pointCoverSp:Sprite;
      
      private var pointTxt:TextField;
      
      private var pointInfoTxt:TextField;
      
      private var resetBtn:NormalBtn;
      
      private var planLabelBox:LabelBox = new LabelBox();
      
      private var pointTag:Sprite;
      
      private var barArr:Array = [];
      
      private var nowAgent:UnendAgent = null;
      
      private var errorB:Boolean = false;
      
      public function TowerUnendBoard()
      {
         super();
         mcTypeArr = ["btnSp","txt","tag"];
      }
      
      private static function get CTRL() : UnendDataCtrl
      {
         return Gaming.defineGroup.unend;
      }
      
      private static function get DATA() : UnendData
      {
         return Gaming.PG.da.tower.unend;
      }
      
      private static function get testB() : Boolean
      {
         return Gaming.testCtrl.canCheatingB();
      }
      
      private static function gotoMap(a0:UnendAgent) : void
      {
         var levelD0:LevelDefine = a0.getLevelDefine(DATA);
         TempLevel.addInObj(levelD0,levelD0.name);
         DATA.setNow(a0);
         Gaming.LG.chooseByLevelDefine(levelD0);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = elementNameArr.concat(["lvMc","pointCoverSp"]);
         super.setImg(img0);
         this.lvMc.stop();
         this.lvTxt.addEventListener(MouseEvent.CLICK,this.lvTxtClick);
         this.bossTag.addChild(this.bossBox);
         this.bossBox.arg.init(5,1,6,0);
         this.bossBox.setIconPro("TowerUI/unendBossGrip");
         this.bossBox.evt.setWant(false,true);
         this.bossBox.addEventListener(ClickEvent.ON_OVER,this.bossGripOver);
         this.bossBox.addEventListener(ClickEvent.ON_OUT,Gaming.uiGroup.tipBox.hide);
         this.bossTxt.addEventListener(MouseEvent.MOUSE_OVER,this.bossTxtOver);
         this.bossTxt.addEventListener(MouseEvent.MOUSE_OUT,Gaming.uiGroup.tipBox.hide);
         this.enemyTag.addChild(this.enemyBox);
         this.enemyBox.arg.init(5,1,6,0);
         this.enemyBox.setIconPro("TowerUI/unendBossGrip");
         this.enemyBox.evt.setWant(false,true);
         this.enemyTxt.addEventListener(MouseEvent.MOUSE_OVER,this.bossTxtOver);
         this.enemyTxt.addEventListener(MouseEvent.MOUSE_OUT,Gaming.uiGroup.tipBox.hide);
         this.giftTag.addChild(this.giftBox);
         this.giftBox.arg.init(5,1,6,0);
         this.giftBox.setIconPro("TowerUI/unendBossGrip");
         this.giftBox.evt.setWant(false,true);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.giftBox);
         FontDeal.dealLine(this.limitInfoTxt);
         this.limitInfoTxt.addEventListener(MouseEvent.MOUSE_OVER,this.limitInfoTextOver);
         this.limitInfoTxt.addEventListener(MouseEvent.MOUSE_OUT,Gaming.uiGroup.tipBox.hide);
         this.planLabelBox.arg.init(3,1,-8,0);
         this.planLabelBox.inData("TowerUI/labelBtn",UnendData.planArr,UnendData.planCnArr);
         this.planLabelBox.setChoose_byIndex(0);
         this.planLabelBox.addEventListener(ClickEvent.ON_CLICK,this.planLabelClick);
         addChild(this.planLabelBox);
         this.planLabelBox.x = this.pointTag.x;
         this.planLabelBox.y = this.pointTag.y - 2;
         FontDeal.dealOne(this.pointTxt);
         FontDeal.dealOne(this.pointInfoTxt);
         this.pointTxt.addEventListener(MouseEvent.MOUSE_OVER,this.pointTxtOver);
         this.pointTxt.addEventListener(MouseEvent.MOUSE_OUT,Gaming.uiGroup.tipBox.hide);
         addChild(this.pointCoverSp);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
         this.winPan();
         this.showLv(DATA.getUILv());
         this.fleshBar();
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var funName0:String = btn0.label + "BtnClick";
         this[funName0](e);
      }
      
      private function winPan() : void
      {
         var winTip0:String = DATA.getWinTipAndClear();
         if(winTip0 != "")
         {
            Gaming.uiGroup.alertBox.showSuccess(winTip0);
         }
      }
      
      private function fleshInfo() : void
      {
         this.infoTxt.htmlText = FontDeal.getDealLeadingStr(this.infoTxt,DATA.getInfoStr());
      }
      
      private function showLv(lv0:int) : void
      {
         var maxB0:Boolean = false;
         var winB0:Boolean = false;
         var unlockB0:Boolean = false;
         var mapD0:WorldMapDefine = null;
         var mapUnlockB0:Boolean = false;
         var info0:String = null;
         if(testB)
         {
            lv0 = UnendAgent.dealLv(lv0);
         }
         else
         {
            lv0 = DATA.dealLv(lv0);
         }
         DATA.setUILv(lv0);
         var a0:UnendAgent = CTRL.getAgent(lv0);
         this.nowAgent = a0;
         if(Boolean(a0))
         {
            maxB0 = a0.isMaxB();
            winB0 = DATA.isWinB(lv0);
            unlockB0 = DATA.isUnlockB(lv0);
            this.prevBtn.visible = lv0 > 1;
            this.nextBtn.visible = !maxB0;
            if(testB)
            {
               this.nextBtn.actived = true;
            }
            else
            {
               this.nextBtn.actived = DATA.isUnlockB(lv0 + 1);
            }
            this.lvMc.visible = true;
            if(winB0)
            {
               this.lvMc.gotoAndStop("win");
            }
            else if(unlockB0 == false)
            {
               this.lvMc.gotoAndStop("lock");
            }
            else
            {
               this.lvMc.visible = false;
            }
            this.lvTxt.htmlText = ComMethod.color("第 ","#999999",16) + lv0 + ComMethod.color(" 层","#999999",16);
            this.errorB = false;
            this.bossBox.inData_byArr(a0.bossCnArr,this.inBossGripFun);
            this.enemyBox.inData_byArr(a0.enemyCnArr,this.inGripFun);
            this.bossProTxt.htmlText = a0.getUILife(UnitType.BOSS) + "\n" + a0.getUIDps(UnitType.BOSS);
            this.enemyProTxt.htmlText = a0.getUILife(UnitType.NORMAL) + "\n" + a0.getUIDps(UnitType.NORMAL);
            this.giftBox.inData_byGift(a0.gift);
            mapD0 = Gaming.defineGroup.worldMap.getDefine(a0.map);
            mapUnlockB0 = Boolean(Gaming.PG.da.worldMap.saveGroup.getSave(a0.map));
            info0 = "·地图：" + mapD0.cnName + (!mapUnlockB0 ? ComMethod.red("（未解锁）") : "");
            this.limitInfoTxt.htmlText = info0 + "\n" + a0.getModeDiyDef().decrip;
            this.fightBtn.visible = unlockB0;
            if(testB)
            {
               this.fightBtn.visible = true;
            }
            this.fightBtn.setName(winB0 ? "已通关" : "挑战");
            this.fightBtn.actived = !this.errorB && mapUnlockB0;
         }
         else
         {
            this.lvMc.visible = false;
            this.prevBtn.visible = false;
            this.nextBtn.visible = false;
            this.fightBtn.visible = false;
            this.bossBox.clearAllData();
            this.enemyBox.clearAllData();
            this.giftBox.clearAllData();
            this.limitInfoTxt.htmlText = "";
         }
         this.fleshInfo();
      }
      
      private function inBossGripFun(grip0:NormalBtn, cn0:String) : void
      {
         this.inGripFun(grip0,cn0,UnitType.BOSS);
      }
      
      private function inGripFun(grip0:NormalBtn, cn0:String, unitType0:String = "normal") : void
      {
         var d0:NormalBodyDefine = null;
         var lifeMul0:Number = NaN;
         var dpsMul0:Number = NaN;
         d0 = Gaming.defineGroup.body.getCnDefine(cn0);
         if(Boolean(d0))
         {
            grip0.label = cn0;
            grip0.itemsData = d0;
            grip0.setIconName(d0.headIconUrl);
            grip0.setNumText("");
            grip0.setSmallIcon(d0.shell);
            if(testB)
            {
               if(unitType0 == UnitType.BOSS)
               {
                  lifeMul0 = UnendAgent.GET_BODY_LIFE_MUL(d0,this.nowAgent.lv,unitType0);
                  dpsMul0 = UnendAgent.GET_BODY_DPS_MUL(d0,this.nowAgent.lv,unitType0);
                  if(lifeMul0 != 1 || dpsMul0 != 1)
                  {
                     grip0.setNumText(lifeMul0 + "+" + dpsMul0);
                  }
               }
            }
         }
         else
         {
            grip0.clearData();
            this.errorB = true;
         }
      }
      
      private function bossGripOver(e:ClickEvent) : void
      {
         var tip0:String = null;
         var d0:NormalBodyDefine = e.childData as NormalBodyDefine;
         if(Boolean(d0))
         {
            tip0 = "<b><yellow " + d0.cnName + "/></b>[" + ElementShell.getSensitiveCn(d0.shell,true) + "]\n";
            if(e.father == this.bossBox)
            {
               tip0 += d0.getEditSkillTip();
            }
            UIOrder.showTip(tip0);
         }
      }
      
      private function bossTxtOver(e0:MouseEvent) : void
      {
         var txt0:TextField = null;
         var tip0:String = null;
         var a0:UnendAgent = null;
         if(Boolean(this.nowAgent))
         {
            txt0 = e0.target as TextField;
            tip0 = "";
            a0 = this.nowAgent;
            if(txt0 == this.bossTxt)
            {
               tip0 += "首领血量系数：" + a0.getUILife(UnitType.BOSS);
               tip0 += "\n首领伤害系数：" + a0.getUIDps(UnitType.BOSS);
               tip0 += "\n\n首先出场第1位首领，这位首领生命值低于30%时将自己隐藏，并出场第2位首领，以此类推。3位首领的生命值都低于30%时，将全部出场。";
            }
            else if(txt0 == this.enemyTxt)
            {
               tip0 += "小怪血量系数：" + a0.getUILife(UnitType.NORMAL);
               tip0 += "\n小怪伤害系数：" + a0.getUIDps(UnitType.NORMAL);
            }
            UIOrder.showTip(tip0);
         }
      }
      
      private function limitInfoTextOver(e:MouseEvent) : void
      {
         var newSkillArr0:Array = null;
         var tip0:String = "";
         if(Boolean(this.nowAgent))
         {
            newSkillArr0 = this.nowAgent.getBossSkillArr();
            if(newSkillArr0.length > 0)
            {
               tip0 += "·首领额外技能：" + SkillDescrip.getCnText(newSkillArr0) + "。";
            }
         }
         var md0:ModeDiyDefine = Gaming.defineGroup.modelDiy.getDefine(ModeDiyDefine.UNEND);
         tip0 += "\n·小白额外增伤30%。";
         tip0 += "\n·无疆骑士“铁拳”眩晕无效。";
         tip0 += "\n·未羊技能无效。";
         tip0 += "\n·光锥欺盲效果降低至" + NumberMethod.toPer(md0.lightConeBlindMul) + "。";
         tip0 += "\n·武器击中回复效果降低。";
         tip0 += "\n·怒气回复技能削弱至" + NumberMethod.toPer(md0.angerAddMulMul) + "。";
         tip0 += "\n·表哥真空只会提高防御力，不会无敌。";
         tip0 += "\n·心零复仇技能无效。";
         tip0 += "\n·骷髅权杖无效。";
         tip0 += "\n·敌人首领无视惊吓效果。";
         UIOrder.showTip(tip0);
      }
      
      private function lvTxtClick(e0:MouseEvent) : void
      {
         var max0:int = DATA.getUnlockMaxLv();
         if(testB)
         {
            max0 = UnendAgent.MAX_LV;
         }
         Gaming.uiGroup.alertBox.showNumChoose("输入层数(1~" + max0 + ")",DATA.getUILv(),max0,1,1,this.yesLvTxtClick,"yesAndNo",null,null,false);
      }
      
      private function yesLvTxtClick(lv0:Number) : void
      {
         this.showLv(lv0);
      }
      
      private function prevBtnClick(e0:MouseEvent) : void
      {
         this.showLv(DATA.getUILv() - 1);
      }
      
      private function nextBtnClick(e0:MouseEvent) : void
      {
         this.showLv(DATA.getUILv() + 1);
      }
      
      private function fightBtnClick(e0:MouseEvent) : void
      {
         if(Boolean(this.nowAgent))
         {
            gotoMap(this.nowAgent);
         }
      }
      
      private function planLabelClick(e:ClickEvent) : void
      {
         var bb0:Boolean = DATA.swapPointPlan(e.label);
         if(!bb0)
         {
            Gaming.uiGroup.alertBox.showError("不存在方案" + e.label);
         }
         else
         {
            this.planLabelBox.setChoose(e.label);
            this.fleshBar();
         }
      }
      
      private function fleshBar() : void
      {
         var darr0:Array = null;
         var d0:PeakProDefine = null;
         var bar0:PeakProBar = null;
         var url0:String = null;
         var all0:int = DATA.getAllPoint();
         var use0:int = DATA.getUsePoint();
         this.pointCoverSp.visible = all0 == 0;
         this.pointTxt.htmlText = "虚天点数 " + use0 + "/" + all0;
         if(this.barArr.length == 0)
         {
            darr0 = Gaming.defineGroup.unend.proG.arr;
            for each(d0 in darr0)
            {
               bar0 = new PeakProBar();
               url0 = "TowerUI/proBar";
               bar0.setImgUrl(url0);
               bar0.inDefine(d0,DATA,this.fleshBar);
               this.pointTag.addChild(bar0);
               this.barArr.push(bar0);
            }
            DisplayMethod.arrange(this.barArr,0,-1,1);
         }
         for each(bar0 in this.barArr)
         {
            bar0.fleshDataBy(DATA);
         }
      }
      
      private function resetBtnClick(e0:MouseEvent) : void
      {
         DATA.resetPoint();
         this.fleshBar();
      }
      
      private function pointTxtOver(e:MouseEvent) : void
      {
         var max0:int = 0;
         var now0:int = 0;
         var tip0:String = null;
         if(Boolean(this.nowAgent))
         {
            max0 = UnendData.LEVEL_POINT;
            now0 = DATA.getNowGetPoint();
            tip0 = "";
            tip0 += "·当前层已获得点数：" + ComMethod.dropColor(now0,max0);
            tip0 += "\n·每击杀1次未通关层的首领，将获得1点点数。";
            tip0 += "\n·每1层最多获得" + max0 + "点点数。";
            tip0 += "\n<orange ·所有加点效果只在虚天塔中有效。/>";
            UIOrder.showTip(tip0);
         }
      }
   }
}

