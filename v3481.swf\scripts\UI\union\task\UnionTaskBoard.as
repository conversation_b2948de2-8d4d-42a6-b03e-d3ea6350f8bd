package UI.union.task
{
   import UI.UIOrder;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.count.CountCtrl;
   import com.sounto.cf.NiuBiCF;
   import dataAll._app.union.UnionData;
   import dataAll._app.union.task.UnionTaskData;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class UnionTaskBoard extends NormalUI
   {
      private var barTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var box:ItemsGripBox = new ItemsGripBox();
      
      private var allBtnSp:MovieClip;
      
      private var allBtn:NormalBtn = new NormalBtn();
      
      private var nowTaskData:UnionTaskData = null;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public function UnionTaskBoard()
      {
         super();
         this.loopIndex = 0;
      }
      
      public function get loopIndex() : Number
      {
         return this.CF.getAttribute("loopIndex");
      }
      
      public function set loopIndex(v0:Number) : void
      {
         this.CF.setAttribute("loopIndex",v0);
      }
      
      public function get loopGetNum() : Number
      {
         return this.CF.getAttribute("loopGetNum");
      }
      
      public function set loopGetNum(v0:Number) : void
      {
         this.CF.setAttribute("loopGetNum",v0);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["barTag","allBtnSp"];
         super.setImg(img0);
         addChild(this.allBtn);
         this.allBtn.setImg(this.allBtnSp);
         this.allBtn.setName("领取所有贡献");
         this.allBtn.addEventListener(MouseEvent.CLICK,this.allBtnClick);
         this.box.setIconPro("UnionUI/taskBar",60,60);
         this.box.arg.init(1,9,5,0);
         this.box.x = this.barTag.x;
         this.box.y = this.barTag.y;
         this.box.evt.setWant(true,true);
         this.box.setCtrlBtnImgType("UnionUI/taskBtn","666,5");
         this.box.addEventListener(ClickEvent.ON_CTRL_CLICK,this.ctrlBtnClick);
         this.box.addEventListener(ClickEvent.ON_OVER,this.barOver);
         this.box.addEventListener(ClickEvent.ON_OUT,Gaming.uiGroup.tipBox.hide);
         addChild(this.box);
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
         var tipB0:Boolean = this.unionData.gotoTaskBoard(Gaming.api.save.getNowServerDate());
         if(tipB0)
         {
            Gaming.uiGroup.alertBox.showError("注意！新加入成员在24小时后才能领取贡献。");
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function get unionData() : UnionData
      {
         return Gaming.PG.da.union;
      }
      
      private function fleshData() : void
      {
         var da0:UnionTaskData = null;
         Gaming.PG.da.fleshMaxDps();
         var arr0:Array = this.unionData.getTaskDataArr();
         this.box.inData_byArr(arr0,"inData_unionTask");
         var haveNum0:int = 0;
         var allGetB0:Boolean = true;
         for each(da0 in arr0)
         {
            if(da0.canGetB())
            {
               haveNum0++;
            }
            if(!da0.save.getB)
            {
               allGetB0 = false;
            }
         }
         this.allBtn.actived = haveNum0 > 0;
         this.allBtn.visible = !allGetB0;
      }
      
      private function ctrlBtnClick(e:ClickEvent) : void
      {
         var da0:UnionTaskData = e.childData as UnionTaskData;
         this.nowTaskData = da0;
         CountCtrl.addUnionContribution(da0.def.contribution);
         Gaming.uiGroup.connectUI.show();
         Gaming.api.union.grow.doTask(Gaming.getSaveIndex(),da0.def.id,this.yes_donation,this.no_donation);
      }
      
      private function yes_donation(bb0:Boolean) : void
      {
         if(bb0)
         {
            Gaming.uiGroup.connectUI.hide();
            this.unionData.getTaskContributionEvent(this.nowTaskData.def.name);
            this.fleshData();
            UIOrder.save(true,true,false,this.afterSave,this.afterSave);
         }
         else
         {
            this.no_donation("");
         }
      }
      
      private function no_donation(str0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError("领取贡献失败！\n" + str0);
      }
      
      private function barOver(e:ClickEvent) : void
      {
         var da0:UnionTaskData = e.childData as UnionTaskData;
         var tip0:String = this.unionData.getTaskBarTip(da0.def.name);
         UIOrder.showTip(tip0);
      }
      
      private function allBtnClick(e:MouseEvent) : void
      {
         this.loopIndex = -1;
         this.loopGetNum = 0;
         this.getGiftLoop();
      }
      
      private function getGiftLoop() : void
      {
         var da0:UnionTaskData = null;
         var arr0:Array = this.unionData.getTaskDataArr();
         if(this.loopIndex < arr0.length - 1)
         {
            ++this.loopIndex;
            da0 = arr0[this.loopIndex];
            if(da0.canGetB())
            {
               this.nowTaskData = da0;
               CountCtrl.addUnionContribution(da0.def.contribution);
               Gaming.uiGroup.connectUI.show("");
               Gaming.api.union.grow.doTask(Gaming.getSaveIndex(),da0.def.id,this.yes_loop,this.no_loop);
            }
            else
            {
               this.getGiftLoop();
            }
         }
         else
         {
            this.over_loop();
         }
      }
      
      private function yes_loop(bb0:Boolean) : void
      {
         var grip0:ItemsGrid = null;
         if(bb0)
         {
            Gaming.uiGroup.connectUI.hide();
            this.unionData.getTaskContributionEvent(this.nowTaskData.def.name);
            grip0 = this.box.gripArr[this.loopIndex];
            grip0.inData_unionTask(this.nowTaskData);
            ++this.loopGetNum;
            this.getGiftLoop();
         }
         else
         {
            this.no_donation("");
         }
      }
      
      private function no_loop(str0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError("领取贡献终止！\n" + str0);
         this.over_loop();
      }
      
      private function over_loop() : void
      {
         if(this.loopGetNum > 0)
         {
            this.loopGetNum = 0;
            this.fleshData();
            UIOrder.save(true,true,false,this.afterSave,this.afterSave);
         }
      }
      
      private function afterSave(e:* = null) : void
      {
         Gaming.uiGroup.unionUI.flesher.fleshMemberInfo();
      }
   }
}

