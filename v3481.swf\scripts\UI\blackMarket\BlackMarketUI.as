package UI.blackMarket
{
   import UI.bag.BagUI;
   import UI.base.AppNormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import dataAll.items.ItemsCompareData;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class BlackMarketUI extends AppNormalUI
   {
      public var labelBox:LabelBox = new LabelBox();
      
      public var browseBoard:BlackMarketBrowseBoard = new BlackMarketBrowseBoard();
      
      private var labelNameArr:Array = ["browse"];
      
      private var labelTag:Sprite;
      
      private var closeBtn:SimpleButton;
      
      public function BlackMarketUI()
      {
         super();
         UICn = "黑市";
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["labelTag"];
         super.setImg(img0);
         var cnNameArr0:Array = ["市场"];
         this.labelBox.arg.init(10,1,-9,0);
         this.labelBox.inData("longLabelBtn",this.labelNameArr,cnNameArr0);
         this.labelBox.setChoose(this.labelNameArr[0]);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.labelBox.x = this.labelTag.x;
         this.labelBox.y = this.labelTag.y;
         addChild(this.labelBox);
         addChild(this.browseBoard);
         this.browseBoard.setImg(Gaming.swfLoaderManager.getResource("BlackMarketUI","browseBoard"));
         this.browseBoard.visible = false;
         this.browseBoard.blackMarketUI = this;
         this.browseBoard.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         addChild(this.labelBox);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         var label0:String = this.labelBox.nowLabel;
         this.showBox(label0);
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      public function showBox(label0:String) : void
      {
         if(label0 == "")
         {
            label0 = this.labelBox.getFirstLabel();
         }
         this.labelBox.setChoose(label0);
         this.browseBoard.hide();
         if(label0 == "browse")
         {
            this.browseBoard.show();
         }
         else if(label0 == "trading")
         {
         }
      }
      
      override public function getNowUICompareData(dd0:ItemsCompareData) : ItemsCompareData
      {
         if(this.browseBoard.visible)
         {
            return this.browseBoard.getNowUICompareData(dd0);
         }
         return ItemsCompareData.ZERO;
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.hide();
      }
      
      override public function hide() : void
      {
         var bagUI:BagUI = Gaming.uiGroup.bagUI;
         bagUI.ctrl.clearFleshFun();
         bagUI.unlockAllLabel();
         if(bagUI.visible)
         {
            bagUI.hide();
         }
         super.hide();
      }
      
      public function outLoginEvent() : void
      {
      }
      
      override public function get width() : Number
      {
         return 833;
      }
   }
}

