package UI.main
{
   import UI.UIShow;
   import UI.base.NormalUI;
   import UI.base.alert.AlertBox;
   import UI.base.font.FontDeal;
   import UI.base.grid.NormalGrid;
   import UI.gameWorld.GameWorldIconBtn;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.StringMethod;
   import dataAll._app.blackMarket.BlackMarketCtrl;
   import dataAll._app.parts.PartsMethod;
   import dataAll._app.worldMap.save.WorldMapSave;
   import dataAll._app.worldMap.save.WorldMapSaveGroup;
   import dataAll._player.PlayerData;
   import dataAll.pet.PetCount;
   import dataAll.ui.guide.GuideSave;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import gameAll.drop.BodyDropCtrl;
   import gameAll.level._diy.active.YXDumplingLevelDiy;
   import gameAll.level._diy.active.ZongziChaseLevelDiy;
   
   public class MainUI extends NormalUI
   {
      public var bbsBtn:SimpleButton;
      
      public var noticeBtn:SimpleButton;
      
      public var helperBtn:SimpleButton;
      
      public var topBtn:SimpleButton;
      
      public var payBtn:SimpleButton;
      
      public var wearBtnSp:Sprite;
      
      public var skillBtnSp:Sprite;
      
      public var taskBtnSp:Sprite;
      
      public var arenaBtnSp:Sprite;
      
      public var forgingBtnSp:Sprite;
      
      public var petBtnSp:Sprite;
      
      public var partsBtnSp:Sprite;
      
      public var bosseditBtnSp:Sprite;
      
      public var shopBtnSp:Sprite;
      
      public var settingBtnSp:Sprite;
      
      public var giftBtnSp:Sprite;
      
      public var vipBtnSp:Sprite;
      
      public var blackMarketBtnSp:Sprite;
      
      public var achieveBtnSp:Sprite;
      
      public var houseBtnSp:Sprite;
      
      public var askBtnSp:Sprite;
      
      public var vehicleBtnSp:Sprite;
      
      public var unionBtnSp:Sprite;
      
      public var headBtnSp:Sprite;
      
      public var postBtnSp:Sprite;
      
      public var cityBtnSp:Sprite;
      
      public var wilderBtnSp:Sprite;
      
      public var peakBtnSp:Sprite;
      
      public var demonBtnSp:Sprite;
      
      public var linkBtnSp:Sprite;
      
      public var link2BtnSp:Sprite;
      
      public var springBtnSp:Sprite;
      
      public var spaceBtnSp:Sprite;
      
      public var towerBtnSp:Sprite;
      
      public var gameBoxBtnSp:Sprite;
      
      public var newSp:Sprite;
      
      public var activeSp:Sprite;
      
      public var coverSp:Sprite;
      
      public var smallMapSp:Sprite;
      
      public var smallMap:MainSmallMapBox = new MainSmallMapBox();
      
      public var giftHomeBtnMc:Sprite;
      
      public var coinTxt:TextField;
      
      public var moneyTxt:TextField;
      
      public var expTxt:TextField;
      
      private var expShowIsZeroB:Boolean = false;
      
      private var btnObj:Object = {};
      
      private var haveSmallIconArr:Array = ["skill","task","parts","blackMarket","pet"];
      
      public function MainUI()
      {
         super();
         this.mouseEnabled = false;
      }
      
      private static function get PD() : PlayerData
      {
         return Gaming.PG.da;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var n:* = undefined;
         var btnName0:String = null;
         var index0:int = 0;
         var name0:String = null;
         var btn0:GameWorldIconBtn = null;
         var sp0:MovieClip = null;
         elementNameArr = ClassProperty.getProArr(this,false);
         super.setImg(img0);
         FontDeal.dealOne(this.expTxt);
         FontDeal.dealOne(this.coinTxt);
         FontDeal.dealOne(this.moneyTxt);
         addChildAt(this.smallMap,0);
         this.smallMap.setImg(this.smallMapSp);
         img0.mouseEnabled = false;
         this.newSp.visible = false;
         this.activeSp.visible = false;
         this.activeSp.mouseChildren = false;
         this.activeSp.mouseEnabled = false;
         for(n in elementNameArr)
         {
            btnName0 = elementNameArr[n];
            index0 = int(btnName0.indexOf("BtnSp"));
            if(index0 > 0)
            {
               name0 = btnName0.substring(0,index0);
               btn0 = new GameWorldIconBtn();
               btn0.tweenB = false;
               sp0 = this[btnName0];
               if(sp0.y < 200)
               {
                  btn0.tweenAddY = -15;
               }
               else
               {
                  btn0.tweenAddY = 15;
               }
               btn0.setNew(false);
               btn0.mouseIconEffectB = true;
               btn0.activedAndEnabled = false;
               btn0.activedAndTextGray = true;
               addChild(btn0);
               btn0.setImg(sp0);
               btn0.label = name0;
               this.btnObj[name0] = btn0;
               btn0.addEventListener(MouseEvent.CLICK,this.btnClick);
               btn0.addEventListener(MouseEvent.MOUSE_OVER,this.btnOver);
               btn0.addEventListener(MouseEvent.MOUSE_OUT,this.btnOut);
            }
         }
         this.setBtnData("wear","背包","IconGather/wear");
         this.setBtnData("skill","技能","IconGather/skill");
         this.setBtnData("task","任务","IconGather/task");
         this.setBtnData("bossedit","首领工厂","IconGather/bossedit");
         this.setBtnData("blackMarket","神秘商人","IconGather/blackMarket");
         this.setBtnData("forging","锻造","IconGather/forging");
         this.setBtnData("pet","尸宠","IconGather/pet");
         this.setBtnData("parts","零件","IconGather/parts");
         this.setBtnData("shop","商店","IconGather/shop");
         this.setBtnData("setting","系统","IconGather/setting");
         this.setBtnData("arena","竞技场","IconGather/arena");
         this.setBtnData("achieve","成就勋章","IconGather/achieve");
         this.setBtnData("gift","礼包","IconGather/dailySign");
         this.setBtnData("vip","永久VIP","IconGather/vip");
         this.setBtnData("house","仓库","IconGather/house");
         this.setBtnData("ask","问答","IconGather/ask");
         this.setBtnData("vehicle","载具","IconGather/vehicle");
         this.setBtnData("union","军队","IconGather/union");
         this.setBtnData("head","称号","IconGather/head");
         this.setBtnData("post","职务","IconGather/post");
         this.setBtnData("city","战斧主城","IconGather/city");
         this.setBtnData("wilder","秘境","IconGather/wilder");
         this.getBtn("wilder").setSmallIcon("new");
         this.setBtnData("peak","巅峰等级","IconGather/peak");
         this.setBtnData("demon","修罗地图","IconGather/demon");
         this.setBtnData("space","太空探索","IconGather/space");
         this.setBtnData("tower","虚天塔","IconGather/tower");
         this.getBtn("spring").visible = false;
         this.setBtnData("gameBox","专属礼包","IconGather/gameBox");
         this.setBtnData("link","暑期签到","IconGather/sign");
         this.getBtn("link").visible = true;
         this.setBtnData("link2","掉汤圆","IconGather/yuanXiao");
         this.getBtn("link2").visible = false;
         this.noticeBtn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.helperBtn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.bbsBtn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.topBtn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.payBtn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.expTxt.addEventListener(MouseEvent.MOUSE_OVER,this.txtOver);
         this.expTxt.addEventListener(MouseEvent.MOUSE_OUT,this.txtOut);
         this.coverSp.visible = true;
         this.coverSp.mouseChildren = false;
         this.coverSp.mouseEnabled = false;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function getBtn(name0:String) : NormalGrid
      {
         return this.btnObj[name0];
      }
      
      private function setBtnData(name0:String, cnName0:String, iconUrl0:String) : void
      {
         var btn0:NormalGrid = this.getBtn(name0);
         btn0.setName(cnName0);
         btn0.setIconName(iconUrl0);
      }
      
      public function affterReadSave() : void
      {
         this.hideAllBtnUnlockShow();
         UIShow.main();
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
         Gaming.uiGroup.worldMapBox.hide();
      }
      
      private function fleshData() : void
      {
         Gaming.uiGroup.worldMapBox.show();
         this.smallMap.fleshData();
         this.fleshCoin();
         this.fleshBtn();
         this.activeSp.visible = BodyDropCtrl.getDropText() != "";
      }
      
      public function fleshBtn() : void
      {
         var task_btn:NormalGrid = null;
         var s0:WorldMapSaveGroup = Gaming.PG.da.worldMap.saveGroup;
         var ls1:WorldMapSave = s0.getSave("XiChi");
         var ls2:WorldMapSave = s0.getSave("BaiLu");
         var bb1:Boolean = Boolean(ls1) ? ls1.winB : false;
         var bb2:Boolean = Boolean(ls2) ? ls2.winB : false;
         var skill_btn:NormalGrid = this.getBtn("skill");
         task_btn = this.getBtn("task");
         skill_btn.setSaturationFilter(bb1 ? 1 : 0);
         skill_btn.actived = bb1;
         skill_btn.secoundEnabled = bb1;
         task_btn.setSaturationFilter(bb2 ? 1 : 0);
         task_btn.actived = bb2;
         task_btn.secoundEnabled = bb2;
         this.setOneBtnUnlockState("parts",PartsMethod.levelCanB());
         this.setOneBtnUnlockState("blackMarket",BlackMarketCtrl.levelCanB());
         this.setOneBtnUnlockState("pet",PetCount.levelCanB());
         if(task_btn.getSmallIconStr() != "unlock")
         {
            task_btn.setSmallIcon(Gaming.PG.da.task.getLeftSmallIcon());
         }
         this.bagFillPan();
      }
      
      private function setOneBtnUnlockState(label0:String, canUnlockB0:Boolean) : void
      {
         var guide0:GuideSave = Gaming.PG.save.guide;
         var _btn:NormalGrid = this.getBtn(label0);
         if(!_btn.actived && canUnlockB0 && !guide0.getOne(label0 + "Unlock"))
         {
            _btn.setSmallIcon("unlock");
            guide0.setOne(label0 + "Unlock",true);
         }
         _btn.setSaturationFilter(canUnlockB0 ? 1 : 0);
         _btn.actived = canUnlockB0;
         _btn.secoundEnabled = canUnlockB0;
      }
      
      public function hideAllBtnUnlockShow() : void
      {
         var n:* = undefined;
         var label0:String = null;
         for(n in this.haveSmallIconArr)
         {
            label0 = this.haveSmallIconArr[n];
            this.getBtn(label0).setSmallIcon("");
         }
      }
      
      public function bagFillPan() : void
      {
         this.getBtn("wear").setSmallIcon(Gaming.PG.da.getBagFillB() ? "fill" : "");
      }
      
      public function fleshCoin() : void
      {
         if(visible)
         {
            if(Boolean(this.coinTxt))
            {
               this.coinTxt.htmlText = NumberMethod.toBigWan(PD.main.save.coin);
               this.moneyTxt.htmlText = PD.main.money + "";
               this.fleshDoubleTime();
            }
         }
      }
      
      private function fleshDoubleTime() : void
      {
         var v0:Number = Gaming.PG.da.time.getDoubleTime("doubleExpTime");
         var timeStr0:String = ComMethod.getTimeStr(v0);
         if(v0 > 0 || v0 == 0 && !this.expShowIsZeroB)
         {
            this.expTxt.htmlText = "双倍经验 " + ComMethod.color(timeStr0,"#FFFF00");
            this.expShowIsZeroB = v0 == 0;
         }
      }
      
      private function txtOver(e:MouseEvent) : void
      {
         var str0:String = null;
         var dropText0:String = null;
         Gaming.uiGroup.tipBox.hide();
         if(e.target == this.expTxt)
         {
            str0 = Gaming.PG.da.time.getDoubleTimeTip();
            dropText0 = BodyDropCtrl.getDropText();
            str0 = StringMethod.addNewLine(str0,dropText0,"<i1>|");
            if(str0 != "")
            {
               Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
            }
         }
      }
      
      private function txtOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var nb0:NormalGrid = null;
         var name0:String = null;
         if(e.target is NormalGrid)
         {
            nb0 = e.target as NormalGrid;
            if(nb0.secoundEnabled)
            {
               if(nb0.label != "spring")
               {
                  if(nb0.label == "link")
                  {
                     UIShow.showApp("summer");
                  }
                  else if(nb0.label == "link2")
                  {
                     YXDumplingLevelDiy.click();
                  }
                  else if(nb0.label == "gameBox")
                  {
                     Gaming.uiGroup.giftUI.gotoShowLabel("gameBox");
                  }
                  else
                  {
                     UIShow.showApp(nb0.label);
                     if(this.haveSmallIconArr.indexOf(nb0.label) >= 0)
                     {
                        if(nb0.getSmallIconStr() == "unlock" || nb0.getSmallIconStr() == "new")
                        {
                           nb0.setSmallIcon("");
                        }
                     }
                     else if(nb0.label == "gift")
                     {
                        nb0.setSmallIcon("");
                     }
                  }
               }
            }
         }
         else if(e.target is SimpleButton)
         {
            name0 = e.target.name.replace("Btn","");
            Gaming.soundGroup.playSound("uiSound","click");
            UIShow.showByLabel(name0);
         }
      }
      
      private function btnOver(e:MouseEvent) : void
      {
         var nb0:NormalGrid = null;
         var str0:String = "";
         if(e.target is NormalGrid)
         {
            nb0 = e.target as NormalGrid;
            if(!nb0.actived)
            {
               if(nb0.label == "skill")
               {
                  str0 = "通关" + ComMethod.color("西池","#00FF00") + "后解锁";
               }
               else if(nb0.label == "task")
               {
                  str0 = "通关" + ComMethod.color("白鹭镇","#00FF00") + "后解锁";
               }
               else if(nb0.label == "parts")
               {
                  str0 = "人物" + ComMethod.color("15级","#00FF00") + "后解锁";
               }
               else if(nb0.label == "blackMarket")
               {
                  str0 = "人物" + ComMethod.color(BlackMarketCtrl.getCanLevel() + "级","#00FF00") + "后解锁";
               }
               else if(nb0.label == "pet")
               {
                  str0 = "人物" + ComMethod.color(PetCount.openLevel + "级","#00FF00") + "后解锁";
               }
            }
         }
         if(str0 == "")
         {
            Gaming.uiGroup.tipBox.hide();
         }
         else
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
      }
      
      private function btnOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function coinTxtOver(e:MouseEvent) : void
      {
      }
      
      private function duanwu2025() : void
      {
         var b0:int = 0;
         var nowNum0:int = 0;
         var s0:String = "在规定时间内吃到更多的粽子。关卡内僵尸伤害极高，对你将一击致命。";
         b0 = Gaming.PG.da.time.getReadTimeDate().betweenIn("2025-5-22","2025-6-2 23:59:59");
         s0 += ComMethod.color("\n活动时间：2025-5-22至2025-6-2","#FFFF00");
         if(b0 == -1)
         {
            s0 += ComMethod.color("(未开始)","#FF0000");
         }
         if(b0 == 1)
         {
            s0 += ComMethod.color("(已结束)","#FF0000");
         }
         var gotoB0:Boolean = false;
         if(b0 == 0)
         {
            nowNum0 = Gaming.PG.save.main.getActiveDay();
            if(nowNum0 >= 1)
            {
               s0 += "\n" + ComMethod.color("<b>今日捡粽子次数已用完！</b>","#FF0000");
            }
            else
            {
               gotoB0 = true;
            }
         }
         var alert0:AlertBox = Gaming.uiGroup.alertBox;
         alert0.showNormal(s0,"yesAndNo",this.gotoDuanwuMap,this.gotoDuanwuShop,"ThingsIcon/zongzi",false,true);
         alert0.setBtnText("捡粽子","兑换物品");
         alert0.yesBtn.actived = gotoB0;
         alert0.noBtn.actived = b0 >= 0;
      }
      
      private function gotoDuanwuMap() : void
      {
         Gaming.PG.save.main.addActiveDay(1);
         ZongziChaseLevelDiy.chooseLevel();
      }
      
      private function gotoDuanwuShop() : void
      {
         Gaming.uiGroup.shopUI.labelBox.nowLabel = "pumpkin";
         Gaming.uiGroup.shopUI.show();
      }
      
      public function FTimerSecond() : void
      {
         var lootB0:Boolean = false;
         if(Gaming.LG.isOnlyIng())
         {
            Gaming.PG.da.time.FTimerSecond();
         }
         this.fleshDoubleTime();
         if(visible)
         {
            lootB0 = Gaming.uiGroup.wilderUI.lootBoard.getShowNewB();
            this.getBtn("wilder").setSmallIcon(lootB0 ? "loot" : "",true);
         }
      }
   }
}

