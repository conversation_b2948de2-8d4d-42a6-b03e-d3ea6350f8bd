package UI.setting
{
   import UI.UIOrder;
   import UI.base.btnList.BtnBox;
   import UI.base.button.NormalBtn;
   import UI.base.scroll.SliderBar;
   import com.sounto.net.SWFLoaderUrl;
   import com.sounto.utils.TextMethod;
   import dataAll._app.setting.SettingSave;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.text.TextField;
   
   public class SettingMusicBox extends BtnBox
   {
      private var barNameArr:Array = [];
      
      private var barObj:Object = {};
      
      public var volumeBar:SliderBar = new SliderBar();
      
      private var volumeSp:Sprite;
      
      public var musicTxt:TextField;
      
      public function SettingMusicBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var n:* = undefined;
         var ele0:String = null;
         var label0:String = null;
         var sb0:SliderBar = null;
         elementNameArr = ["volumeSp","musicTxt"];
         super.setImg(img0);
         for(n in elementNameArr)
         {
            ele0 = elementNameArr[n];
            if(ele0.indexOf("Sp") > 0)
            {
               label0 = ele0.substr(0,ele0.length - 2);
               this.barNameArr[n] = label0;
               sb0 = new SliderBar();
               sb0.label = label0;
               addChild(sb0);
               sb0.setImg(this[ele0]);
               sb0.setChangeFun(this.barChange);
               this.barObj[label0] = sb0;
            }
         }
         this.musicTxt.styleSheet = TextMethod.getLinkCss();
         this.musicTxt.addEventListener(TextEvent.LINK,this.linkClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function get settingSave() : SettingSave
      {
         return Gaming.PG.save.setting;
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      public function fleshData() : void
      {
         var n:* = undefined;
         var sb0:SliderBar = null;
         if(!Gaming.PG.save)
         {
            return;
         }
         for(n in this.barObj)
         {
            sb0 = this.barObj[n];
            sb0.setPer(this.settingSave.getValue(n));
         }
         this.fleshChooseBtn();
         this.fleshTxt();
      }
      
      private function fleshChooseBtn() : void
      {
         var btn0:NormalBtn = null;
         var label0:String = null;
         var bb0:Boolean = false;
         if(!Gaming.PG.save)
         {
            return;
         }
         var save0:SettingSave = this.settingSave;
         for each(btn0 in btnArr)
         {
            label0 = btn0.label;
            if(this.settingSave.haveValue(label0))
            {
               bb0 = this.settingSave.getValue(label0);
               btn0.activedAndIgnoreChosen = true;
               btn0.isChosen = bb0;
            }
         }
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var bb0:Boolean = false;
         if(!Gaming.PG.save)
         {
            return;
         }
         var btn0:NormalBtn = e.target as NormalBtn;
         var label0:String = btn0.label;
         if(this.settingSave.haveValue(label0))
         {
            bb0 = this.settingSave.getValue(label0);
            this.settingSave.setValue(label0,!bb0);
            this.fleshChooseBtn();
         }
      }
      
      private function barChange(v0:Number, label0:String) : void
      {
         if(Boolean(Gaming.PG.save))
         {
            this.settingSave.setValue(label0,v0);
         }
      }
      
      private function fleshTxt() : void
      {
         this.musicTxt.htmlText = this.settingSave.getMusicText();
      }
      
      private function linkClick(e:TextEvent) : void
      {
         this.settingSave.swapMusic(e.text);
         this.fleshTxt();
         var s0:SWFLoaderUrl = UIOrder.playMainMusic();
         if(Boolean(s0))
         {
            Gaming.uiGroup.alertBox.showCheck("切换下一个首：" + s0.cn,"",0.3);
         }
      }
   }
}

