package UI.base.font
{
   import flash.text.StyleSheet;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class FontDeal
   {
      private static var _countGap:int = 0;
      
      private static var dealB:Boolean = false;
      
      public function FontDeal()
      {
         super();
      }
      
      public static function get leadGap() : int
      {
         return _countGap;
      }
      
      public static function get countGap() : int
      {
         return _countGap;
      }
      
      public static function init() : int
      {
         if(dealB)
         {
            return 0;
         }
         dealB = true;
         var t0:TextField = new TextField();
         t0.mouseWheelEnabled = false;
         t0.selectable = false;
         t0.multiline = true;
         t0.embedFonts = false;
         t0.wordWrap = false;
         t0.autoSize = "left";
         var tf:TextFormat = new TextFormat("SimSun",String(12),16777215,null,null,null,null,null,t0.autoSize);
         tf.leading = 0;
         t0.defaultTextFormat = tf;
         t0.text = "1";
         var gap0:Number = t0.height - 16;
         _countGap = Math.ceil(gap0);
         if(_countGap < 0)
         {
            _countGap = 0;
         }
         return _countGap;
      }
      
      private static function doSize(t0:TextField, leadAdd0:int = 0) : void
      {
         t0.height += leadGap;
         t0.y -= int(leadGap / 2);
      }
      
      private static function doLead(t0:TextField, setB0:Boolean = false, leadAdd0:int = 0) : void
      {
         var tf:TextFormat = t0.defaultTextFormat;
         var lead0:int = int(tf.leading) - leadGap + leadAdd0;
         if(lead0 < 0)
         {
            lead0 = 0;
         }
         tf.leading = lead0;
         if(setB0)
         {
            t0.setTextFormat(tf);
         }
         else
         {
            t0.defaultTextFormat = tf;
         }
      }
      
      public static function dealStyleSheet(s0:StyleSheet, t0:TextField) : void
      {
         var lead0:int = 0;
         if(!t0.embedFonts && _countGap > 0)
         {
            lead0 = int(t0.defaultTextFormat.leading) - leadGap;
            s0.setStyle("leading",lead0);
         }
      }
      
      public static function dealOne(t0:TextField) : void
      {
         if(Boolean(t0))
         {
            if(!t0.embedFonts && _countGap > 0)
            {
               doSize(t0);
            }
         }
      }
      
      public static function dealLine(t0:TextField, leadC0:int = 0) : void
      {
         if(Boolean(t0))
         {
            if(!t0.embedFonts && _countGap > 0)
            {
               doSize(t0,leadC0);
               doLead(t0,false,leadC0);
            }
         }
      }
      
      public static function dealStaticLine(t0:TextField) : void
      {
         if(Boolean(t0))
         {
            if(!t0.embedFonts && _countGap > 0)
            {
               doSize(t0);
               doLead(t0,true);
            }
         }
      }
      
      public static function onlyDealLine(t0:TextField) : void
      {
         if(Boolean(t0))
         {
            if(!t0.embedFonts && _countGap > 0)
            {
               doLead(t0);
            }
         }
      }
      
      public static function getDealLeadingStr(t0:TextField, str0:String) : String
      {
         var tf:TextFormat = null;
         var lead0:int = 0;
         if(_countGap > 0)
         {
            tf = t0.defaultTextFormat;
            lead0 = int(tf.leading) - leadGap;
            if(lead0 >= 0)
            {
               str0 = "<textformat leading=\"" + lead0 + "\">" + str0 + "</textformat>";
            }
         }
         return str0;
      }
   }
}

