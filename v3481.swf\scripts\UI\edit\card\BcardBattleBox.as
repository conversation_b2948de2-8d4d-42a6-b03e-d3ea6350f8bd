package UI.edit.card
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.bag.ItemsGripBox;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.StringMethod;
   import dataAll._app.edit.card.BCardPKCreator;
   import dataAll._app.edit.card.BcardPKCode;
   import dataAll._app.edit.card.BossCardData;
   import dataAll._app.edit.card.BossCardDataGroup;
   import dataAll._app.edit.card.BossCardSave;
   import dataAll.level.define.LevelDefine;
   import dataAll.ui.GatherColor;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.geom.ColorTransform;
   import flash.text.TextField;
   import gameAll.level.data.OverLevelShow;
   
   public class BcardBattleBox extends AutoNormalUI
   {
      private var cardTxt:TextField;
      
      private var filterBtn:NormalBtn;
      
      private var boxTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var titleTxt:TextField;
      
      private var weBtn:NormalBtn;
      
      private var enemyBtn:NormalBtn;
      
      private var fightBtn:NormalBtn;
      
      private var closeBtn:NormalBtn;
      
      private var box:ItemsGripBox = new ItemsGripBox();
      
      private var downUI:Object = null;
      
      private var tempDownUI:Object = null;
      
      private var enemyDa:BossCardData = null;
      
      private var weDa:BossCardData = null;
      
      private var overDa:BossCardData = null;
      
      private var tempCode:String = "";
      
      public function BcardBattleBox()
      {
         super();
         mcTypeArr = ["tag","txt","btnSp"];
      }
      
      override protected function firstLoad() : void
      {
         setImgUrl("BosseditUI/battleBox");
         FontDeal.dealOne(this.cardTxt);
         FontDeal.dealLine(this.titleTxt);
         this.fightBtn.setName("斗卡");
         this.closeBtn.setName("返回");
         this.box.setIconPro("BosseditUI/cardBtn");
         this.box.arg.init(5,5,7,7);
         this.box.evt.setWant(true,true);
         addChild(this.box);
         NormalUICtrl.setTag(this.box,this.boxTag);
         this.box.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.box.addEventListener(ClickEvent.ON_OVER,this.gripOver);
         this.box.addEventListener(ClickEvent.ON_OUT,this.gripOut);
         this.box.pageBox.setToNormalBtn();
         this.box.setPagePos(this.pageTag);
         x = Gaming.uiGroup.bosseditUI.x;
         y = Gaming.uiGroup.bosseditUI.y + 15;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      protected function get dataG() : BossCardDataGroup
      {
         return Gaming.PG.da.bossCard;
      }
      
      public function outLoginEvent() : void
      {
         this.clearEnemyData();
         this.downUI = null;
         this.tempCode = "";
      }
      
      public function showCodeInput(downUI0:Object = null) : void
      {
         this.tempDownUI = downUI0;
         Gaming.uiGroup.alertBox.textInput.showTextInput("输入敌方魂卡代码（在魂卡界面，点击魂卡进行复制）。",this.tempCode,this.yesCodeInput,"yesAndNo",10000);
      }
      
      private function yesCodeInput(str0:String) : void
      {
         var da0:BossCardData = BcardPKCode.codeToDataAndPan(str0);
         if(Boolean(da0))
         {
            this.tempCode = str0;
            Gaming.uiGroup.bcardBattleBox.showEnemyData(da0,this.tempDownUI);
            this.tempDownUI = null;
         }
         else
         {
            this.tempCode = "";
         }
      }
      
      public function showEnemyData(da0:BossCardData, downUI0:Object = null) : void
      {
         this.downUI = downUI0;
         this.enemyDa = da0;
         this.show();
      }
      
      public function showWin() : void
      {
         Gaming.LG.pauseLevel();
         this.show();
      }
      
      public function showByDownUI(ui0:Object) : Boolean
      {
         if(ui0 == this.downUI)
         {
            this.show();
            return true;
         }
         return false;
      }
      
      override public function show() : void
      {
         super.show();
         this.overDa = null;
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
         this.overDa = null;
      }
      
      private function fleshData() : void
      {
         this.fleshEnemy();
         this.fleshBox();
      }
      
      public function getPKPreArr(levelD0:LevelDefine) : Array
      {
         var arr0:Array = [];
         if(levelD0.name == BCardPKCreator.bcardBattle)
         {
            if(Boolean(this.weDa))
            {
               arr0.push(this.weDa);
            }
            if(Boolean(this.enemyDa))
            {
               arr0.push(this.enemyDa);
            }
         }
         return arr0;
      }
      
      public function getWeDa() : BossCardData
      {
         return this.weDa;
      }
      
      public function getEnemyDa() : BossCardData
      {
         return this.enemyDa;
      }
      
      public function winEvent(tip0:String = "斗卡成功！") : void
      {
         Gaming.uiGroup.alertBox.showSuccess(tip0,this.showWin);
      }
      
      private function clearEnemyData() : void
      {
         this.weDa = null;
         this.enemyDa = null;
      }
      
      private function fleshEnemy() : void
      {
         if(Boolean(this.weDa))
         {
            if(this.dataG.findDataIndex(this.weDa) == -1)
            {
               this.weDa = null;
            }
         }
         var daArr0:Array = this.dataG.fleshEnemyPKArr();
         this.inBigGripFun(this.weBtn,this.weDa);
         this.weBtn.mouseEnabled = this.weDa;
         this.inBigGripFun(this.enemyBtn,this.enemyDa);
         this.enemyBtn.mouseEnabled = false;
         this.fightBtn.actived = this.weDa != null;
         this.fightBtn.visible = true;
         if(Gaming.LG.isGaming())
         {
            this.cardTxt.htmlText = "我方魂卡";
            this.box.transform.colorTransform = new ColorTransform(0.5,0.5,0.5,1);
            this.fightBtn.visible = false;
            this.weBtn.mouseEnabled = false;
         }
         else
         {
            this.cardTxt.htmlText = ComMethod.color("<b>选择我方魂卡</b>",GatherColor.yellowColor);
            this.box.transform.colorTransform = NormalBtn.normal_CF;
            this.weBtn.mouseEnabled = true;
         }
      }
      
      private function inBigGripFun(grip0:NormalBtn, da0:BossCardData) : void
      {
         var enemyB0:Boolean = false;
         var s0:BossCardSave = null;
         var n0:String = null;
         grip0.itemsData = da0;
         if(Boolean(da0))
         {
            enemyB0 = da0.getFatherData() == null;
            s0 = da0.getCardSave();
            n0 = "<b>" + ComMethod.color(da0.getBodyDefine().cnName,GatherColor.blueColor) + "</b>";
            n0 += "\n\n" + StringMethod.concatStringArr(Gaming.defineGroup.skill.getCnArrByNameArr(da0.getSkillNameArr()),1);
            grip0.setName(n0);
            grip0.setIconName(da0.getIconUrl());
            grip0.setStarIcon(da0.getStar());
            grip0.setNumText(s0.li + "\n" + s0.dp);
            grip0.setNew(false);
            if(enemyB0)
            {
               if(Gaming.LG.isGaming())
               {
                  grip0.setNewMc("beat");
               }
            }
         }
         else
         {
            grip0.clearShow();
            grip0.setName("");
            grip0.setNumText("");
            grip0.setNew(true);
         }
      }
      
      private function fleshBox() : void
      {
         var daArr0:Array = this.dataG.getPKArr();
         this.box.inData_byArr(daArr0,this.inBarFun);
         this.filterBtn.setStarIcon(this.dataG.getStarFilter());
      }
      
      private function inBarFun(grip0:NormalBtn, da0:BossCardData) : void
      {
         var enemyB0:Boolean = false;
         grip0.itemsData = da0;
         if(Boolean(da0))
         {
            enemyB0 = da0.getFatherData() == null;
            grip0.setIconName(da0.getIconUrl());
            grip0.setStarIcon(da0.getStar());
            grip0.actived = true;
            if(enemyB0)
            {
               grip0.setSmallIcon("");
            }
            else
            {
               grip0.setSmallIcon(this.weDa == da0 ? "fight" : "");
            }
         }
         else
         {
            grip0.clearShow();
            grip0.setStarIcon("");
         }
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         if(Gaming.LG.isGaming())
         {
            return;
         }
         var da0:BossCardData = e.childData as BossCardData;
         if(Boolean(this.enemyDa) && Boolean(da0))
         {
            if(this.weDa == da0)
            {
               this.weDa = null;
            }
            else
            {
               this.weDa = da0;
            }
            this.fleshEnemy();
            this.fleshBox();
         }
      }
      
      private function gripOver(e:ClickEvent) : void
      {
         var da0:BossCardData = e.childData as BossCardData;
         var tip0:String = da0.getGatherTip(false,false);
         UIOrder.showTip(tip0);
         this.overDa = da0;
      }
      
      private function gripOut(e:* = null) : void
      {
         this.overDa = null;
         UIOrder.showTip("");
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var funName0:String = btn0.label + "BtnClick";
         this[funName0](e);
      }
      
      private function filterBtnClick(e:MouseEvent) : void
      {
         this.dataG.swapStarFilter();
         this.fleshBox();
      }
      
      private function weBtnClick(e:MouseEvent) : void
      {
         if(Gaming.LG.isGaming())
         {
            return;
         }
         if(Boolean(this.weDa))
         {
            this.weDa = null;
            this.fleshEnemy();
            this.fleshBox();
         }
      }
      
      private function enemyBtnClick(e:MouseEvent) : void
      {
      }
      
      private function closeBtnClick(e:MouseEvent) : void
      {
         this.hide();
         this.downUI = null;
         if(Gaming.LG.isGaming())
         {
            Gaming.LG.overLevel(OverLevelShow.ORDER);
         }
      }
      
      public function FKey() : void
      {
         var tip0:String = null;
         if(visible && Boolean(this.overDa))
         {
            tip0 = this.overDa.getFTip();
            UIOrder.showTip(tip0);
         }
      }
      
      private function fightBtnClick(e:MouseEvent) : void
      {
         Gaming.uiGroup.alertBox.showChoose("是否要进行斗魂卡？",this.gotoMap);
      }
      
      private function gotoMap() : void
      {
         var d0:LevelDefine = null;
         if(Boolean(this.weDa) && Boolean(this.enemyDa))
         {
            d0 = Gaming.defineGroup.level.getDefineBy(BCardPKCreator.bcardBattle);
            Gaming.LG.chooseByLevelDefine(d0);
         }
         else
         {
            UIOrder.alertError("数据丢失：" + this.weDa + " " + this.enemyDa);
         }
      }
   }
}

