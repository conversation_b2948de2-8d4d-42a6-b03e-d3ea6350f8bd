package UI.pet.dispatch
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import com.sounto.cf.NiuBiCF;
   import dataAll.pet.PetData;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class PetDispatchChooseBox extends AutoNormalUI
   {
      private var titleTxt:TextField;
      
      private var gripBox:ItemsGripBox = new ItemsGripBox();
      
      private var gripTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      private var yesFun:Function = null;
      
      public function PetDispatchChooseBox()
      {
         super();
         mcTypeArr = ["btnSp","tag","txt"];
         this.maxNum = 0;
         btnSetB = false;
      }
      
      public function get maxNum() : Number
      {
         return this.CF.getAttribute("maxNum");
      }
      
      public function set maxNum(v0:Number) : void
      {
         this.CF.setAttribute("maxNum",v0);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         this.gripBox.setIconPro("PetUI/dispatchBtn");
         this.gripBox.arg.init(2,4,5,5);
         this.gripBox.evt.setWant(true,true);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         addChild(this.gripBox);
         NormalUICtrl.setTag(this.gripBox,this.gripTag);
         this.gripBox.pageBox.setToNormalBtn();
         this.gripBox.pageBox.setXY_bySp(this.pageTag,this.gripTag);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      public function showChoose(fun0:Function, max0:int) : void
      {
         this.yesFun = fun0;
         this.maxNum = max0;
         var arr0:Array = Gaming.PG.da.pet.dispatch.getNoDataArr();
         this.gripBox.inData_byArr(arr0,"inData_dispatchPet");
         this.gripBox.setAllPro("activedAndIgnoreChosen",true);
         this.fleshTitle();
         show();
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0.label == "close")
         {
            hide();
         }
         else
         {
            hide();
            this.yesFun(this.getChooseDataArr());
         }
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var nowChooseArr0:Array = this.getChooseDataArr();
         var grip0:ItemsGrid = e.child as ItemsGrid;
         if(!grip0.isChosen && nowChooseArr0.length >= this.maxNum)
         {
            Gaming.uiGroup.alertBox.showError("最多只能添加" + this.maxNum + "个尸宠！");
         }
         else
         {
            grip0.isChosen = !grip0.isChosen;
            this.fleshTitle();
         }
      }
      
      private function getChooseDataArr() : Array
      {
         var grip0:ItemsGrid = null;
         var da0:PetData = null;
         var dataArr0:Array = [];
         var num0:int = 0;
         for each(grip0 in this.gripBox.gripArr)
         {
            if(grip0.isChosen)
            {
               da0 = grip0.itemsData as PetData;
               if(Boolean(da0) && num0 < this.maxNum)
               {
                  dataArr0.push(da0);
                  num0++;
               }
            }
         }
         return dataArr0;
      }
      
      private function fleshTitle() : void
      {
         this.titleTxt.text = "选择宠物(" + this.getChooseDataArr().length + "/" + this.maxNum + ")";
      }
   }
}

