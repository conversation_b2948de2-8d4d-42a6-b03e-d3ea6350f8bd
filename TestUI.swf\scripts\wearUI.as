package
{
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol448")]
   public dynamic class wearUI extends MovieClip
   {
      public var chargerTag:MovieClip;
      
      public var headBtnSp:MovieClip;
      
      public var vehicleGrip:equipGrip;
      
      public var beltGrip:equipGrip;
      
      public var armsTag:MovieClip;
      
      public var deviceGrip:equipGrip;
      
      public var outfitBtnSp:MovieClip;
      
      public var pantsGrip:equipGrip;
      
      public var equipBackMc:MovieClip;
      
      public var vipTxt:TextField;
      
      public var heroImgTag:MovieClip;
      
      public var loveBtnSp:MovieClip;
      
      public var weaponGrip:equipGrip;
      
      public var infoTag:MovieClip;
      
      public var coatGrip:equipGrip;
      
      public var titleTxt:TextField;
      
      public var lvTxt:TextField;
      
      public var headGrip:equipGrip;
      
      public var wingsGrip:smallThingsGrip;
      
      public var showFashionBtnSp:MovieClip;
      
      public var dpsTxt:TextField;
      
      public var fashionGrip:equipGrip;
      
      public var jewelryGrip:smallThingsGrip;
      
      public var nameChangeSp:MovieClip;
      
      public var levelGiftBtn:SimpleButton;
      
      public var converBtnSp:MovieClip;
      
      public var suitTxt:TextField;
      
      public function wearUI()
      {
         super();
      }
   }
}

