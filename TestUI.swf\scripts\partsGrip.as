package
{
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol688")]
   public dynamic class partsGrip extends MovieClip
   {
      public var numTxt:TextField;
      
      public var annotationTxt:TextField;
      
      public var lockSp:MovieClip;
      
      public var levelTxt:TextField;
      
      public var backMc:MovieClip;
      
      public var newMc:MovieClip;
      
      public var numBackSp:MovieClip;
      
      public var iconCon:MovieClip;
      
      public var levelTxtBack:MovieClip;
      
      public var chooseMc:MovieClip;
      
      public var btnMc:MovieClip;
      
      public function partsGrip()
      {
         super();
      }
   }
}

