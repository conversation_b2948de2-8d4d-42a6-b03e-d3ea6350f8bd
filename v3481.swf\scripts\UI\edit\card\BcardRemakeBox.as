package UI.edit.card
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.must.NormalMustBox;
   import UI.edit.BosseditCardBoard;
   import dataAll._app.edit.card.BossCardData;
   import dataAll._app.edit.card.BossCardDataGroup;
   import dataAll._app.edit.card.BossCardRemake;
   import dataAll.items.creator.OneProAgent;
   import dataAll.items.creator.OneProAgentState;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class BcardRemakeBox extends AutoNormalUI
   {
      private var titleTxt:TextField;
      
      private var nameTxt:TextField;
      
      private var cardBtn:NormalBtn;
      
      private var yesBtn:NormalBtn;
      
      private var closeBtn:NormalBtn;
      
      private var mustBox:NormalMustBox;
      
      private var proTag:Sprite;
      
      private var proBox:ItemsGripBox = new ItemsGripBox();
      
      private var DA:BossCardData = null;
      
      private var agentArr:Array = null;
      
      private var tempGrip:ItemsGrid = null;
      
      private var tempAgent:OneProAgent = null;
      
      private var tempEvent:ClickEvent = null;
      
      public function BcardRemakeBox()
      {
         super();
         mcTypeArr = ["tag","txt","btnSp","mustBoxSp"];
      }
      
      override protected function firstLoad() : void
      {
         setImgUrl("BosseditUI/cardRemakeBox");
         addChild(this.proBox);
         NormalUICtrl.setTag(this.proBox,this.proTag);
         this.proBox.arg.init(1,8,0,8);
         this.proBox.evt.setWant(true,true);
         this.proBox.setIconPro("BosseditUI/cardProBar");
         this.proBox.addEventListener(ClickEvent.ON_CLICK,this.proBarClick);
         this.proBox.addEventListener(ClickEvent.ON_OVER,this.proBarOver);
         this.proBox.addEventListener(ClickEvent.ON_OUT,this.proBarOut);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.cardBtn);
         this.yesBtn.setName("重造");
         this.x = 2;
         this.y = 69;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      protected function get dataG() : BossCardDataGroup
      {
         return Gaming.PG.da.bossCard;
      }
      
      public function clearData() : void
      {
         this.DA = null;
         this.agentArr = null;
      }
      
      public function outLoginEvent() : void
      {
         this.clearData();
      }
      
      public function showData(da0:BossCardData) : void
      {
         this.DA = da0;
         this.show();
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      private function fleshData() : void
      {
         var beforeArr0:Array = null;
         if(Boolean(this.DA))
         {
            BosseditCardBoard.inBarFun(this.cardBtn,this.DA);
            beforeArr0 = this.agentArr;
            this.agentArr = BossCardRemake.getAgentArr(this.DA);
            if(Boolean(beforeArr0))
            {
               BossCardRemake.inStateArr(this.agentArr,beforeArr0);
            }
            this.proBox.inData_byArr(this.agentArr,this.inBarFun);
         }
         else
         {
            this.agentArr = null;
            this.proBox.clearData();
         }
         this.fleshMust();
      }
      
      private function fleshMust() : void
      {
         var mustD0:MustDefine = null;
         var mustB0:Boolean = false;
         if(Boolean(this.DA))
         {
            mustD0 = BossCardRemake.getMust(this.agentArr);
            mustB0 = this.mustBox.inData(mustD0);
            this.yesBtn.actived = mustB0 && BossCardRemake.isAllLockB(this.agentArr) == false;
         }
         else
         {
            this.mustBox.setShowState(false);
            this.yesBtn.actived = false;
         }
      }
      
      private function inBarFun(grip0:NormalBtn, a0:OneProAgent) : void
      {
         grip0.itemsData = a0;
         if(Boolean(a0))
         {
            if(a0.overMaxB())
            {
               if(a0.state == OneProAgentState.up)
               {
                  a0.state = OneProAgentState.lock;
               }
            }
            grip0.setName(a0.getBcardCn());
            grip0.setNumText(a0.valueString);
            grip0.setSmallIcon(a0.state);
         }
         else
         {
            grip0.clearShow();
         }
      }
      
      private function proBarClick(e:ClickEvent) : void
      {
         this.tempEvent = e;
         var da0:OneProAgent = e.childData as OneProAgent;
         if(da0.state == OneProAgentState.lock && da0.overMaxB())
         {
            Gaming.uiGroup.alertBox.showChoose("该属性已到达最大值，是否要取消锁定？",this.yesProBarClick);
         }
         else
         {
            this.yesProBarClick();
         }
      }
      
      private function yesProBarClick() : void
      {
         var grip0:ItemsGrid = this.tempEvent.child as ItemsGrid;
         var da0:OneProAgent = this.tempEvent.childData as OneProAgent;
         da0.bcardClick();
         this.inBarFun(grip0,da0);
         this.proBarOver(this.tempEvent);
         this.fleshMust();
      }
      
      private function proBarOver(e:ClickEvent) : void
      {
         this.proBarOut(e);
         var da0:OneProAgent = e.childData as OneProAgent;
         Gaming.uiGroup.tipBox.showText(da0.getBcardTip());
      }
      
      private function proBarOut(e:Event) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0 == this.closeBtn)
         {
            this.hide();
         }
         else if(btn0 == this.yesBtn)
         {
            if(this.yesBtn.actived)
            {
               UIOrder.getStoreState(this.affterGetStoreState);
            }
         }
      }
      
      private function affterGetStoreState(v0:int) : void
      {
         var must_d0:MustDefine = null;
         if(v0 == 1 || v0 == -2)
         {
            Gaming.uiGroup.connectUI.hide();
            must_d0 = BossCardRemake.getMust(this.agentArr);
            PlayerMustCtrl.deductMust(must_d0,this.yesCard);
         }
      }
      
      private function yesCard() : void
      {
         var bb0:Boolean = BossCardRemake.remake(this.DA,this.agentArr);
         if(bb0 == true)
         {
            UIOrder.yesSaveTip = "重造成功！";
            UIOrder.save(true,false,false,this.yesSave,Gaming.uiGroup.bosseditUI.hide,false,true);
         }
         else
         {
            Gaming.uiGroup.alertBox.showLock("数据错误！请刷新游戏！");
         }
      }
      
      private function yesSave(v:* = null) : void
      {
         this.fleshData();
      }
   }
}

