package UI.base.loadBar
{
   import UI.base.font.FontDeal;
   import UI.base.grid.NormalGridIcon;
   import com.common.text.TextWay;
   import com.sounto.utils.NumberMethod;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class LoadBar extends Sprite
   {
      public var index:int = 0;
      
      protected var img:Sprite = null;
      
      protected var icon:NormalGridIcon = null;
      
      private var barSp:MovieClip = null;
      
      private var textTxt:TextField = null;
      
      private var topTextTxt:TextField = null;
      
      private var downTextTxt:TextField = null;
      
      private var rightTextTxt:TextField = null;
      
      private var rightTxt:TextField = null;
      
      private var otherTxt:TextField = null;
      
      private var lightSp:Sprite = null;
      
      private var iconMc:MovieClip = null;
      
      private var backMc:MovieClip;
      
      private var iconCon:Sprite;
      
      private var _per:Number = 999;
      
      private var _text:String = "";
      
      private var _topText:String = "";
      
      private var _downText:String = "";
      
      private var _rightText:String = "";
      
      private var _rightStr:String = "";
      
      private var _iconName:String = "";
      
      private var _iconAlpha:Number = 1;
      
      public var itemsData:Object = null;
      
      public function LoadBar()
      {
         super();
      }
      
      public function setImg(img0:Sprite, followXYB0:Boolean = true) : void
      {
         this.img = img0;
         this.barSp = this.img["barSp"];
         this.barSp.stop();
         this.textTxt = this.img["textTxt"];
         this.topTextTxt = this.img["topTextTxt"];
         this.downTextTxt = this.img["downTextTxt"];
         this.rightTextTxt = this.img["rightTextTxt"];
         this.rightTxt = this.img["rightTxt"];
         this.otherTxt = this.img["otherTxt"];
         this.lightSp = this.img["lightSp"];
         this.iconCon = img0["iconCon"];
         this.backMc = img0["backMc"];
         this.iconMc = img0["iconMc"];
         this.dealFont();
         if(Boolean(this.iconCon))
         {
            if(!this.icon)
            {
               this.icon = new NormalGridIcon();
            }
            this.iconCon.addChild(this.icon);
         }
         if(Boolean(this.backMc))
         {
            this.backMc.stop();
         }
         if(Boolean(this.iconMc))
         {
            this.iconMc.stop();
         }
         addChild(this.img);
         if(followXYB0)
         {
            this.x = this.img.x;
            this.img.x = 0;
            this.y = this.img.y;
            this.img.y = 0;
         }
      }
      
      protected function dealFont() : void
      {
         FontDeal.dealOne(this.textTxt);
         FontDeal.dealOne(this.topTextTxt);
         FontDeal.dealOne(this.downTextTxt);
         FontDeal.dealOne(this.rightTextTxt);
         FontDeal.dealOne(this.otherTxt);
      }
      
      public function clearData() : void
      {
         this.setPer(0);
         this.setText("");
         this.setTopText("");
         this.setDownText("");
         this.setRightText("");
      }
      
      public function setLife(now0:Number, max0:Number, space0:String = "", toWanB0:Boolean = false) : void
      {
         this.setPer(now0 / max0);
         var nowS0:String = now0 + "";
         if(toWanB0)
         {
            nowS0 = NumberMethod.toWan(now0);
         }
         var maxS0:String = max0 + "";
         if(toWanB0)
         {
            maxS0 = NumberMethod.toWan(max0);
         }
         this.setText(nowS0 + space0 + "/" + space0 + maxS0);
      }
      
      public function setPer(per0:Number) : void
      {
         if(this._per != per0 && this.barSp is Sprite)
         {
            if(per0 < 0)
            {
               per0 = 0;
            }
            else if(per0 > 1)
            {
               per0 = 1;
            }
            this.barSp.scaleX = per0;
            this._per = per0;
            if(Boolean(this.lightSp))
            {
               this.lightSp.x = this.barSp.x + this.barSp.width;
            }
         }
         this.setBack(6);
      }
      
      public function setBodyPer(per0:Number) : void
      {
         var f0:int = int((1 - per0) * 6) + 1;
         if(f0 > 6)
         {
            f0 = 6;
         }
         var newPer0:Number = (per0 - (6 - f0) / 6) * 6;
         this.setPer(newPer0);
         this.setBack(f0);
         this.setText(TextWay.numberToPer(per0));
         this.setBarBack("boss" + f0);
         this.setOtherText("");
      }
      
      protected function setBack(f0:int) : void
      {
         if(Boolean(this.backMc))
         {
            this.backMc.gotoAndStop(f0);
         }
      }
      
      protected function setBackVisible(bb0:Boolean) : void
      {
         if(Boolean(this.backMc))
         {
            this.backMc.visible = bb0;
         }
      }
      
      public function setIconMc(str0:String) : void
      {
         if(Boolean(this.iconMc))
         {
            this.iconMc.gotoAndStop(str0);
         }
      }
      
      public function setBarBack(str0:String) : void
      {
         this.barSp.gotoAndStop(str0);
      }
      
      public function setText(str0:String) : void
      {
         if(this._text != str0 && Boolean(this.textTxt))
         {
            this.textTxt.htmlText = str0;
            this._text = str0;
         }
      }
      
      public function setTopText(str0:String) : void
      {
         if(this._topText != str0 && Boolean(this.topTextTxt))
         {
            this.topTextTxt.htmlText = str0;
            this._topText = str0;
         }
      }
      
      public function setDownText(str0:String) : void
      {
         if(this._downText != str0 && Boolean(this.downTextTxt))
         {
            this.downTextTxt.htmlText = str0;
            this._downText = str0;
         }
      }
      
      public function setRightText(str0:String) : void
      {
         if(this._rightText != str0 && Boolean(this.rightTextTxt))
         {
            this.rightTextTxt.htmlText = str0;
            this._rightText = str0;
         }
      }
      
      public function setRightStr(str0:String) : void
      {
         if(this._rightStr != str0 && Boolean(this.rightTxt))
         {
            this.rightTxt.htmlText = str0;
            this._rightStr = str0;
         }
      }
      
      public function setOtherText(str0:String) : void
      {
         if(Boolean(this.otherTxt))
         {
            this.otherTxt.htmlText = str0;
         }
      }
      
      public function setIconName(str0:String) : void
      {
         if(this._iconName != str0 && this.icon is NormalGridIcon)
         {
            this.icon.setIconName(str0);
            this._iconName = str0;
         }
      }
      
      public function setIconAlpla(v0:Number) : void
      {
         if(this._iconAlpha != v0 && this.icon is NormalGridIcon)
         {
            this.icon.alpha = v0;
            this._iconAlpha = v0;
         }
      }
   }
}

