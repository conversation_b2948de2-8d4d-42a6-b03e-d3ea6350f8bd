package UI.gift.active
{
   import UI.base.button.NormalBtn;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.active.ActiveGiftData;
   import flash.display.MovieClip;
   
   public class ActiveGiftGrip extends NormalBtn
   {
      private var btnSp:MovieClip;
      
      public var btn:NormalBtn = new NormalBtn();
      
      public var canGetGiftB:Boolean = false;
      
      public function ActiveGiftGrip()
      {
         super();
      }
      
      override public function setImg(img0:MovieClip) : void
      {
         super.setImg(img0);
         this.btnSp = img0["btnSp"];
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.mouseChildren = true;
         this.btn.visible = false;
      }
      
      public function inData_byActiveGiftData(da0:ActiveGiftData) : void
      {
         itemsData = da0;
         if(index == 5)
         {
            setIconName("ThingsIcon/zodiacCash");
         }
         else
         {
            setIconName("ThingsIcon/normalGift");
         }
         this.btn.visible = da0.enoughB;
         this.btn.setName(da0.haveGiftB ? "已领取" : "领取");
         this.btn.actived = !da0.haveGiftB;
         nameTxt.visible = !da0.enoughB;
         setName("活跃值\n" + ComMethod.color(da0.must + "","#FFFF00"),true,true);
         this.canGetGiftB = da0.enoughB && !da0.haveGiftB;
         this.filters = this.canGetGiftB ? [] : [no_filter];
      }
   }
}

