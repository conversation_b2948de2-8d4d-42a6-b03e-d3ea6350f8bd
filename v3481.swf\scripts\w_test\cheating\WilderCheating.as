package w_test.cheating
{
   import dataAll._app.edit.boss.BossEditData;
   import dataAll._app.edit.boss.CodeBossEditDataGroup;
   import dataAll._app.wilder.WilderData;
   
   public class WilderCheating extends OneCheating
   {
      public function WilderCheating()
      {
         super();
      }
      
      public function unlockAllWider(str0:String, v0:int) : String
      {
         Gaming.PG.da.wilder.unlockAllWider();
         return "解锁所有秘境";
      }
      
      public function setNowWiderNum(str0:String, v0:int) : String
      {
         var da0:WilderData = Gaming.uiGroup.wilderUI.getNowData();
         if(<PERSON><PERSON><PERSON>(da0))
         {
            da0.save.num = v0;
            return "设置秘境" + da0.def.cnName + "：已使用次数：" + v0;
         }
         return "找不到秘境，请选择指定秘境";
      }
      
      public function ulockAllBossEditLevel(str0:String, v0:int) : String
      {
         var da0:BossEditData = null;
         var g0:CodeBossEditDataGroup = Gaming.PG.da.bossEdit.getTask();
         var daArr0:Array = g0.getArr();
         for each(da0 in daArr0)
         {
            da0.setTaskState("");
         }
         return "解锁成功！";
      }
   }
}

