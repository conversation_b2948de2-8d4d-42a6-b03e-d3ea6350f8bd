package UI.api.union
{
   import dataAll._app.union.info.MemberCheckListInfo;
   import unit4399.events.UnionEvent;
   
   public class UnionMaster_API extends UnionBase_API
   {
      public function UnionMaster_API()
      {
         super();
      }
      
      public function getApplyList(idx:int, pageNum:int, pageSize:int, yesFun0:Function = null, noFun0:Function = null) : void
      {
         setFun(yesFun0,noFun0);
         if(Gaming.isLocal())
         {
            this.yes_getApplyList(MemberCheckListInfo.getSimulatedJson(pageSize));
         }
         else
         {
            serviceHold.getApplyList(idx,pageNum,pageSize);
         }
      }
      
      private function yes_getApplyList(jsonStr0:String) : void
      {
         doYesFun(jsonStr0);
      }
      
      public function auditMember(idx:int, userId:Number, userIndex:int, auditResult:int, yesFun0:Function = null, noFun0:Function = null) : void
      {
         setFun(yesFun0,noFun0);
         if(Gaming.isLocal())
         {
            this.yes_auditMember(true);
         }
         else
         {
            serviceHold.auditMember(idx,String(userId),userIndex,auditResult);
         }
      }
      
      private function yes_auditMember(bb0:Boolean) : void
      {
         doYesFun(bb0);
      }
      
      public function applyMultiAudit(idx:int, usersAry:Array, auditResult:int, yesFun0:Function = null, noFun0:Function = null) : void
      {
         setFun(yesFun0,noFun0);
         if(Gaming.isLocal())
         {
            this.yes_applyMultiAudit(true);
         }
         else
         {
            serviceHold.applyMultiAudit(idx,usersAry,auditResult);
         }
      }
      
      private function yes_applyMultiAudit(bb0:Boolean) : void
      {
         doYesFun(bb0);
      }
      
      public function removeMember(idx:int, userId:Number, userIndex:int, yesFun0:Function = null, noFun0:Function = null) : void
      {
         setFun(yesFun0,noFun0,"removeMember");
         if(Gaming.isLocal())
         {
            this.yes_removeMember(true);
         }
         else
         {
            serviceHold.removeMember(idx,String(userId),userIndex);
         }
      }
      
      private function yes_removeMember(bb0:Boolean) : void
      {
         doYesFun(bb0,"removeMember");
      }
      
      public function useUnionContribution(idx:int, contribution:int) : void
      {
         serviceHold.useUnionContribution(idx,contribution);
      }
      
      private function yes_useUnionContribution(num0:int) : void
      {
      }
      
      public function dissolveUnion(idx:int, actionType:int, yesFun0:Function = null, noFun0:Function = null) : void
      {
         setFun(yesFun0,noFun0,"dissolveUnion");
         if(Gaming.isLocal())
         {
            this.yes_dissolveUnion(Gaming.api.save.getNowServerDate().toString());
         }
         else
         {
            serviceHold.dissolveUnion(idx,actionType);
         }
      }
      
      private function yes_dissolveUnion(timeStr0:String) : void
      {
         doYesFun(timeStr0,"dissolveUnion");
      }
      
      public function transferUnion(idx:int, userId:Number, userIndex:int, result:int) : void
      {
         serviceHold.transferUnion(idx,String(userId),userIndex,result);
      }
      
      private function yes_transferUnion(bb0:Boolean) : void
      {
      }
      
      public function onMasterSuccess(e:UnionEvent) : *
      {
         var dataObj:Object = e.data;
         var data0:* = dataObj.data;
         switch(dataObj.apiName)
         {
            case UnionEvent.UNI_API_DSHLB:
               this.yes_getApplyList(data0);
               break;
            case UnionEvent.UNI_API_CYSH:
               this.yes_auditMember(data0);
               break;
            case UnionEvent.UNI_API_SHDGCY:
               this.yes_applyMultiAudit(data0);
               break;
            case UnionEvent.UNI_API_CYYC:
               this.yes_removeMember(data0);
               break;
            case UnionEvent.UNI_API_JSBH:
               this.yes_dissolveUnion(data0);
               break;
            case UnionEvent.UNI_API_XHBHGXD:
               this.yes_useUnionContribution(data0);
               break;
            case UnionEvent.UNI_API_SHDGCY:
               this.yes_transferUnion(data0);
         }
      }
   }
}

