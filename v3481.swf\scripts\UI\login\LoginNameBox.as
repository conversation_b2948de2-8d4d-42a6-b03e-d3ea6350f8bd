package UI.login
{
   import UI.UIOrder;
   import UI.base.AutoNormalUI;
   import UI.base.box.NormalBox;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._data.ConstantDefine;
   import dataAll._player.define.KeywordShield;
   import dataAll._player.role.RoleName;
   import dataAll.body.define.HeroDefine;
   import dataAll.ui.GatherColor;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class LoginNameBox extends AutoNormalUI
   {
      private var yesBtn:NormalBtn;
      
      private var nameTxt:TextField;
      
      private var gripTag:Sprite;
      
      private var gripBox:NormalBox = new NormalBox();
      
      private var nowName:String = "";
      
      public function LoginNameBox()
      {
         super();
         mcTypeArr = ["btnSp","txt","tag"];
      }
      
      public static function inGripData(grip0:NormalBtn, d0:HeroDefine) : void
      {
         var name0:String = null;
         var unlockB0:Boolean = false;
         name0 = d0.name;
         unlockB0 = RoleName.loginUnlockArr.indexOf(name0) >= 0;
         grip0.label = name0;
         grip0.itemsData = d0;
         grip0.setName(d0.getRoleCn());
         grip0.setNumText("");
         grip0.setIconName("LoginUI/" + name0,false);
         grip0.activedAndEnabled = false;
         grip0.activedAndGray = true;
         grip0.actived = unlockB0;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         FontDeal.dealOne(this.nameTxt);
         this.yesBtn.setName("确定");
         this.yesBtn.addEventListener(MouseEvent.CLICK,this.click);
         this.gripTag.addChild(this.gripBox);
         this.gripBox.arg.init(RoleName.loginArr.length,1,20,0);
         this.gripBox.setIconPro("LoginUI/roleBar");
         this.gripBox.evt.setWant(true,true);
         this.gripBox.addEventListener(ClickEvent.ON_OVER,this.gripOver);
         this.gripBox.addEventListener(ClickEvent.ON_OUT,Gaming.uiGroup.tipBox.hide);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function initGripBox() : void
      {
         var roleArr0:Array = null;
         var index0:int = 0;
         var name0:* = null;
         var d0:HeroDefine = null;
         var grip0:NormalBtn = null;
         if(this.gripBox.gripArr.length == 0)
         {
            roleArr0 = RoleName.loginArr;
            index0 = 0;
            this.gripBox.setNowGripNum(roleArr0.length);
            for each(name0 in roleArr0)
            {
               d0 = Gaming.defineGroup.body.getHeroDefine(name0);
               grip0 = this.gripBox.gripArr[index0];
               inGripData(grip0,d0);
               index0++;
            }
         }
         this.setChooseGrip(RoleName.Striker);
      }
      
      override public function show() : void
      {
         this.initGripBox();
         super.show();
         if(Bug.nameNoChangeB)
         {
            this.nowName = Gaming.PG.loginData.nickName;
            this.nameTxt.text = this.nowName;
            this.nameTxt.mouseEnabled = false;
         }
         else
         {
            this.nameTxt.text = ConstantDefine.playerName;
            this.nowName = "";
            this.nameTxt.mouseEnabled = true;
         }
      }
      
      private function click(e:MouseEvent) : void
      {
         var htmlNoStr0:String = null;
         var str0:String = TextWay.toHanSpace(this.nameTxt.text);
         str0 = TextWay.toHan2(str0);
         if(str0 == "" || !str0)
         {
            Gaming.uiGroup.alertBox.showNormal("昵称不能为空！","yes",null,null,"no");
            this.nameTxt.text = ConstantDefine.playerName;
         }
         else if(KeywordShield.haveKey(str0))
         {
            Gaming.uiGroup.alertBox.showError("昵称不能包含敏感词！");
         }
         else
         {
            htmlNoStr0 = TextWay.haveHtmlNoB(str0);
            if(htmlNoStr0 != "")
            {
               this.nameTxt.text = TextWay.clearHtmlNo(str0);
               Gaming.uiGroup.alertBox.showError("昵称不能包含" + htmlNoStr0 + "字符!");
            }
            else
            {
               this.nowName = str0;
               Gaming.uiGroup.connectUI.show("语法判断中……");
               Gaming.api.badWorld.check(this.nowName,this.yes_badWorld,this.no_badWorld);
            }
         }
      }
      
      private function no_badWorld(str0:String) : void
      {
         str0 += "\n" + ComMethod.color("如果多次失败，请使用昵称“" + ConstantDefine.playerName + "”。",GatherColor.greenColor);
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showNormal(str0,"yes",null,null,"no");
      }
      
      private function yes_badWorld() : void
      {
         Gaming.uiGroup.connectUI.hide();
         this.gotoGame();
      }
      
      private function gotoGame() : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.PG.da.setNewP1NameNewSave(this.gripBox.nowLabel);
         Gaming.PG.save.base.playerName = this.nowName;
         this.nowName = "";
         UIOrder.affter_readSave(true);
      }
      
      private function gripOver(e:ClickEvent) : void
      {
         Gaming.uiGroup.tipBox.textTip.showFollowText(RoleName.getLoginTip(e.label));
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var btn0:NormalBtn = e.child as NormalBtn;
         if(btn0.actived)
         {
            this.setChooseGrip(btn0.label);
         }
      }
      
      private function setChooseGrip(label0:String) : void
      {
         var btn0:NormalBtn = this.gripBox.getBtnByLabel(label0);
         if(Boolean(btn0))
         {
            this.gripBox.setChoose(label0);
            this.gripBox.setAllFun("setSmallIcon","");
            btn0.setSmallIcon("p1");
         }
      }
   }
}

