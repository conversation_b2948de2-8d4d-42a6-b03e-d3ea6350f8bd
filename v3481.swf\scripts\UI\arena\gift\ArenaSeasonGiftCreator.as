package UI.arena.gift
{
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipFatherDefine;
   import dataAll.equip.save.EquipSave;
   import dataAll.gift.define.ExchangeGiftAddDefineGroup;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class ArenaSeasonGiftCreator
   {
      public function ArenaSeasonGiftCreator()
      {
         super();
      }
      
      public function getGift(rank0:int, score0:int) : GiftAddDefineGroup
      {
         var rankD0:GiftAddDefineGroup = this.getRankGift(rank0);
         var normalD0:GiftAddDefineGroup = this.getNormalGift(rank0,score0);
         if(Boolean(normalD0))
         {
            rankD0.merge(normalD0);
         }
         return rankD0;
      }
      
      public function getRankGift(rank0:int) : GiftAddDefineGroup
      {
         var mustD0:GiftAddDefineGroup = new GiftAddDefineGroup();
         if(rank0 == 1)
         {
            mustD0.addGiftByStr("equip;xiaoMing");
         }
         else if(rank0 == 2)
         {
            mustD0.addGiftByStr("equip;xiaoBo");
         }
         else if(rank0 == 3)
         {
            mustD0.addGiftByStr("equip;xiaoAi");
         }
         if(rank0 <= 5)
         {
            mustD0.addGiftByStr("head;superHero;1");
         }
         else if(rank0 <= 9)
         {
            mustD0.addGiftByStr("head;specialSoldiers;1");
         }
         else if(rank0 % 10 == 0 && rank0 <= 100)
         {
            if(rank0 == 10 || rank0 == 40)
            {
               mustD0.addGiftByStr("equip;xiaoBo");
            }
            else if(rank0 == 70 || rank0 == 100)
            {
               mustD0.addGiftByStr("equip;xiaoAi");
            }
            else
            {
               mustD0.addGiftByStr("head;specialSoldiers;1");
            }
         }
         return mustD0;
      }
      
      public function getNormalGiftByRank(rank0:int, score0:int) : GiftAddDefineGroup
      {
         var d0:ExchangeGiftAddDefineGroup = null;
         var arr0:Array = Gaming.defineGroup.gift.getArrByFather("arenaSeason");
         var arr_len0:int = int(arr0.length);
         var mustD0:GiftAddDefineGroup = null;
         for(var i:int = 0; i < arr_len0; i++)
         {
            d0 = arr0[i];
            if(rank0 <= d0.mustLevel)
            {
               if(d0.mustLevel != 99999)
               {
                  mustD0 = d0;
                  break;
               }
               if(score0 >= int(d0.codeUrl))
               {
                  mustD0 = d0;
                  break;
               }
            }
         }
         return mustD0;
      }
      
      private function getNormalGift(rank0:int, score0:int) : GiftAddDefineGroup
      {
         var mustD0:GiftAddDefineGroup = this.getNormalGiftByRank(rank0,score0);
         if(Boolean(mustD0))
         {
            mustD0 = mustD0.clone();
            mustD0 = this.converGift(mustD0);
         }
         return mustD0;
      }
      
      private function converGift(dg0:GiftAddDefineGroup) : GiftAddDefineGroup
      {
         var d0:GiftAddDefine = null;
         var dg2:GiftAddDefineGroup = null;
         var fun0:Function = null;
         var arr0:Array = dg0.arr;
         var newArr0:Array = [];
         for each(d0 in arr0)
         {
            if(this.hasOwnProperty(d0.type))
            {
               fun0 = this[d0.type];
               newArr0 = newArr0.concat(fun0(d0));
            }
            else
            {
               newArr0.push(d0);
            }
         }
         dg2 = new GiftAddDefineGroup();
         dg2.arr = newArr0;
         return dg2;
      }
      
      public function suit(d0:GiftAddDefine) : Array
      {
         var imgD0:EquipDefine = null;
         var s0:EquipSave = null;
         var giftD0:GiftAddDefine = null;
         var arr0:Array = [];
         var f0:EquipFatherDefine = Gaming.defineGroup.equip.getFatherDefine(d0.name);
         for each(imgD0 in f0.partObj)
         {
            s0 = Gaming.defineGroup.equipCreator.getSuperSave("red",60,imgD0.name);
            giftD0 = new GiftAddDefine();
            giftD0.type = "equip";
            giftD0.itemsSave = s0;
            arr0.push(giftD0);
         }
         return arr0;
      }
      
      public function arms(d0:GiftAddDefine) : Array
      {
         return [];
      }
      
      public function parts(d0:GiftAddDefine) : Array
      {
         var name0:* = null;
         var d2:GiftAddDefine = null;
         var arr0:Array = [];
         var nameArr0:Array = Gaming.defineGroup.things.normalPartsNameArr;
         for each(name0 in nameArr0)
         {
            d2 = new GiftAddDefine();
            d2.inData_byStr("parts;" + name0 + "_" + d0.lv + ";" + d0.num);
            arr0.push(d2);
         }
         return arr0;
      }
   }
}

