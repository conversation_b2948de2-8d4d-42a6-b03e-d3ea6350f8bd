package UI.base.tip
{
   import UI.base.box.NormalGridIconBox;
   import com.sounto.image.BmpMovieClip;
   import com.sounto.utils.NumberMethod;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class ArmsTipBox extends EquipTipBox
   {
      private var dpsTxt:TextField = null;
      
      private var dpsStrTxt:TextField = null;
      
      private var dpsIcon:BmpMovieClip = null;
      
      private var iconBox:NormalGridIconBox = new NormalGridIconBox();
      
      public function ArmsTipBox()
      {
         super();
         valueTxt.y = 95;
      }
      
      override public function init() : void
      {
         super.init();
         var sp0:Sprite = Gaming.uiGroup.getBasicMovieClip("armsTipDpsText");
         this.dpsTxt = sp0["dpsTxt"];
         this.dpsStrTxt = sp0["strTxt"];
         this.dpsIcon = OneTextGather.getIconCopy();
         addChild(this.dpsTxt);
         addChild(this.dpsStrTxt);
         addChild(this.dpsIcon);
         this.dpsTxt.wordWrap = false;
         this.dpsTxt.autoSize = "left";
         valueTxt.setNormalTextColor(13421772);
         addChild(this.iconBox);
         this.iconBox.scaleX = 0.5;
         this.iconBox.scaleY = 0.5;
         this.iconBox.x = valueTxt.x + 12 + 75;
         this.iconBox.y = valueTxt.y + 6;
         this.iconBox.arg.init(100,1,-3,0);
      }
      
      public function setDpsText(dps0:Number, compareDps0:Number = 0) : void
      {
         this.dpsTxt.width = 10;
         this.dpsTxt.htmlText = NumberMethod.toBigWan(dps0,"#FF6600");
         this.dpsStrTxt.x = this.dpsTxt.x + this.dpsTxt.width;
         this.dpsIcon.y = this.dpsTxt.y + 20;
         this.dpsIcon.visible = true;
         if(compareDps0 == 0 || dps0 == compareDps0)
         {
            this.dpsIcon.visible = false;
         }
         else if(dps0 > compareDps0)
         {
            this.dpsIcon.gotoAndStop(6);
         }
         else if(dps0 < compareDps0)
         {
            this.dpsIcon.gotoAndStop(7);
         }
      }
      
      public function setPartsIconUrlArr(arr0:Array) : void
      {
         if(arr0 == null || arr0.length == 0)
         {
            this.iconBox.clear();
         }
         else
         {
            this.iconBox.addByUrlArr(arr0);
         }
      }
      
      override protected function fleshSize() : void
      {
         var w0:int = valueTxt.x + valueTxt.width;
         var h0:int = valueTxt.y + valueTxt.height + 10;
         var max0:int = w0;
         if(max0 < titleTxt.width + lvTxt.width * 2 + 10)
         {
            max0 = titleTxt.width + lvTxt.width * 2 + 10;
         }
         if(getTestB())
         {
            if(max0 < timeTxt.width + 10)
            {
               max0 = timeTxt.width + 10;
            }
            h0 += 20;
         }
         if(max0 < this.dpsTxt.x + this.dpsTxt.width + this.dpsStrTxt.width + 30)
         {
            max0 = this.dpsTxt.x + this.dpsTxt.width + this.dpsStrTxt.width + 30;
         }
         if(max0 < MIN_WIDTH)
         {
            max0 = MIN_WIDTH;
         }
         w0 = max0;
         w = w0;
         h = h0;
         this.dpsIcon.x = w - 35;
      }
      
      override public function show() : void
      {
         var w0:int = w;
         var h0:int = h;
         if(Boolean(tipBox))
         {
            tipBox.hideAllshowBack("arms",w0,h0);
            tipBox.show();
         }
         this.visible = true;
      }
   }
}

