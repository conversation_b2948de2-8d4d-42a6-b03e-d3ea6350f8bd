package UI.api.save
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.StringDate;
   import dataAll._app.login.LoginData4399;
   import dataAll._data.ConstantDefine;
   import flash.external.ExternalInterface;
   import flash.utils.getTimer;
   
   public class SaveAPI
   {
      public var localSave:LocalSave = new LocalSave();
      
      public var s4399:Save4399 = new Save4399();
      
      public var nowIndex:int = 0;
      
      private var yesGetServerTimeFun:Function = null;
      
      private var _nowSeverTime:String = "";
      
      private var getTimeValue:Number = 0;
      
      private var alreadyTime:int = -1;
      
      public var localTimeB:Boolean = false;
      
      public function SaveAPI()
      {
         super();
      }
      
      public function set nowSeverTime(v0:String) : void
      {
         this._nowSeverTime = TextWay.toCode(String(v0));
      }
      
      public function get nowSeverTime() : String
      {
         return String(TextWay.getText(this._nowSeverTime));
      }
      
      public function outLoginEvent() : void
      {
         this.s4399.outLoginEvent();
         this.alreadyTime = -1;
      }
      
      public function showLogPanel(_loginFun0:Function) : *
      {
         if(this.isLocal())
         {
            _loginFun0(new LoginData4399());
         }
         else
         {
            this.s4399.yes_login_fun = _loginFun0;
            if(!this.s4399.isLogin())
            {
               this.s4399.showLogPanel();
            }
         }
      }
      
      public function userLogOut() : *
      {
         if(this.isLocal())
         {
            if(this.s4399.yes_outLogin_fun is Function)
            {
               this.s4399.yes_outLogin_fun();
            }
         }
         else
         {
            this.s4399.userLogOut();
         }
      }
      
      public function setOutLoginEvent(fun0:Function) : *
      {
         this.s4399.yes_outLogin_fun = fun0;
      }
      
      public function setClosePanelEvent(fun0:Function) : *
      {
         this.s4399.yes_closePanel_fun = fun0;
      }
      
      public function getUserNameByCookie() : String
      {
         if(this.isLocal())
         {
            return "local";
         }
         return ExternalInterface.call("UniLogin.getUname");
      }
      
      public function getUidByCookie() : String
      {
         if(this.isLocal())
         {
            return "0";
         }
         return ExternalInterface.call("UniLogin.getUid");
      }
      
      public function getServerTime(_yesFun0:Function, _noFun0:Function = null, alreadyB0:Boolean = false) : *
      {
         var local_str0:String = null;
         this.yesGetServerTimeFun = _yesFun0;
         var alreadyLocalB0:Boolean = false;
         if(alreadyB0)
         {
            if(this.alreadyTime >= 0 && this.alreadyTime <= 10)
            {
               this.alreadyTime = -1;
               alreadyLocalB0 = true;
            }
            else
            {
               this.alreadyTime = 0;
            }
         }
         else
         {
            this.alreadyTime = -1;
         }
         if(alreadyLocalB0)
         {
            this.yesGetServerTime(this.nowSeverTime);
         }
         else if(this.isLocal() || this.localTimeB)
         {
            local_str0 = StringDate.getLocalTimeStr();
            this.setNowServerDate(local_str0);
            this.yesGetServerTime(local_str0);
         }
         else
         {
            this.s4399.getServerTime(this.yesGetServerTime,_noFun0);
         }
      }
      
      private function yesGetServerTime(timeStr0:String) : void
      {
         ConstantDefine.anniver.countSignActivityTime(timeStr0);
         if(this.yesGetServerTimeFun is Function)
         {
            this.yesGetServerTimeFun(timeStr0);
         }
      }
      
      public function getNowServerDate() : StringDate
      {
         var s0:StringDate = new StringDate();
         if(this.nowSeverTime == "" || this.nowSeverTime == "0-01-00 00:00:00")
         {
            if(Boolean(Gaming.PG.save))
            {
               this.setNowServerDate(Gaming.PG.save.time.nowReadTime);
            }
         }
         s0.inData_byStr(this.nowSeverTime);
         return s0;
      }
      
      public function getNowTimeValueSec() : Number
      {
         var s0:StringDate = null;
         var de0:Date = null;
         if(!(this.nowSeverTime == "" || this.nowSeverTime == "0-01-00 00:00:00"))
         {
            s0 = new StringDate(this.nowSeverTime);
            de0 = s0.getDateClass();
            if(de0.time <= 0)
            {
               de0 = null;
            }
         }
         if(!de0)
         {
            de0 = new Date();
         }
         return Math.floor(de0.time / 1000);
      }
      
      public function isInTimeRange(before0:String, after0:String) : Boolean
      {
         var now0:StringDate = this.getNowServerDate();
         var be0:StringDate = new StringDate(before0);
         var af0:StringDate = new StringDate(after0);
         return now0.reductionOne(be0) >= 0 && af0.reductionOne(now0) > 0;
      }
      
      public function getForecastServerDate() : StringDate
      {
         var d0:Date = this.getNowServerDate().getDateClass();
         var ct0:Number = getTimer() - this.getTimeValue;
         if(ct0 < 0)
         {
            ct0 = 0;
         }
         d0.setTime(ct0 + d0.time);
         var s0:StringDate = new StringDate();
         s0.inData_byObj(d0);
         return s0;
      }
      
      public function setNowServerDate(str0:String) : void
      {
         this.nowSeverTime = str0;
         this.getTimeValue = getTimer();
      }
      
      public function save(obj0:Object, title0:String, _yesFun0:Function, _noFun0:Function) : *
      {
         if(this.isLocal())
         {
            this.localSave.WriteSO(obj0);
            _yesFun0();
         }
         else
         {
            this.s4399.save(obj0,title0,this.nowIndex,_yesFun0,_noFun0);
         }
      }
      
      public function read(index0:int, _yesFun0:Function, _noFun0:Function) : *
      {
         var obj0:Object = null;
         this.nowIndex = index0;
         if(this.isLocal())
         {
            obj0 = this.localSave.ReadSO();
            _yesFun0(obj0,{
               "create_time":"2024-01-01",
               "update_times":"900"
            });
         }
         else
         {
            this.s4399.read(index0,_yesFun0,_noFun0);
         }
      }
      
      public function getList(_yesFun0:Function) : void
      {
         if(this.isLocal())
         {
            _yesFun0(this.localSave.getList());
         }
         else
         {
            this.s4399.getList(_yesFun0);
         }
      }
      
      public function getStoreState(_yesFun0:Function, _noFun0:Function = null, errorTipB0:Boolean = true, errorLockB0:Boolean = true) : void
      {
         this.s4399.getStoreState(_yesFun0,_noFun0,errorTipB0,errorLockB0);
      }
      
      public function isLocal() : Boolean
      {
         return !Gaming.serviceHold;
      }
      
      public function isLogin() : Boolean
      {
         if(this.isLocal())
         {
            return true;
         }
         return this.s4399.isLogin();
      }
      
      public function FTimerSecond() : void
      {
         if(this.alreadyTime >= 0)
         {
            ++this.alreadyTime;
         }
      }
   }
}

