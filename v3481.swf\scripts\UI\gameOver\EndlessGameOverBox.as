package UI.gameOver
{
   import UI.base.NormalUI;
   import dataAll._app.worldMap.save.WorldMapSave;
   import flash.display.Sprite;
   import flash.text.TextField;
   import gameAll.level.data.OverLevelShow;
   import gameAll.level.endless.EndlessModelCtrl;
   
   public class EndlessGameOverBox extends NormalUI
   {
      private var scoreTxt:TextField;
      
      private var maxTxt:TextField;
      
      private var otherTxt:TextField;
      
      public function EndlessGameOverBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["maxTxt","otherTxt","scoreTxt"];
         super.setImg(img0);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function inData(show0:OverLevelShow) : void
      {
         var mapId0:String = show0.mapDefine.name;
         var save0:WorldMapSave = Gaming.PG.da.worldMap.saveGroup.getSave(mapId0);
         var score0:Number = EndlessModelCtrl.dat.tempScore;
         var nowMax0:Number = save0.maxEndlessScore;
         var allMax0:Number = Gaming.PG.da.worldMap.getMaxEndlessScore();
         var cnName0:String = show0.mapDefine.cnName;
         var lv0:int = show0.enemyLv;
         var maxGrade0:int = save0.maxEndlessGrade;
         this.scoreTxt.text = "评分：" + score0;
         this.maxTxt.text = "本图最高评分：" + nowMax0 + "\n" + "全图最高评分：" + allMax0;
         this.otherTxt.text = "挑战地图：" + cnName0 + "\n地图等级：" + lv0 + "级" + "\n已挑战最高层级：第" + maxGrade0 + "层";
      }
   }
}

