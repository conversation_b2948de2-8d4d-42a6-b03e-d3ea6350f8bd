package w_test
{
   import flash.display.Graphics;
   import gameAll.scene.MapData;
   
   public class TestCtrl_Map extends TestCtrl_Normal
   {
      public var gr:Graphics;
      
      public function TestCtrl_Map()
      {
         super();
      }
      
      private function testRectGroupMax() : void
      {
         var x0:int = Gaming.gameSprite.L_game.mouseX;
         var y0:int = Gaming.gameSprite.L_game.mouseY;
         var map0:MapData = Gaming.sceneGroup.map;
         var ra0:Number = map0.rectGroup.getSlopeVerticalRa(x0,y0);
         this.drawLine(x0,y0,ra0,100);
      }
      
      private function drawPoint(x0:Number, y0:Number, color0:uint = 16711680) : void
      {
         this.gr.beginFill(color0,0.7);
         this.gr.drawCircle(x0,y0,4);
      }
      
      private function drawLine(x0:Number, y0:Number, ra0:Number, len0:Number, color0:uint = 16711680) : void
      {
         var x1:Number = Math.cos(ra0) * len0 + x0;
         var y1:Number = Math.sin(ra0) * len0 + y0;
         this.gr.lineStyle(null,color0);
         this.gr.moveTo(x0,y0);
         this.gr.lineTo(x1,y1);
      }
   }
}

