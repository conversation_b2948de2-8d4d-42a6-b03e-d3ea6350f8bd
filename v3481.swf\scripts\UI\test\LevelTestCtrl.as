package UI.test
{
   import com.sounto.other.FunGroup;
   
   public class LevelTestCtrl extends FunGroup
   {
      public function LevelTestCtrl()
      {
         super();
      }
      
      public function overLevel() : void
      {
         if(Gaming.LG.autoTestB)
         {
            if(!haveOnceFun(this.gotoNextLevel))
            {
               addOnceFun(this.gotoNextLevel,3);
            }
         }
      }
      
      private function gotoNextLevel() : void
      {
         var noWinArr0:Array = Gaming.PG.save.worldMap.getNoWinArr();
         if(noWinArr0.length > 0)
         {
            Gaming.LG.chooseLevel(noWinArr0[0].name,0);
         }
      }
   }
}

