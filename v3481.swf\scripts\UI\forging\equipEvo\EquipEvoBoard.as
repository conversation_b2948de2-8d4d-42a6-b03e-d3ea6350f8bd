package UI.forging.equipEvo
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.must.NormalMustBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.equip.EquipData;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.creator.EquipEvoCtrl;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class EquipEvoBoard extends AutoNormalUI
   {
      private var beforeTxt:TextField;
      
      private var beforeGrip:ItemsGrid = new ItemsGrid();
      
      private var afterTxt:TextField;
      
      private var afterGrip:ItemsGrid = new ItemsGrid();
      
      private var mustBox:NormalMustBox;
      
      private var btn:NormalBtn;
      
      private var infoBtn:NormalBtn;
      
      public var nowData:EquipData = null;
      
      public function EquipEvoBoard()
      {
         super();
         mcTypeArr = ["btnSp","mustBoxSp","txt","Bx","pos"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         this.beforeGrip.setImgToEquipGrip();
         this.afterGrip.setImgToEquipGrip();
         super.setImg(img0);
         this.btn.setName("进化");
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.beforeGrip);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.afterGrip);
         ItemsGripTipCtrl.addNormalBtnTip(this.infoBtn);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,"equipEvo");
         this.showOneEquipDataAndPan(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      public function equipGripClick(da0:EquipData) : void
      {
         if(visible)
         {
            this.showOneEquipDataAndPan(da0);
         }
      }
      
      private function showOneEquipDataAndPan(da0:EquipData) : void
      {
         var dg0:EquipDataGroup = null;
         this.showNone();
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择要进化的装备。");
         if(Boolean(da0))
         {
            dg0 = Gaming.PG.da.findEquipData(da0);
            if(dg0 is EquipDataGroup)
            {
               this.showOneEquipData(da0);
            }
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      private function showOneEquipData(da0:EquipData) : void
      {
         this.nowData = da0;
         var after_da0:EquipData = EquipEvoCtrl.getAfterData(da0);
         this.showOneText(da0,"before");
         this.showOneText(after_da0,"after");
         var must_d0:MustDefine = EquipEvoCtrl.getMust(da0);
         var bodyLv0:int = 0;
         if(this.nowData.normalPlayerData is NormalPlayerData)
         {
            bodyLv0 = this.nowData.normalPlayerData.level;
         }
         var mustB0:Boolean = this.mustBox.inData(must_d0,bodyLv0);
         var canB0:Boolean = da0.canEvoB();
         this.btn.actived = canB0 && mustB0;
         this.infoBtn.tipString = EquipEvoCtrl.getAllEvoTip(da0);
      }
      
      private function showOneText(da0:EquipData, label0:String) : void
      {
         var grip0:ItemsGrid = this[label0 + "Grip"];
         var text0:TextField = this[label0 + "Txt"];
         var str0:String = "";
         if(Boolean(da0))
         {
            grip0.inData_equip(da0);
            str0 = da0.save.getColorCnName2();
            str0 += "\n" + EquipEvoCtrl.getProText(da0);
         }
         else
         {
            grip0.clearData();
            str0 = ComMethod.color("已进化至最高等级","#00FF00");
         }
         text0.htmlText = str0;
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var must_d0:MustDefine = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(Boolean(btn0) && btn0 == this.btn)
         {
            if(Boolean(this.nowData))
            {
               if(this.btn.actived)
               {
                  must_d0 = EquipEvoCtrl.getMust(this.nowData);
                  PlayerMustCtrl.deductMust(must_d0,this.afterStrengthen);
               }
            }
            else
            {
               Gaming.uiGroup.alertBox.showError("装备数据不存在！");
            }
         }
      }
      
      private function afterStrengthen() : void
      {
         EquipEvoCtrl.evo(this.nowData);
         this.yes_save();
      }
      
      private function yes_save(v:* = null) : void
      {
         this.showOneEquipDataAndPan(this.nowData);
         Gaming.uiGroup.allBagUI.fleshAllBox();
         Gaming.uiGroup.alertBox.showSuccess("进化成功！");
      }
      
      private function showNone() : void
      {
         this.nowData = null;
         this.beforeGrip.clearData();
         this.beforeTxt.text = "";
         this.afterGrip.clearData();
         this.afterTxt.text = "";
         this.mustBox.setShowState(false);
         this.btn.actived = false;
      }
   }
}

