package UI.base.alert
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.HaveConSprite;
   import UI.base.NormalUI;
   import UI.base.alert.colorChoose.AlertColorChooseBox;
   import UI.base.alert.colorChoose.AlertTextBox;
   import UI.base.button.NormalBtn;
   import UI.base.grid.NormalGridIcon;
   import UI.base.numChoose.NumChooseBox;
   import UI.base.tip.OneTextGather;
   import UI.pay.PayCtrl;
   import com.greensock.TweenLite;
   import com.sounto.oldUtils.ComMethod;
   import fl.motion.easing.Back;
   import flash.display.Shape;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.KeyboardEvent;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   import flash.text.TextField;
   import flash.text.TextFieldAutoSize;
   import flash.text.TextFormat;
   import flash.ui.Keyboard;
   import w_test.drop.LevelDropCount;
   
   public class AlertBox extends HaveConSprite
   {
      private var boardCon:Sprite = new Sprite();
      
      private var boardSp:Sprite;
      
      private var downSp:Sprite;
      
      private var bottomSp:Sprite;
      
      private var boardRect:Rectangle = new Rectangle();
      
      private var backSp:Shape = new Shape();
      
      private var icon:NormalGridIcon = new NormalGridIcon();
      
      private var grip:ItemsGrid = new ItemsGrid();
      
      private var text:TextField = new TextField();
      
      public var shop:ShopAlertBoard = new ShopAlertBoard();
      
      public var diff:DiffAlertBoard = new DiffAlertBoard();
      
      public var arena:ArenaModelBoard = new ArenaModelBoard();
      
      public var double:DoubleModelBoard = new DoubleModelBoard();
      
      public var info:InfoAlertBoard = new InfoAlertBoard();
      
      public var itemsColor:AlertColorChooseBox = new AlertColorChooseBox();
      
      public var textInput:AlertTextBox = new AlertTextBox();
      
      public var option:OptionAlertBoard = new OptionAlertBoard();
      
      private var numChooseBox:NumChooseBox = new NumChooseBox();
      
      private var numChangeFun:Function = null;
      
      private var boxArr:Array = [this.shop,this.diff,this.arena,this.double,this.info,this.itemsColor,this.textInput,this.numChooseBox,this.option];
      
      public var yesBtn:NormalBtn = new NormalBtn();
      
      public var noBtn:NormalBtn = new NormalBtn();
      
      public var closeBtn:SimpleButton = null;
      
      private var textAlign:String = "center";
      
      private var delayHideTime:Number = 0;
      
      private var _yesFun:Function = null;
      
      private var _noFun:Function = null;
      
      private var hideFunBeforeB:Boolean = false;
      
      private var allClickB:Boolean = true;
      
      private var yesObj:Object = null;
      
      public function AlertBox()
      {
         super();
      }
      
      public function imgInit() : void
      {
         var box0:NormalUI = null;
         OneTextGather.setNormalFormat(this.text,14,false,TextFieldAutoSize.CENTER);
         this.backSp.graphics.beginFill(0,0.8);
         this.backSp.graphics.drawRect(0,0,Gaming.WIDTH,Gaming.HEIGHT);
         addChild(this.backSp);
         addChild(this.boardCon);
         this.boardSp = Gaming.uiGroup.getBasicMovieClip("alertBox");
         this.boardRect = this.boardSp["arenaSp"].getRect(this.boardSp["arenaSp"]);
         this.downSp = this.boardSp["downSp"];
         this.bottomSp = this.boardSp["bottomSp"];
         this.grip.setImgToEquipGrip();
         this.grip.mouseEnabled = false;
         this.boardCon.addChild(this.boardSp);
         this.boardSp.x = -this.boardSp["diffSp"].width / 2;
         this.boardSp.y = -this.boardSp["diffSp"].height / 2;
         this.boardCon.x = this.backSp.width / 2;
         this.boardCon.y = this.backSp.height / 2;
         this.boardSp.addChild(this.text);
         this.boardSp.addChild(this.icon);
         this.icon.visible = false;
         this.boardSp.addChild(this.grip);
         this.grip.visible = false;
         this.boardSp.addChild(this.yesBtn);
         this.yesBtn.setImg(this.boardSp["yesBtn"]);
         this.yesBtn.setName("确定");
         this.yesBtn.addEventListener(MouseEvent.CLICK,this.btnClick);
         ItemsGripTipCtrl.addNormalBtnTip(this.yesBtn);
         this.yesBtn.activedAndEnabled = false;
         this.boardSp.addChild(this.noBtn);
         this.noBtn.setImg(this.boardSp["noBtn"]);
         this.noBtn.setName("取消");
         this.noBtn.addEventListener(MouseEvent.CLICK,this.btnClick);
         ItemsGripTipCtrl.addNormalBtnTip(this.noBtn);
         this.noBtn.activedAndEnabled = false;
         this.closeBtn = this.boardSp["closeBtn"];
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.shop.setImg(this.boardSp["shopSp"]);
         this.shop.visible = false;
         this.shop.box = this;
         this.diff.setImg(this.boardSp["diffSp"]);
         this.diff.visible = false;
         this.diff.box = this;
         this.arena.setImg(this.boardSp["arenaSp"]);
         this.arena.visible = false;
         this.arena.box = this;
         this.double.setImg(this.boardSp["doubleSp"]);
         this.double.visible = false;
         this.double.box = this;
         this.info.setImg(this.boardSp["infoSp"]);
         this.info.visible = false;
         this.info.box = this;
         this.itemsColor.setImg(this.boardSp["itemsColorSp"]);
         this.itemsColor.visible = false;
         this.itemsColor.box = this;
         this.textInput.setImg(this.boardSp["textInputSp"]);
         this.textInput.visible = false;
         this.textInput.box = this;
         this.numChooseBox.setImg(this.boardSp["numSp"]);
         this.numChooseBox.visible = false;
         this.numChooseBox.addEventListener(Event.CHANGE,this.numChange);
         this.option.box = this;
         for each(box0 in this.boxArr)
         {
            box0.setCon(this.boardSp);
         }
         this.visible = false;
         this.addEventListener(MouseEvent.CLICK,this.allClick);
      }
      
      public function getEquipGrip() : ItemsGrid
      {
         return this.grip;
      }
      
      private function show() : void
      {
         this.visible = true;
         super.inCon();
         this.mouseChildren = true;
         this.backSp.alpha = 0;
         this.boardCon.scaleX = 0.7;
         this.boardCon.scaleY = this.boardCon.scaleX;
         TweenLite.to(this.backSp,0.3,{"alpha":1});
         TweenLite.to(this.boardCon,0.3,{
            "scaleX":1,
            "scaleY":1,
            "ease":Back.easeOut,
            "onComplete":(this.delayHideTime == 0 ? null : this.hide)
         });
         if(Gaming.LG.state == "ing")
         {
            Gaming.LG.pauseLevel();
         }
         if(LevelDropCount.testB && Gaming.LG.isGaming())
         {
            this.doYesFun();
         }
      }
      
      public function hide() : void
      {
         TweenLite.to(this.backSp,0.2,{
            "alpha":0,
            "delay":this.delayHideTime
         });
         TweenLite.to(this.boardCon,0.2,{
            "scaleX":0.7,
            "scaleY":0.7,
            "ease":Back.easeIn,
            "delay":this.delayHideTime,
            "onComplete":this.affterHide
         });
         this.mouseChildren = false;
         if(Gaming.LG.state == "pause")
         {
            if(Gaming.uiGroup.canResumeB(true))
            {
               Gaming.LG.resumeLevel();
            }
         }
      }
      
      private function affterHide() : void
      {
         this.visible = false;
         super.outCon();
         this._yesFun = null;
         this._noFun = null;
         this.yesObj = null;
      }
      
      public function showCheck(str0:String, btnShowState0:String, delayHide0:Number = 0, yesFun0:Function = null, noFun0:Function = null, iconUrl0:String = "", iconType0:String = "", textAlign0:String = "center", hideFunBeforeB0:Boolean = true, allClickB0:Boolean = true, closeBtnB0:Boolean = false) : void
      {
         Gaming.soundGroup.playSound("uiSound","alertShow");
         this._yesFun = yesFun0;
         this._noFun = noFun0;
         this.textAlign = textAlign0;
         this.delayHideTime = delayHide0;
         this.hideFunBeforeB = hideFunBeforeB0;
         this.allClickB = allClickB0;
         this.text.width = 10;
         this.text.wordWrap = false;
         this.text.autoSize = this.textAlign;
         this.text.defaultTextFormat.align = this.textAlign;
         var tf0:TextFormat = new TextFormat("SimSun",String(this.text.defaultTextFormat.size),16777215,null,null,null,null,null,this.textAlign);
         this.text.defaultTextFormat = tf0;
         this.text.htmlText = str0;
         this.bottomSp.visible = str0 != " ";
         this.setIcon(iconUrl0,iconType0);
         this.yesObj = null;
         this.fleshBtnPosition(btnShowState0);
         this.yesBtn.setName("确定");
         this.yesBtn.tipString = "";
         this.noBtn.setName("取消");
         this.noBtn.tipString = "";
         this.fleshTextPosition();
         this.hideAllBox();
         this.closeBtn.visible = closeBtnB0;
         this.show();
      }
      
      private function hideAllBox() : void
      {
         var box0:NormalUI = null;
         for each(box0 in this.boxArr)
         {
            box0.hide();
         }
      }
      
      public function showNormal(str0:String, btnShowState0:String = "yesAndNo", yesFun0:Function = null, noFun0:Function = null, iconUrl0:String = "", allClickB0:Boolean = true, closeBtnB0:Boolean = false) : void
      {
         this.showCheck(str0,btnShowState0,0,yesFun0,noFun0,iconUrl0,"icon","center",true,allClickB0,closeBtnB0);
      }
      
      public function showInfoNoBtnDelay(str0:String, delay0:Number, iconUrl0:String = "") : void
      {
         this.showCheck(str0,"",delay0,null,null,iconUrl0,"icon");
      }
      
      public function showChoose(str0:String, yesFun0:Function = null, noFun0:Function = null, iconUrl0:String = "", closeB0:Boolean = false) : void
      {
         this.showNormal(str0,"yesAndNo",yesFun0,noFun0,iconUrl0,true,closeB0);
      }
      
      public function showSuccess(str0:String, yesFun0:Function = null, allClickB0:Boolean = true, iconUrl0:String = "") : void
      {
         this.showNormal(str0,"yes",yesFun0,null,iconUrl0 == "" ? "yes" : iconUrl0,allClickB0);
      }
      
      public function showError(str0:String, yesFun0:Function = null, allClickB0:Boolean = true, hideBtnB0:Boolean = false) : void
      {
         this.showNormal(str0,hideBtnB0 ? "" : "yes",yesFun0,null,"no",allClickB0);
      }
      
      public function showLock(str0:String) : void
      {
         this.showError(str0,null,false,true);
      }
      
      public function showInfo(str0:String, yesFun0:Function = null, allClickB0:Boolean = true) : void
      {
         this.showNormal(str0,"yes",yesFun0,null,"",allClickB0);
      }
      
      public function showPay(str0:String = "") : void
      {
         if(str0 == "")
         {
            str0 = "黄金余额不足，是否充值？";
         }
         this.showNormal(str0,"yesAndNo",PayCtrl.gotoPay,null,"no");
         this.yesBtnToPay();
      }
      
      public function showNumChoose(str0:String, nowNum0:Number, max0:Number, min0:Number = 1, gapNum0:Number = 1, yesFun0:Function = null, btnShowState0:String = "yesAndNo", numChangeFun0:Function = null, noFun0:Function = null, textChangeFleshB0:Boolean = true, fixed0:int = 0) : void
      {
         this.showNormal(str0,btnShowState0,yesFun0,noFun0,"",false,true);
         this.numChangeFun = numChangeFun0;
         this.numChooseBox.nowNum = nowNum0;
         this.numChooseBox.max = max0;
         this.numChooseBox.min = min0;
         this.numChooseBox.gapNum = gapNum0;
         this.numChooseBox.textChangeFleshB = textChangeFleshB0;
         this.numChooseBox.fixed = fixed0;
         this.numChooseBox.show();
         this.numChooseBox.fleshPriceAndNum(0,false,false);
         this.fleshNumChooseBoxPos();
      }
      
      private function fleshNumChooseBoxPos() : void
      {
         this.numChooseBox.y = this.text.y + this.text.height + 10 + 13;
      }
      
      private function numChange(e:Event) : void
      {
         if(this.numChooseBox.visible)
         {
            if(this.numChangeFun is Function)
            {
               this.text.htmlText = this.numChangeFun(this.numChooseBox);
               this.fleshTextPosition();
               this.fleshNumChooseBoxPos();
            }
         }
      }
      
      public function yesBtnToPay() : void
      {
         this.yesBtn.setName(ComMethod.color("充值","#FFFF00"));
      }
      
      private function setIcon(iconUrl0:String, iconType0:String = "") : void
      {
         this.icon.visible = false;
         this.grip.visible = false;
         if(iconUrl0 != "")
         {
            if(iconType0 == "icon" && iconUrl0.indexOf("/") > 0)
            {
               iconType0 = "url";
            }
            if(iconType0 == "equip")
            {
               this.grip.visible = true;
               this.grip.clearData();
               this.grip.setIconName(iconUrl0);
            }
            else if(iconType0 == "url")
            {
               this.icon.setIconName(iconUrl0);
               this.icon.visible = true;
            }
            else if(iconType0 == "icon")
            {
               this.icon.setIconName("BasicUI/" + iconUrl0 + "Icon");
               this.icon.visible = true;
               if(iconUrl0 == "yes")
               {
                  Gaming.soundGroup.playSound("uiSound","success");
               }
               else if(iconUrl0 == "no")
               {
                  Gaming.soundGroup.playSound("uiSound","fail");
               }
            }
         }
      }
      
      private function fleshTextPosition() : void
      {
         var left_w0:int = 0;
         var right_w0:int = 0;
         if(this.haveIconB())
         {
            left_w0 = this.grip.width + 35 + 15;
            right_w0 = 35;
         }
         else
         {
            left_w0 = 35;
            right_w0 = left_w0;
         }
         var c_y0:int = (this.boardRect.height - Number(this.downSp.visible ? 60 : 0)) / 2;
         var maxW0:int = this.boardRect.width - left_w0 - right_w0;
         if(this.text.width > maxW0)
         {
            this.text.wordWrap = true;
            this.text.width = maxW0;
         }
         if(this.textAlign == "center" && this.text.width < maxW0)
         {
            this.text.x = this.boardRect.width / 2 - this.text.width / 2;
         }
         else
         {
            this.text.x = left_w0;
         }
         this.text.y = c_y0 - this.text.height / 2;
         this.grip.y = c_y0 - this.grip.height / 2;
         this.grip.x = this.text.x - 15 - this.grip.width;
         this.icon.y = c_y0;
         this.icon.x = this.text.x - 15 - this.icon.width / 2;
      }
      
      private function haveIconB() : Boolean
      {
         return this.grip.visible || this.icon.visible;
      }
      
      private function fleshBtnPosition(btnShowState0:String) : void
      {
         this.yesBtn.visible = false;
         this.yesBtn.actived = true;
         this.noBtn.visible = false;
         this.noBtn.actived = true;
         this.downSp.visible = true;
         var c_x0:int = this.boardRect.width / 2;
         if(btnShowState0 == "yes")
         {
            this.yesBtn.visible = true;
            this.yesBtn.x = c_x0 - this.yesBtn.width / 2;
         }
         else if(btnShowState0 == "no")
         {
            this.noBtn.visible = true;
            this.noBtn.x = c_x0 - this.noBtn.width / 2;
         }
         else if(btnShowState0 == "yesAndNo")
         {
            this.yesBtn.visible = true;
            this.noBtn.visible = true;
            this.yesBtn.x = c_x0 - this.yesBtn.width - 8;
            this.noBtn.x = c_x0 + 8;
         }
         else
         {
            this.downSp.visible = false;
         }
         this.closeBtn.x = this.boardRect.width - this.closeBtn.width - 2;
         this.closeBtn.y = 2;
      }
      
      public function setBtnText(yes0:String, no0:String) : void
      {
         this.yesBtn.setName(yes0);
         this.noBtn.setName(no0);
      }
      
      public function setYesActived(bb0:Boolean) : void
      {
         this.yesBtn.actived = bb0;
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         if(Boolean(btn0))
         {
            if(btn0.actived == false)
            {
               return;
            }
         }
         if(this.hideFunBeforeB)
         {
            this.hide();
         }
         if(e.target == this.yesBtn)
         {
            this.doYesFun();
         }
         else if(e.target == this.noBtn)
         {
            if(this._noFun is Function)
            {
               this._noFun();
            }
         }
         if(!this.hideFunBeforeB)
         {
            this.hide();
         }
      }
      
      public function enterKey(e:KeyboardEvent) : void
      {
         if(e.keyCode == Keyboard.ENTER)
         {
            if(this.yesBtn.visible && this.yesBtn.actived && this.mouseChildren)
            {
               this.hide();
               this.doYesFun();
            }
         }
      }
      
      private function allClick(e:MouseEvent) : void
      {
         if(this.allClickB && this.yesBtn.visible && this.yesBtn.actived && !this.noBtn.visible && this.mouseChildren && !this.diff.visible && !this.shop.visible && !this.double.visible && !this.info.visible && !this.shop.visible && !this.textInput.visible)
         {
            if(e.target != this.yesBtn && e.target != this.noBtn)
            {
               Gaming.soundGroup.playSound("uiSound","click");
               if(this.hideFunBeforeB)
               {
                  this.hide();
               }
               this.doYesFun();
               if(!this.hideFunBeforeB)
               {
                  this.hide();
               }
            }
         }
      }
      
      public function setYesObj(obj0:Object) : void
      {
         this.yesObj = obj0;
      }
      
      private function doYesFun() : void
      {
         if(this._yesFun is Function)
         {
            if(this.textInput.visible)
            {
               this._yesFun(this.textInput.getText());
            }
            else if(this.numChooseBox.visible)
            {
               this._yesFun(this.numChooseBox.getFleshNum());
            }
            else
            {
               this._yesFun();
            }
         }
      }
   }
}

