package UI.api.save
{
   import dataAll._app.login.SaveBaseData4399;
   import flash.net.SharedObject;
   import flash.utils.ByteArray;
   import flash.utils.getTimer;
   
   public class LocalSave
   {
      public var SO:SharedObject = SharedObject.getLocal("BaoQiang");
      
      private var other:SharedObject = SharedObject.getLocal("other");
      
      public function LocalSave()
      {
         super();
      }
      
      public function WriteSO(gd:*) : *
      {
         var tt:Number = getTimer();
         var str2:ByteArray = new ByteArray();
         str2.writeObject(gd);
         str2.position = 0;
         this.SO.data.a = str2.readObject();
         this.SO.flush();
      }
      
      public function ReadSO() : Object
      {
         var obj:Object = null;
         var str2:ByteArray = null;
         var str3:ByteArray = null;
         if(this.SO.data.a is Object)
         {
            if(this.SO.data.a is ByteArray)
            {
               str2 = this.SO.data.a;
               str3 = new ByteArray();
               str3.writeBytes(str2);
               str3.inflate();
               str3.position = 0;
               obj = str3.readObject();
            }
            else
            {
               obj = this.SO.data.a;
            }
            return obj;
         }
         return null;
      }
      
      public function haveDataB() : Boolean
      {
         if(this.SO.data.a is Object)
         {
            return true;
         }
         return false;
      }
      
      public function getList() : Array
      {
         var d0:SaveBaseData4399 = null;
         var d2:SaveBaseData4399 = null;
         var d3:SaveBaseData4399 = null;
         if(this.haveDataB())
         {
            d0 = new SaveBaseData4399();
            d0.index = 0;
            d0.datetime = "2013-1-1 00:00:00";
            d0.title = "爆枪";
            d0.status = "0";
            d0.create_time = "2014-01-06 11:48:48";
            d0.update_times = 1000;
            d2 = new SaveBaseData4399();
            d2.index = 1;
            d2.datetime = "2024-1-1 00:00:00";
            d2.title = "表哥";
            d2.status = "0";
            d2.create_time = "2018-10-01 10:10:10";
            d2.update_times = 2000;
            d3 = new SaveBaseData4399();
            d3.index = 2;
            d3.datetime = "2024-1-1 00:00:00";
            d3.title = "小樱Girl";
            d3.status = "0";
            d3.create_time = "2018-10-01 10:10:10";
            d3.update_times = 2000;
            return [d0,d2,d3];
         }
         return [];
      }
      
      public function ClearSO() : *
      {
         this.SO.clear();
      }
      
      public function setPro(name0:String, v0:*) : void
      {
         this.other.data[name0] = v0;
      }
      
      public function getPro(name0:String) : *
      {
         return this.other.data[name0];
      }
   }
}

