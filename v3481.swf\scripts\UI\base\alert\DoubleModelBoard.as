package UI.base.alert
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGripBox;
   import UI.base.NormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.grid.NormalGrid;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class DoubleModelBoard extends NormalUI
   {
      public var box:AlertBox;
      
      private var closeBtn:SimpleButton;
      
      private var gripTag:Sprite;
      
      private var gripBox:ItemsGripBox = new ItemsGripBox();
      
      private var _yesFun:Function;
      
      public function DoubleModelBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var n:* = undefined;
         var grip0:NormalGrid = null;
         var name0:String = null;
         elementNameArr = ["closeBtn","gripTag"];
         super.setImg(img0);
         this.gripBox.imgType = "doubleModelBar";
         this.gripBox.arg.init(2,1,2,2);
         addChild(this.gripBox);
         NormalUICtrl.setTag(this.gripBox,this.gripTag);
         this.gripBox.evt.setWantEvent(true,false,false,true,true,false);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.click);
         this.gripBox.addEventListener(ClickEvent.ON_OVER,this.barOver);
         this.gripBox.addEventListener(ClickEvent.ON_OUT,this.barOut);
         this.gripBox.setNowGripNum(2);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         var nameArr0:Array = ["single","double"];
         for(n in this.gripBox.gripArr)
         {
            grip0 = this.gripBox.gripArr[n];
            name0 = nameArr0[n];
            grip0.label = name0;
            grip0.setSmallIcon(name0);
            grip0.activedAndEnabled = false;
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function showModel(_yesFun0:Function) : void
      {
         this.box.showCheck("","");
         show();
         this._yesFun = _yesFun0;
      }
      
      private function click(e:ClickEvent) : void
      {
         this.box.hide();
         this._yesFun(e.label);
         this._yesFun = null;
      }
      
      private function barOver(e:ClickEvent) : void
      {
      }
      
      private function barOut(e:ClickEvent) : void
      {
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.box.hide();
         this._yesFun = null;
      }
   }
}

