package UI.arena.history
{
   import UI.bag.ItemsGripBox;
   import UI.base.NormalUI;
   import UI.base.event.ClickEvent;
   import UI.test.SaveTestBox;
   import dataAll._app.arena.record.ArenaRecordBarData;
   import dataAll._app.top.TopBarData;
   import flash.display.Sprite;
   import flash.text.TextField;
   import gameAll.level.arena.ArenaCtrl;
   
   public class ArenaHistoryBoard extends NormalUI
   {
      private var box:ItemsGripBox = new ItemsGripBox();
      
      private var titleTxt:TextField;
      
      private var barTag:Sprite;
      
      private var pageTag:Sprite;
      
      public function ArenaHistoryBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["titleTxt","barTag","pageTag"];
         super.setImg(img0);
         this.box.imgType = "ArenaUI/historyBar";
         this.box.arg.init(1,10,0,1);
         this.box.evt.setWant(true,true);
         this.box.x = this.barTag.x;
         this.box.y = this.barTag.y;
         this.box.addEventListener(ClickEvent.ON_CLICK,this.barClick);
         this.box.addEventListener(ClickEvent.ON_OVER,this.barOver);
         addChild(this.box);
         this.box.pageBox.setToNormalBtn();
         this.box.pageBox.x = this.pageTag.x - this.barTag.x;
         this.box.pageBox.y = this.pageTag.y - this.barTag.y;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      public function fleshData() : void
      {
         var arr0:Array = Gaming.PG.da.arena.getRecordDataArr();
         this.box.inData_byArr(arr0,"inData_arenaRecord");
         this.titleTxt.text = Gaming.PG.da.base.save.playerName + "的战史";
      }
      
      private function barClick(e:ClickEvent) : void
      {
         var da0:ArenaRecordBarData = e.childData as ArenaRecordBarData;
         var topDa0:TopBarData = da0.getTopBarData();
         SaveTestBox.addText("TopBarData.uname:" + topDa0.uname);
         SaveTestBox.addText("挑战码:" + topDa0.getNowArenaCode());
         ArenaCtrl.chooseArivalByTopBarData(topDa0);
      }
      
      private function barOver(e:ClickEvent) : void
      {
         var da0:ArenaRecordBarData = null;
         var topDa0:TopBarData = null;
         var s0:String = null;
         if(Gaming.testCtrl.enabled)
         {
            da0 = e.childData as ArenaRecordBarData;
            topDa0 = da0.getTopBarData();
            s0 = "排名：" + topDa0.rank;
            s0 += "\n分数：" + topDa0.score;
            Gaming.uiGroup.tipBox.textTip.showFollowText(s0);
         }
      }
   }
}

