package UI.bag
{
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   
   public class ItemsResolveCtrl
   {
      private static var nowDa:IO_ItemsData;
      
      private static var nowDg:ItemsDataGroup;
      
      public function ItemsResolveCtrl()
      {
         super();
      }
      
      public static function resolveArr(arr0:Array, dg0:ItemsDataGroup) : void
      {
         var noInNum0:Number = NaN;
         var g0:GiftAddDefineGroup = null;
         var da0:IO_ItemsData = null;
         var bagStr0:String = null;
         var one0:GiftAddDefineGroup = null;
         if(arr0.length > 0)
         {
            noInNum0 = dg0.noInArrPan(arr0);
            if(noInNum0 > 0)
            {
               Gaming.uiGroup.alertBox.showError("数据错误！");
            }
            else
            {
               g0 = new GiftAddDefineGroup();
               for each(da0 in arr0)
               {
                  one0 = da0.getResolveGift();
                  g0.merge(one0);
               }
               bagStr0 = GiftAddit.bagSpacePan(g0);
               if(bagStr0 != "")
               {
                  Gaming.uiGroup.alertBox.showError(bagStr0);
               }
               else
               {
                  dg0.removeDataArr(arr0);
                  GiftAddit.add(g0,"分解成功！获得：\n" + g0.getDescription());
                  Gaming.TG.dat.removeArr(arr0,dg0);
                  ItemsGripBtnListCtrl.fleshAllBy(dg0,false);
               }
            }
         }
      }
      
      public static function showResolve(da0:IO_ItemsData, dg0:ItemsDataGroup) : void
      {
         if(da0.getSave().getLockB())
         {
            Gaming.uiGroup.alertBox.showError("已锁物品不能被分解！");
            return;
         }
         nowDa = da0;
         nowDg = dg0;
         var gift0:GiftAddDefineGroup = da0.getResolveGift();
         var str0:String = "分解1个当前物品可获得：\n" + gift0.getDescription();
         var numB0:Boolean = da0.getNowNum() > 1;
         if(numB0)
         {
            str0 += "\n请选择分解数量：";
            Gaming.uiGroup.alertBox.showNumChoose(str0,1,da0.getNowNum(),1,1,yesResolve);
         }
         else
         {
            str0 += "\n是否要分解？";
            Gaming.uiGroup.alertBox.showNormal(str0,"yesAndNo",yesResolve);
         }
      }
      
      private static function yesResolve(num0:int = 1) : void
      {
         var dg0:ItemsDataGroup = null;
         var da0:IO_ItemsData = nowDa;
         var gift0:GiftAddDefineGroup = da0.getResolveGift();
         gift0.addNumMul(num0);
         var bagStr0:String = GiftAddit.bagSpacePan(gift0);
         if(bagStr0 == "")
         {
            dg0 = nowDg;
            if(Boolean(da0))
            {
               dg0.removeDataByNum(da0,num0);
               Gaming.TG.dat.remove(da0,dg0);
               GiftAddit.add(gift0,"分解成功！获得：\n" + gift0.getDescription());
               ItemsGripBtnListCtrl.fleshAllBy(nowDg,false);
            }
            else
            {
               Gaming.uiGroup.alertBox.showError("该物品已不存在于你的背包中。");
            }
         }
         else
         {
            Gaming.uiGroup.alertBox.showError(bagStr0);
         }
      }
   }
}

