package w_test
{
   import com.common.data.Base64;
   import com.common.text.TextWay;
   import com.sounto.cf.ObjectToXml;
   import com.sounto.oldUtils.Positive64;
   import com.sounto.oldUtils.Sounto64;
   import com.sounto.oldUtils.SountoCF;
   import flash.utils.getTimer;
   
   public class TestCtrl_Text extends TestCtrl_Normal
   {
      public var CF:SountoCF = new SountoCF();
      
      public function TestCtrl_Text()
      {
         super();
      }
      
      public function set money(v0:Number) : void
      {
         this.CF.setAttribute("money",v0);
      }
      
      public function get money() : Number
      {
         return Number(this.CF.getAttribute("money"));
      }
      
      public function testRound() : void
      {
         var num0:Number = NaN;
         var tt0:Number = getTimer();
         for(var i:int = 0; i < 100000; i++)
         {
            num0 = Math.round(236446.135644654);
         }
      }
      
      public function testRandom() : void
      {
         var num0:Number = NaN;
         var tt0:Number = getTimer();
         for(var i:int = 0; i < 1000000; i++)
         {
            num0 = Math.random();
         }
      }
      
      public function testParseInt() : void
      {
         var str0:String = null;
         var num2:Number = NaN;
         var tt0:Number = getTimer();
         for(var i:int = 0; i < 1000000; i++)
         {
            str0 = (123456789).toString(36);
            num2 = parseInt(str0,36);
         }
      }
      
      public function testSubString() : void
      {
         var str1:String = null;
         var tt0:Number = getTimer();
         for(var i:int = 0; i < 1000000; i++)
         {
            str1 = "654563321456459875645".substr(7,7);
         }
      }
      
      public function testPositive64() : void
      {
         var num0:Number = NaN;
         var str1:String = null;
         var num1:Number = NaN;
         var tt0:Number = getTimer();
         for(var i:int = 0; i < 10; i++)
         {
            num0 = int(Math.random() * 10000) * int(Math.random() * 10000);
            str1 = Positive64.encode(num0);
            num1 = Positive64.decode(str1);
            if(num0 != num1)
            {
            }
         }
      }
      
      public function testTextCode() : void
      {
         var str1:String = null;
         var tt0:Number = getTimer();
         for(var i:int = 0; i < 1000000; i++)
         {
            str1 = String("我".charCodeAt());
         }
      }
      
      public function testBase64() : void
      {
         var str0:String = null;
         var str1:String = null;
         var tt0:Number = getTimer();
         var num0:Number = -6546623146.221555;
         for(var i:int = 0; i < 10000; i++)
         {
            str0 = Base64.encodeString(String(num0));
            str1 = Base64.decodeString(str0);
            num0 = Number(str1);
         }
      }
      
      public function testSounto32() : void
      {
         var str0:String = null;
         var str1:String = null;
         var tt0:Number = getTimer();
         var num0:Number = -6546623146;
         for(var i:int = 0; i < 10000; i++)
         {
            str0 = TextWay.toCode(String(num0));
            str1 = TextWay.getText(str0);
            num0 = Number(str1);
         }
         trace("testSounto32 10000次耗时：" + (getTimer() - tt0));
      }
      
      public function testSountoCF() : void
      {
         var tt0:Number = getTimer();
         this.money = 123456;
         for(var i:int = 0; i < 100000; i++)
         {
            this.money = this.money;
         }
         trace("testSountoCF 100000次耗时：" + (getTimer() - tt0));
      }
      
      public function testSounto64() : void
      {
         var str0:String = null;
         var str1:String = null;
         var tt0:Number = getTimer();
         var num0:Number = 123456;
         for(var i:int = 0; i < 100000; i++)
         {
            str0 = Sounto64.encode(String(num0));
            str1 = Sounto64.decode(str0);
            num0 = Number(str1);
         }
      }
      
      public function testToXML() : void
      {
         var obj0:Object = Gaming.PG.save.getCopyObj();
         var xml0:XML = ObjectToXml.decode4399(obj0);
      }
   }
}

