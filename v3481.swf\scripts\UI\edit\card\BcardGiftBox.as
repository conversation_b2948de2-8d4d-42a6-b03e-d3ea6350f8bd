package UI.edit.card
{
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.DisplayMethod;
   import dataAll._app.edit.card.BCardPKCreator;
   import dataAll._app.edit.card.BossCardData;
   import dataAll._app.edit.card.BossCardDataGroup;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class BcardGiftBox extends AutoNormalUI
   {
      private var closeBtn:NormalBtn;
      
      private var propsBtn:NormalBtn;
      
      private var titleTxt:TextField;
      
      private var propsTxt:TextField;
      
      private var barArr:Array = [];
      
      public function BcardGiftBox()
      {
         super();
         mcTypeArr = ["btnSp","txt"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         FontDeal.dealOne(this.titleTxt);
         this.propsBtn.addEventListener(MouseEvent.MOUSE_OVER,this.propsBtnOver);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      protected function get dataG() : BossCardDataGroup
      {
         return Gaming.PG.da.bossCard;
      }
      
      override public function show() : void
      {
         super.show();
         this.firstAddBox();
         this.fleshData();
      }
      
      private function firstAddBox() : void
      {
         var darr0:Array = null;
         var da0:BossCardData = null;
         var bar0:BcardGiftBar = null;
         if(this.barArr.length == 0)
         {
            darr0 = BCardPKCreator.getArrByFather("achieveGift");
            for each(da0 in darr0)
            {
               bar0 = new BcardGiftBar();
               bar0.setToNormalImg();
               bar0.fleshFun = this.fleshData;
               bar0.inData(da0);
               this.barArr.push(bar0);
               this.addChild(bar0);
            }
            DisplayMethod.arrange(this.barArr,12,12,5,3,18,73);
         }
      }
      
      private function fleshData() : void
      {
         var bar0:BcardGiftBar = null;
         var da0:BossCardData = null;
         var getted0:Boolean = false;
         var m0:BossCardData = null;
         var must0:int = 0;
         var vipMust0:int = 0;
         var canB0:Boolean = false;
         var drawNum0:int = this.dataG.getDrawNum();
         for each(bar0 in this.barArr)
         {
            da0 = bar0.itemsData;
            getted0 = this.dataG.getAchivevGiftGetted(da0);
            if(getted0)
            {
               bar0.setGiftBtn("已领取",false);
            }
            else
            {
               m0 = bar0.itemsData;
               must0 = m0.getAchieveGiftMust();
               vipMust0 = m0.getVipGiftMust();
               canB0 = drawNum0 >= must0 && Gaming.PG.da.vip.def.getTrueLevel() >= vipMust0;
               bar0.filters = canB0 ? [] : [NormalBtn.no_filter];
               bar0.setGiftBtn("领取",canB0,canB0);
            }
         }
         this.fleshSum();
      }
      
      private function fleshSum() : void
      {
         var num0:int = this.dataG.getBossSumNum();
         var getted0:int = this.dataG.getBossSumGetted();
         var str0:String = "领取 " + ComMethod.dropColor(getted0,num0);
         this.propsBtn.setName(str0);
         this.propsBtn.activedAndEnabled = false;
         this.propsBtn.actived = num0 > getted0;
         this.propsTxt.htmlText = this.dataG.getBossSumStr();
      }
      
      private function propsBtnOver(e:MouseEvent) : void
      {
         Gaming.uiGroup.giftTip.showTip(this.dataG.getBossSumGift(),this.propsBtn);
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var g0:GiftAddDefineGroup = null;
         var bb0:Boolean = false;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0 == this.closeBtn)
         {
            hide();
         }
         else if(btn0 == this.propsBtn)
         {
            if(this.dataG.getBossSumNum() > this.dataG.getBossSumGetted())
            {
               g0 = this.dataG.getBossSumGift();
               bb0 = GiftAddit.addAndAutoBagSpacePan(g0);
               if(bb0)
               {
                  this.dataG.bossSumGetEvent();
                  this.fleshSum();
               }
            }
         }
      }
   }
}

