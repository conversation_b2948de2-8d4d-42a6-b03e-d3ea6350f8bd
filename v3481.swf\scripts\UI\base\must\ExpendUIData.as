package UI.base.must
{
   import UI.base.button.NormalBtn;
   import dataAll.must.define.MustDefine;
   import flash.text.TextField;
   
   public class ExpendUIData
   {
      protected var successReSetDataB:Boolean = true;
      
      public function ExpendUIData()
      {
         super();
      }
      
      public function getBtnCn(name0:String) : String
      {
         return "";
      }
      
      public function showDataEvent(okBtn0:NormalBtn, infoTxt0:TextField, mustBox0:NormalMustBox) : void
      {
      }
      
      public function getMustDefine() : MustDefine
      {
         return new MustDefine();
      }
      
      public function successEvent() : void
      {
      }
      
      public function getSuccessReSetDataB() : Bo<PERSON>an
      {
         return this.successReSetDataB;
      }
   }
}

