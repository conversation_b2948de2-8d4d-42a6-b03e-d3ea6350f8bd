package UI.arena.room
{
   import UI.base.box.NormalGridIconBox;
   import UI.base.font.FontDeal;
   import UI.base.grid.NormalGrid;
   import UI.base.heroImg.HeroEquipImgBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.top.TopBarData;
   import dataAll._app.top.extra.ArenaTopExtra;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.filters.BitmapFilter;
   import flash.text.TextField;
   
   public class ArenaRivalGrip extends NormalGrid
   {
      private var imgTag:Sprite;
      
      private var skillTag:Sprite;
      
      private var leftTxt:TextField;
      
      private var rightTxt:TextField;
      
      private var skillFilter:BitmapFilter;
      
      private var imgBox:HeroEquipImgBox = new HeroEquipImgBox();
      
      private var skillBox:NormalGridIconBox = new NormalGridIconBox();
      
      public function ArenaRivalGrip()
      {
         super();
      }
      
      override public function setImg(img0:MovieClip) : void
      {
         super.setImg(img0);
         this.leftTxt = img0["leftTxt"];
         this.rightTxt = img0["rightTxt"];
         this.imgTag = img0["imgTag"];
         this.imgTag.addChild(this.imgBox);
         this.imgBox.imgInit();
         this.skillTag = img0["skillTag"];
         this.skillTag.addChild(this.skillBox);
         this.skillBox.arg.init(5,2,0,0);
         this.skillBox.scaleX = 0.5;
         this.skillBox.scaleY = this.skillBox.scaleX;
         this.skillFilter = this.skillTag.filters[0];
         FontDeal.dealLine(this.leftTxt);
         FontDeal.dealLine(this.rightTxt);
      }
      
      public function inDataByTopBarData(da0:TopBarData) : void
      {
         itemsData = da0;
         var extra0:ArenaTopExtra = da0.extraObj as ArenaTopExtra;
         setName("<b>" + ComMethod.color("第" + da0.rank + "名","#FFFF00") + "</b>");
         setPriceText(extra0.player);
         setLevelText(extra0.lv + "");
         var left0:String = "";
         var right0:String = "";
         left0 += "积分";
         right0 += da0.score + "";
         left0 += "\n战斗力";
         right0 += "\n" + extra0.dps + "";
         left0 += "\n生命值";
         right0 += "\n" + extra0.life + "";
         this.leftTxt.text = left0;
         this.rightTxt.text = right0;
         var skillconUrlArr0:Array = Gaming.defineGroup.skill.getIconUrlByNameArr(extra0.skill.split(","));
         this.skillBox.addByUrlArr(skillconUrlArr0);
         var equipNameArr0:Array = extra0.equip.split(",");
         var imgObj0:Object = Gaming.BG.getPartImageMcObjByEquipNameArr(equipNameArr0,extra0.getRoleDefine());
         this.imgBox.setEquip_byObj(imgObj0,extra0.getImgRoleDefine().name);
      }
      
      override public function setBtnBack(str0:String) : void
      {
         super.setBtnBack(str0);
         this.skillTag.filters = str0 == "over" ? [] : [this.skillFilter];
      }
   }
}

