package UI.forging.weaponUpgrade
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.must.NormalMustBox;
   import UI.base.tip.TextGatherAnalyze;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.weapon.WeaponData;
   import dataAll.equip.weapon.WeaponDataCreator;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class WeaponUpgradeBoard extends NormalUI
   {
      private var btn:NormalBtn = new NormalBtn();
      
      private var beforeGrip:ItemsGrid = new ItemsGrid();
      
      private var nextGrip:ItemsGrid = new ItemsGrid();
      
      private var infoBtnSp:MovieClip;
      
      private var infoBtn:NormalBtn = new NormalBtn();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      public var nowData:WeaponData = null;
      
      private var mustBoxSp:Sprite;
      
      private var btnSp:MovieClip;
      
      private var beforeTag:Sprite;
      
      private var nextTag:Sprite;
      
      private var beforeTxt:TextField;
      
      private var nextTxt:TextField;
      
      public function WeaponUpgradeBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["mustBoxSp","btnSp","infoBtnSp","beforeTag","nextTag","beforeTxt","nextTxt"];
         super.setImg(img0);
         addChild(this.btn);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.beforeTag.addChild(this.beforeGrip);
         this.beforeGrip.setImgToEquipGrip();
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.beforeGrip);
         this.nextTag.addChild(this.nextGrip);
         this.nextGrip.setImgToEquipGrip();
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.nextGrip);
         addChild(this.infoBtn);
         this.infoBtn.setImg(this.infoBtnSp);
         this.infoBtn.setName("进阶所需");
         ItemsGripTipCtrl.addNormalBtnTip(this.infoBtn);
         addChild(this.mustBox);
         this.mustBox.setImg(this.mustBoxSp);
         this.btn.setImg(this.btnSp);
         this.btn.setName("进阶");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,"weapon");
         this.showOneWeaponDataAndPan(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      public function equipGripClick(da0:WeaponData) : void
      {
         if(visible)
         {
            this.showOneWeaponDataAndPan(da0);
         }
      }
      
      private function showOneWeaponDataAndPan(da0:WeaponData) : void
      {
         var dg0:EquipDataGroup = null;
         this.showNone();
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择要进阶的副手。");
         if(Boolean(da0))
         {
            dg0 = Gaming.PG.da.findEquipData(da0);
            if(dg0 is EquipDataGroup)
            {
               this.showOneWeaponData(da0);
            }
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      private function showOneWeaponData(da0:WeaponData) : void
      {
         var must_d0:MustDefine = null;
         var bb0:Boolean = false;
         this.nowData = da0;
         var afterData:WeaponData = da0.getUpradeData();
         this.inOneData(this.beforeGrip,this.beforeTxt,da0);
         this.inOneData(this.nextGrip,this.nextTxt,afterData);
         if(Boolean(afterData))
         {
            must_d0 = da0.getUpradeMust();
            bb0 = this.mustBox.inData(must_d0);
            this.btn.actived = bb0;
         }
         else
         {
            this.mustBox.setShowState(false);
            this.btn.actived = false;
         }
         this.infoBtn.tipString = WeaponDataCreator.getMustTip(da0.weaponDefine);
      }
      
      private function inOneData(grip0:ItemsGrid, txt0:TextField, da0:WeaponData) : void
      {
         var str0:String = "";
         if(!da0)
         {
            grip0.visible = false;
            str0 = "<green 已升至最高等级/>";
         }
         else
         {
            grip0.visible = true;
            grip0.inData_equip(da0);
            grip0.setNumText("");
            str0 = da0.getUIUpgradeGatherStr();
         }
         str0 = TextGatherAnalyze.swapText(str0);
         txt0.htmlText = str0;
      }
      
      private function showNone() : void
      {
         this.infoBtn.tipString = "";
         this.beforeGrip.clearData();
         this.nextGrip.clearData();
         this.beforeTxt.text = "";
         this.nextTxt.text = "";
         this.mustBox.setShowState(false);
         this.btn.actived = false;
      }
      
      public function btnClick(e:MouseEvent) : void
      {
         var mustBagNum0:int = 0;
         var must_d0:MustDefine = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0.actived)
         {
            if(btn0 == this.btn)
            {
               if(Boolean(this.nowData))
               {
                  mustBagNum0 = 0;
                  if(this.nowData.placeType == "bag")
                  {
                     mustBagNum0 = 1;
                  }
                  if(Gaming.PG.da.equipBag.getSpaceSiteNum() < mustBagNum0)
                  {
                     Gaming.uiGroup.alertBox.showError("当前装备背包至少需要 " + ComMethod.color(mustBagNum0 + "个","#00FF00") + " 空位！");
                  }
                  else
                  {
                     must_d0 = this.nowData.getUpradeMust();
                     PlayerMustCtrl.deductMust(must_d0,this.affter_click);
                  }
               }
            }
         }
      }
      
      private function affter_click() : void
      {
         var upgradeData0:WeaponData = this.nowData.getUpradeData();
         if(this.nowData.placeType == "wear" || this.nowData.getNowNum() == 1)
         {
            this.nowData.changeToOneData(upgradeData0);
         }
         else if(this.nowData.placeType == "bag")
         {
            if(this.nowData.getNowNum() > 1 && this.nowData.getSave().itemsLevel >= 2)
            {
               this.nowData.addNowNum(-1);
            }
            this.nowData = Gaming.PG.da.equipBag.addHaveNumData(upgradeData0) as WeaponData;
         }
         this.show();
         Gaming.uiGroup.alertBox.showSuccess("进阶成功！");
      }
   }
}

