package UI.gameWorld
{
   import UI.NormalUICtrl;
   import UI.base.NormalUI;
   import UI.gameWorld.agent.GWAgent;
   import UI.gameWorld.bodyLifeBar.BodyLifeBarGroup;
   import UI.gameWorld.headBox.MoreHeadBox;
   import UI.gameWorld.headBox.PlayerHeadBox;
   import UI.gameWorld.lottery.GameLotteryBox;
   import UI.gameWorld.pointer.GameWorldPointer;
   import UI.gameWorld.props.GameWorldPropsBox;
   import UI.gameWorld.skill.GameWorldSkillBox;
   import UI.gameWorld.task.GameWorldTaskBox;
   import UI.gameWorld.tip.GameWorldThingsBox;
   import UI.gameWorld.tip.GameWorldTipBox;
   import UI.gameWorld.vehicle.VehicleGrip;
   import dataAll._app.setting.SettingSave;
   import dataAll.level.define.LevelDefine;
   import flash.display.BlendMode;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.skill.SkillData;
   import gameAll.hero.HeroBody;
   import gameAll.level.arena.ArenaCtrl;
   import gameAll.more.DoubleCtrl;
   
   public class GameWorldUI extends NormalUI
   {
      public var headBox:PlayerHeadBox = new PlayerHeadBox();
      
      public var moreHeadBox:MoreHeadBox = new MoreHeadBox();
      
      private var armsBox:GameWorldArmsBox = new GameWorldArmsBox();
      
      private var skillBox:GameWorldSkillBox = new GameWorldSkillBox();
      
      private var propsBox:GameWorldPropsBox = new GameWorldPropsBox();
      
      private var iconBox:GameWorldIconBox = new GameWorldIconBox();
      
      public var mapBox:GameWorldSmallMap = new GameWorldSmallMap();
      
      public var taskBox:GameWorldTaskBox = new GameWorldTaskBox();
      
      public var unionBox:GameWorldTaskBox = new GameWorldTaskBox();
      
      public var bossLifeBox:GameWorldBossBox = new GameWorldBossBox();
      
      public var lotteryBox:GameLotteryBox = new GameLotteryBox();
      
      public var tipBox:GameWorldTipBox = new GameWorldTipBox();
      
      public var frontTipBox:GameWorldTipBox = new GameWorldTipBox();
      
      public var thingsTipBox:GameWorldThingsBox = new GameWorldThingsBox();
      
      public var bodyLifeBox:BodyLifeBarGroup = new BodyLifeBarGroup();
      
      public var overPointer:GameWorldPointer = new GameWorldPointer();
      
      private var sightCover:MovieClip = null;
      
      private var sightHide_t:Number = -1;
      
      private var sightShow_t:Number = -1;
      
      private var effectCover:MovieClip = null;
      
      private var effectHide_t:Number = -1;
      
      private var tipSp:Sprite = null;
      
      private var frontTipSp:Sprite = null;
      
      private var thingsTipBoxSp:Sprite = null;
      
      private var armsSp:Sprite = null;
      
      private var skillSp:Sprite = null;
      
      private var iconSp:Sprite = null;
      
      private var mapSp:Sprite = null;
      
      private var taskSp:Sprite = null;
      
      private var unionSp:Sprite = null;
      
      private var bossLifeSp:Sprite = null;
      
      private var headTag:Sprite;
      
      private var moreHeadTag:Sprite;
      
      private var propsTag:Sprite;
      
      private var rightVehicleGrip:VehicleGrip = new VehicleGrip();
      
      private var leftVehicleGrip:VehicleGrip = new VehicleGrip();
      
      private var rightDeviceGrip:VehicleGrip = new VehicleGrip();
      
      private var leftDeviceGrip:VehicleGrip = new VehicleGrip();
      
      private var rightWeaponGrip:VehicleGrip = new VehicleGrip();
      
      private var leftWeaponGrip:VehicleGrip = new VehicleGrip();
      
      private var rightHeadBox:PlayerHeadBox = new PlayerHeadBox();
      
      private var leftArmsBox:GameWorldArmsBox = new GameWorldArmsBox();
      
      private var rightArmsBox:GameWorldArmsBox = new GameWorldArmsBox();
      
      private var rightIconBox:GameWorldIconBox = new GameWorldIconBox();
      
      private var leftSkillBox:GameWorldSkillBox = new GameWorldSkillBox();
      
      private var rightSkillBox:GameWorldSkillBox = new GameWorldSkillBox();
      
      private var rightVehicleSp:Sprite;
      
      private var leftVehicleSp:Sprite;
      
      private var rightDeviceSp:Sprite;
      
      private var leftDeviceSp:Sprite;
      
      private var rightWeaponSp:Sprite;
      
      private var leftWeaponSp:Sprite;
      
      private var rightHeadTag:Sprite;
      
      private var leftArmsSp:Sprite;
      
      private var rightArmsSp:Sprite;
      
      private var leftSkillSp:Sprite;
      
      private var rightSkillSp:Sprite;
      
      private var rightIconSp:Sprite;
      
      private var leftPropsTag:Sprite;
      
      private var doubleBackSp:Sprite;
      
      private var lotteryBoxSp:Sprite;
      
      private var leftCardArr:Array = [this.leftVehicleGrip,this.leftDeviceGrip,this.leftWeaponGrip];
      
      private var rightCardArr:Array = [this.rightVehicleGrip,this.rightDeviceGrip,this.rightWeaponGrip];
      
      private var singleVehicleGripY:int = 0;
      
      private var doubleArmsY:int = 0;
      
      private var doubleB:Boolean = false;
      
      private var eleArr:Array = [];
      
      public function GameWorldUI()
      {
         super();
         this.mouseEnabled = false;
         elementNameArr = ["lotteryBoxSp","thingsTipBoxSp","unionSp","leftWeaponSp","rightWeaponSp","frontTipSp","rightDeviceSp","leftDeviceSp","rightVehicleSp","leftVehicleSp","doubleBackSp","rightHeadTag","leftArmsSp","rightArmsSp","leftSkillSp","rightSkillSp","rightIconSp","leftPropsTag","tipSp","armsSp","skillSp","iconSp","mapSp","bossLifeSp","headTag","moreHeadTag","taskSp","propsTag"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         img0.mouseEnabled = false;
         if(this.eleArr.length == 0)
         {
            this.eleArr.push(this.skillBox);
            this.eleArr.push(this.propsBox);
            this.eleArr.push(this.armsBox);
            this.eleArr.push(this.iconBox);
            this.eleArr.push(this.rightHeadBox);
            this.eleArr.push(this.leftArmsBox);
            this.eleArr.push(this.rightArmsBox);
            this.eleArr.push(this.rightIconBox);
            this.eleArr.push(this.leftSkillBox);
            this.eleArr.push(this.rightSkillBox);
            this.eleArr.push(this.leftVehicleGrip);
            this.eleArr.push(this.rightVehicleGrip);
            this.eleArr.push(this.leftDeviceGrip);
            this.eleArr.push(this.rightDeviceGrip);
            this.eleArr.push(this.leftWeaponGrip);
            this.eleArr.push(this.rightWeaponGrip);
         }
         addChild(this.skillBox);
         this.skillBox.setSkillIconMaxWidth(36);
         this.skillBox.setImg(this.skillSp);
         addChild(this.propsBox);
         this.propsBox.imgInit();
         addChild(this.armsBox);
         this.armsBox.setImg(this.armsSp);
         addChild(this.iconBox);
         this.iconBox.setImg(this.iconSp);
         addChild(this.mapBox);
         this.mapBox.setImg(this.mapSp);
         addChild(this.taskBox);
         this.taskBox.setImg(this.taskSp);
         addChild(this.unionBox);
         this.unionBox.setImg(this.unionSp);
         this.unionBox.setTitle("军队派遣");
         addChild(this.bossLifeBox);
         this.bossLifeBox.setImg(this.bossLifeSp);
         this.tipBox.setImg(this.tipSp);
         this.tipBox.con = this;
         this.tipBox.hide();
         this.frontTipBox.setImg(this.frontTipSp);
         this.frontTipBox.con = this;
         this.frontTipBox.hide();
         this.thingsTipBox.setImg(this.thingsTipBoxSp);
         this.thingsTipBox.con = this;
         this.thingsTipBox.hide();
         this.lotteryBox.setImg(this.lotteryBoxSp);
         this.lotteryBox.setCon(this);
         this.lotteryBox.hide();
         this.overPointer.setImg();
         this.bodyLifeBox.imgInit();
         this.headBox.setToNormalImg();
         this.headTag.addChild(this.headBox);
         this.headTag.mouseChildren = false;
         this.headTag.mouseEnabled = false;
         this.moreHeadBox.imgInit();
         this.moreHeadTag.addChild(this.moreHeadBox);
         this.moreHeadTag.mouseChildren = false;
         this.moreHeadTag.mouseEnabled = false;
         addChild(this.rightHeadBox);
         this.rightHeadBox.setToRightImg();
         NormalUICtrl.setTag(this.rightHeadBox,this.rightHeadTag);
         this.rightHeadBox.mouseChildren = false;
         this.rightHeadBox.mouseEnabled = false;
         addChild(this.leftArmsBox);
         this.leftArmsBox.setImg(this.leftArmsSp);
         addChild(this.rightArmsBox);
         this.rightArmsBox.setImg(this.rightArmsSp);
         this.doubleArmsY = this.leftArmsBox.y;
         addChild(this.rightIconBox);
         this.rightIconBox.setImg(this.rightIconSp);
         addChild(this.leftSkillBox);
         this.leftSkillBox.setSkillIconMaxWidth(50);
         this.leftSkillBox.setImg(this.leftSkillSp);
         addChild(this.rightSkillBox);
         this.rightSkillBox.setSkillIconMaxWidth(50);
         this.rightSkillBox.setImg(this.rightSkillSp);
         addChild(this.rightVehicleGrip);
         this.rightVehicleGrip.setImg(this.rightVehicleSp);
         addChild(this.leftVehicleGrip);
         this.leftVehicleGrip.setImg(this.leftVehicleSp);
         this.singleVehicleGripY = this.leftVehicleGrip.y;
         addChild(this.rightDeviceGrip);
         this.rightDeviceGrip.setImg(this.rightDeviceSp);
         addChild(this.leftDeviceGrip);
         this.leftDeviceGrip.setImg(this.leftDeviceSp);
         addChild(this.rightWeaponGrip);
         this.rightWeaponGrip.setImg(this.rightWeaponSp);
         addChild(this.leftWeaponGrip);
         this.leftWeaponGrip.setImg(this.leftWeaponSp);
         addChild(this.iconBox);
         addChild(this.rightIconBox);
         this.showModel(false,"");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function startLevel(levelModel0:String) : void
      {
         this.bodyLifeBox.startLevel();
         this.doubleB = DoubleCtrl.levelDoubleB();
         this.showModel(this.doubleB,levelModel0);
         this.mapBox.startLevel();
         this.bagFillPan();
         this.propsBox.startLevel();
         this.lotteryBox.hide();
         if(this.iconBox.visible)
         {
            this.iconBox.startLevel();
         }
         else
         {
            this.rightIconBox.startLevel();
         }
         this.fleshSetting();
         var levelD0:LevelDefine = Gaming.LG.getLevelDefineNull();
         if(Boolean(levelD0))
         {
            if(levelD0.info.sightCover > 0)
            {
               this.sightCoverOpen(levelD0.info.sightCover);
            }
         }
      }
      
      public function fleshSetting() : void
      {
         var settingS0:SettingSave = null;
         if(Boolean(Gaming.PG.da))
         {
            settingS0 = Gaming.PG.save.setting;
            this.bossLifeBox.lifeMustNumberB = settingS0.bossLifeB;
         }
      }
      
      public function overLevel() : void
      {
         this.frontTipBox.hide();
         this.thingsTipBox.hide();
      }
      
      public function setHero(b0:HeroBody) : void
      {
         this.fleshMoreHead();
      }
      
      public function setP2(b0:IO_NormalBody) : void
      {
      }
      
      public function fleshMoreHead() : void
      {
         this.moreHeadBox.inBodyArr(DoubleCtrl.getUIHeadBodyArr(false));
      }
      
      public function overGamingClear() : void
      {
         this.frontTipBox.hide();
         this.thingsTipBox.hide();
         this.moreHeadBox.overGamingClear();
         this.mapBox.clearAll();
         this.bossLifeBox.clearAll();
         this.bodyLifeBox.clearAll();
         this.skillBox.clearAll();
         this.leftSkillBox.clearAll();
         this.rightSkillBox.clearAll();
         this.overPointer.overGamingClear();
         this.clearSightCover();
      }
      
      private function showModel(doubleB0:Boolean, levelModel0:String) : void
      {
         var esp0:Sprite = null;
         this.tipBox.hide();
         this.doubleBackSp.visible = false;
         this.rightHeadBox.visible = false;
         this.iconBox.visible = false;
         this.rightIconBox.visible = false;
         this.skillBox.visible = false;
         this.leftSkillBox.visible = false;
         this.rightSkillBox.visible = false;
         this.armsBox.visible = false;
         this.leftArmsBox.visible = false;
         this.rightArmsBox.visible = false;
         this.mapBox.visible = true;
         this.mapBox.y = this.rightHeadTag.y;
         this.headBox.visible = true;
         this.leftVehicleGrip.visible = true;
         this.rightVehicleGrip.visible = false;
         this.leftDeviceGrip.visible = true;
         this.rightDeviceGrip.visible = false;
         this.leftWeaponGrip.visible = true;
         this.rightWeaponGrip.visible = false;
         var canSkillB0:Boolean = Gaming.LG.canUsePlayerSkillB();
         var canPropsB0:Boolean = Gaming.LG.canPropsB();
         this.propsBox.visible = canPropsB0;
         if(!doubleB0)
         {
            NormalUICtrl.setTag(this.propsBox,this.propsTag);
            this.iconBox.visible = true;
            this.skillBox.visible = canSkillB0;
            this.armsBox.visible = true;
            this.leftVehicleGrip.y = this.singleVehicleGripY;
            this.leftDeviceGrip.y = this.singleVehicleGripY;
            this.leftWeaponGrip.y = this.singleVehicleGripY;
         }
         else
         {
            NormalUICtrl.setTag(this.propsBox,this.leftPropsTag);
            this.doubleBackSp.visible = true;
            this.rightHeadBox.visible = true;
            this.rightIconBox.visible = true;
            this.leftSkillBox.visible = canSkillB0;
            this.rightSkillBox.visible = canSkillB0;
            this.leftArmsBox.visible = true;
            this.rightArmsBox.visible = true;
            this.mapBox.y = 64;
            this.leftVehicleGrip.y = this.rightVehicleGrip.y;
            this.leftDeviceGrip.y = this.rightDeviceGrip.y;
            this.leftWeaponGrip.y = this.rightDeviceGrip.y;
            this.rightVehicleGrip.visible = true;
            this.rightDeviceGrip.visible = true;
            this.rightWeaponGrip.visible = true;
            if(levelModel0 == "arena" && !ArenaCtrl.dat.moreB)
            {
               this.leftSkillBox.visible = false;
               this.leftArmsBox.visible = false;
               this.headBox.visible = false;
            }
         }
         for each(esp0 in this.eleArr)
         {
            if(esp0.visible)
            {
               if(esp0.parent == null)
               {
                  addChild(esp0);
               }
            }
            else if(Boolean(esp0.parent))
            {
               esp0.parent.removeChild(esp0);
            }
         }
      }
      
      public function toPlotMode() : void
      {
         this.headBox.visible = false;
         this.rightHeadBox.visible = false;
         this.mapBox.visible = false;
         this.armsBox.visible = false;
         this.leftArmsBox.visible = false;
         this.rightArmsBox.visible = false;
         this.skillBox.visible = false;
         this.rightSkillBox.visible = false;
      }
      
      public function fleshEach() : void
      {
         var p1:IO_NormalBody = DoubleCtrl.P_1;
         var p2:IO_NormalBody = DoubleCtrl.P_2;
         if(this.doubleB)
         {
            this.fleshBoxByBody(p1,this.rightSkillBox,this.rightArmsBox,this.rightHeadBox,this.rightVehicleGrip,this.rightDeviceGrip,this.rightWeaponGrip);
            this.fleshBoxByBody(p2,this.leftSkillBox,this.leftArmsBox,this.headBox,this.leftVehicleGrip,this.leftDeviceGrip,this.leftWeaponGrip);
            this.fleshCardPosition(this.leftCardArr,"left");
            this.fleshCardPosition(this.rightCardArr,"right");
         }
         else
         {
            this.fleshCardPosition(this.leftCardArr,"left");
            this.fleshBoxByBody(p1,this.skillBox,this.armsBox,this.headBox,this.leftVehicleGrip,this.leftDeviceGrip,this.leftWeaponGrip);
         }
      }
      
      private function fleshBoxByBody(b0:IO_NormalBody, skillBox0:GameWorldSkillBox, armsBox0:GameWorldArmsBox, headBox0:PlayerHeadBox, vehicleGrip0:VehicleGrip, deviceGrip0:VehicleGrip, weaponGrip0:VehicleGrip) : void
      {
         var a0:GWAgent = null;
         var changeB0:Boolean = false;
         if(b0 is IO_NormalBody)
         {
            a0 = b0.getData().getGWAgent();
            headBox0.inData(a0.headBody);
            changeB0 = skillBox0.inSkillDataGroup(a0.skill);
            if(changeB0)
            {
               if(skillBox0 == this.rightSkillBox)
               {
                  skillBox0.x = int(this.propsTag.x + 208 - skillBox0.width);
               }
            }
            armsBox0.inAgent(a0.arms);
            vehicleGrip0.inAgent(a0.vehicle);
            deviceGrip0.inAgent(a0.device);
            weaponGrip0.inAgent(a0.weapon);
         }
      }
      
      private function fleshCardPosition(arr0:Array, type0:String) : void
      {
         var n:* = undefined;
         var grip0:VehicleGrip = null;
         var armsBox0:GameWorldArmsBox = null;
         var x0:int = int(this[type0 + "VehicleGrip"].x);
         var w0:int = int(this[type0 + "VehicleGrip"].width);
         var index0:int = 0;
         var haveGripB0:Boolean = false;
         for(n in arr0)
         {
            grip0 = arr0[n];
            if(grip0.visible)
            {
               haveGripB0 = true;
               if(type0 == "left")
               {
                  grip0.x = x0 + index0 * (w0 + 1);
               }
               else
               {
                  grip0.x = x0 - index0 * (w0 + 1);
               }
               index0++;
            }
         }
         if(this.doubleB)
         {
            armsBox0 = this[type0 + "ArmsBox"];
            if(haveGripB0)
            {
               armsBox0.y = this.doubleArmsY;
            }
            else
            {
               armsBox0.y = this.leftPropsTag.y - 6;
            }
         }
      }
      
      public function showBossLifeBar(b0:IO_NormalBody) : void
      {
         this.bossLifeBox.bossLifeTarget = b0;
      }
      
      public function showBossLifeBarPanTrue(b0:IO_NormalBody) : void
      {
         if(b0.getDieCtrl().isTrueBody())
         {
            this.bossLifeBox.bossLifeTarget = b0;
         }
      }
      
      public function clearBossLifeBarIfIsOne(b0:IO_NormalBody) : void
      {
         this.bossLifeBox.clearIfIsOne(b0);
      }
      
      public function bagFillPan() : void
      {
         var iconBox0:GameWorldIconBox = this.doubleB ? this.rightIconBox : this.iconBox;
         iconBox0.wearBtn.setSmallIcon(Gaming.PG.da.getBagFillB() ? "fill" : "");
      }
      
      public function propsNumChangeEvent() : void
      {
         if(visible)
         {
            this.propsBox.fleshData();
         }
      }
      
      public function fleshSkillGripByDa(skillDa0:SkillData) : void
      {
         if(this.skillBox.visible)
         {
            this.skillBox.fleshBySkillDa(skillDa0);
         }
         else
         {
            if(Boolean(this.leftSkillBox))
            {
               this.leftSkillBox.fleshBySkillDa(skillDa0);
            }
            if(Boolean(this.rightSkillBox))
            {
               this.rightSkillBox.fleshBySkillDa(skillDa0);
            }
         }
      }
      
      public function showEffectCover(label0:String, t0:Number, alpha0:Number = 1, blendMode0:String = "normal") : void
      {
         if(this.effectCover == null)
         {
            this.effectCover = Gaming.swfLoaderManager.getResource("GameWorldUI","effectCover");
            this.effectCover.stop();
            this.effectCover.mouseChildren = false;
            this.effectCover.mouseEnabled = false;
            Gaming.gameSprite.L_mouse.addChildAt(this.effectCover,0);
         }
         this.effectCover.gotoAndStop(label0);
         this.effectCover.alpha = alpha0;
         this.effectCover.blendMode = blendMode0;
         this.effectHide_t = t0;
      }
      
      private function hideEffectCover() : void
      {
         if(Boolean(this.effectCover))
         {
            this.effectCover.gotoAndStop("no");
         }
      }
      
      public function sightCoverOpen(size0:int, blendMode0:String = "normal") : void
      {
         if(size0 > 0)
         {
            if(!this.sightCover)
            {
               this.sightCover = Gaming.swfLoaderManager.getResource("GameWorldUI","sightCover");
            }
            if(!this.sightCover.parent)
            {
               Gaming.gameSprite.L_text.addChild(this.sightCover);
            }
            this.sightCover.gotoAndStop(size0);
            this.sightCover.blendMode = blendMode0;
         }
         else if(Boolean(this.sightCover))
         {
            if(Boolean(this.sightCover.parent))
            {
               this.sightCover.parent.removeChild(this.sightCover);
            }
         }
      }
      
      public function showInverSightCover(t0:Number) : void
      {
         this.sightCoverOpen(1,BlendMode.INVERT);
         this.setSightCoverP(-300,-300);
         this.showSightTime(t0);
      }
      
      public function showSightTime(t0:Number) : void
      {
         this.sightCoverVisible(true);
         this.sightShow_t = t0;
         this.sightHide_t = -1;
      }
      
      public function hideSightTime(t0:Number) : void
      {
         this.sightCoverVisible(false);
         this.sightHide_t = t0;
         this.sightShow_t = -1;
      }
      
      public function sightCoverVisible(bb0:Boolean) : void
      {
         if(Boolean(this.sightCover))
         {
            this.sightCover.visible = bb0;
         }
      }
      
      public function setSightCoverP(x0:int, y0:int) : void
      {
         if(Boolean(this.sightCover))
         {
            this.sightCover.x = x0;
            this.sightCover.y = y0;
         }
      }
      
      private function clearSightCover() : void
      {
         this.sightCoverOpen(0);
         this.sightCover = null;
      }
      
      public function useThingsEvent() : void
      {
         if(visible)
         {
            this.FTimer();
            this.FTimerSecond();
            this.propsNumChangeEvent();
         }
      }
      
      public function FTimer() : void
      {
         this.fleshEach();
         this.bossLifeBox.fleshEach();
         this.bodyLifeBox.fleshEach();
         this.overPointer.fleshEach();
         this.moreHeadBox.fleshEach();
         this.tipBox.FTimer();
         this.frontTipBox.FTimer();
         this.mapBox.FTimer();
         if(Gaming.LG.isOnlyIng())
         {
            if(this.sightHide_t > -1)
            {
               this.sightHide_t -= 0.03333333333333333;
               if(this.sightHide_t <= 0)
               {
                  this.sightHide_t = -1;
                  this.sightCoverVisible(true);
               }
            }
            if(this.sightShow_t > -1)
            {
               this.sightShow_t -= 0.03333333333333333;
               if(this.sightShow_t <= 0)
               {
                  this.sightShow_t = -1;
                  this.sightCoverVisible(false);
               }
            }
            if(this.effectHide_t > -1)
            {
               this.effectHide_t -= 0.03333333333333333;
               if(this.effectHide_t <= 0)
               {
                  this.effectHide_t = -1;
                  this.hideEffectCover();
               }
            }
         }
      }
      
      public function FTimerSecond() : void
      {
         var p1:IO_NormalBody = null;
         var p2:IO_NormalBody = null;
         if(Gaming.LG.isGaming())
         {
            p1 = DoubleCtrl.P_1;
            p2 = DoubleCtrl.P_2;
            if(this.doubleB)
            {
               this.rightHeadBox.inDataSecond(p1.getData().getGWAgent().headBody);
               this.headBox.inDataSecond(p2.getData().getGWAgent().headBody);
            }
            else
            {
               this.headBox.inDataSecond(p1.getData().getGWAgent().headBody);
            }
         }
      }
   }
}

