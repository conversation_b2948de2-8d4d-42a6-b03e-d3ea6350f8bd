package UI.task
{
   import UI.UIOrder;
   import UI.UIShow;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AppNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.gift.GiftBox;
   import UI.base.label.LabelBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.task.TaskData;
   import dataAll._app.task.TaskDataGroup;
   import dataAll._app.task.TaskState;
   import dataAll._app.task.define.TaskDefine;
   import dataAll._app.task.define.TaskFatherDefine;
   import dataAll._app.task.define.TaskType;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._player.base.PlayerBaseData;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.level.define.LevelDefine;
   import dataAll.level.define.unit.OneUnitOrderDefine;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.skill.define.SkillDescrip;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.text.TextField;
   import gameAll.level._diy.task.CustomTaskLevelDiy;
   
   public class TaskUI extends AppNormalUI
   {
      private var openBox:TaskOpenBox = new TaskOpenBox();
      
      public var labelBox:LabelBox = new LabelBox();
      
      private var labelArr:Array = TaskType.labelArr.concat([]);
      
      private var nowList:ItemsGripBox = null;
      
      private var barObj:Object = {};
      
      private var numTxt:TextField;
      
      private var titleTxt:TextField;
      
      private var infoTxt:TextField;
      
      public var targetTxt:TextField;
      
      private var bossTxt:TextField;
      
      private var giftBox:GiftBox = new GiftBox();
      
      public var getBtn:NormalBtn = new NormalBtn();
      
      private var giftBtn:NormalBtn = new NormalBtn();
      
      private var giveupBtn:NormalBtn = new NormalBtn();
      
      private var directGiftBtn:NormalBtn = new NormalBtn();
      
      public var mapBtn:NormalBtn = new NormalBtn();
      
      private var onlyCompleteBtn:NormalBtn = new NormalBtn();
      
      private var diffBtn:NormalBtn = new NormalBtn();
      
      private var bossDownLevelBtn:NormalBtn = new NormalBtn();
      
      private var buyBtn:NormalBtn = new NormalBtn();
      
      private var coverBuyBtn:NormalBtn = new NormalBtn();
      
      private var swapMapBtn:NormalBtn = new NormalBtn();
      
      private var otherBtn:NormalBtn = new NormalBtn();
      
      private var barTag:Sprite;
      
      private var labelTag:Sprite;
      
      private var giftTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var closeBtn:SimpleButton;
      
      private var redBtnSp:MovieClip;
      
      private var blueBtnSp:MovieClip;
      
      private var yellowBtnSp:MovieClip;
      
      private var mapBtnSp:MovieClip;
      
      private var directGiftBtnSp:MovieClip;
      
      private var onlyCompleteBtnSp:MovieClip;
      
      private var diffBtnSp:MovieClip;
      
      private var bossDownLevelBtnSp:MovieClip;
      
      private var buyBtnSp:MovieClip;
      
      private var swapMapBtnSp:MovieClip;
      
      private var otherBtnSp:MovieClip;
      
      private var coverBuyBtnSp:MovieClip;
      
      private var coverSp:Sprite;
      
      private var coverTxt:TextField;
      
      private var openSp:Sprite;
      
      public var nowLabel:String = "";
      
      private var nowDataObj:Object = {};
      
      private var bossTip:String = "";
      
      public function TaskUI()
      {
         super();
         UICn = "任务";
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var labelArr0:Array = null;
         var n:* = undefined;
         var barList:ItemsGripBox = null;
         elementNameArr = ["openSp","bossDownLevelBtnSp","buyBtnSp","otherBtnSp","directGiftBtnSp","swapMapBtnSp","numTxt","coverSp","labelTag","barTag","giftTag","pageTag","closeBtn","infoTxt","targetTxt","diffBtnSp","mapBtnSp","redBtnSp","blueBtnSp","yellowBtnSp","titleTxt","onlyCompleteBtnSp","bossTxt"];
         super.setImg(img0);
         FontDeal.dealOne(this.numTxt);
         FontDeal.dealOne(this.titleTxt);
         FontDeal.dealOne(this.targetTxt);
         FontDeal.dealOne(this.infoTxt);
         FontDeal.dealOne(this.bossTxt);
         labelArr0 = this.labelArr;
         this.nowLabel = this.labelArr[0];
         this.labelBox.arg.init(12,1,-10,0);
         this.labelBox.evt.setWantEvent(true,false,false,true,true);
         this.labelBox.inData("bagLabelBtn",labelArr0,TaskType.labelCnArr);
         this.labelBox.setAllPro("activedAndEnabled",false);
         this.labelBox.setChoose_byIndex(0);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.labelBox.addEventListener(ClickEvent.ON_OVER,this.labelOver);
         this.labelBox.addEventListener(ClickEvent.ON_OUT,this.labelOut);
         this.labelBox.x = this.labelTag.x;
         this.labelBox.y = this.labelTag.y;
         addChild(this.labelBox);
         for(n in labelArr0)
         {
            barList = new ItemsGripBox();
            barList.imgType = "TaskUI/taskBar";
            barList.arg.init(1,11,0,1);
            barList.evt.setWantEvent(true,false,false,true,true);
            barList.evt.clickMustAcitved = false;
            barList.x = this.barTag.x;
            barList.y = this.barTag.y;
            barList.addEventListener(ClickEvent.ON_CLICK,this.barClick);
            barList.addEventListener(ClickEvent.ON_OVER,this.barOver);
            barList.addEventListener(ClickEvent.ON_OUT,this.barOut);
            addChild(barList);
            this.barObj[labelArr0[n]] = barList;
            barList.visible = false;
            barList.pageBox.setToSmall();
            barList.setPagePos(this.pageTag);
         }
         addChild(this.getBtn);
         this.getBtn.setImg(this.blueBtnSp);
         this.getBtn.setName("接受任务");
         this.getBtn.addEventListener(MouseEvent.CLICK,this.btnClick);
         addChild(this.giftBtn);
         this.giftBtn.setImg(this.yellowBtnSp);
         this.giftBtn.setName("领取奖励");
         this.giftBtn.addEventListener(MouseEvent.CLICK,this.btnClick);
         addChild(this.giveupBtn);
         this.giveupBtn.setImg(this.redBtnSp);
         this.giveupBtn.setName("放弃任务");
         this.giveupBtn.addEventListener(MouseEvent.CLICK,this.btnClick);
         addChild(this.mapBtn);
         this.mapBtn.setImg(this.mapBtnSp);
         this.mapBtn.setName("前往地图");
         this.mapBtn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.directGiftBtn.setImg(this.directGiftBtnSp);
         this.directGiftBtn.setName("直接领奖");
         this.directGiftBtn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.directGiftBtn.activedAndEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.directGiftBtn);
         addChild(this.onlyCompleteBtn);
         this.onlyCompleteBtn.setImg(this.onlyCompleteBtnSp);
         this.onlyCompleteBtn.addEventListener(MouseEvent.CLICK,this.btnClick);
         addChild(this.diffBtn);
         this.diffBtn.setImg(this.diffBtnSp);
         this.diffBtn.addEventListener(MouseEvent.CLICK,this.diffClick);
         addChild(this.swapMapBtn);
         this.swapMapBtn.setImg(this.swapMapBtnSp);
         this.swapMapBtn.activedAndEnabled = false;
         this.swapMapBtn.addEventListener(MouseEvent.CLICK,this.swapMapBtnClick);
         ItemsGripTipCtrl.addNormalBtnTip(this.swapMapBtn);
         addChild(this.otherBtn);
         this.otherBtn.setImg(this.otherBtnSp);
         this.otherBtn.activedAndEnabled = false;
         this.otherBtn.addEventListener(MouseEvent.CLICK,this.otherBtnClick);
         ItemsGripTipCtrl.addNormalBtnTip(this.otherBtn);
         this.bossDownLevelBtnSp.parent.removeChild(this.bossDownLevelBtnSp);
         this.bossTxt.addEventListener(MouseEvent.MOUSE_OVER,this.bossTxtOver);
         this.bossTxt.addEventListener(MouseEvent.MOUSE_OUT,Gaming.uiGroup.tipBox.hide);
         addChild(this.bossTxt);
         addChild(this.buyBtn);
         this.buyBtn.setImg(this.buyBtnSp);
         this.buyBtn.addEventListener(MouseEvent.CLICK,this.buyClick);
         this.buyBtn.addEventListener(MouseEvent.MOUSE_OVER,this.buyOver);
         this.buyBtn.addEventListener(MouseEvent.MOUSE_OUT,this.buyOut);
         this.coverBuyBtnSp = this.coverSp["coverBuyBtnSp"];
         this.coverSp.addChild(this.coverBuyBtn);
         this.coverBuyBtn.setImg(this.coverBuyBtnSp);
         this.coverBuyBtn.addEventListener(MouseEvent.CLICK,this.coverBuyClick);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         this.giftTag.addChild(this.giftBox);
         this.giftBox.setToNormalImg();
         addChild(this.coverSp);
         this.coverSp.visible = true;
         this.coverTxt = this.coverSp["txt"];
         addChild(this.openBox);
         this.openBox.setImg(this.openSp);
         this.openBox.hide();
         this.openBox.taskUI = this;
         addChild(this.coverSp);
         addChild(this.directGiftBtn);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function get taskG() : TaskDataGroup
      {
         return Gaming.PG.da.task;
      }
      
      public function inSaveInit() : void
      {
         this.nowLabel = this.labelArr[0];
         this.nowDataObj = {};
      }
      
      private function fleshLabelNew() : void
      {
         var type0:* = null;
         var btn0:NormalBtn = null;
         var num0:int = 0;
         for each(type0 in TaskType.showNewArr)
         {
            btn0 = this.labelBox.getBtnByLabel(type0);
            num0 = this.taskG.getTaskNoOverNum(type0);
            btn0.setSmallIcon(num0 > 0 ? "new" : "");
         }
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
         if(e.label == TaskType.KING)
         {
            (e.child as NormalBtn).setSmallIcon("");
         }
      }
      
      private function labelOver(e:ClickEvent) : void
      {
         var f0:TaskFatherDefine = Gaming.defineGroup.task.getFatherDefine(e.label);
         var tipText0:String = f0.getTipText();
         if(tipText0 != "")
         {
            Gaming.uiGroup.tipBox.followMouseB = true;
            Gaming.uiGroup.tipBox.textTip.setText(tipText0);
            Gaming.uiGroup.tipBox.textTip.show();
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      private function labelOut(e:ClickEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      public function showBox(label0:String) : void
      {
         this.nowLabel = label0;
         this.labelBox.setChoose(label0);
         if(Boolean(this.nowList))
         {
            this.nowList.visible = false;
         }
         this.nowList = this.barObj[label0];
         this.nowList.visible = true;
         this.openBox.hide();
         this.showTaskListByType(label0);
         this.chooseBarByTaskData(this.nowData);
      }
      
      private function get nowData() : TaskData
      {
         return this.nowDataObj[this.nowLabel];
      }
      
      private function set nowData(da0:TaskData) : void
      {
         this.nowDataObj[this.nowLabel] = da0;
      }
      
      public function getNowData() : TaskData
      {
         return this.nowData;
      }
      
      public function gotoTaskName(name0:String) : void
      {
         var grip0:ItemsGrid = null;
         var d0:TaskDefine = Gaming.defineGroup.task.getOneDefine(name0);
         if(Boolean(d0))
         {
            this.nowLabel = d0.father;
            UIShow.showByLabel("task");
            grip0 = this.nowList.getBtnByLabel(name0) as ItemsGrid;
            if(Boolean(grip0))
            {
               this.chooseBar(grip0);
            }
         }
      }
      
      override public function show() : void
      {
         super.show();
         if(!Gaming.uiGroup.mainUI.getBtn("task").actived)
         {
            hide();
         }
         this.showBox(this.nowLabel);
         var taskData:TaskDataGroup = this.taskG;
         this.onlyCompleteBtn.isChosen = taskData.saveGroup.onlyCompleteB;
         this.fleshLabelNew();
      }
      
      private function showTaskListByType(type0:String) : void
      {
         this.taskG.unlockByUnlockLevel(type0);
         var arr0:Array = this.taskG.getUITaskArr(type0);
         if(arr0 is Array)
         {
            this.nowList.inData_byArr(arr0,"inData_task");
         }
         this.fleshNowTypeTodayNumText();
      }
      
      private function barClick(e:ClickEvent) : void
      {
         this.chooseBar(e.child as ItemsGrid);
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function barOver(e:ClickEvent) : void
      {
         var bar0:ItemsGrid = e.child as ItemsGrid;
         var da0:TaskData = bar0.itemsData as TaskData;
         var tipText0:String = da0.getBtnOverTip(bar0);
         UIOrder.showTip(tipText0);
      }
      
      private function barOut(e:ClickEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      public function fleshNowChooseTaskListBar() : void
      {
         var bar0:ItemsGrid = this.nowList.getChooseBtn() as ItemsGrid;
         if(Boolean(bar0))
         {
            bar0.inData_task(bar0.itemsData);
         }
      }
      
      private function fleshNowTypeTodayNumText() : void
      {
         var str0:String = this.taskG.getTodayNumText(this.nowLabel);
         this.numTxt.htmlText = str0;
         this.buyBtn.visible = this.taskG.getTodayAddBtnVisible(this.nowLabel);
         var unlockNum0:int = this.taskG.getUnlockNum(this.nowLabel);
         this.buyBtn.actived = unlockNum0 > 0;
      }
      
      private function chooseBarByTaskData(da0:TaskData) : void
      {
         var btn0:ItemsGrid = this.nowList.findGripByData(da0) as ItemsGrid;
         if(Boolean(btn0))
         {
            this.chooseBar(btn0);
         }
         else
         {
            this.chooseBar(this.nowList.gripArr[0]);
         }
      }
      
      private function chooseBar(bar0:ItemsGrid) : void
      {
         var da0:TaskData = null;
         var worldMap_d0:WorldMapDefine = null;
         var unlockB0:Boolean = false;
         var gunlockLv0:int = 0;
         var breakB0:Boolean = !this.fleshCover(bar0);
         if(breakB0)
         {
            this.fleshDirectGiftBtn(true);
            return;
         }
         this.nowList.setChoose_byIndex(bar0.index);
         this.nowList.gotoChoosePage();
         da0 = bar0.itemsData as TaskData;
         var d0:TaskDefine = da0.def;
         var worldMapId0:String = da0.getWorldMapId();
         this.nowData = da0;
         if(d0.completeLimitNum == -1 && d0.father != TaskType.WEEK)
         {
            this.titleTxt.htmlText = d0.cnName + ComMethod.color("（可重复领取）","#FFFFFF");
         }
         else
         {
            this.titleTxt.text = d0.cnName;
         }
         var descrip0:String = TaskDescrp.getText(da0);
         this.infoTxt.htmlText = FontDeal.getDealLeadingStr(this.infoTxt,"  " + descrip0);
         var m_str0:String = "";
         if(worldMapId0 != "")
         {
            m_str0 += "地点：";
            worldMap_d0 = da0.getWorldMapDefine();
            unlockB0 = !d0.mustWorldMapUnlockB() || Boolean(Gaming.PG.da.worldMap.saveGroup.getSave(worldMapId0));
            if(unlockB0)
            {
               if(da0.isIng())
               {
                  m_str0 += ComMethod.link(worldMap_d0.cnName,"worldMap");
               }
               else
               {
                  m_str0 += ComMethod.color(worldMap_d0.cnName,"#00FFFF");
               }
            }
            else
            {
               m_str0 += ComMethod.color(worldMap_d0.cnName,"#00FFFF") + ComMethod.color("（未解锁）","#FF0000");
            }
            m_str0 += "\n";
         }
         this.fleshBoss(d0);
         m_str0 += "目标：" + TaskCondtionText.getText(da0);
         this.targetTxt.htmlText = FontDeal.getDealLeadingStr(this.targetTxt,m_str0);
         this.giftBox.inGiftAddDefineGroup(da0.getGift());
         this.showBtnState(da0.state);
         var todayNum0:int = this.taskG.getTodayNum(this.nowData.def.father);
         if(todayNum0 == -1 || todayNum0 > 0)
         {
            gunlockLv0 = da0.getGrowthUnlockLv();
            if(Gaming.PG.da.level >= gunlockLv0)
            {
               this.getBtn.actived = true;
               if(da0.getMaxDiff() > 1)
               {
                  this.getBtn.setName(ComMethod.color("接受" + (da0.getDiff() + 1) + "星难度任务","#FFFFFF",12));
               }
               else
               {
                  this.getBtn.setName("接受任务");
               }
            }
            else
            {
               this.getBtn.actived = false;
               this.getBtn.setName(gunlockLv0 + "级以后开放");
            }
         }
         else
         {
            this.getBtn.actived = false;
            this.getBtn.setName("今日次数用完");
         }
         this.fleshDirectGiftBtn();
         this.fleshRightBtn();
         this.openBox.fleshData();
      }
      
      private function fleshBoss(d0:TaskDefine) : void
      {
         var bossCnStr0:String = null;
         this.bossTxt.visible = false;
         this.bossTxt.htmlText = "";
         this.bossTip = "";
         var levelId0:String = d0.levelId;
         var ld0:LevelDefine = Gaming.defineGroup.level.getDefineUrl(levelId0);
         if(Boolean(ld0))
         {
            if(d0.isMainB())
            {
               bossCnStr0 = ld0.unitG.getBossCnStr();
               if(bossCnStr0 != "")
               {
                  this.bossTxt.visible = true;
                  this.bossTxt.htmlText = "首领：" + ComMethod.color(bossCnStr0,"#FFFF00");
                  this.bossTip = ld0.unitG.getBossSkillGather();
               }
            }
            else if(d0.father == TaskType.CUSTOM)
            {
               this.fleshCustomBoss(ld0);
            }
         }
      }
      
      private function fleshCustomBoss(ld0:LevelDefine) : void
      {
         var d0:NormalBodyDefine = null;
         var skillArr0:Array = null;
         var diySkillArr0:Array = null;
         var one0:OneUnitOrderDefine = ld0.unitG.getUnitOne("weBoss");
         if(Boolean(one0))
         {
            d0 = one0.getBodyDefine();
            skillArr0 = d0.getEditSkillArr();
            diySkillArr0 = CustomTaskLevelDiy.getBossSkillArr(d0.cnName);
            if(Boolean(diySkillArr0))
            {
               skillArr0 = skillArr0.concat(diySkillArr0);
            }
            this.bossTxt.visible = true;
            this.bossTxt.htmlText = "操纵首领：" + ComMethod.color(d0.cnName,"#FFFF00");
            this.bossTip = SkillDescrip.getSkillArrGather(skillArr0,"#FFFF00");
         }
         else
         {
            this.bossTxt.visible = false;
         }
      }
      
      private function bossTxtOver(e:MouseEvent) : void
      {
         UIOrder.showTip(this.bossTip);
      }
      
      private function fleshCover(bar0:ItemsGrid) : Boolean
      {
         var nowNum0:int = 0;
         this.coverSp.visible = true;
         this.coverBuyBtn.visible = false;
         this.coverBuyBtn.setName("购买任务次数");
         this.coverTxt.htmlText = "请在左边列表选择任务";
         if(!bar0)
         {
            this.nowList.setChoose_byIndex(-1);
            return false;
         }
         this.coverBuyBtn.visible = this.buyBtn.visible && this.buyBtn.actived;
         this.nowData = bar0.itemsData as TaskData;
         nowNum0 = this.taskG.getTodayNum(this.nowLabel);
         if(!bar0.actived)
         {
            this.coverTxt.htmlText = "当前任务未解锁。" + this.nowData.getBtnOverTip(bar0);
         }
         else if(this.nowData.state == TaskState.over)
         {
            this.coverTxt.htmlText = "当前任务已完成。" + this.nowData.getBtnOverTip(bar0);
         }
         else if(nowNum0 == 0)
         {
            this.coverTxt.htmlText = "今日该类型的任务次数已经用完。";
         }
         else
         {
            this.coverSp.visible = false;
         }
         if(this.coverBuyBtn.visible)
         {
            this.coverTxt.appendText("\n如果想继续领取该任务，请增加任务次数。");
         }
         if(this.nowData.canRegetB())
         {
            this.coverBuyBtn.visible = true;
            this.coverBuyBtn.setName("重新体验任务");
            this.coverTxt.htmlText = "当前任务已完成，点击下列按钮可重新体验该任务。";
         }
         return true;
      }
      
      private function fleshRightBtn() : void
      {
         var diff0:int = this.nowData.getDiff();
         if(diff0 == -1)
         {
            this.diffBtn.visible = false;
         }
         else
         {
            this.diffBtn.visible = true;
            this.diffBtn.setSmallIcon("diff_" + String(diff0));
            this.diffBtn.actived = this.nowData.state == TaskState.no;
         }
         var d0:TaskDefine = this.nowData.def;
         if(d0.worldMapType == "random")
         {
            this.swapMapBtn.visible = true;
            if(this.nowData.state == TaskState.no)
            {
               this.swapMapBtn.actived = this.taskG.saveGroup.getSwapCanB();
               this.swapMapBtn.tipString = this.taskG.saveGroup.getSwapBtnTip();
            }
            else
            {
               this.swapMapBtn.actived = false;
               this.swapMapBtn.tipString = "任务未接受时才能换地图。";
            }
         }
         else
         {
            this.swapMapBtn.visible = false;
         }
         TaskOtherBtn.flesh(this.nowData,this.otherBtn);
      }
      
      private function showBtnState(taskState0:String) : void
      {
         this.getBtn.visible = false;
         this.giveupBtn.visible = false;
         this.giftBtn.visible = false;
         this.mapBtn.visible = false;
         if(taskState0 == TaskState.no)
         {
            this.getBtn.visible = true;
         }
         else if(taskState0 == TaskState.ing)
         {
            this.giveupBtn.visible = true;
            this.mapBtn.visible = true;
         }
         else if(taskState0 == TaskState.complete)
         {
            this.giftBtn.visible = true;
         }
      }
      
      private function fleshDirectGiftBtn(lockB0:Boolean = false) : void
      {
         var state0:String = null;
         this.directGiftBtn.visible = false;
         if(Boolean(this.nowData))
         {
            if(TaskType.directArr.indexOf(this.nowData.def.father) >= 0)
            {
               state0 = this.nowData.state;
               if(state0 == TaskState.no || state0 == TaskState.lock || state0 == TaskState.over)
               {
                  this.directGiftBtn.visible = true;
               }
               this.directGiftBtn.tipString = this.nowData.getDirectGiftBtnTip();
               this.directGiftBtn.actived = !this.openBox.visible && !this.coverSp.visible && (this.getBtn.visible && this.getBtn.actived) && !lockB0 && this.nowData.getDirectGiftBtnActived() && state0 == TaskState.no;
            }
         }
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var taskData:TaskDataGroup = this.taskG;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(Boolean(btn0))
         {
            if(btn0.actived == false)
            {
               return;
            }
         }
         if(e.target == this.getBtn)
         {
            if(this.getTaskPanAndTip())
            {
               this.nowData.getTask();
               Gaming.uiGroup.worldMapBox.fleshData();
            }
         }
         else if(e.target == this.mapBtn)
         {
            TaskGotoPan.gotoMap(this.nowData,this.gotoNowWorldMap);
         }
         else if(e.target == this.giftBtn)
         {
            this.giftBtnClick(e);
         }
         else if(e.target == this.giveupBtn)
         {
            Gaming.uiGroup.alertBox.showNormal("\n放弃任务将清除当前任务的进度。\n确定要放弃任务？","yesAndNo",this.giveupTask);
         }
         else if(e.target == this.onlyCompleteBtn)
         {
            taskData.saveGroup.onlyCompleteB = !taskData.saveGroup.onlyCompleteB;
         }
         else if(e.target == this.directGiftBtn)
         {
            this.directGiftBtnClick(e);
         }
         this.show();
      }
      
      private function getTaskPanAndTip() : Boolean
      {
         var tipStr0:String = null;
         var worldmap_d0:WorldMapDefine = null;
         var taskData:TaskDataGroup = this.taskG;
         var nowWorldMapTaskData0:TaskData = taskData.getIngTaskDataByWorldMap(this.nowData.getWorldMapId());
         var nowTypeTaskData0:TaskData = taskData.getIngOrCompleteTaskDataByType(this.nowData.def.father);
         var unlockB0:Boolean = !this.nowData.def.mustWorldMapUnlockB() || Boolean(Gaming.PG.da.worldMap.saveGroup.getSave(this.nowData.getWorldMapId()));
         if(nowTypeTaskData0 is TaskData)
         {
            tipStr0 = "你正在进行任务：" + nowTypeTaskData0.def.getAlertTitleText() + "，\n无法接受同类型的任务。";
            if(nowTypeTaskData0.state == TaskState.complete)
            {
               tipStr0 = "请领取任务奖励：" + nowTypeTaskData0.def.getAlertTitleText() + "，\n再来接受该任务。";
            }
            Gaming.uiGroup.alertBox.showNormal(tipStr0,"yes",null,null,"no");
         }
         else if(nowWorldMapTaskData0 is TaskData)
         {
            worldmap_d0 = Gaming.defineGroup.worldMap.getDefine(this.nowData.getWorldMapId());
            Gaming.uiGroup.alertBox.showNormal("不能在" + ComMethod.color(worldmap_d0.cnName,"#00FF00") + "领取2个以上的任务。\n你必须完成或者放弃 " + nowWorldMapTaskData0.def.getAlertTitleText() + "\n才能接受当前任务。","yes",null,null,"no");
         }
         else
         {
            if(unlockB0)
            {
               return true;
            }
            Gaming.uiGroup.alertBox.showError("任务地点未解锁，无法接受当前任务！");
         }
         return false;
      }
      
      private function directGiftBtnClick(e:MouseEvent = null) : void
      {
         var todayNum0:int = 0;
         var gift0:GiftAddDefineGroup = null;
         var bagStr0:String = null;
         if(this.getTaskPanAndTip())
         {
            todayNum0 = this.taskG.getTodayNum(this.nowData.def.father);
            if(todayNum0 == -1 || todayNum0 > 0)
            {
               gift0 = this.nowData.getGift();
               bagStr0 = GiftAddit.bagSpacePan(gift0);
               if(bagStr0 != "")
               {
                  Gaming.uiGroup.alertBox.showError(bagStr0);
               }
               else
               {
                  this.nowData.complete();
                  this.giftBtnClick(null);
               }
            }
            else
            {
               Gaming.uiGroup.alertBox.showError("任务次数不足！");
            }
         }
      }
      
      private function giftBtnClick(e:MouseEvent = null) : void
      {
         var unlock_da0:TaskData = null;
         var tipStr0:String = null;
         var gift0:GiftAddDefineGroup = this.nowData.getGift();
         var bagStr0:String = GiftAddit.bagSpacePan(gift0);
         if(bagStr0 != "")
         {
            Gaming.uiGroup.alertBox.showNormal(bagStr0,"no",null,null,"no");
         }
         else
         {
            unlock_da0 = this.nowData.getTaskGift();
            tipStr0 = "领取奖励成功！";
            if(Boolean(unlock_da0))
            {
               tipStr0 = "领取奖励成功！\n解锁新任务 " + unlock_da0.def.getAlertTitleText() + " !";
            }
            GiftAddit.add(gift0,tipStr0);
            UIShow.flesh_coinChange();
            Gaming.uiGroup.mainUI.fleshBtn();
            Gaming.uiGroup.moreBox.fleshData();
         }
         Gaming.uiGroup.worldMapBox.fleshData();
      }
      
      private function swapMapBtnClick(e:MouseEvent) : void
      {
         if(this.swapMapBtn.actived)
         {
            if(this.nowData.state == TaskState.no)
            {
               Gaming.uiGroup.alertBox.showChoose("确定要换个地图？",this.swapMap);
            }
         }
      }
      
      private function swapMap() : void
      {
         var bb0:Boolean = this.nowData.rebirth(true);
         if(bb0)
         {
            ++this.taskG.saveGroup.swapNum;
            this.show();
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("切换失败！");
         }
      }
      
      private function otherBtnClick(e:MouseEvent) : void
      {
         if(this.otherBtn.actived)
         {
            TaskOtherBtn.click(this.nowData,this.otherBtn);
         }
      }
      
      private function giveupTask() : void
      {
         this.nowData.giveup();
         Gaming.uiGroup.worldMapBox.fleshData();
         this.show();
      }
      
      private function linkClick(e:TextEvent) : void
      {
      }
      
      private function gotoNowWorldMap() : void
      {
         if(this.nowData.def.father == "main" && this.nowData.getLv() > PlayerBaseData.ENEMY_LEVEL)
         {
            Gaming.uiGroup.alertBox.showError("任务等级：" + this.nowData.getLv() + ">ENEMY_LEVEL：" + PlayerBaseData.ENEMY_LEVEL);
         }
         Gaming.LG.chooseLevel(this.nowData.getWorldMapId(),0);
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         hide();
      }
      
      private function diffClick(e:MouseEvent) : void
      {
         if(Boolean(this.nowData))
         {
            this.nowData.setToNextDiff();
            this.chooseBarByTaskData(this.nowData);
            this.fleshNowChooseTaskListBar();
         }
      }
      
      private function bossDownLevelClick(e:MouseEvent) : void
      {
         var str0:String = this.getBossDownLevelTip();
         var must_d0:MustDefine = Gaming.defineGroup.must.downBossLevelNum;
         PlayerMustCtrl.deductMust(must_d0,this.yes_bossDownLevel,Gaming.uiGroup.alertBox.showPay);
      }
      
      private function yes_bossDownLevel() : void
      {
         this.nowData.downNowBossUpLevel();
         this.show();
      }
      
      private function bossDownLevelOver(e:MouseEvent) : void
      {
         var str0:String = this.getBossDownLevelTip();
         Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
      }
      
      private function getBossDownLevelTip() : String
      {
         var must_d0:MustDefine = Gaming.defineGroup.must.downBossLevelNum;
         var str0:String = "使当前任务中的首领每次倒下后只升" + ComMethod.color("1级","#00FFFF") + "。需要消耗：";
         str0 += "\n" + must_d0.getText();
         return str0 + ComMethod.color("\n\n今日内持续有效。","#00FF00");
      }
      
      private function coverBuyClick(e:MouseEvent) : void
      {
         if(Boolean(this.nowData))
         {
            if(this.nowData.canRegetB())
            {
               this.taskG.chooseCustom(this.nowData);
               Gaming.LG.chooseByLevelDefine(this.nowData.getLevelDefine());
               Gaming.LG.setRetaskB(true);
            }
            else
            {
               this.buyClick(e);
            }
         }
      }
      
      private function buyClick(e:MouseEvent) : void
      {
         var f0:TaskFatherDefine = Gaming.defineGroup.task.getFatherDefine(this.nowLabel);
         var must_d0:MustDefine = Gaming.defineGroup.must[f0.buyMustDefineName];
         var canNum0:int = this.taskG.getBuyNum(this.nowLabel);
         var nowNum0:int = this.taskG.getTodayNum(this.nowLabel);
         var unlockNum0:int = this.taskG.getUnlockNum(this.nowLabel);
         if(f0.clearCompleteAfterBuyNumB)
         {
            if(nowNum0 >= unlockNum0)
            {
               Gaming.uiGroup.alertBox.showError("当前任务次数必须小于" + ComMethod.color(unlockNum0 + "","#00FF00") + "，\n才能继续增加任务次数。");
               return;
            }
         }
         PlayerMustCtrl.numBuy(must_d0,"任务次数",canNum0,this.yes_buyNum,1);
      }
      
      private function yes_buyNum() : void
      {
         var f0:TaskFatherDefine = Gaming.defineGroup.task.getFatherDefine(this.nowLabel);
         Gaming.soundGroup.playSound("uiSound","getItems");
         this.taskG.addBuyNum(this.nowLabel,1);
         this.fleshNowTypeTodayNumText();
         this.show();
      }
      
      private function buyOver(e:MouseEvent) : void
      {
         var f0:TaskFatherDefine = Gaming.defineGroup.task.getFatherDefine(this.nowLabel);
         var must_d0:MustDefine = Gaming.defineGroup.must[f0.buyMustDefineName];
         var canNum0:int = this.taskG.getBuyNum(this.nowLabel);
         var str0:String = must_d0.getNumBuyText("任务次数",canNum0,1);
         if(f0.clearCompleteAfterBuyNumB)
         {
            str0 += "\n" + ComMethod.color("增加任务次数后，当前已完成任务将会被清除状态，从而可以再次领取。","#00FF00");
         }
         Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
      }
      
      private function buyOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      public function test_nowTaskComplete() : void
      {
         if(Boolean(this.nowData))
         {
            this.nowData.complete();
            this.show();
         }
      }
      
      public function test_nowTaskGet() : void
      {
         var name0:String = null;
         var da0:TaskData = null;
         if(Boolean(this.nowData))
         {
            name0 = this.nowData.def.name;
            if(this.taskG.getTaskDataByName(name0) == null)
            {
               this.taskG.addTask(name0);
            }
            da0 = this.taskG.getTaskDataByName(name0);
            da0.getTask();
            this.show();
         }
      }
      
      public function test_setTaskNum(num0:int) : void
      {
         if(Boolean(this.nowData))
         {
            this.nowData.def.condition.value = num0;
            this.show();
         }
      }
   }
}

