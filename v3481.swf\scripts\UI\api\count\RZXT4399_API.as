package UI.api.count
{
   import UI.api.shop.ShopBuyObject;
   import UI.test.SaveTestBox;
   import com.common.data.Base64;
   import com.sounto.oldUtils.StringDate;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll.must.define.MustDefine;
   import dataAll.things.ThingsData;
   import dataAll.things.define.ThingsDefine;
   import flash.net.URLLoader;
   import flash.net.URLLoaderDataFormat;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   
   public class RZXT4399_API
   {
      private var gameId:String = "100027788";
      
      private var regLoader:URLLoader = new URLLoader();
      
      private var loginLoader:URLLoader = new URLLoader();
      
      private var unloginLoader:URLLoader = new URLLoader();
      
      private var heartbeatLoader:URLLoader = new URLLoader();
      
      private var gold_getLoader:URLLoader = new URLLoader();
      
      private var gold_consumeLoader:URLLoader = new URLLoader();
      
      private var item_consumeLoader:URLLoader = new URLLoader();
      
      private var limit_serviceLoader:URLLoader = new URLLoader();
      
      private var item_allLoader:URLLoader = new URLLoader();
      
      private var money_logLoader:URLLoader = new URLLoader();
      
      public function RZXT4399_API()
      {
         super();
      }
      
      private function getNowTimeValue() : Number
      {
         var st0:StringDate = Gaming.api.save.getNowServerDate();
         var date0:Date = null;
         if(st0.fullYear == 0)
         {
            date0 = new Date();
         }
         else
         {
            date0 = st0.getDateClass();
         }
         return Math.ceil(date0.getTime() / 1000);
      }
      
      private function getNowTime() : StringDate
      {
         var st0:StringDate = Gaming.api.save.getNowServerDate();
         if(st0.fullYear == 0)
         {
            st0.inData_byObj(new Date());
         }
         return st0;
      }
      
      private function getIp() : String
      {
         return "";
      }
      
      private function getMoney() : Number
      {
         if(Boolean(Gaming.PG.da))
         {
            return Gaming.PG.da.main.money;
         }
         return 0;
      }
      
      private function sendNormal(loader0:URLLoader, label0:String, str0:String, timeB0:Boolean = true) : void
      {
         var s0:String = "server_id=" + Gaming.getSaveIndex();
         s0 += "&uid=" + Gaming.getUid();
         if(timeB0)
         {
            s0 += "&time=" + this.getNowTimeValue();
         }
         s0 += str0;
         var urlStr0:String = "https://tjapi.rzxt.4399doc.com/game/" + label0 + "/" + this.gameId + "/upload.gif?p=" + Base64.encodeString(s0);
         var url0:URLRequest = new URLRequest(urlStr0);
         url0.method = URLRequestMethod.GET;
         loader0.dataFormat = URLLoaderDataFormat.TEXT;
         INIT.tempTrace(urlStr0);
         SaveTestBox.addText(urlStr0);
         loader0.load(url0);
      }
      
      public function reg() : void
      {
         var s0:String = "";
         s0 += "&ip=" + this.getIp();
         this.sendNormal(this.regLoader,"reg_log",s0);
      }
      
      public function login() : void
      {
         var s0:String = "";
         s0 += "&ip=" + this.getIp();
         s0 += "&operate=1";
         this.sendNormal(this.loginLoader,"login_log",s0);
      }
      
      public function unlogin() : void
      {
         var s0:String = "";
         s0 += "&ip=" + this.getIp();
         s0 += "&operate=0";
         this.sendNormal(this.unloginLoader,"login_log",s0);
      }
      
      public function heartbeat() : void
      {
         var s0:String = "";
         s0 += "&ip=" + this.getIp();
         s0 += "&operate=0";
         this.sendNormal(this.heartbeatLoader,"heart_beat_log",s0);
      }
      
      private function gold_get(goldAmount0:int) : void
      {
         var s0:String = "";
         s0 += "&pay_time=" + this.getNowTimeValue();
         s0 += "&price=" + goldAmount0 / 10;
         s0 += "&amount=" + goldAmount0;
         s0 += "&ip=" + this.getIp();
         this.sendNormal(this.gold_getLoader,"gold_get_log",s0);
      }
      
      private function gold_consume(da0:GoodsData, type0:String, retain0:int) : void
      {
         var s0:String = "";
         s0 += "&item_tid=" + da0.def.propId;
         s0 += "&item_name=" + da0.def.name;
         s0 += "&type=" + type0;
         s0 += "&amount=" + String(da0.getPrice());
         s0 += "&num=" + da0.nowNum;
         s0 += "&retain=" + retain0;
         s0 += "&currency=";
         this.sendNormal(this.gold_consumeLoader,"gold_consume_log",s0);
      }
      
      private function mustGold_consume(d0:MustDefine, type0:String, retain0:int) : void
      {
         var s0:String = "";
         s0 += "&item_tid=" + d0.propId;
         s0 += "&item_name=" + d0.name;
         s0 += "&type=" + type0;
         s0 += "&amount=" + String(d0.money);
         s0 += "&num=1";
         s0 += "&retain=" + retain0;
         this.sendNormal(this.gold_consumeLoader,"gold_consume_log",s0);
      }
      
      private function item_consume(id0:String, name0:String, num0:int, retain0:Number) : void
      {
         var s0:String = "";
         s0 += "&item_tid=" + id0;
         s0 += "&item_name=" + name0;
         s0 += "&item_num=" + num0;
         s0 += "&retain=" + retain0;
         this.sendNormal(this.item_consumeLoader,"item_consume_log",s0);
      }
      
      private function limit_service(id0:String, name0:String, day0:int) : void
      {
         var s0:String = "";
         var now0:StringDate = this.getNowTime();
         var end0:StringDate = new StringDate();
         end0.inData_byObj(now0);
         end0.addDay(day0);
         s0 += "&item_tid=" + id0;
         s0 += "&item_name=" + name0;
         s0 += "&start_time=" + now0.getDateStr();
         s0 += "&end_time=" + end0.getDateStr();
         this.sendNormal(this.limit_serviceLoader,"limit_service_log",s0);
      }
      
      private function item_all(id0:String, name0:String, change0:Number, retain0:Number, reason0:String) : void
      {
         var s0:String = "";
         var now0:StringDate = this.getNowTime();
         var end0:StringDate = new StringDate();
         end0.inData_byObj(now0);
         end0.addDay(30);
         s0 += "&item_tid=" + id0;
         s0 += "&item_name=" + name0;
         s0 += "&type=" + (change0 >= 0 ? 1 : 0);
         s0 += "&change=" + Math.abs(change0);
         s0 += "&retain=" + retain0;
         s0 += "&reason=" + reason0;
         s0 += "&param=";
         this.sendNormal(this.item_allLoader,"item_all_log",s0);
      }
      
      private function money_log(change0:Number, retain0:Number, reason0:String) : void
      {
         var s0:String = "";
         s0 += "&type=" + (change0 >= 0 ? 1 : 0);
         s0 += "&kind=";
         s0 += "&change=" + Math.abs(change0);
         s0 += "&retain=" + retain0;
         s0 += "&reason=" + reason0;
         s0 += "&param=";
         this.sendNormal(this.money_logLoader,"money_all_log",s0);
      }
      
      public function useThings(da0:ThingsData, num0:Number, surplus0:Number) : void
      {
         var id0:String = null;
         var name0:String = null;
         var d0:ThingsDefine = da0.save.getDefine();
         var gd0:GoodsDefine = Gaming.defineGroup.goods.getDefine(d0.name);
         if(Boolean(gd0))
         {
            id0 = gd0.propId;
            name0 = gd0.name;
            if(id0 != "" && gd0.isMoneyB())
            {
               this.item_consume(id0,name0,num0,surplus0);
               this.item_all(id0,name0,-num0,surplus0,"useProps");
            }
         }
      }
      
      public function buy(obj0:ShopBuyObject, money0:Number) : void
      {
         var num0:int = 0;
         var d0:GoodsDefine = null;
         var time0:Number = NaN;
         var goodsDa0:GoodsData = obj0.getFather() as GoodsData;
         var mustD0:MustDefine = obj0.getFather() as MustDefine;
         var type0:String = "0";
         if(Boolean(goodsDa0))
         {
            num0 = goodsDa0.nowNum;
            d0 = goodsDa0.def;
            if(d0.isMonthCard())
            {
               time0 = 30 * num0;
               type0 = String(time0);
               this.limit_service(d0.propId,d0.name,time0);
            }
            else if(!d0.isCountInstantB())
            {
               if(d0.isCountInstantB())
               {
                  type0 = "-1";
               }
            }
            this.gold_consume(goodsDa0,type0,money0);
            this.money_log(-goodsDa0.getPrice(),money0,"buy");
         }
         else if(Boolean(mustD0))
         {
            if(mustD0.everB)
            {
               type0 = "-1";
            }
            this.mustGold_consume(mustD0,type0,money0);
            this.money_log(-mustD0.money,money0,"buy");
         }
      }
      
      public function goldChange(v0:Number, now0:Number) : void
      {
         if(v0 > 0)
         {
         }
      }
   }
}

