package w_test.cheating
{
   import UI.UIShow;
   import UI.guide.GuideOrder;
   import com.sounto.utils.NumberMethod;
   import dataAll.bullet.BulletDefine;
   import dataAll.image.ImageUrlDefine;
   import gameAll.trigger.HurtTrigger;
   
   public class UICheating extends OneCheating
   {
      public function UICheating()
      {
         super();
      }
      
      public function showApp(str0:String, v0:int) : String
      {
         UIShow.showByLabel(str0);
         return "";
      }
      
      public function hideStat(str0:String, v0:int) : String
      {
         Gaming.uiGroup.showStat(false);
         return "隐藏stat";
      }
      
      public function hideUI(str0:String, v0:int) : String
      {
         var bb0:Boolean = Boolean(Gaming.gameSprite.L_UI.parent);
         if(bb0)
         {
            Gaming.gameSprite.hideAllUI();
         }
         else
         {
            Gaming.gameSprite.showAllUI();
         }
         return bb0 ? "显示UI" : "隐藏UI";
      }
      
      public function stopHandUp(str0:String, v0:int) : String
      {
         Gaming.uiGroup.stopHandUpBox.show();
         return "";
      }
      
      public function showGuide(str0:String, v0:int) : String
      {
         GuideOrder.open("mainTask");
         return "";
      }
      
      public function showHurt(str0:String, v0:int) : String
      {
         HurtTrigger.HURT_SHOW = !HurtTrigger.HURT_SHOW;
         return "所有伤害显示开关：" + HurtTrigger.HURT_SHOW;
      }
      
      public function setEffectUrl(str0:String, v0:int) : String
      {
         var strArr0:Array = str0.split(":");
         var name0:String = strArr0[0];
         var url0:String = strArr0[1];
         var d0:ImageUrlDefine = Gaming.defineGroup.imageUrl.getDefine(name0);
         if(Boolean(d0))
         {
            d0.url = url0;
            Gaming.EG.bmpEffectM.addImgUrlDefineKeep(d0);
            return "设置特效：" + name0 + "(" + d0.cnName + ") Url为：" + url0;
         }
         return "找不到特效：" + name0;
      }
      
      public function setBulletEffectUrl(str0:String, v0:int) : String
      {
         var name0:String = null;
         var strArr0:Array = str0.split(":");
         name0 = strArr0[0];
         var url0:String = strArr0[1];
         var bu0:BulletDefine = Gaming.defineGroup.bullet.getDefine(name0);
         if(Boolean(bu0))
         {
            bu0.bulletImg.url = url0;
            Gaming.EG.bmpEffectM.addImgUrlDefineKeep(bu0.bulletImg);
            return "设置子弹：" + name0 + "(" + bu0.cnName + ") Url为：" + url0;
         }
         return "找不到子弹：" + name0;
      }
      
      public function scaleScene(str0:String, v0:int) : String
      {
         Gaming.gameSprite.swapScale(v0 / 100);
         return "设定视野缩放比例：" + NumberMethod.toPer(v0 / 100);
      }
   }
}

