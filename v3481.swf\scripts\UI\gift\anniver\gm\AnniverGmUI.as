package UI.gift.anniver.gm
{
   import UI.base.AppNormalUI;
   import UI.base.NormalUI;
   import UI.base.event.ClickEvent;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class AnniverGmUI extends AppNormalUI
   {
      private var labelArr:Array;
      
      private var gmBoard:AnniverGmBoard;
      
      private var boxArr:Array;
      
      private var coverSp:Sprite;
      
      private var coverTxt:TextField;
      
      private var labelTag:Sprite;
      
      private var closeBtn:SimpleButton;
      
      private var timeTxt:TextField;
      
      private var timeSp:Sprite;
      
      public function AnniverGmUI()
      {
         var label0:* = null;
         var box0:NormalUI = null;
         this.labelArr = ["gm"];
         this.gmBoard = new AnniverGmBoard();
         this.boxArr = [];
         super();
         for each(label0 in this.labelArr)
         {
            box0 = this[label0 + "Board"];
            this.boxArr.push(box0);
            box0.UILabel = label0;
            if(box0.hasOwnProperty("setCoverText"))
            {
               box0["setCoverText"] = this.setCoverText;
            }
         }
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var label0:String = null;
         var normalUI0:NormalUI = null;
         elementNameArr = ["labelTag","closeBtn","coverSp","timeSp","timeTxt"];
         super.setImg(img0);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         var boardArr0:Array = this.labelArr;
         for each(label0 in boardArr0)
         {
            label0 += "Board";
            normalUI0 = this[label0];
            addChild(normalUI0);
            normalUI0.visible = false;
            normalUI0.setImg(Gaming.swfLoaderManager.getResource("AnniverUI",label0));
         }
         addChild(this.coverSp);
         this.coverTxt = this.coverSp["txt"];
         this.setCoverText("");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.connectUI.show();
         Gaming.api.save.getServerTime(this.afterGetTime,this.noGetTime);
      }
      
      private function afterGetTime(timeStr0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         this.showBox(this.labelArr[0]);
      }
      
      private function noGetTime(v0:* = null) : void
      {
         Gaming.uiGroup.connectUI.hide();
         hide();
         Gaming.uiGroup.alertBox.showError("服务器时间获取错误！");
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      public function showBox(label0:String) : void
      {
         var n:* = undefined;
         var ui0:NormalUI = null;
         if(label0 == "")
         {
            label0 = this.labelArr[0];
         }
         for(n in this.boxArr)
         {
            this.boxArr[n].hide();
         }
         ui0 = this[label0 + "Board"];
         if(Boolean(ui0))
         {
            ui0.show();
         }
         this.setCoverText("");
         this.timeTxt.visible = false;
         this.timeSp.visible = false;
      }
      
      public function setCoverText(str0:String) : void
      {
         this.coverSp.visible = str0 != "";
         this.coverTxt.htmlText = str0;
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         hide();
      }
   }
}

