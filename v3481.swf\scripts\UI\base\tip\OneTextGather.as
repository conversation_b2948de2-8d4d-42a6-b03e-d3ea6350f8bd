package UI.base.tip
{
   import UI.base.font.FontDeal;
   import com.sounto.image.BmpMovieClip;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.filters.GlowFilter;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class OneTextGather extends Sprite
   {
      public static var shadowFilter:GlowFilter = new GlowFilter(0,1,3,3,20);
      
      private static var iconBmp:BmpMovieClip = null;
      
      public var TEXT2_MAX_X:int = 75;
      
      public var MAX_WIDTH:int = 180;
      
      public var LAST_ICON_GAP:int = 0;
      
      private var textTxt:TextField = new TextField();
      
      private var valueTxt:TextField = new TextField();
      
      private var firstIconArr:Array = [];
      
      private var lastIconArr:Array = [];
      
      public function OneTextGather(leading0:int = 5)
      {
         super();
         setNormalFormat(this.textTxt,12,false,"left",true,leading0);
         setNormalFormat(this.valueTxt,12,false,"left",true,leading0);
         addChild(this.textTxt);
         addChild(this.valueTxt);
      }
      
      public static function setIconResource(mc0:MovieClip) : void
      {
         if(!iconBmp)
         {
            iconBmp = new BmpMovieClip();
            iconBmp.Switch(mc0,1,true);
         }
         else
         {
            INIT.showError("只能设置一次资源");
         }
      }
      
      public static function getIconCopy() : BmpMovieClip
      {
         if(Boolean(iconBmp))
         {
            return iconBmp.copy();
         }
         return null;
      }
      
      public static function setNormalFormat(t0:TextField, size0:int = 12, shadowB:Boolean = false, align0:String = "left", leadGapB0:Boolean = true, leading0:int = 5) : void
      {
         t0.mouseWheelEnabled = false;
         t0.selectable = false;
         t0.multiline = true;
         t0.embedFonts = false;
         t0.wordWrap = false;
         t0.autoSize = align0;
         var tf:TextFormat = new TextFormat("SimSun",String(size0),16777215,null,null,null,null,null,align0);
         tf.leading = leading0 + (leadGapB0 ? -FontDeal.leadGap : 0);
         t0.y -= leadGapB0 ? int(FontDeal.leadGap / 2) : 0;
         t0.defaultTextFormat = tf;
         if(shadowB)
         {
            t0.filters = [shadowFilter];
         }
      }
      
      public function setNormalTextColor(color0:uint) : void
      {
         this.textTxt.textColor = color0;
         this.valueTxt.textColor = color0;
      }
      
      public function init() : void
      {
         var i1:BmpMovieClip = null;
         var i2:BmpMovieClip = null;
         for(var i:int = 0; i < 30; i++)
         {
            i1 = iconBmp.copy();
            i2 = iconBmp.copy();
            i1.gotoAndStop(1);
            i2.gotoAndStop(1);
            this.addChild(i1);
            this.addChild(i2);
            this.firstIconArr.push(i1);
            this.lastIconArr.push(i2);
         }
      }
      
      public function setText(str0:String) : void
      {
         this.textTxt.width = 10;
         this.textTxt.wordWrap = false;
         this.valueTxt.width = 10;
         this.valueTxt.wordWrap = false;
         var arr0:Array = TextGatherAnalyze.swapToFourText(str0);
         this.textTxt.htmlText = arr0[1];
         if(this.textTxt.width > this.MAX_WIDTH)
         {
            this.textTxt.wordWrap = true;
            this.textTxt.width = this.MAX_WIDTH;
         }
         this.valueTxt.htmlText = arr0[2];
         this.valueTxt.x = this.textTxt.x + this.textTxt.width + 5;
         if(this.valueTxt.x > this.TEXT2_MAX_X)
         {
            this.valueTxt.x = this.TEXT2_MAX_X;
         }
         var str_size0:int = int(this.textTxt.defaultTextFormat.size);
         var lead0:int = str_size0 + this.textTxt.defaultTextFormat.leading + FontDeal.countGap;
         var y0:int = str_size0 / 2 + 2;
         var x2:int = this.valueTxt.x + this.valueTxt.width + this.LAST_ICON_GAP;
         this.showIcon(arr0[0],this.firstIconArr,-6,y0,lead0);
         this.showIcon(arr0[3],this.lastIconArr,x2,y0,lead0);
      }
      
      public function showIcon(nameArr0:Array, iconArr0:Array, x0:int, y0:int, yGap:int) : void
      {
         var n:* = undefined;
         var i1:BmpMovieClip = null;
         var n0:String = null;
         var frame0:int = 0;
         for(n in iconArr0)
         {
            i1 = iconArr0[n];
            if(n < nameArr0.length)
            {
               n0 = nameArr0[n];
               if(n0 == "")
               {
                  i1.visible = false;
               }
               else
               {
                  i1.visible = true;
                  frame0 = int(n0.substring(2,n0.length - 1));
                  i1.gotoAndStop(frame0);
               }
               i1.x = x0;
               i1.y = y0 + n * yGap;
            }
            else
            {
               i1.visible = false;
               i1.x = 0;
               i1.y = 0;
            }
         }
      }
      
      private function clear() : void
      {
         var n:* = undefined;
         var i1:BmpMovieClip = null;
         var i2:BmpMovieClip = null;
         this.textTxt.htmlText = "";
         this.valueTxt.htmlText = "";
         for(n in this.firstIconArr)
         {
            i1 = this.firstIconArr[n];
            i1.visible = false;
            i2 = this.lastIconArr[n];
            i2.visible = false;
         }
      }
   }
}

