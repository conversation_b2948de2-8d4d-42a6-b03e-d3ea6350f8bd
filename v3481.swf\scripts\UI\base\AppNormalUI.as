package UI.base
{
   import UI.UIShow;
   import UI.base._hide.HideNormalUI;
   import dataAll.items.ItemsCompareData;
   
   public class AppNormalUI extends HideNormalUI
   {
      public function AppNormalUI()
      {
         super();
      }
      
      override public function show() : void
      {
         super.show();
         if(UILabel != "bag")
         {
            UIShow.appUIShowEvent(this);
         }
      }
      
      override public function hide() : void
      {
         super.hide();
         if(UILabel != "bag")
         {
            UIShow.appUIHideEvent(this);
         }
      }
      
      public function hideWhenVisible() : void
      {
         if(visible)
         {
            this.hide();
         }
      }
      
      public function bagCloseEvent() : void
      {
         if(visible)
         {
            this.hide();
         }
      }
      
      public function getMoreBoxVisible() : <PERSON><PERSON>an
      {
         return true;
      }
      
      public function getNowUICompareData(dd0:ItemsCompareData) : ItemsCompareData
      {
         return ItemsCompareData.ZERO;
      }
   }
}

