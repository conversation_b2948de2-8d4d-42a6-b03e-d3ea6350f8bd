package w_test.cheating
{
   import UI.pet.PetUI;
   import dataAll._app.space.craft.CraftData;
   import dataAll.pet.PetData;
   import dataAll.pet.gene.define.GeneDefine;
   
   public class PetCheating extends OneCheating
   {
      public function PetCheating()
      {
         super();
      }
      
      public function addNowCraftExp(str0:String, v0:int) : String
      {
         Gaming.PG.da.space.addNowCraftExp(v0);
         return "添加飞船经验值：" + v0;
      }
      
      public function setNowCraftLv(str0:String, v0:int) : String
      {
         var da0:CraftData = Gaming.PG.da.space.getNowCraft();
         if(<PERSON><PERSON>an(da0))
         {
            da0.setLevel(v0);
            return da0.getCnName() + " 设置飞船等级：" + v0;
         }
         return "";
      }
      
      public function clearAllPet(str0:String, v0:int) : String
      {
         Gaming.PG.da.pet.clearData();
         return "清除所有宠物";
      }
      
      public function setPetLv(str0:String, v0:int) : String
      {
         var da0:PetData = PetUI.getNowData();
         if(da0 is PetData)
         {
            da0.base.save.level = v0;
         }
         return "设置当前尸宠等级为：" + v0;
      }
      
      public function addNowGenePro(str0:String, v0:int) : String
      {
         var d0:GeneDefine = Gaming.uiGroup.petUI.bookBoard.nowGeneDefine;
         if(Boolean(d0))
         {
            Gaming.PG.da.pet.saveGroup.map.setDropPro(d0.name,v0 / 100,true);
         }
         return "添加" + d0.cnName + "基因体掉落概率：" + v0 / 100;
      }
   }
}

