package UI.base.alert.colorChoose
{
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.alert.AlertBox;
   import UI.base.btnList.BtnBox;
   import UI.base.button.NormalBtn;
   import dataAll.equip.define.EquipColor;
   import dataAll.items.ItemsBatchColor;
   import dataAll.items.ItemsDataGroup;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class AlertColorChooseBox extends BtnBox
   {
      public var box:AlertBox;
      
      private var dataType:String = "";
      
      private var decomposeB:Boolean = false;
      
      public function AlertColorChooseBox()
      {
         super();
      }
      
      private function get typeArr() : Array
      {
         return ItemsBatchColor.allArr;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var name0:* = null;
         var btn0:NormalBtn = null;
         super.setImg(img0);
         var typeArr0:Array = this.typeArr;
         for each(name0 in typeArr0)
         {
            btn0 = getBtn(name0);
            if(btn0 is NormalBtn)
            {
               btn0.setName(ItemsBatchColor.getColorCn(btn0.label,""));
               if(name0 == ItemsBatchColor.auto || name0 == ItemsBatchColor.rare)
               {
                  btn0.activedAndEnabled = false;
                  ItemsGripTipCtrl.addNormalBtnTip(btn0);
               }
            }
         }
      }
      
      public function showColorChoose(str0:String, btnStateArr0:Array, dataType0:String, decomposeB0:Boolean, yesFun0:Function) : void
      {
         this.box.showNormal("\n\n\n\n" + str0,"yesAndNo",yesFun0);
         this.show();
         this.inDataArr(btnStateArr0,dataType0,decomposeB0);
      }
      
      public function inDataArr(arr0:Array, dataType0:String, decomposeB0:Boolean) : void
      {
         var name0:* = null;
         var btn0:NormalBtn = null;
         var visibleB0:Boolean = false;
         var chosenB0:Boolean = false;
         var actived0:Boolean = false;
         this.dataType = dataType0;
         this.decomposeB = decomposeB0;
         var typeArr0:Array = this.typeArr;
         var redB0:Boolean = arr0.indexOf(EquipColor.RED) >= 0;
         var haveRareB0:Boolean = ItemsBatchColor.rareTypeArr.indexOf(dataType0) >= 0 && decomposeB0;
         var haveAutoB0:Boolean = ItemsBatchColor.autoTypeArr.indexOf(dataType0) >= 0 && decomposeB0;
         var autoRightB0:Boolean = Gaming.PG.da.post.haveAutoDesB();
         for each(name0 in typeArr0)
         {
            btn0 = getBtn(name0);
            if(btn0 is NormalBtn)
            {
               visibleB0 = true;
               chosenB0 = arr0.indexOf(name0) >= 0;
               actived0 = true;
               if(name0 == ItemsBatchColor.rare)
               {
                  visibleB0 = haveRareB0;
                  btn0.setName(ItemsBatchColor.getColorCn(name0,dataType0));
                  if(dataType0 == ItemsDataGroup.TYPE_ARMS)
                  {
                     actived0 = true;
                  }
                  else
                  {
                     actived0 = redB0;
                  }
               }
               else if(name0 == ItemsBatchColor.auto)
               {
                  visibleB0 = haveAutoB0;
                  actived0 = autoRightB0;
                  chosenB0 &&= autoRightB0;
                  btn0.setName(ItemsBatchColor.getColorCn(name0,dataType0));
                  if(haveRareB0)
                  {
                     btn0.x = 220;
                  }
                  else
                  {
                     btn0.x = 140;
                  }
               }
               else
               {
                  if(name0 == EquipColor.BLACK)
                  {
                     if(dataType0 != ItemsDataGroup.TYPE_ARMS)
                     {
                        actived0 = false;
                     }
                  }
                  if(haveRareB0 || haveAutoB0)
                  {
                     btn0.y = 30;
                  }
                  else
                  {
                     btn0.y = 40;
                  }
               }
               btn0.tipString = ItemsBatchColor.getBtnTip(name0,dataType0);
               btn0.isChosen = chosenB0;
               btn0.visible = visibleB0;
               btn0.actived = actived0;
            }
         }
      }
      
      public function getDataArr() : Array
      {
         var name0:* = null;
         var btn0:NormalBtn = null;
         var typeArr0:Array = this.typeArr;
         var arr0:Array = [];
         for each(name0 in typeArr0)
         {
            btn0 = getBtn(name0);
            if(btn0 is NormalBtn)
            {
               if(btn0.isChosen)
               {
                  arr0.push(name0);
               }
            }
         }
         return arr0;
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var rareIsPointerB0:Boolean = false;
         var chosenChangeB0:Boolean = false;
         var btn2:NormalBtn = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0.actived)
         {
            rareIsPointerB0 = ItemsBatchColor.rareIsPointerB(this.dataType);
            chosenChangeB0 = true;
            if(btn0.label == ItemsBatchColor.rare && rareIsPointerB0)
            {
               this.gotoPointer(this.dataType);
            }
            else
            {
               btn0.isChosen = !btn0.isChosen;
               if(btn0.label == EquipColor.RED)
               {
                  if(rareIsPointerB0 == false)
                  {
                     btn2 = getBtn(ItemsBatchColor.rare);
                     if(Boolean(btn2))
                     {
                        btn2.actived = btn0.isChosen;
                     }
                  }
               }
               Gaming.PG.save.setting.setColorArr(this.dataType,this.decomposeB,this.getDataArr());
            }
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      protected function gotoPointer(dataType0:String) : void
      {
         if(dataType0 == ItemsDataGroup.TYPE_ARMS)
         {
            Gaming.uiGroup.armsFilterBoard.show();
            Gaming.uiGroup.alertBox.hide();
         }
      }
   }
}

