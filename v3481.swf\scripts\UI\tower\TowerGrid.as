package UI.tower
{
   import UI.bag.ItemsGrid;
   import UI.base.box.NormalGridIconBox;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   
   public class TowerGrid extends ItemsGrid
   {
      private var iconTag:Sprite;
      
      private var iconBox:NormalGridIconBox = new NormalGridIconBox();
      
      public function TowerGrid()
      {
         super();
      }
      
      override public function setImg(img0:MovieClip) : void
      {
         super.setImg(img0);
         this.iconTag = img0["iconTag"];
         this.iconTag.addChild(this.iconBox);
         this.iconBox.x = 68;
         this.iconBox.y = 14;
         this.iconBox.arg.init(3,1,0,0);
      }
      
      public function setIconUrlArr(arr0:Array) : void
      {
         this.iconBox.addByUrlArr(arr0);
         setSecMc(arr0.length);
      }
      
      override public function clearShow() : void
      {
         super.clearShow();
         this.iconBox.clear();
      }
   }
}

