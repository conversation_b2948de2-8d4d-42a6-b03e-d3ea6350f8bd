package UI.bag
{
   import UI.base.event.BoxEventAddit;
   import UI.base.event.ClickEvent;
   import UI.base.grid.NormalGrid;
   import flash.events.EventDispatcher;
   import flash.events.MouseEvent;
   
   public class GripBoxEventAddit extends BoxEventAddit
   {
      public var fatherData:Object = null;
      
      public function GripBoxEventAddit(father0:EventDispatcher)
      {
         super(father0);
      }
      
      override protected function setGoul(clickEvent:ClickEvent, event:MouseEvent) : *
      {
         super.setGoul(clickEvent,event);
         var target0:NormalGrid = event.target as NormalGrid;
         clickEvent.fatherData = this.fatherData;
      }
   }
}

