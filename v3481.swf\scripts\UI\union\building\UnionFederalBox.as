package UI.union.building
{
   import UI.UIOrder;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import com.adobe.serialization.json.JSON2;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldNiuBiCF;
   import dataAll._app.union.building.UnionBuildingDataGroup;
   import dataAll._app.union.building.cooking.CookingState;
   import dataAll._app.union.building.federal.UnionSendTaskData;
   import dataAll._app.union.building.federal.UnionSendTaskDataGroup;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import gameAll.level._diy.FederalLevelDiy;
   
   public class UnionFederalBox extends AutoNormalUI
   {
      private var unionNumTxt:TextField;
      
      private var unionBtn:NormalBtn;
      
      private var unionGiftBtn:NormalBtn;
      
      private var sendBtn:NormalBtn;
      
      private var sendGiftBtn:NormalBtn;
      
      private var federalTipBtn:NormalBtn;
      
      private var sendTipBtn:NormalBtn;
      
      private var gripTag:Sprite;
      
      private var box:ItemsGripBox = new ItemsGripBox();
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      private var saveB:Boolean = false;
      
      public function UnionFederalBox()
      {
         super();
         this.API_ID = 136;
      }
      
      public function get API_ID() : Number
      {
         return this.CF.getAttribute("API_ID");
      }
      
      public function set API_ID(v0:Number) : void
      {
         this.CF.setAttribute("API_ID",v0);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         FontDeal.dealOne(this.unionNumTxt);
         this.box.setIconPro("UnionUI/federalTaskGrip");
         this.box.arg.init(1,5,0,0);
         this.box.x = this.gripTag.x;
         this.box.y = this.gripTag.y;
         this.box.evt.setWantEvent(true,false,false,true,true);
         this.box.addEventListener(ClickEvent.ON_CLICK,this.barClick);
         this.box.addEventListener(ClickEvent.ON_OVER,this.barOver);
         this.box.addEventListener(ClickEvent.ON_OUT,this.barOut);
         addChild(this.box);
         this.unionBtn.setName("开始行动");
         this.unionGiftBtn.setName("领取奖励");
         this.unionGiftBtn.activedAndEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.unionGiftBtn);
         this.sendBtn.setName("开始派遣");
         this.sendGiftBtn.setName("领取奖励");
         this.sendGiftBtn.activedAndEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.sendGiftBtn);
         ItemsGripTipCtrl.addNormalBtnTip(this.federalTipBtn);
         ItemsGripTipCtrl.addNormalBtnTip(this.sendTipBtn);
         this.federalTipBtn.tipString = "1、每个成员成功完成行动，则军队完成任务总次数+1。";
         this.federalTipBtn.tipString += "\n2、总次数到达一定值，则参加任务的所有成员都可领取奖励。";
         this.federalTipBtn.tipString += "\n3、提升联邦大厦等级，则会提高奖励的军备物资数量。";
         this.sendTipBtn.tipString = "1、派遣任务由个人单独完成。";
         this.sendTipBtn.tipString += "\n2、开始派遣后进入倒计时，期间会自动累计奖励。并且能随时领取获得的奖励。";
         this.sendTipBtn.tipString += "\n3、提升联邦大厦等级，则会提高奖励的军备物资数量。";
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
         this.openUI();
      }
      
      private function fleshData() : void
      {
         this.fleshFederal();
         this.fleshSendTask();
      }
      
      public function outLoginEvent() : void
      {
         this.saveB = false;
      }
      
      private function openUI() : void
      {
         var dg0:UnionBuildingDataGroup = Gaming.PG.da.union.building;
         var state0:int = dg0.saveGroup.federalState;
         if(state0 == 1)
         {
            this.saveB = true;
            Gaming.uiGroup.connectUI.show();
            Gaming.api.union.variables.doVariable(Gaming.getSaveIndex(),this.API_ID,this.yes_addFederal,this.no_addFederal);
         }
         else
         {
            this.after_addFederal();
         }
      }
      
      private function yes_addFederal(bb0:Boolean) : void
      {
         if(bb0)
         {
            if(this.saveB)
            {
               Gaming.PG.da.union.building.setFederalTaskState(2);
               UIOrder.save(true,true,false,this.after_addFederal,this.no_addFederal);
            }
            else
            {
               this.after_addFederal();
            }
         }
         else
         {
            this.no_addFederal();
         }
      }
      
      private function no_addFederal(str0:String = "") : void
      {
         Gaming.uiGroup.alertBox.showError("与服务器链接失败！");
         this.after_addFederal();
      }
      
      private function after_addFederal(v0:* = null) : void
      {
         this.saveB = false;
         Gaming.uiGroup.connectUI.show();
         Gaming.api.union.variables.getVariables(Gaming.getSaveIndex(),[this.API_ID],this.yes_getFederal,this.no_getFederal);
      }
      
      private function yes_getFederal(jsonStr0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         var arr0:Array = JSON2.decode(jsonStr0);
         var num0:int = int(arr0[0]["value"]);
         var dg0:UnionBuildingDataGroup = Gaming.PG.da.union.building;
         dg0.allFederalNum = num0;
         this.fleshData();
      }
      
      private function no_getFederal(str0:String = "") : void
      {
         Gaming.uiGroup.alertBox.showError("与服务器链接失败！");
         this.yes_getFederal("{\"value\":0}");
      }
      
      private function fleshFederal() : void
      {
         var dg0:UnionBuildingDataGroup = Gaming.PG.da.union.building;
         var state0:int = dg0.saveGroup.federalState;
         var giftB0:Boolean = dg0.saveGroup.federalGiftB;
         var gift0:GiftAddDefineGroup = dg0.getFederalTaskGift();
         var nowGiftNum0:int = dg0.getFederalGiftNowNum();
         var maxGiftNum0:int = dg0.getFederalGiftMaxNum();
         var now0:int = dg0.allFederalNum;
         var must0:int = dg0.getFederalTaskMustNum();
         this.unionBtn.actived = state0 == 0;
         this.unionBtn.setName(state0 == 0 ? "开始行动" : "行动完毕");
         this.unionGiftBtn.actived = !giftB0 && state0 > 0 && nowGiftNum0 > 0;
         this.unionGiftBtn.setName(giftB0 ? "已领取" : "领取奖励");
         this.unionNumTxt.htmlText = "今日军队已完成联邦任务：" + ComMethod.colorMustNum(now0,must0);
         this.unionGiftBtn.itemsData = gift0;
         var str0:String = "";
         str0 += "参加并完成联邦任务，任务完成度越高，奖励也就越高。";
         str0 += "\n\n<b><blue 奖励：/></b>";
         str0 += "\n军备物资：" + nowGiftNum0 + "/" + maxGiftNum0;
         str0 += "\n\n<b><orange " + dg0.getData("federal").def.lvName + "越高，所获得的奖励也会越多。/></b>";
         this.unionGiftBtn.tipString = str0;
      }
      
      public function unionBtnClick(e:MouseEvent) : void
      {
         if(this.unionBtn.actived)
         {
            FederalLevelDiy.chooseLevel();
         }
      }
      
      public function unionGiftBtnClick(e:MouseEvent) : void
      {
         var dg0:UnionBuildingDataGroup = null;
         var nowGiftNum0:int = 0;
         var maxGiftNum0:int = 0;
         if(this.unionGiftBtn.actived)
         {
            dg0 = Gaming.PG.da.union.building;
            nowGiftNum0 = dg0.getFederalGiftNowNum();
            maxGiftNum0 = dg0.getFederalGiftMaxNum();
            if(nowGiftNum0 < maxGiftNum0)
            {
               Gaming.uiGroup.alertBox.showNormal("联邦任务完成度未达到100%，\n确定要提前领取奖励？","yesAndNo",this.getUnionGift);
            }
            else
            {
               this.getUnionGift();
            }
         }
      }
      
      private function getUnionGift() : void
      {
         var dg0:UnionBuildingDataGroup = Gaming.PG.da.union.building;
         var gift0:GiftAddDefineGroup = dg0.getFederalTaskGift();
         var bb0:Boolean = GiftAddit.addAndAutoBagSpacePan(gift0);
         if(bb0)
         {
            dg0.saveGroup.federalGiftB = true;
            ++dg0.saveGroup.federalTaskNum;
            this.fleshFederal();
         }
      }
      
      private function fleshSendTask() : void
      {
         var n:* = undefined;
         var da0:UnionSendTaskData = null;
         var grip0:ItemsGrid = null;
         var state0:String = null;
         var activedB0:Boolean = false;
         var smallIconStr0:String = null;
         var daArr0:Array = Gaming.PG.da.union.building.sendTask.getDataArr();
         if(this.box.gripArr.length < daArr0.length)
         {
            this.box.setNowGripNum(daArr0.length);
         }
         for(n in daArr0)
         {
            da0 = daArr0[n];
            grip0 = this.box.gripArr[n];
            grip0.activedAndEnabled = false;
            state0 = da0.getState();
            activedB0 = state0 == "no";
            smallIconStr0 = state0;
            if(grip0.getSmallIconStr() != smallIconStr0 || grip0.itemsData != da0)
            {
               grip0.itemsData = da0;
               grip0.setName(da0.def.cnName);
               grip0.setSmallIcon(smallIconStr0);
            }
            grip0.setNumText(da0.getSurplusTimeStr());
         }
         this.fleshSendBtn();
      }
      
      public function fleshSendBtn() : void
      {
         var dg0:UnionSendTaskDataGroup = Gaming.PG.da.union.building.sendTask;
         var state0:String = dg0.getAllState();
         var sendBtnActived0:Boolean = false;
         if(state0 == CookingState.NO)
         {
            sendBtnActived0 = true;
            this.sendBtn.setName("开始派遣任务");
         }
         else if(state0 == CookingState.ING)
         {
            this.sendBtn.setName("任务进行中");
         }
         else
         {
            this.sendBtn.setName("任务已结束");
         }
         if(this.sendBtn.actived != sendBtnActived0)
         {
            this.sendBtn.actived = sendBtnActived0;
         }
         var gift0:GiftAddDefineGroup = dg0.getGiftAddDefineGroup();
         var sendGiftBtnActived0:Boolean = false;
         var tipStr0:String = "";
         if(Boolean(gift0))
         {
            sendGiftBtnActived0 = true;
            tipStr0 = "<blue 可领取的派遣任务奖励：/>";
            tipStr0 += "\n" + gift0.getDescription(1);
         }
         this.sendGiftBtn.tipString = tipStr0;
         if(this.sendGiftBtn.actived != sendGiftBtnActived0)
         {
            this.sendGiftBtn.actived = sendGiftBtnActived0;
         }
      }
      
      public function sendBtnClick(e:MouseEvent) : void
      {
         var dg0:UnionSendTaskDataGroup = Gaming.PG.da.union.building.sendTask;
         var allState0:String = dg0.getAllState();
         if(allState0 == "no")
         {
            dg0.nextEat();
            ++dg0.saveGroup.sendTaskNum;
            this.fleshSendTask();
            Gaming.uiGroup.unionTaskBox.fleshDataByUnion();
            Gaming.uiGroup.unionTaskBox.show(99999);
         }
      }
      
      public function sendGiftBtnClick(e:MouseEvent) : void
      {
         var dg0:UnionSendTaskDataGroup = null;
         var gift0:GiftAddDefineGroup = null;
         var bb0:Boolean = false;
         if(this.sendGiftBtn.actived)
         {
            dg0 = Gaming.PG.da.union.building.sendTask;
            gift0 = dg0.getGiftAddDefineGroup();
            bb0 = GiftAddit.addAndAutoBagSpacePan(gift0);
            if(bb0)
            {
               dg0.getGift();
               this.fleshSendTask();
            }
         }
      }
      
      private function barClick(e:ClickEvent) : void
      {
         var da0:UnionSendTaskData = e.childData as UnionSendTaskData;
         var dg0:UnionSendTaskDataGroup = Gaming.PG.da.union.building.sendTask;
         if(dg0.getAllState() == "no")
         {
            dg0.oneFirst(da0);
            this.fleshSendTask();
         }
      }
      
      private function barOver(e:ClickEvent) : void
      {
         var da0:UnionSendTaskData = e.childData as UnionSendTaskData;
         Gaming.uiGroup.tipBox.textTip.showFollowText(da0.getTipStr());
      }
      
      private function barOut(e:ClickEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var funName0:String = btn0.label + "BtnClick";
         if(this.hasOwnProperty(funName0))
         {
            this[funName0](e);
         }
      }
      
      public function stopAll() : void
      {
         this.box.stopAll();
      }
      
      public function playAll() : void
      {
         this.box.playAll();
      }
      
      public function FTimerSecond() : void
      {
         if(visible)
         {
            this.fleshSendTask();
         }
      }
   }
}

