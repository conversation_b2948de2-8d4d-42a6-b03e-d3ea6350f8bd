package UI.base.alert.colorChoose
{
   import UI.base.alert.AlertBox;
   import UI.base.btnList.BtnBox;
   import UI.base.font.FontDeal;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class AlertTextBox extends BtnBox
   {
      private var txt:TextField;
      
      public var box:AlertBox;
      
      public function AlertTextBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["txt"];
         super.setImg(img0);
         FontDeal.dealOne(this.txt);
      }
      
      public function getText() : String
      {
         return this.txt.text;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function showTextInput(str0:String, valueStr0:String, yesFun0:Function, btnShowState0:String = "yesAndNo", maxChar0:int = 14, noFun0:Function = null) : void
      {
         this.txt.text = valueStr0;
         this.txt.maxChars = maxChar0;
         this.box.showNormal(str0 + "\n\n",btnShowState0,yesFun0,noFun0);
         this.show();
      }
   }
}

