package UI
{
   import flash.display.DisplayObject;
   
   public class NormalUICtrl
   {
      public function NormalUICtrl()
      {
         super();
      }
      
      public static function setTag(sp0:DisplayObject, tag0:DisplayObject) : void
      {
         if(<PERSON><PERSON><PERSON>(sp0) && <PERSON><PERSON>an(tag0))
         {
            sp0.x = tag0.x;
            sp0.y = tag0.y;
            tag0.visible = false;
         }
      }
      
      public static function alignMiddle(sp0:DisplayObject, tag0:DisplayObject) : void
      {
         var mx0:int = tag0.x + tag0.width / 2;
         sp0.x = mx0 - sp0.width / 2;
      }
   }
}

