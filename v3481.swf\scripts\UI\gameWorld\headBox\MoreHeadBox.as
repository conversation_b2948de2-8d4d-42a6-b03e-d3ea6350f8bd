package UI.gameWorld.headBox
{
   import UI.base.loadBar.LoadBar;
   import flash.display.Sprite;
   import gameAll.body.IO_NormalBody;
   
   public class MoreHeadBox extends Sprite
   {
      private var barArr:Array = [];
      
      public function MoreHeadBox()
      {
         super();
         this.mouseChildren = false;
         this.mouseEnabled = false;
      }
      
      public function imgInit() : void
      {
         var bar0:LoadBar = null;
         for(var i:int = 0; i < 6; i++)
         {
            bar0 = new LoadBar();
            bar0.setImg(Gaming.swfLoaderManager.getResourceFull("GameWorldUI/partnerIcon"));
            bar0.x = 0;
            bar0.y = i * (bar0.width + 5);
            addChild(bar0);
            this.barArr.push(bar0);
         }
         this.overGamingClear();
      }
      
      public function overGamingClear() : void
      {
         var n:* = undefined;
         var bar0:LoadBar = null;
         for(n in this.barArr)
         {
            bar0 = this.barArr[n];
            this.clearOne(bar0);
         }
      }
      
      public function inBodyArr(arr0:Array) : void
      {
         var n:* = undefined;
         var bar0:LoadBar = null;
         var b0:IO_NormalBody = null;
         for(n in this.barArr)
         {
            bar0 = this.barArr[n];
            this.clearOne(bar0);
            b0 = arr0[n];
            if(b0 is IO_NormalBody)
            {
               addChild(bar0);
               bar0.visible = true;
               bar0.itemsData = b0;
               bar0.setIconName(b0.getDefine().headIconUrl);
               this.fleshOne(bar0);
            }
         }
      }
      
      public function getMouseBody() : IO_NormalBody
      {
         var bar0:LoadBar = null;
         var b0:IO_NormalBody = null;
         var x0:Number = Gaming.ME.mouseX;
         var y0:Number = Gaming.ME.mouseY;
         for each(bar0 in this.barArr)
         {
            b0 = bar0.itemsData as IO_NormalBody;
            if(Boolean(b0))
            {
               if(bar0.hitTestPoint(x0,y0))
               {
                  if(b0.getDie() == 0)
                  {
                     return b0;
                  }
               }
            }
         }
         return null;
      }
      
      private function clearOne(bar0:LoadBar) : void
      {
         if(bar0.parent == this)
         {
            bar0.visible = false;
            this.removeChild(bar0);
            bar0.itemsData = null;
         }
      }
      
      private function fleshOne(bar0:LoadBar) : void
      {
         var b0:IO_NormalBody = bar0.itemsData as IO_NormalBody;
         bar0.setIconAlpla(1);
         if(b0 is IO_NormalBody)
         {
            if(b0.getDie() == 0)
            {
               bar0.setPer(b0.getData().getLifePer());
            }
            else
            {
               bar0.setIconAlpla(0.5);
               bar0.setPer(0);
            }
         }
      }
      
      public function fleshEach() : void
      {
         var n:* = undefined;
         var bar0:LoadBar = null;
         for(n in this.barArr)
         {
            bar0 = this.barArr[n];
            if(bar0.parent == this)
            {
               this.fleshOne(bar0);
            }
         }
      }
   }
}

