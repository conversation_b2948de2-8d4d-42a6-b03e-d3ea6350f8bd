package UI.gameOver
{
   import UI.base.NormalUI;
   import UI.base.font.FontDeal;
   import dataAll._app.arena.ArenaCountData;
   import dataAll._player.PlayerData;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class ArenaGameOverBox extends NormalUI
   {
      private var scoreTxt:TextField;
      
      private var leftTxt:TextField;
      
      private var rightTxt:TextField;
      
      private var starMc:MovieClip;
      
      private var winSp:Sprite;
      
      private var failSp:Sprite;
      
      private var winBox:ArenaPlayerBox = new ArenaPlayerBox();
      
      private var failBox:ArenaPlayerBox = new ArenaPlayerBox();
      
      public function ArenaGameOverBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["winSp","failSp","starMc","rightTxt","leftTxt","scoreTxt"];
         super.setImg(img0);
         FontDeal.dealLine(this.rightTxt);
         FontDeal.dealStaticLine(this.leftTxt);
         this.starMc.stop();
         addChild(this.winBox);
         this.winBox.setImg(this.winSp);
         addChild(this.failBox);
         this.failBox.setImg(this.failSp);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function inData(da0:ArenaCountData, pd1:PlayerData) : void
      {
         this.scoreTxt.text = da0.score + "";
         this.rightTxt.text = da0.baseScore + "\n" + da0.streakScore + "\n" + da0.stampNum + "张";
         this.starMc.gotoAndStop(da0.startNum);
         this.winBox.inData(da0.winB ? Gaming.PG.da : pd1,true);
         this.failBox.inData(da0.winB ? pd1 : Gaming.PG.da,false);
      }
   }
}

