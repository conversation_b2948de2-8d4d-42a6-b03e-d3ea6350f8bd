package UI.gameWorld.headBox
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.base.loadBar.LoadBar;
   import com.sounto.utils.NumberMethod;
   import dataAll.ui.StateIconData;
   import flash.display.Sprite;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.data.NormalBodyData;
   import gameAll.hero.HeroBody;
   import gameAll.hero.shield.ShieldCtrl;
   
   public class PlayerHeadBox extends LoadBar
   {
      private var stateTag:Sprite;
      
      private var stateBox:ItemsGripBox = new ItemsGripBox();
      
      private var aiSp:Sprite;
      
      private var possSp:Sprite;
      
      private var _possPer:Number = 999;
      
      private var expSp:Sprite;
      
      private var _expPer:Number = 999;
      
      private var shieldSp:Sprite;
      
      private var _shieldPer:Number = 999;
      
      private var shieldIconSp:Sprite;
      
      private var shieldIcon:ShieldSmallGrip = new ShieldSmallGrip();
      
      public function PlayerHeadBox()
      {
         super();
         this.mouseEnabled = false;
         this.mouseChildren = false;
      }
      
      override public function setImg(img0:Sprite, followXYB0:Boolean = true) : void
      {
         super.setImg(img0,followXYB0);
         this.aiSp = img["aiSp"];
         this.expSp = img["expSp"];
         this.possSp = img["possSp"];
         this.shieldSp = img["shieldSp"];
         this.shieldIconSp = img["shieldIconSp"];
         this.stateTag = img["stateTag"];
         this.stateTag.addChild(this.stateBox);
         this.stateBox.arg.init(2,3,6,0);
         this.stateBox.setIconPro("stateGrip");
         this.stateBox.evt.setWantEvent(false,false,false,false,false);
         this.stateBox.setNowGripNum(6);
         this.stateBox.visible = false;
         if(Boolean(this.shieldSp))
         {
            this.shieldIcon.setImg(this.shieldIconSp);
            addChild(this.shieldIcon);
            this.shieldIcon.visible = false;
         }
      }
      
      override protected function dealFont() : void
      {
      }
      
      public function setToNormalImg() : void
      {
         this.setImg(Gaming.swfLoaderManager.getResourceFull("GameWorldUI/playerHeadBox"));
      }
      
      public function setToRightImg() : void
      {
         this.setImg(Gaming.swfLoaderManager.getResourceFull("GameWorldUI/playerRightHeadBox"));
      }
      
      public function inData(b0:IO_NormalBody) : void
      {
         var dat0:NormalBodyData = null;
         var per0:Number = NaN;
         var hero0:HeroBody = null;
         var shield0:ShieldCtrl = null;
         if(Boolean(b0))
         {
            dat0 = b0.getData();
            setRightText(String(dat0.bodyLevel));
            setIconName(dat0.getHeadIconUrl());
            per0 = dat0.nowLife / dat0.maxLife;
            setPer(per0);
            setText(NumberMethod.toPer(per0,0));
            hero0 = b0 as HeroBody;
            if(Boolean(hero0))
            {
               shield0 = hero0.shieldCtrl;
               this.shieldIcon.setCdPer(shield0.getCdPer());
               this.setShieldPer(shield0.getLifePer());
            }
            else
            {
               this.setShieldPer(0);
            }
            this.aiSp.visible = !dat0.playerCtrlB;
         }
      }
      
      public function inDataSecond(b0:IO_NormalBody) : void
      {
         var dat0:NormalBodyData = null;
         var hero0:HeroBody = null;
         var father0:IO_NormalBody = null;
         var vfather0:HeroBody = null;
         var shield0:ShieldCtrl = null;
         var stateArr0:Array = [];
         if(Boolean(b0))
         {
            dat0 = b0.getData();
            this.shieldIcon.visible = false;
            this.setExpPer(dat0.getNowExp() / dat0.getNextMaxExp());
            hero0 = b0 as HeroBody;
            father0 = b0.getTransCtrl().getFatherBody();
            vfather0 = b0.getData().vehicleFatherBody as HeroBody;
            if(Boolean(hero0))
            {
               if(dat0.isWeMainPlayerB())
               {
                  stateArr0 = Gaming.PG.da.getPlayerStateDataArr(Gaming.LG);
               }
               shield0 = hero0.shieldCtrl;
               this.shieldIcon.visible = shield0.haveB();
               this.shieldIcon.setIconName(shield0.getIconName24());
            }
            if(Boolean(father0))
            {
               this.setPossPer(father0.getTransCtrl().getUITimePer());
            }
            else if(Boolean(vfather0))
            {
               this.setPossPer(vfather0.vehicleCtrl.getDurationMul());
            }
            else
            {
               this.setPossPer(0);
            }
         }
         this.inPlayerStateDataArr(stateArr0);
      }
      
      private function setExpPer(per0:Number) : void
      {
         if(this._expPer != per0 && Boolean(this.expSp))
         {
            if(per0 < 0)
            {
               per0 = 0;
            }
            else if(per0 > 1)
            {
               per0 = 1;
            }
            this.expSp.scaleX = per0;
            this._expPer = per0;
         }
      }
      
      private function setShieldPer(per0:Number) : void
      {
         if(this._shieldPer != per0 && Boolean(this.shieldSp))
         {
            if(per0 < 0)
            {
               per0 = 0;
            }
            else if(per0 > 1)
            {
               per0 = 1;
            }
            this.shieldSp.scaleX = per0;
            this._shieldPer = per0;
         }
      }
      
      private function setPossPer(per0:Number) : void
      {
         if(this._possPer != per0 && Boolean(this.possSp))
         {
            if(per0 < 0)
            {
               per0 = 0;
            }
            else if(per0 > 1)
            {
               per0 = 1;
            }
            this.possSp.scaleX = per0;
            this._possPer = per0;
         }
      }
      
      private function inPlayerStateDataArr(arr0:Array) : void
      {
         var n:* = undefined;
         var grip0:ItemsGrid = null;
         var da0:StateIconData = null;
         var gripArr0:Array = this.stateBox.gripArr;
         var showNum0:int = 0;
         for(n in gripArr0)
         {
            grip0 = gripArr0[n];
            da0 = arr0[n];
            if(da0 is StateIconData)
            {
               grip0.visible = true;
               grip0.setIconName(da0.iconUrl);
               grip0.label = da0.name;
               grip0.setName(da0.getTimeStr());
               showNum0++;
            }
            else
            {
               grip0.visible = false;
            }
         }
         this.stateBox.visible = showNum0 > 0;
      }
   }
}

