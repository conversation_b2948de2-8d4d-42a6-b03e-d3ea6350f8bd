package UI.base.alert
{
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class InfoAlertBoard extends NormalUI
   {
      public var box:AlertBox;
      
      private var closeBtn:SimpleButton;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var titleTxt:TextField;
      
      private var gripTag:Sprite;
      
      private var imgMc:Sprite;
      
      private var btnSp:MovieClip;
      
      private var closeNoTipB:Boolean = true;
      
      private var guideProName:String = "";
      
      public function InfoAlertBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["closeBtn","gripTag","titleTxt","btnSp"];
         super.setImg(img0);
         FontDeal.dealOne(this.titleTxt);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("不再提示");
         this.btn.addEventListener(MouseEvent.CLICK,this.click);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function showInfo(title0:String, url0:String, guideProName0:String, btnName0:String = "", closeNoTipB0:Boolean = true) : void
      {
         var mc0:Sprite = Gaming.swfLoaderManager.getResourceFull(url0);
         this.showInfoBySp(title0,mc0,guideProName0,btnName0);
      }
      
      public function showInfoBySp(title0:String, mc0:Sprite, guideProName0:String, btnName0:String = "", closeNoTipB0:Boolean = true) : void
      {
         this.clearImgMc();
         this.btn.setName(btnName0 == "" ? "不再提示" : btnName0);
         this.box.showCheck("","");
         this.closeNoTipB = closeNoTipB0;
         show();
         this.titleTxt.text = title0;
         if(mc0 is Sprite)
         {
            addChild(mc0);
            mc0.x = this.gripTag.x;
            mc0.y = this.gripTag.y;
            this.imgMc = mc0;
            addChild(this.btn);
         }
         this.guideProName = guideProName0;
      }
      
      private function click(e:MouseEvent) : void
      {
         this.box.hide();
         this.setGuideBooble(true);
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.box.hide();
         if(this.closeNoTipB)
         {
            this.setGuideBooble(true);
         }
         this.guideProName = "";
      }
      
      private function setGuideBooble(bb0:Boolean) : void
      {
         if(this.guideProName != "")
         {
            Gaming.PG.save.guide[this.guideProName] = true;
         }
      }
      
      private function clearImgMc() : void
      {
         if(Boolean(this.imgMc))
         {
            removeChild(this.imgMc);
         }
         this.imgMc = null;
         this.guideProName = "";
      }
   }
}

