package w_test.cheating
{
   import UI.test.SaveTestBox;
   import dataAll._app.edit.boss.BossEditSkill;
   import dataAll._app.edit.card.BossCardCreator;
   import dataAll._app.worldMap.save.WorldMapSaveGroup;
   import dataAll.skill.HeroSkillData;
   
   public class SkillCheating extends OneCheating
   {
      public function SkillCheating()
      {
         super();
      }
      
      public function unlockSkill(str0:String, v0:int) : String
      {
         var s0:WorldMapSaveGroup = Gaming.PG.da.worldMap.saveGroup;
         s0.unlockOne("XiChi");
         s0.winOne("XiChi",0);
         Gaming.uiGroup.mainUI.fleshBtn();
         return "解锁技能系统成功";
      }
      
      public function clearNowSkill(str0:String, v0:int) : String
      {
         var da0:HeroSkillData = Gaming.uiGroup.skillUI.upgradeBox.nowData;
         if(Boolean(da0))
         {
            da0.heroSkillDataGroup.removeData(da0);
            return "删除技能：" + da0.save.getDefine().cnName;
         }
         return "找不到当前技能";
      }
      
      public function addProfi(str0:String, v0:int) : String
      {
         var da0:HeroSkillData = Gaming.uiGroup.skillUI.upgradeBox.nowData;
         if(Boolean(da0))
         {
            da0.addProfi(v0);
            return "技能" + da0.save.getDefine().cnName + "添加熟练度：" + v0;
         }
         return "找不到当前技能";
      }
      
      public function setProfi(str0:String, v0:int) : String
      {
         var da0:HeroSkillData = Gaming.uiGroup.skillUI.upgradeBox.nowData;
         if(Boolean(da0))
         {
            da0.save.profi = v0;
            return "技能" + da0.save.getDefine().cnName + "设置熟练度：" + v0;
         }
         return "找不到当前技能";
      }
      
      public function setDayProfi(str0:String, v0:int) : String
      {
         var da0:HeroSkillData = Gaming.uiGroup.skillUI.upgradeBox.nowData;
         if(Boolean(da0))
         {
            da0.save.dayProfi = v0;
            return "技能" + da0.save.getDefine().cnName + "设置今日熟练度：" + v0;
         }
         return "找不到当前技能";
      }
      
      public function getAllEnemySkillStr(str0:String, v0:int) : String
      {
         var s0:String = Gaming.defineGroup.skill.getAllEnemySkillStr();
         SaveTestBox.addText(s0);
         return "显示在M框中";
      }
      
      public function bossCardSkillConver(str0:String, v0:int) : String
      {
         var cnArr0:Array = str0.split(" ");
         var idArr0:Array = BossCardCreator.getIdArrBySkillCnArr(cnArr0);
         var s0:String = idArr0.toString();
         SaveTestBox.addText(s0);
         return "显示在M框中";
      }
      
      public function skillCnArrTo(str0:String, v0:int) : String
      {
         var cnArr0:Array = str0.split(",");
         var nameArr0:Array = BossEditSkill.cnArrToNameArr(cnArr0,"\"","\"");
         SaveTestBox.addText(String(nameArr0));
         return "M框：" + String(nameArr0);
      }
   }
}

