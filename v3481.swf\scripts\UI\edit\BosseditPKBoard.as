package UI.edit
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.bag.ItemsGripBox;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.StringMethod;
   import dataAll._app.edit.card.BCardPKCreator;
   import dataAll._app.edit.card.BossCardData;
   import dataAll._app.edit.card.BossCardDataGroup;
   import dataAll._app.edit.card.BossCardSave;
   import dataAll.level.define.LevelDefine;
   import dataAll.ui.GatherColor;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.geom.ColorTransform;
   import flash.text.TextField;
   
   public class BosseditPKBoard extends AutoNormalUI
   {
      private var cardTxt:TextField;
      
      private var filterBtn:NormalBtn;
      
      private var boxTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var titleTxt:TextField;
      
      private var weBtn:NormalBtn;
      
      private var enemyBtn:NormalBtn;
      
      private var fightBtn:NormalBtn;
      
      private var closeBtn:NormalBtn;
      
      private var coverSp:Sprite;
      
      private var battleBtn:NormalBtn;
      
      private var box:ItemsGripBox = new ItemsGripBox();
      
      private var enemyBox:ItemsGripBox = new ItemsGripBox();
      
      private var overDa:BossCardData = null;
      
      private var enemyDa:BossCardData = null;
      
      private var weDa:BossCardData = null;
      
      public function BosseditPKBoard()
      {
         super();
         mcTypeArr = ["tag","txt","btnSp"];
      }
      
      public static function inBigGripFun(grip0:NormalBtn, da0:BossCardData) : void
      {
         var enemyB0:Boolean = false;
         var s0:BossCardSave = null;
         var n0:String = null;
         grip0.itemsData = da0;
         if(Boolean(da0))
         {
            enemyB0 = da0.getFatherData() == null;
            s0 = da0.getCardSave();
            n0 = "<b>" + ComMethod.color(da0.getBodyDefine().cnName,GatherColor.blueColor) + "</b>";
            n0 += "\n\n" + StringMethod.concatStringArr(Gaming.defineGroup.skill.getCnArrByNameArr(da0.getSkillNameArr()),1);
            grip0.setName(n0);
            grip0.setIconName(da0.getIconUrl());
            grip0.setStarIcon(da0.getStar());
            grip0.setNumText(s0.li + "\n" + s0.dp);
            if(enemyB0)
            {
               if(da0.pkB)
               {
                  grip0.setNewMc("beat");
               }
               else
               {
                  grip0.setNew(false);
               }
            }
            else
            {
               grip0.setNew(false);
            }
         }
         else
         {
            grip0.clearShow();
            grip0.setName("");
            grip0.setNumText("");
            grip0.setNew(true);
         }
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         FontDeal.dealOne(this.cardTxt);
         FontDeal.dealLine(this.titleTxt);
         FontDeal.dealLine(this.coverSp["infoTxt"]);
         this.fightBtn.setName("PK");
         this.closeBtn.setName("返回");
         this.box.setIconPro("BosseditUI/cardBtn");
         this.box.arg.init(5,5,7,7);
         this.box.evt.setWant(true,true);
         addChild(this.box);
         NormalUICtrl.setTag(this.box,this.boxTag);
         this.box.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.box.addEventListener(ClickEvent.ON_OVER,this.gripOver);
         this.box.addEventListener(ClickEvent.ON_OUT,this.gripOut);
         this.box.pageBox.setToNormalBtn();
         this.box.setPagePos(this.pageTag);
         addChild(this.coverSp);
         this.enemyBox.setIconPro("BosseditUI/cardBtn");
         this.enemyBox.arg.init(6,3,20,16);
         this.enemyBox.evt.setWant(true,true);
         this.coverSp.addChild(this.enemyBox);
         this.enemyBox.x = 58;
         this.enemyBox.y = 70;
         this.enemyBox.addEventListener(ClickEvent.ON_CLICK,this.enemyGripClick);
         this.enemyBox.addEventListener(ClickEvent.ON_OVER,this.enemyGripOver);
         this.enemyBox.addEventListener(ClickEvent.ON_OUT,this.gripOut);
         this.battleBtn = new NormalBtn();
         this.battleBtn.setImg(this.coverSp["battleBtnSp"]);
         this.battleBtn.setName("斗魂卡");
         this.coverSp.addChild(this.battleBtn);
         this.battleBtn.addEventListener(MouseEvent.CLICK,this.battleBtnClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      protected function get dataG() : BossCardDataGroup
      {
         return Gaming.PG.da.bossCard;
      }
      
      public function outLoginEvent() : void
      {
         this.clearEnemyData();
      }
      
      override public function show() : void
      {
         super.show();
         this.overDa = null;
         this.fleshData();
         Gaming.uiGroup.bcardBattleBox.showByDownUI(this);
      }
      
      override public function hide() : void
      {
         super.hide();
         this.overDa = null;
      }
      
      private function fleshData() : void
      {
         this.fleshEnemy();
         this.fleshBox();
      }
      
      private function clearEnemyData() : void
      {
         this.weDa = null;
         this.enemyDa = null;
      }
      
      private function fleshEnemy() : void
      {
         var daArr0:Array = null;
         var winAll0:int = 0;
         var coverStr0:String = null;
         if(Boolean(this.weDa))
         {
            if(this.dataG.findDataIndex(this.weDa) == -1)
            {
               this.weDa = null;
            }
         }
         daArr0 = this.dataG.fleshEnemyPKArr();
         var win0:int = this.dataG.getPKWinNum();
         winAll0 = this.dataG.getPKWinAll();
         var weekNum0:int = this.dataG.getPKWeekNum();
         var enemyMax0:int = this.dataG.getPKEnemyMax();
         if(Boolean(this.enemyDa))
         {
            this.coverSp.visible = false;
            inBigGripFun(this.weBtn,this.weDa);
            this.weBtn.mouseEnabled = this.weDa;
            inBigGripFun(this.enemyBtn,this.enemyDa);
            this.enemyBtn.mouseEnabled = false;
            this.fightBtn.actived = this.weDa != null && this.weDa.pkB == false;
            if(this.enemyDa.pkB)
            {
               this.weBtn.mouseEnabled = false;
               this.fightBtn.visible = false;
               this.titleTxt.htmlText = ComMethod.color("获得" + this.enemyDa.getStar() + "次抽卡机会！",GatherColor.yellowColor,12);
            }
            else
            {
               this.titleTxt.htmlText = "第" + (win0 + 1) + "局";
               this.weBtn.mouseEnabled = true;
               this.fightBtn.visible = true;
            }
            this.cardTxt.htmlText = ComMethod.color("<b>选择魂卡</b>",GatherColor.yellowColor);
            this.box.transform.colorTransform = NormalBtn.normal_CF;
         }
         else
         {
            this.coverSp.visible = true;
            this.enemyBox.inData_byArr(daArr0,this.inBarFun);
            coverStr0 = "击败敌人魂卡，可获得与星级相等的抽卡次数。";
            coverStr0 += "本周剩余击败次数：<b>" + ComMethod.color(weekNum0 - win0 + "次","#00FF00",14) + "</b>。";
            coverStr0 += "\n1月13日开始，每击败1张8星魂卡，可获得" + BossCardDataGroup.GetPkStoneNum() + "颗聚魂石。";
            coverStr0 += "\n累计击败" + ComMethod.yellow(winAll0 + "") + "张魂卡";
            coverStr0 += "、获得" + ComMethod.yellow(this.dataG.getPKDrawAdd() + "") + "次抽卡机会";
            coverStr0 += "、获得" + ComMethod.yellow(this.dataG.getSaveG().pkStone + "") + "个聚魂石。";
            this.coverSp["infoTxt"].htmlText = FontDeal.getDealLeadingStr(this.coverSp["infoTxt"],coverStr0);
            this.cardTxt.htmlText = "我的魂卡";
            this.box.transform.colorTransform = new ColorTransform(0.5,0.5,0.5,1);
         }
      }
      
      private function enemyGripClick(e:ClickEvent) : void
      {
         var win0:int = 0;
         var weekNum0:int = 0;
         var da0:BossCardData = e.childData as BossCardData;
         if(da0.pkB)
         {
            UIOrder.alertError("你已经击败了该魂卡。");
         }
         else
         {
            win0 = this.dataG.getPKWinNum();
            weekNum0 = this.dataG.getPKWeekNum();
            if(win0 >= weekNum0)
            {
               UIOrder.alertError("本周次数已用完。");
            }
            else
            {
               this.enemyDa = da0;
               this.fleshEnemy();
            }
         }
      }
      
      private function enemyGripOver(e:ClickEvent) : void
      {
         var da0:BossCardData = e.childData as BossCardData;
         var tip0:String = da0.getGatherTip(false,false);
         UIOrder.showTip(tip0);
         this.overDa = da0;
      }
      
      private function fleshBox() : void
      {
         var daArr0:Array = this.dataG.getPKArr();
         this.box.inData_byArr(daArr0,this.inBarFun);
         this.filterBtn.setStarIcon(this.dataG.getStarFilter());
      }
      
      private function inBarFun(grip0:NormalBtn, da0:BossCardData) : void
      {
         var enemyB0:Boolean = false;
         grip0.itemsData = da0;
         if(Boolean(da0))
         {
            enemyB0 = da0.getFatherData() == null;
            grip0.setIconName(da0.getIconUrl());
            grip0.setStarIcon(da0.getStar());
            grip0.actived = true;
            if(enemyB0)
            {
               grip0.setSmallIcon(da0.pkB ? "beat" : "");
            }
            else if(da0.pkB)
            {
               grip0.setSmallIcon("use");
            }
            else
            {
               grip0.setSmallIcon(this.weDa == da0 ? "fight" : "");
            }
         }
         else
         {
            grip0.clearShow();
            grip0.setStarIcon("");
         }
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var da0:BossCardData = e.childData as BossCardData;
         if(Boolean(this.enemyDa) && Boolean(da0))
         {
            if(da0.pkB == false)
            {
               if(this.enemyDa.pkB == false)
               {
                  if(this.weDa == da0)
                  {
                     this.weDa = null;
                  }
                  else
                  {
                     this.weDa = da0;
                  }
                  this.fleshEnemy();
                  this.fleshBox();
               }
            }
         }
      }
      
      private function gripOver(e:ClickEvent) : void
      {
         var da0:BossCardData = e.childData as BossCardData;
         var tip0:String = da0.getGatherTip(false,false);
         UIOrder.showTip(tip0);
         this.overDa = da0;
      }
      
      private function gripOut(e:* = null) : void
      {
         this.overDa = null;
         UIOrder.showTip("");
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var funName0:String = btn0.label + "BtnClick";
         this[funName0](e);
      }
      
      private function filterBtnClick(e:MouseEvent) : void
      {
         this.dataG.swapStarFilter();
         this.fleshBox();
      }
      
      private function weBtnClick(e:MouseEvent) : void
      {
         if(Boolean(this.weDa) && this.weDa.pkB == false)
         {
            this.weDa = null;
            this.fleshEnemy();
            this.fleshBox();
         }
      }
      
      private function enemyBtnClick(e:MouseEvent) : void
      {
      }
      
      private function closeBtnClick(e:MouseEvent) : void
      {
         this.clearEnemyData();
         this.fleshEnemy();
         this.fleshBox();
      }
      
      public function FKey() : void
      {
         var tip0:String = null;
         if(visible && Boolean(this.overDa))
         {
            tip0 = this.overDa.getFTip();
            UIOrder.showTip(tip0);
         }
      }
      
      private function fightBtnClick(e:MouseEvent) : void
      {
         Gaming.uiGroup.alertBox.showChoose("是否要进行魂卡PK？",this.gotoMap);
      }
      
      private function gotoMap() : void
      {
         var d0:LevelDefine = null;
         if(Boolean(this.weDa) && Boolean(this.enemyDa))
         {
            d0 = Gaming.defineGroup.level.getDefineBy(BCardPKCreator.bossCardPK);
            this.dataG.startPK(this.weDa,this.enemyDa);
            Gaming.LG.chooseByLevelDefine(d0);
         }
         else
         {
            UIOrder.alertError("数据丢失：" + this.weDa + " " + this.enemyDa);
         }
      }
      
      private function battleBtnClick(e:MouseEvent) : void
      {
         Gaming.uiGroup.bcardBattleBox.showCodeInput(this);
      }
   }
}

