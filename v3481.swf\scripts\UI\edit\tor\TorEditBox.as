package UI.edit.tor
{
   import UI.UIOrder;
   import UI.base.AutoNormalUI;
   import UI.base.alert.AlertBox;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import UI.base.scroll.NormalScrollBar;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._app.edit.TorData;
   import dataAll._app.edit._def.EditProDefine;
   import dataAll._app.edit._def.EditProMethod;
   import dataAll._app.edit.arms.ArmsTorData;
   import dataAll._app.edit.boss.BossEditSkill;
   import dataAll._app.edit.list.EditListAgent;
   import dataAll._app.edit.tor.IO_TorEditDefine;
   import dataAll._app.edit.tor.TorEditAgent;
   import dataAll._base.IO_Define;
   import dataAll._base.IO_TipDefine;
   import dataAll.image.ImageUrlDefine;
   import dataAll.pro.ProType;
   import dataAll.ui.GatherColor;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class TorEditBox extends AutoNormalUI
   {
      private var proTxt:TextField;
      
      private var valueTxt:TextField;
      
      private var lineMax:int = 0;
      
      private var lineBtn:NormalBtn;
      
      private var maskTargetSp:Sprite;
      
      private var scrollBarSp:Sprite;
      
      private var scrollLineSp:Sprite;
      
      private var scrollBar:NormalScrollBar;
      
      private var scrollCon:Sprite = new Sprite();
      
      private var agent:TorEditAgent = null;
      
      private var mouseLine:int = -1;
      
      private var nowPro:EditProDefine = null;
      
      private var nowChild:IO_TorEditDefine = null;
      
      public function TorEditBox()
      {
         super();
         mcTypeArr = ["txt","btnSp"];
      }
      
      private function get alertBox() : AlertBox
      {
         return Gaming.uiGroup.alertBox;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         addChild(this.scrollCon);
         this.scrollCon.addChild(this.valueTxt);
         this.valueTxt.htmlText = "";
         this.scrollCon.addChild(this.proTxt);
         this.proTxt.htmlText = "";
         this.scrollCon.addChild(this.lineBtn);
         this.lineBtn.visible = false;
         this.lineBtn.addEventListener(MouseEvent.CLICK,this.lineClick);
         this.lineBtn.addEventListener(MouseEvent.MOUSE_OUT,this.mouseOut);
         this.scrollCon.addEventListener(MouseEvent.MOUSE_MOVE,this.mouseMove,true);
         TextMethod.setAutoFormat(this.proTxt);
         TextMethod.setAutoFormat(this.valueTxt);
         FontDeal.dealLine(this.proTxt);
         FontDeal.dealLine(this.valueTxt);
         this.scrollBar = new NormalScrollBar(this.scrollCon,this.maskTargetSp,this.scrollBarSp,this.scrollLineSp,1,false,true,true);
         this.scrollBar.speed = 30;
         this.scrollBar.refresh();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         this[str0] = value0;
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      public function outLoginEvent() : void
      {
         this.clearData();
      }
      
      public function clearData() : void
      {
         this.clearImgAndAgent();
         this.nowPro = null;
         this.nowChild = null;
      }
      
      public function clearImgAndAgent() : void
      {
         this.lineMax = 0;
         this.proTxt.htmlText = "";
         this.valueTxt.htmlText = "";
         if(Boolean(this.agent))
         {
            this.agent.clearDataArr();
         }
         this.agent = null;
         this.clearLine();
      }
      
      public function setScrollPer(per0:Number) : void
      {
         this.scrollBar.setPer(per0);
      }
      
      public function inAgentAndClear(a0:TorEditAgent) : void
      {
         this.clearData();
         this.inAgent(a0);
      }
      
      private function inAgent(a0:TorEditAgent) : void
      {
         var ui0:IO_TorEditDefine = null;
         var pro0:String = null;
         var value0:String = null;
         var objHaveB0:Boolean = false;
         var canChangeB0:Boolean = false;
         var color0:String = null;
         var d0:EditProDefine = null;
         this.agent = a0;
         var da0:TorData = a0.da;
         var defArr0:Array = a0.getDefArr();
         var proStr0:String = "";
         var valueStr0:String = "";
         this.lineMax = 0;
         for each(ui0 in defArr0)
         {
            pro0 = "";
            value0 = "";
            objHaveB0 = false;
            canChangeB0 = false;
            color0 = "";
            d0 = ui0 as EditProDefine;
            if(Boolean(d0))
            {
               objHaveB0 = da0.getObjValue(d0) != null;
               canChangeB0 = this.agent.getChangeB(d0);
               color0 = d0.color;
               pro0 = "  " + da0.getUICn(d0);
               value0 = da0.getUIValue(d0);
            }
            else
            {
               d0 = ui0.getParent() as EditProDefine;
               if(Boolean(d0))
               {
                  objHaveB0 = da0.getObjValue(d0) != null;
                  canChangeB0 = this.agent.getChangeB(d0);
                  pro0 = "";
                  value0 = ui0.getCnName();
               }
               else
               {
                  pro0 = ui0.getCnName();
                  value0 = "";
                  color0 = GatherColor.orangenessColor;
               }
            }
            if(color0 == "")
            {
               color0 = objHaveB0 ? GatherColor.bloveColor : "";
               if(canChangeB0 == false)
               {
                  color0 = GatherColor.graydarkColor;
               }
            }
            if(color0 != "")
            {
               if(pro0 != "")
               {
                  pro0 = ComMethod.color(pro0,color0);
               }
               if(value0 != "")
               {
                  value0 = ComMethod.color(value0,color0);
               }
            }
            proStr0 += "\n" + pro0;
            valueStr0 += "\n" + value0;
            ++this.lineMax;
         }
         proStr0 += "\n  ";
         this.lineMax += 2;
         this.proTxt.htmlText = FontDeal.getDealLeadingStr(this.proTxt,proStr0);
         this.valueTxt.htmlText = FontDeal.getDealLeadingStr(this.valueTxt,valueStr0);
         this.scrollBar.refresh();
      }
      
      private function fleshNow(agentFleshB0:Boolean = false) : void
      {
         var new0:TorEditAgent = null;
         if(Boolean(this.agent))
         {
            if(agentFleshB0)
            {
               new0 = this.agent.getNewMe();
               this.clearImgAndAgent();
               this.inAgent(new0);
            }
            else
            {
               this.inAgent(this.agent);
            }
         }
         else
         {
            this.lineMax = 0;
            this.proTxt.htmlText = "";
            this.valueTxt.htmlText = "";
            this.scrollBar.refresh();
         }
      }
      
      public function clearLine() : void
      {
         this.lineBtn.visible = false;
         this.mouseLine = -1;
      }
      
      private function getMouseDef() : IO_TorEditDefine
      {
         var index0:int = 0;
         var proD0:IO_TorEditDefine = null;
         if(Boolean(this.agent))
         {
            index0 = this.mouseLine - 1;
            return this.agent.getUIData(index0);
         }
         return null;
      }
      
      private function lineClick(e:MouseEvent) : void
      {
         var d0:IO_TorEditDefine = null;
         var da0:TorData = null;
         var proD0:EditProDefine = null;
         var title0:String = null;
         var method0:String = null;
         var v0:* = undefined;
         var stringAgent0:EditListAgent = null;
         var type0:String = null;
         var num0:Number = NaN;
         var min0:Number = NaN;
         var max0:Number = NaN;
         var imgD0:ImageUrlDefine = null;
         if(Boolean(this.agent))
         {
            d0 = this.getMouseDef();
            if(Boolean(d0))
            {
               if(this.agent.getChangeBIO(d0))
               {
                  da0 = this.agent.da;
                  proD0 = d0 as EditProDefine;
                  if(Boolean(proD0))
                  {
                     this.nowPro = proD0;
                     title0 = "修改属性 " + ComMethod.color(proD0.cnName,"#00FF00") + " 为：";
                     method0 = proD0.method;
                     v0 = da0.getValue(proD0);
                     stringAgent0 = EditProMethod.getEditAgentByStringMethod(proD0.method,v0,proD0.cnName);
                     if(Boolean(stringAgent0))
                     {
                        stringAgent0.linkFun = this.methodStringYesFun;
                        Gaming.uiGroup.editList.showAgent(stringAgent0);
                     }
                     else if(method0 == EditProMethod.color)
                     {
                        this.colorChoose(v0 as String);
                     }
                     else if(method0 == EditProMethod.skill)
                     {
                        this.skillChoose(proD0,v0);
                     }
                     else if(method0 == EditProMethod.sound)
                     {
                        this.soundChoose(proD0,v0);
                     }
                     else if(method0 == EditProMethod.armsCn)
                     {
                        this.armsCnChoose();
                     }
                     else
                     {
                        type0 = proD0.type;
                        if(type0 == ProType.BOOLEAN)
                        {
                           this.booleanYesFun();
                        }
                        else if(ProType.isNum(type0))
                        {
                           num0 = v0;
                           min0 = da0.getNumberMin(proD0);
                           max0 = da0.getNumberMax(proD0);
                           title0 += "\n（范围" + min0 + "~" + max0 + "）";
                           this.alertBox.showNumChoose(title0,num0,max0,min0,proD0.getNumGap(),this.numberYesFun,"yesAndNo",null,null,false,proD0.fixed);
                        }
                        else if(type0 != ProType.STRING)
                        {
                           imgD0 = v0 as ImageUrlDefine;
                           if(Boolean(imgD0))
                           {
                              this.imageChoose(proD0,imgD0);
                           }
                        }
                     }
                  }
                  else
                  {
                     proD0 = d0.getParent() as EditProDefine;
                     if(Boolean(proD0))
                     {
                        this.childClick(proD0,d0);
                     }
                  }
               }
            }
         }
      }
      
      private function methodStringYesFun(str0:String) : void
      {
         this.stringYesFun(str0);
      }
      
      private function booleanYesFun() : void
      {
         if(!this.nowPro || !this.agent)
         {
            return;
         }
         var v0:Boolean = this.agent.da.getValue(this.nowPro);
         var bb0:Boolean = this.agent.da.changeValue(this.nowPro,!v0);
         this.changeFunAndShowError(bb0);
      }
      
      private function numberYesFun(num0:Number) : void
      {
         if(!this.nowPro || !this.agent)
         {
            return;
         }
         var v0:Number = this.nowPro.fixedNumber(num0);
         var bb0:Boolean = this.agent.da.changeValue(this.nowPro,v0);
         this.changeFunAndShowError(bb0);
      }
      
      private function stringYesFun(str0:String) : void
      {
         if(!this.nowPro || !this.agent)
         {
            return;
         }
         var bb0:Boolean = this.agent.da.changeValue(this.nowPro,str0);
         this.changeFunAndShowError(bb0);
      }
      
      private function armsCnChoose() : void
      {
         var a0:EditListAgent = null;
         if(Gaming.testCtrl.cheating.enabled)
         {
            this.alertBox.textInput.showTextInput("输入前缀","",this.armsCnYesFun);
         }
         else
         {
            a0 = BossEditSkill.getCnAgent();
            a0.linkFun = this.armsCnYesFun;
            Gaming.uiGroup.editList.showAgent(a0);
         }
      }
      
      private function armsCnYesFun(cn0:String) : void
      {
         if(!this.nowPro || !this.agent)
         {
            return;
         }
         var da0:ArmsTorData = this.agent.da as ArmsTorData;
         cn0 += da0.getBaseDef().cnName;
         var bb0:Boolean = this.agent.da.changeValue(this.nowPro,cn0);
         this.changeFunAndShowError(bb0);
      }
      
      private function colorChoose(uintColor0:String) : void
      {
         var a0:EditListAgent = GatherColor.getColorListAgent(uintColor0);
         a0.info = "选择颜色代码";
         a0.linkFun = this.colorYesFun;
         Gaming.uiGroup.editList.showAgent(a0);
      }
      
      private function colorYesFun(uintColor0:String) : void
      {
         if(!this.nowPro || !this.agent)
         {
            return;
         }
         if(uintColor0 == GatherColor.zero)
         {
            uintColor0 = "";
         }
         var bb0:Boolean = this.agent.da.changeValue(this.nowPro,uintColor0);
         this.changeFunAndShowError(bb0);
      }
      
      private function soundChoose(proD0:EditProDefine, nowUrl0:String) : void
      {
         var a0:EditListAgent = Gaming.defineGroup.sound.getEditAgent(proD0,nowUrl0,this.agent.da);
         a0.info = "选择音效";
         a0.linkFun = this.soundYesFun;
         a0.tipFun = this.ioDefineTipFun;
         Gaming.uiGroup.editList.showAgent(a0);
      }
      
      private function soundYesFun(url0:String) : void
      {
         if(!this.nowPro || !this.agent)
         {
            return;
         }
         var bb0:Boolean = this.agent.da.changeValue(this.nowPro,url0);
         this.changeFunAndShowError(bb0);
      }
      
      private function imageChoose(proD0:EditProDefine, nowD0:ImageUrlDefine) : void
      {
         var a0:EditListAgent = Gaming.defineGroup.imageUrl.getEditAgent(proD0,nowD0,this.agent.da);
         a0.info = "选择模型";
         a0.linkFun = this.imgYesFun;
         a0.tipFun = this.ioDefineTipFun;
         Gaming.uiGroup.editList.showAgent(a0);
      }
      
      private function imgYesFun(name0:String) : void
      {
         if(!this.nowPro || !this.agent)
         {
            return;
         }
         var d0:ImageUrlDefine = Gaming.defineGroup.imageUrl.getDefine(name0);
         var bb0:Boolean = this.agent.da.changeImageUrlDefine(this.nowPro,d0);
         this.changeFunAndShowError(bb0,this.nowPro.name == "bulletImg");
      }
      
      private function skillChoose(proD0:EditProDefine, nowArr0:Array) : void
      {
         var a0:EditListAgent = null;
         var nowNum0:int = 0;
         if(Boolean(nowArr0))
         {
            nowNum0 = int(nowArr0.length);
         }
         if(nowNum0 >= proD0.max)
         {
            this.alertBox.showError("最多只能添加" + proD0.max + "个");
         }
         else
         {
            a0 = Gaming.defineGroup.skill.getEditAgent(proD0,this.agent.da,nowArr0);
            a0.info = "添加技能（" + nowNum0 + "/" + proD0.max + "）";
            a0.linkFun = this.skillYesFun;
            a0.tipFun = this.ioDefineTipFun;
            Gaming.uiGroup.editList.showAgent(a0);
         }
      }
      
      private function skillYesFun(name0:String) : void
      {
         if(!this.nowPro || !this.agent)
         {
            return;
         }
         var now0:Array = this.agent.da.getValue(this.nowPro);
         now0 = now0.concat();
         ArrayMethod.addNoRepeatInArr(now0,name0);
         var bb0:Boolean = this.agent.da.changeValue(this.nowPro,now0);
         this.changeFunAndShowError(bb0,true);
      }
      
      private function childClick(proD0:EditProDefine, child0:IO_TorEditDefine) : void
      {
         this.nowPro = proD0;
         this.nowChild = child0;
         if(proD0.isEditArray())
         {
            this.alertBox.showChoose("是否删除" + proD0.cnName + "：" + ComMethod.color(child0.getCnName(),GatherColor.greenColor),this.delArrayYesFun);
         }
      }
      
      private function delArrayYesFun() : void
      {
         var bb0:Boolean = false;
         if(!this.nowPro || !this.agent || !this.nowChild)
         {
            return;
         }
         var before0:Array = this.agent.da.getValue(this.nowPro);
         before0 = before0.concat();
         var delNum0:int = ArrayMethod.remove(before0,this.nowChild.getName());
         if(delNum0 >= 0)
         {
            bb0 = this.agent.da.changeValue(this.nowPro,before0);
            this.changeFunAndShowError(bb0,true);
         }
         else
         {
            this.alertBox.showError("该技能不存在！");
         }
         this.nowChild = null;
      }
      
      private function changeFunAndShowError(successB0:Boolean, agentFleshB0:Boolean = false, fleshB0:Boolean = true) : void
      {
         if(successB0 == false)
         {
            UIOrder.alertError("修改属性失败，可能是属性值没有变化。");
         }
         else
         {
            if(fleshB0)
            {
               this.fleshNow(agentFleshB0);
            }
            if(this.agent.changeFun is Function && Boolean(this.nowPro))
            {
               this.agent.changeFun(this.nowPro,this.nowChild);
            }
         }
      }
      
      private function ioDefineTipFun(d0:IO_Define) : void
      {
         var tip0:String = null;
         var td0:IO_TipDefine = d0 as IO_TipDefine;
         if(Boolean(td0))
         {
            tip0 = td0.getTipStr();
            UIOrder.showTip(tip0);
         }
      }
      
      private function txtOut() : void
      {
         this.mouseLine = -1;
         this.lineBtn.visible = false;
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function txtOver() : void
      {
         var tip0:String = null;
         var d0:IO_TorEditDefine = null;
         if(Gaming.uiGroup.alertBox.visible == false)
         {
            tip0 = "";
            d0 = this.getMouseDef();
            if(Boolean(d0))
            {
               tip0 = this.agent.da.getProTip(d0);
            }
            UIOrder.showTip(tip0);
         }
      }
      
      private function txtLine(line0:int, lineY0:int) : void
      {
         var proD0:EditProDefine = null;
         var small0:String = null;
         var d0:IO_TorEditDefine = this.getMouseDef();
         if(Boolean(d0) && this.agent.getChangeBIO(d0))
         {
            proD0 = d0 as EditProDefine;
            small0 = "";
            this.lineBtn.visible = true;
            this.lineBtn.y = lineY0;
            if(Boolean(proD0))
            {
               if(proD0.isEditArray())
               {
                  small0 = "add";
               }
               else
               {
                  small0 = "edit";
               }
            }
            else
            {
               small0 = "del";
            }
            this.lineBtn.setSmallIcon(small0);
         }
         else
         {
            this.lineBtn.visible = false;
         }
      }
      
      private function mouseOut(e:MouseEvent) : void
      {
         this.txtOut();
      }
      
      private function mouseMove(e:MouseEvent) : void
      {
         var txtMouseY0:int = 0;
         var leading0:int = 0;
         var lineMax0:int = 0;
         var lineH0:Number = NaN;
         var line0:int = 0;
         var lineY0:int = 0;
         if(visible == false)
         {
            return;
         }
         var beforeLine0:int = this.mouseLine;
         var x0:int = this.maskTargetSp.x + 40;
         var y0:int = this.maskTargetSp.y;
         var right0:int = x0 + this.maskTargetSp.width - 60;
         var down0:int = y0 + this.maskTargetSp.height;
         var mx0:Number = mouseX;
         var my0:Number = mouseY;
         if(mx0 > x0 && mx0 < right0 && my0 > y0 && my0 < down0)
         {
            txtMouseY0 = this.proTxt.mouseY;
            leading0 = (this.proTxt.defaultTextFormat.leading as int) + FontDeal.leadGap;
            lineMax0 = this.lineMax;
            if(lineMax0 == 0)
            {
               lineMax0 = 1;
            }
            lineH0 = (this.proTxt.height + leading0) / lineMax0;
            line0 = (txtMouseY0 - this.proTxt.y + leading0 / 2) / lineH0;
            if(line0 < 0)
            {
               line0 = -1;
               this.mouseLine = line0;
            }
            else if(this.mouseLine != line0)
            {
               this.mouseLine = line0;
               lineY0 = line0 * lineH0 + y0 - leading0 / 2;
               this.txtLine(line0,lineY0);
            }
         }
         else
         {
            this.mouseLine = -1;
         }
         if(beforeLine0 != this.mouseLine)
         {
            if(this.mouseLine == -1)
            {
               this.txtOut();
            }
            else
            {
               this.txtOver();
            }
         }
      }
   }
}

