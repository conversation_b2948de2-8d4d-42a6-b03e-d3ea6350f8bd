package w_test.cheating
{
   import UI.pay.PayCtrl;
   import UI.test.SaveTestBox;
   import w_test.count.TestCount_pay;
   
   public class PayCheating extends OneCheating
   {
      public function PayCheating()
      {
         super();
      }
      
      public function testYue(str0:String, v0:int) : String
      {
         Gaming.api.shop.test_balance = v0;
         return "设置黄金" + v0;
      }
      
      public function testChong<PERSON>hi(str0:String, v0:int) : String
      {
         Gaming.api.shop.test_totalRecharged = v0;
         return "设置黄金累计充值" + v0;
      }
      
      public function pay(str0:String, v0:int) : String
      {
         Gaming.api.shop.payMoney(v0);
         PayCtrl.getBalance();
         return "充值：" + v0;
      }
      
      public function showPayCount(str0:String, v0:int) : String
      {
         var str2:String = TestCount_pay.showOnePropsCountObj(Gaming.PG.save.pay.obj);
         SaveTestBox.addText(str2);
         return "显示在M框里";
      }
   }
}

