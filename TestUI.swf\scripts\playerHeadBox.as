package
{
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol674")]
   public dynamic class playerHeadBox extends MovieClip
   {
      public var iconTag:MovieClip;
      
      public var stateTag:MovieClip;
      
      public var lifeSp:MovieClip;
      
      public var bbsBtnSp:MovieClip;
      
      public var levelTxt:TextField;
      
      public var nameTxt:TextField;
      
      public function playerHeadBox()
      {
         super();
      }
   }
}

