package UI.edit
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.grid.NormalGrid;
   import UI.base.grid.NormalGridIcon;
   import UI.edit.tor.TorEditBox;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.NumberMethod;
   import dataAll._app.edit._def.EditProDefine;
   import dataAll._app.edit.arms.ArmsEditMethod;
   import dataAll._app.edit.arms.ArmsTorData;
   import dataAll._app.edit.arms.ArmsTorDataGroup;
   import dataAll._app.edit.list.EditListAgent;
   import dataAll._app.edit.tor.IO_TorEditDefine;
   import dataAll._app.edit.tor.TorEditAgent;
   import dataAll._app.edit.tor.TorEditMethod;
   import dataAll._base.IO_Define;
   import dataAll.arms.ArmsData;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.level.define.LevelDefine;
   import dataAll.ui.GatherColor;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.system.System;
   import flash.text.TextField;
   
   public class BosseditArmsBoard extends AutoNormalUI
   {
      private var gripBox:ItemsGripBox = new ItemsGripBox();
      
      private var gripTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var iconTag:Sprite;
      
      private var iconCon:NormalGridIcon = new NormalGridIcon();
      
      private var pnTxt:TextField;
      
      private var addBtn:NormalBtn;
      
      private var addCodeBtn:NormalBtn;
      
      private var gotoBtn:NormalBtn;
      
      private var mapBtn:NormalBtn;
      
      private var mainBtn:NormalBtn;
      
      private var delBtn:NormalBtn;
      
      private var codeBtn:NormalBtn;
      
      private var jsonBtn:NormalBtn;
      
      private var hideSp:Sprite;
      
      private var hideBtn:NormalBtn;
      
      private var torSp:Sprite;
      
      private var torBox:TorEditBox = new TorEditBox();
      
      private var nowData:ArmsTorData = null;
      
      private var nowDiff:int = 1;
      
      public function BosseditArmsBoard()
      {
         super();
         mcTypeArr = ["tag","txt","btnSp"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         this.torBox.setImg(this.torSp);
         addChild(this.torBox);
         FontDeal.dealOne(this.pnTxt);
         this.gripBox.setIconPro("BosseditUI/armsGrip");
         this.gripBox.arg.init(1,6,0,0,false);
         this.gripBox.evt.setWant(true);
         addChild(this.gripBox);
         NormalUICtrl.setTag(this.gripBox,this.gripTag);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.gripBox.pageBox.setToSmall();
         this.gripBox.setPagePos(this.pageTag);
         this.iconTag.addChild(this.iconCon);
         this.iconTag.mouseChildren = false;
         this.iconTag.addEventListener(MouseEvent.MOUSE_OVER,this.iconOver);
         this.iconTag.addEventListener(MouseEvent.MOUSE_OUT,this.iconOut);
         this.pnTxt.htmlText = "";
         this.addBtn.setName("添加武器");
         this.addCodeBtn.setName("添加代码");
         this.gotoBtn.setName("测试");
         this.mapBtn.setName("测试\n地图");
         this.codeBtn.setName("分享");
         this.codeBtn.setSmallIcon("blue");
         this.codeBtn.activedAndEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.codeBtn);
         this.jsonBtn.setName("Json");
         this.jsonBtn.setSmallIcon("blue");
         this.mainBtn.activedAndEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.mainBtn);
         this.delBtn.setName("删除");
         this.hideBtn.setName("隐藏高级属性");
         addChild(this.hideSp);
         addChild(this.hideBtn);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function get dataG() : ArmsTorDataGroup
      {
         return Gaming.PG.da.armsTor;
      }
      
      public function outLoginEvent() : void
      {
         this.nowData = null;
         this.torBox.outLoginEvent();
      }
      
      private function clearShow() : void
      {
         var btn0:NormalBtn = null;
         this.torBox.visible = false;
         this.torBox.clearData();
         this.iconCon.clearData();
         for each(btn0 in btnObj)
         {
            btn0.visible = false;
         }
         this.addBtn.visible = true;
         this.addCodeBtn.visible = true;
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
         this.torBox.clearLine();
      }
      
      private function fleshData() : void
      {
         var arr0:Array = this.dataG.getReverseDataArr();
         this.gripBox.inData_byArr(arr0,this.gripFun);
         if(Boolean(this.nowData) && arr0.indexOf(this.nowData) == -1)
         {
            this.nowData = null;
         }
         if(!this.nowData && arr0.length >= 1)
         {
            this.nowData = arr0[0];
         }
         this.fleshChooseData();
      }
      
      private function fleshBoxUI() : void
      {
         var grip0:ItemsGrid = null;
         for each(grip0 in this.gripBox.gripArr)
         {
            this.gripFun(grip0,grip0.itemsData as ArmsTorData);
         }
      }
      
      private function fleshNowGripUI() : void
      {
         var grip0:ItemsGrid = null;
         if(Boolean(this.nowData))
         {
            grip0 = this.gripBox.findGripByData(this.nowData);
            if(Boolean(grip0))
            {
               this.gripFun(grip0,grip0.itemsData as ArmsTorData);
            }
         }
      }
      
      private function gripFun(grip0:NormalGrid, da0:ArmsTorData) : void
      {
         grip0.itemsData = da0;
         var mainB0:Boolean = this.dataG.mainPan(da0);
         var armsD0:ArmsDefine = da0.getArmsDef();
         if(Boolean(armsD0))
         {
            grip0.setName(da0.getCnName());
            grip0.setIconName(da0.getIconUrl());
            grip0.setSmallIcon(da0.getIconSmallLabel(mainB0));
            grip0.getIcon().scaleX = 0.6;
            grip0.getIcon().scaleY = 0.6;
         }
         else
         {
            grip0.setName("无");
            grip0.setIconName("");
         }
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         this.nowData = e.childData as ArmsTorData;
         INIT.TRACE(this.nowData.getTorSave().name);
         this.fleshChooseData();
      }
      
      private function addClick(e:MouseEvent) : void
      {
         var a0:EditListAgent = null;
         var error0:String = this.dataG.addPan();
         if(error0 == "")
         {
            if(Gaming.isLoginAndHaveUid() == false)
            {
               error0 = "无法创建武器，请重新登录！";
            }
         }
         if(error0 == "")
         {
            a0 = ArmsEditMethod.getArmsListAgent();
            a0.linkFun = this.addLink;
            a0.tipFun = this.addTip;
            Gaming.uiGroup.editList.showAgent(a0);
         }
         else
         {
            UIOrder.alertError(error0);
         }
      }
      
      private function addTip(nd0:IO_Define) : void
      {
      }
      
      private function addLink(name0:String) : void
      {
         this.nowData = this.dataG.newArmsData(name0,Gaming.PG.getUidx());
         this.fleshData();
      }
      
      private function addCodeClick(e:MouseEvent) : void
      {
         var error0:String = this.dataG.addPan();
         if(error0 == "")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("请输入武器代码","",this.yesAddCode,"yesAndNo",10000);
         }
         else
         {
            UIOrder.alertError(error0);
         }
      }
      
      private function yesAddCode(code0:String) : void
      {
         var error0:String = null;
         var obj0:Object = TorEditMethod.codeToObjAndPan(code0);
         if(obj0 is String)
         {
            UIOrder.alertError(obj0 as String);
         }
         else
         {
            error0 = this.dataG.panCodeObj(obj0);
            if(error0 != "")
            {
               UIOrder.alertError(error0);
            }
            else
            {
               this.nowData = this.dataG.addDataByCode(code0,obj0);
               this.fleshData();
            }
         }
      }
      
      private function fleshChooseData() : void
      {
         var da0:ArmsTorData = null;
         var armsD0:ArmsDefine = null;
         var pn0:String = null;
         da0 = this.nowData;
         if(da0 == null)
         {
            this.gripBox.setChoose_byIndex(-1);
            this.clearShow();
         }
         else
         {
            this.torBox.visible = true;
            this.gripBox.setChooseByItemsData(da0);
            if(da0.haveDataB())
            {
               da0 = this.nowData;
               armsD0 = da0.getArmsDef();
               this.iconCon.setIconName(armsD0.getBookIconUrl());
               pn0 = da0.getTorSave().pn;
               if(pn0 != "")
               {
                  this.pnTxt.htmlText = ComMethod.color("作者 ",GatherColor.gray2Color) + pn0;
               }
               else
               {
                  this.pnTxt.htmlText = "";
               }
               this.fleshTorBox();
               this.fleshBtnMustHave();
               this.fleshNowGripUI();
            }
            else
            {
               this.clearShow();
               this.delBtn.visible = true;
            }
         }
      }
      
      private function fleshTorBox() : void
      {
         var a0:TorEditAgent = null;
         if(Boolean(this.nowData))
         {
            a0 = this.nowData.getTorEditAgent();
            a0.changeFun = this.changeFun;
            this.torBox.inAgentAndClear(a0);
         }
      }
      
      private function fleshBtnMustHave() : void
      {
         var mainB0:Boolean = this.dataG.mainPan(this.nowData);
         this.delBtn.visible = true;
         this.gotoBtn.visible = true;
         this.codeBtn.visible = true;
         this.jsonBtn.visible = Gaming.testCtrl.canCheatingB();
         this.mapBtn.visible = this.jsonBtn.visible;
         this.hideBtn.isChosen = this.dataG.hideHighB;
         this.hideBtn.visible = true;
         this.mainBtn.actived = !this.nowData.havePn();
         this.mainBtn.visible = true;
         this.mainBtn.tipString = "";
         if(mainB0)
         {
            this.mainBtn.setSmallIcon("yellowYes");
            this.mainBtn.setName("主力",true,true);
         }
         else
         {
            this.mainBtn.setSmallIcon("yellowHole");
            if(this.mainBtn.actived)
            {
               this.mainBtn.tipString = ArmsEditMethod.getMainLimitTip(this.nowData);
            }
            this.mainBtn.setName("设为\n主力",true,true);
         }
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0.actived)
         {
            if(btn0 == this.addBtn || btn0 == this.addCodeBtn)
            {
               this[btn0.label + "Click"](e);
            }
            else if(Boolean(this.nowData))
            {
               this[btn0.label + "Click"](e);
            }
         }
      }
      
      private function hideClick(e:MouseEvent) : void
      {
         this.dataG.hideHighB = !this.dataG.hideHighB;
         this.fleshTorBox();
         this.fleshBtnMustHave();
         this.torBox.setScrollPer(0);
      }
      
      private function mainClick(e:MouseEvent) : void
      {
         var canB0:Boolean = false;
         if(this.dataG.mainPan(this.nowData))
         {
            this.dataG.clearMain();
            Gaming.soundGroup.playSound("uiSound","swapHero");
         }
         else
         {
            canB0 = ArmsEditMethod.getMainLimitB(this.nowData);
            if(!canB0)
            {
               UIOrder.alertError(ArmsEditMethod.getMainLimitTip(this.nowData));
               return;
            }
            this.dataG.setMain(this.nowData);
            Gaming.soundGroup.playSound("uiSound","getLottery");
         }
         this.fleshBtnMustHave();
         this.fleshBoxUI();
      }
      
      private function codeClick(e:MouseEvent) : void
      {
         TorEditMethod.shareCode(this.nowData);
      }
      
      private function jsonClick(e:MouseEvent) : void
      {
         var code0:String = this.nowData.getInJson(Gaming.PG);
         System.setClipboard(code0);
         Gaming.uiGroup.alertBox.textInput.showTextInput("Json代码",code0,null,"yesAndNo",10000);
      }
      
      private function delClick(e:MouseEvent) : void
      {
         Gaming.uiGroup.alertBox.showChoose("是否删除该武器？",this.yesDel);
      }
      
      private function yesDel() : void
      {
         this.dataG.removeData(this.nowData);
         this.fleshData();
      }
      
      private function gotoClick(e:MouseEvent) : void
      {
         UIOrder.showTip("");
         var title0:String = "选择地图难度：\n" + NumberMethod.getRangeTip(1,10000);
         Gaming.uiGroup.alertBox.showNumChoose(title0,this.nowDiff,10000,1,1,this.afterGoto);
      }
      
      private function afterGoto(num0:int) : void
      {
         this.nowDiff = num0;
         var levelD0:LevelDefine = ArmsEditMethod.getTestLevelDefine(this.nowData.getArmsDef().name,num0);
         Gaming.LG.chooseByLevelDefine(levelD0);
      }
      
      private function mapClick(e:MouseEvent) : void
      {
         UIOrder.showTip("");
         var a0:EditListAgent = ArmsEditMethod.getMapListAgent();
         a0.linkFun = this.afterMap;
         Gaming.uiGroup.editList.showAgent(a0);
      }
      
      private function afterMap(map0:String) : void
      {
         var levelD0:LevelDefine = ArmsEditMethod.getMapLevelDefine(this.nowData.getArmsDef().name,map0,1);
         if(Boolean(levelD0))
         {
            Gaming.LG.chooseByLevelDefine(levelD0);
         }
         else
         {
            UIOrder.alertError("地图未解锁。");
         }
      }
      
      private function changeFun(proD0:EditProDefine, child0:IO_TorEditDefine) : void
      {
         var fleshGripB0:Boolean = false;
         if(Boolean(this.nowData))
         {
            Gaming.PG.da.bossEdit.changeArmsTorDeal(this.nowData.getName());
            fleshGripB0 = false;
            this.nowData.clearTempArms();
            if(proD0.name == "cnName")
            {
               fleshGripB0 = true;
            }
            if(this.dataG.mainPan(this.nowData))
            {
               fleshGripB0 = true;
               this.dataG.clearMain();
               Gaming.soundGroup.playSound("uiSound","swapHero");
            }
            this.fleshBtnMustHave();
            if(fleshGripB0)
            {
               this.fleshNowGripUI();
            }
         }
      }
      
      private function iconOver(e:MouseEvent) : void
      {
         var da0:ArmsData = null;
         if(Boolean(this.nowData))
         {
            da0 = this.nowData.getTempArms(true);
            Gaming.uiGroup.tipBox.followMouseB = true;
            ItemsGripTipCtrl.armsTip(da0);
         }
      }
      
      private function iconOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      public function FTimer() : void
      {
         if(visible)
         {
         }
      }
   }
}

