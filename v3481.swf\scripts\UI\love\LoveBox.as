package UI.love
{
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.heroImg.HeroEquipImgBox;
   import UI.base.label.LabelBox;
   import dataAll._app.love.LoveData;
   import dataAll._app.love.define.LoveLevelDefine;
   import dataAll._player.more.MorePlayerData;
   import dataAll.body.define.BodySex;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Shape;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class LoveBox extends NormalUI
   {
      private var labelArr:Array;
      
      private var boxArr:Array;
      
      private var labelBox:LabelBox;
      
      private var talkBoard:LoveTalkBoard;
      
      private var giftBoard:LoveGiftBoard;
      
      private var levelBoard:LoveLevelBoard;
      
      private var heroImgBox:HeroEquipImgBox;
      
      private var labelTag:Sprite = null;
      
      private var closeBtnSp:MovieClip;
      
      private var closeBtn:NormalBtn;
      
      private var loveTxt:TextField;
      
      private var imgTag:Sprite;
      
      private var heartMc:MovieClip;
      
      private var loveHeartMc:MovieClip;
      
      private var tipBtn:SimpleButton;
      
      private var backSp:Shape;
      
      private var allWidth:int = 0;
      
      private var allHeight:int = 0;
      
      private var wearWidth:int = 0;
      
      public function LoveBox()
      {
         var label0:* = null;
         var box0:NormalUI = null;
         this.labelArr = ["talk","gift","level"];
         this.boxArr = [];
         this.labelBox = new LabelBox();
         this.talkBoard = new LoveTalkBoard();
         this.giftBoard = new LoveGiftBoard();
         this.levelBoard = new LoveLevelBoard();
         this.heroImgBox = new HeroEquipImgBox();
         this.closeBtn = new NormalBtn();
         this.backSp = new Shape();
         super();
         for each(label0 in this.labelArr)
         {
            box0 = this[label0 + "Board"];
            this.boxArr.push(box0);
            box0.UILabel = label0;
         }
         this.giftBoard.loveBox = this;
      }
      
      public function setSize(wearUI0:DisplayObject, bagUI0:DisplayObject) : void
      {
         this.allWidth = wearUI0.width + bagUI0.width;
         this.wearWidth = wearUI0.width;
         this.allHeight = wearUI0.height;
         this.backSp.graphics.clear();
         this.backSp.graphics.beginFill(0,0.85);
         this.backSp.graphics.drawRect(0,0,this.wearWidth,this.allHeight);
         addChildAt(this.backSp,0);
         this.backShowOnlyWear();
      }
      
      private function backShowOnlyWear(bb0:Boolean = true) : void
      {
         this.backSp.x = 2;
         this.backSp.y = 2;
         this.backSp.height = this.allHeight - 4;
         this.backSp.width = bb0 ? this.wearWidth - 4 : this.allWidth - 4;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var label0:* = null;
         var box0:NormalUI = null;
         elementNameArr = ["tipBtn","heartMc","loveHeartMc","closeBtnSp","loveTxt","imgTag","labelTag"];
         super.setImg(img0);
         FontDeal.dealOne(this.loveTxt);
         this.tipBtn.visible = true;
         addChild(this.closeBtn);
         this.closeBtn.setImg(this.closeBtnSp);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         this.loveHeartMc.stop();
         this.heartMc.stop();
         this.heartMc.mc.stop();
         this.labelBox.arg.init(4,1,-6,0);
         addChild(this.labelBox);
         this.labelBox.inData("LoveUI/labelBtn",this.labelArr,["对话","赠礼","感情"]);
         this.labelBox.setChoose_byIndex(0);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.labelBox.x = this.labelTag.x;
         this.labelBox.y = this.labelTag.y;
         this.imgTag.addChild(this.heroImgBox);
         this.imgTag.scaleX = -1;
         for each(label0 in this.labelArr)
         {
            box0 = this[label0 + "Board"];
            addChild(box0);
            box0.visible = false;
            box0.setImg(Gaming.swfLoaderManager.getResource("LoveUI",label0 + "Board"));
         }
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      protected function get loveData() : LoveData
      {
         var pd0:MorePlayerData = Gaming.PG.DATA as MorePlayerData;
         if(Boolean(pd0))
         {
            return pd0.love;
         }
         return null;
      }
      
      override public function show() : void
      {
         super.show();
         this.showLabel(this.labelBox.nowLabel);
         Gaming.uiGroup.wearUI.stopAction();
         Gaming.uiGroup.bagUI.showAndLabel("things",true);
         this.heartMc.play();
         if(Boolean(this.loveData))
         {
            this.labelBox.getBtnByLabel("level").setName(this.loveData.getCn().substr(0,2));
         }
      }
      
      override public function hide() : void
      {
         if(this.visible)
         {
            Gaming.uiGroup.bagUI.unlockAllLabel();
            Gaming.uiGroup.wearUI.pauseAction();
         }
         super.hide();
         this.talkBoard.overShowText();
         this.heartMc.stop();
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showLabel(e.label);
      }
      
      private function showLabel(label0:String) : void
      {
         var n:* = undefined;
         var ui0:NormalUI = null;
         if(label0 == "")
         {
            label0 = this.labelArr[0];
         }
         this.labelBox.setChoose(label0);
         for(n in this.boxArr)
         {
            this.boxArr[n].hide();
         }
         ui0 = this[label0 + "Board"];
         if(Boolean(ui0))
         {
            ui0.show();
         }
         if(label0 == "talk")
         {
            this.backShowOnlyWear(false);
         }
         else
         {
            this.backShowOnlyWear(true);
         }
         this.fleshInfo();
      }
      
      public function fleshInfo() : void
      {
         var loveData0:LoveData = null;
         var value0:int = 0;
         var loveLevelDefine0:LoveLevelDefine = null;
         var lv0:int = 0;
         var frame0:int = 0;
         var pd0:MorePlayerData = Gaming.PG.DATA as MorePlayerData;
         if(Boolean(pd0))
         {
            this.heroImgBox.setEquip_byPlayerData(pd0,true);
            this.heroImgBox.y = (this.heroImgBox.scaleY - 1) * 60;
            loveData0 = pd0.love;
            value0 = loveData0.save.value;
            loveLevelDefine0 = loveData0.getLoveRoleAll().getLevelDefineByValue(value0);
            lv0 = loveLevelDefine0.lv;
            frame0 = lv0 + (loveData0.isMaleB() ? 10 : 0);
            this.loveTxt.htmlText = value0 + "";
            this.loveHeartMc.gotoAndStop(frame0);
            this.heartMc.mc.gotoAndStop(frame0);
            this.heartMc.scaleX = (lv0 * 15 + 10) / 100;
            this.heartMc.scaleY = this.heartMc.scaleX;
            this.heartMc.visible = lv0 > 2;
            this.heartMc.play();
         }
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         this.hide();
      }
      
      private function tipOver(e:MouseEvent) : void
      {
         var str0:String = "";
         if(Boolean(this.loveData))
         {
            str0 = this.getTip(this.loveData.getRoleCn(),this.loveData.getCn(),this.loveData.getSex());
         }
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      private function getTip(role0:String, cn0:String, sex0:String) : String
      {
         var str0:String = "";
         var sexCn0:String = sex0 == BodySex.MALE ? "他" : "她";
         str0 += "1、每天和" + role0 + "对话，可以了解" + sexCn0 + "的喜好（喜好每天都会不同）。";
         str0 += "\n2、赠送" + role0 + "喜欢的物品会提高" + cn0 + "，赠送" + sexCn0 + "厌恶的物品则会倒扣" + cn0 + "。";
         str0 += "\n3、" + cn0 + "达到一定阶段，会开启特殊技能，以及获得稀有道具。";
         str0 += "\n4、每天第一次打开" + cn0 + "界面，在没有和" + role0 + "进行后续对话的情况下直接赠送" + role0 + "喜欢的物品，会获得4倍" + cn0 + "加成。";
         str0 += "\n5、" + cn0 + "超过" + LoveData.getReductLove() + "以后，同时" + role0 + "在战斗中倒下会扣除" + cn0 + "（扣除2点）。";
         return str0 + ("\n6、一周以上没有送" + role0 + "物品，将扣除100" + cn0 + "。");
      }
      
      private function tipOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      public function FTimer() : void
      {
         if(this.visible)
         {
            this.heartMc.mc.stop();
            this.talkBoard.FTimer();
            this.giftBoard.FTimer();
            this.heroImgBox.Ftimer();
         }
      }
   }
}

