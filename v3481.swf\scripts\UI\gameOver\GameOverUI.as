package UI.gameOver
{
   import UI.NormalUICtrl;
   import UI.UIGroup;
   import UI.UIShow;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AppNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import UI.main.WorldMapBox;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.space.SpaceLevelCount;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._app.worldMap.save.WorldMapSave;
   import dataAll.level.LevelCountSave;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import gameAll.level.PlayMode;
   import gameAll.level.arena.ArenaCtrl;
   import gameAll.level.data.OverLevelShow;
   import gameAll.level.endless.EndlessModelCtrl;
   
   public class GameOverUI extends AppNormalUI
   {
      private var starMc:MovieClip;
      
      private var starTxt:TextField;
      
      private var txt:TextField;
      
      private var titleTxt:TextField;
      
      private var closeBtn:SimpleButton;
      
      private var uiBtnSp:MovieClip;
      
      private var mapBtnSp:MovieClip;
      
      private var uiBtn:NormalBtn = new NormalBtn();
      
      private var mapBtn:NormalBtn = new NormalBtn();
      
      private var lotteryTag:Sprite;
      
      private var lotteryTxt:TextField;
      
      private var lotteryBox:ItemsGripBox = new ItemsGripBox();
      
      private var arenaSp:Sprite;
      
      private var endlessSp:Sprite;
      
      private var arena:ArenaGameOverBox = new ArenaGameOverBox();
      
      private var endless:EndlessGameOverBox = new EndlessGameOverBox();
      
      public function GameOverUI()
      {
         super();
         UILabel = "gameOver";
      }
      
      override protected function firstLoad() : void
      {
         setImgUrl("GameOverUI/gameOverUI");
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["lotteryTxt","lotteryTag","titleTxt","starMc","starTxt","txt","closeBtn","arenaSp","endlessSp","uiBtnSp","mapBtnSp"];
         super.setImg(img0);
         FontDeal.dealOne(this.titleTxt);
         FontDeal.dealOne(this.starTxt);
         FontDeal.dealOne(this.txt);
         addChild(this.uiBtn);
         this.uiBtn.setImg(this.uiBtnSp);
         this.uiBtn.addEventListener(MouseEvent.CLICK,this.uiClick);
         addChild(this.mapBtn);
         this.mapBtn.setImg(this.mapBtnSp);
         this.mapBtn.addEventListener(MouseEvent.CLICK,this.mapClick);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         addChild(this.arena);
         this.arena.setImg(this.arenaSp);
         this.arena.visible = false;
         addChild(this.endless);
         this.endless.setImg(this.endlessSp);
         this.endless.visible = false;
         addChild(this.lotteryTxt);
         addChild(this.lotteryBox);
         NormalUICtrl.setTag(this.lotteryBox,this.lotteryTag);
         this.lotteryBox.setIconPro("equipGrip",50,50);
         this.lotteryBox.evt.setWant(true,true);
         this.lotteryBox.arg.init(5,1,5,5);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.lotteryBox);
         UIGroup.setUIMiddle(this);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function showPan(show0:OverLevelShow) : void
      {
         UIShow.showApp("gameOver");
         this.fleshData(show0);
      }
      
      override public function show() : void
      {
         super.show();
         this.arena.visible = false;
         this.endless.visible = false;
      }
      
      private function fleshData(show0:OverLevelShow) : void
      {
         this.arena.visible = false;
         this.endless.visible = false;
         if(show0.model == PlayMode.ARENA)
         {
            this.titleTxt.text = show0.winB ? "挑战成功" : "挑战失败";
            this.arena.visible = true;
            this.arena.inData(Gaming.PG.da.arena.nowCount,ArenaCtrl.dat.rivalPC.da);
         }
         else if(show0.worldMapModel == MapMode.ENDLESS)
         {
            this.titleTxt.text = "无尽模式";
            this.fleshEndless(show0);
         }
         else if(Boolean(show0.mapDefine) && show0.mapDefine.isSpaceB())
         {
            this.fleshSpace(show0.mapDefine,Gaming.PG.da.base.nowCountSave);
         }
         else
         {
            this.titleTxt.text = "胜利";
            this.fleshNormal(Gaming.PG.da.base.nowCountSave);
         }
         this.lotteryTxt.visible = false;
         this.lotteryBox.visible = false;
         this.fleshDownBtn(show0);
      }
      
      private function fleshNormal(s0:LevelCountSave) : void
      {
         this.starMc.gotoAndStop(s0.star);
         this.starTxt.htmlText = "综合评分：" + ComMethod.color(s0.star + "星","#FFFF00");
         var str0:String = "";
         str0 += "所用时间：" + ComMethod.color(ComMethod.getTimeStrTwo(s0.time),"#00FFFF");
         str0 += "\n命中率：" + ComMethod.color(TextWay.numberToPer(s0.getHitRate()),"#00FF00");
         str0 += "\n爆头率：" + ComMethod.color(TextWay.numberToPer(s0.getHeadshotRate()),"#FF6600");
         str0 += "\n获得银币：" + ComMethod.color(s0.coin + "","#FFFF00");
         str0 += "\n获得武器：" + ComMethod.color(s0.armsNum + "个","#FF66FF");
         str0 += "\n获得装备：" + ComMethod.color(s0.equipNum + "个","#FF66FF");
         this.txt.htmlText = FontDeal.getDealLeadingStr(this.txt,str0);
      }
      
      private function fleshEndless(show0:OverLevelShow) : void
      {
         var mapId0:String = show0.mapDefine.name;
         var save0:WorldMapSave = Gaming.PG.da.worldMap.saveGroup.getSave(mapId0);
         var score0:Number = EndlessModelCtrl.dat.tempScore;
         var star0:int = EndlessModelCtrl.dat.getStar();
         var nowMax0:Number = save0.maxEndlessScore;
         var allMax0:Number = Gaming.PG.da.worldMap.getMaxEndlessScore();
         var cnName0:String = show0.mapDefine.cnName;
         var lv0:int = show0.enemyLv;
         var maxGrade0:int = save0.maxEndlessGrade;
         this.starMc.gotoAndStop(star0);
         this.starTxt.htmlText = "评分：" + score0;
         var str0:String = "";
         str0 += "本图最高评分：" + nowMax0;
         str0 += "\n全图最高评分：" + allMax0;
         str0 += "\n挑战地图：" + cnName0;
         str0 += "\n地图等级：" + lv0 + "级";
         str0 += "\n已挑战最高层级：第" + maxGrade0 + "层";
         this.txt.htmlText = FontDeal.getDealLeadingStr(this.txt,str0);
      }
      
      private function fleshSpace(mapD0:WorldMapDefine, s0:LevelCountSave) : void
      {
         var c0:SpaceLevelCount = Gaming.PG.da.space.count;
         var star0:int = c0.getStar(s0.time);
         this.starMc.gotoAndStop(star0);
         this.starTxt.htmlText = "综合评分：" + ComMethod.color(star0 + "星","#FFFF00");
         var str0:String = "";
         str0 += "\n地图：" + ComMethod.color(mapD0.cnName,"#00FFFF");
         str0 += "\n所用时间：" + ComMethod.color(ComMethod.getTimeStrTwo(s0.time),"#00FFFF");
         str0 += "\n获得经验：" + ComMethod.color(c0.exp,"#00FF00");
         str0 += "\n摧毁目标：" + ComMethod.color(c0.enemy,"#FF6600") + "个";
         this.txt.htmlText = FontDeal.getDealLeadingStr(this.txt,str0);
         this.titleTxt.text = "胜利";
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         hide();
      }
      
      private function fleshDownBtn(show0:OverLevelShow) : void
      {
         var btn0:NormalBtn = null;
         var mapD0:WorldMapDefine = show0.mapDefine;
         var mapCn0:String = "地图";
         var mapActived0:Boolean = false;
         this.mapBtn.itemsData = null;
         if(Boolean(mapD0))
         {
            mapCn0 = mapD0.cnName;
            btn0 = Gaming.uiGroup.worldMapBox.getBtnByName(mapD0.name);
            if(Boolean(btn0))
            {
               if(btn0.visible && btn0.actived)
               {
                  mapActived0 = show0.overBackMapB;
                  if(mapActived0)
                  {
                     this.mapBtn.itemsData = mapD0;
                  }
               }
            }
         }
         this.mapBtn.setName("前往<b>" + ComMethod.color(mapCn0,"#FFFF00") + "</b>");
         this.mapBtn.actived = Boolean(mapD0) && show0.overBackMapB && show0.enemyLv >= 30;
         var ui0:AppNormalUI = UIShow.getBeforeLevelApp();
         var uiCn0:String = "上一个界面";
         this.uiBtn.itemsData = null;
         if(Boolean(ui0))
         {
            uiCn0 = ui0.UICn;
            if(uiCn0 != "")
            {
               this.uiBtn.itemsData = ui0.UILabel;
            }
         }
         this.uiBtn.setName("返回<b>" + ComMethod.color(uiCn0,"#FFFF00") + "</b>");
         this.uiBtn.actived = ui0;
      }
      
      private function uiClick(e:MouseEvent) : void
      {
         if(this.uiBtn.itemsData != null)
         {
            UIShow.showApp(this.uiBtn.itemsData as String,true);
         }
      }
      
      private function mapClick(e:MouseEvent) : void
      {
         var mapD0:WorldMapDefine = this.mapBtn.itemsData as WorldMapDefine;
         if(Boolean(mapD0))
         {
            hide();
            WorldMapBox.gotoLevelByWorldMapId(mapD0.name);
         }
      }
   }
}

