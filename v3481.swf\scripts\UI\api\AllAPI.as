package UI.api
{
   import UI.api.audit.AuditHeartbeat;
   import UI.api.audit.AuditRoleCreate;
   import UI.api.audit.AuditUserLogin;
   import UI.api.audit.ShopItemList;
   import UI.api.audit.ShopItemLog;
   import UI.api.badWord.Audit4399Api;
   import UI.api.badWord.BadWorldApi;
   import UI.api.count.Count4399_API;
   import UI.api.count.RZXT4399_API;
   import UI.api.count.ShopCount4399_API;
   import UI.api.exchange.BaoXiao_API;
   import UI.api.exchange.BbsActived_API;
   import UI.api.exchange.CardExchange_API;
   import UI.api.exchange.CodeExchange_API;
   import UI.api.exchange.Dujia_API;
   import UI.api.exchange.HomeExchange_API;
   import UI.api.exchange.KuangExchange_API;
   import UI.api.exchange.MillionpackExchange_API;
   import UI.api.exchange.MyCreditExchange_API;
   import UI.api.exchange.NewCodeExchange_API;
   import UI.api.exchange.Summer2017_API;
   import UI.api.exchange.WeiXinDay_API;
   import UI.api.exchange.ZhuZhan2_API;
   import UI.api.exchange.ZhuZhan_API;
   import UI.api.realName.RealNameApi;
   import UI.api.save.SaveAPI;
   import UI.api.shop.ShopAPI;
   import UI.api.top.TopAPI;
   import UI.api.union.Union_API;
   import dataAll._app.goods.GoodsData;
   import dataAll.must.define.MustDefine;
   
   public class AllAPI
   {
      public var save:SaveAPI = new SaveAPI();
      
      public var badWorld:BadWorldApi = new BadWorldApi();
      
      public var aduit:Audit4399Api = new Audit4399Api();
      
      public var rzxt:RZXT4399_API = new RZXT4399_API();
      
      public var top:TopAPI = new TopAPI();
      
      public var shop:ShopAPI = new ShopAPI();
      
      public var codeExchange:CodeExchange_API = new CodeExchange_API();
      
      public var myCreditExchange:MyCreditExchange_API = new MyCreditExchange_API();
      
      public var newExchange:NewCodeExchange_API = new NewCodeExchange_API();
      
      public var kuangExchange:KuangExchange_API = new KuangExchange_API();
      
      public var cardExchange:CardExchange_API = new CardExchange_API();
      
      public var weixinDay:WeiXinDay_API = new WeiXinDay_API();
      
      public var baoXiao:BaoXiao_API = new BaoXiao_API();
      
      public var zhuZhan:ZhuZhan_API = new ZhuZhan_API();
      
      public var zhuZhan2:ZhuZhan2_API = new ZhuZhan2_API();
      
      public var summer2017:Summer2017_API = new Summer2017_API();
      
      public var dujia:Dujia_API = new Dujia_API();
      
      public var bbs5:BbsActived_API = new BbsActived_API();
      
      public var home:HomeExchange_API = new HomeExchange_API();
      
      public var millionpack:MillionpackExchange_API = new MillionpackExchange_API();
      
      public var union:Union_API = new Union_API();
      
      public var count4399:Count4399_API = new Count4399_API();
      
      public var shopCount4399:ShopCount4399_API = new ShopCount4399_API();
      
      public var realName:RealNameApi = new RealNameApi();
      
      private var shopItemList:ShopItemList = new ShopItemList();
      
      private var shopItemLog:ShopItemLog = new ShopItemLog();
      
      public var auditHeartbeat:AuditHeartbeat = new AuditHeartbeat();
      
      public var auditRoleCreate:AuditRoleCreate = new AuditRoleCreate();
      
      public var auditUserLogin:AuditUserLogin = new AuditUserLogin();
      
      public function AllAPI()
      {
         super();
      }
      
      public function init() : *
      {
         this.save.s4399.serviceHold = Gaming.serviceHold;
         this.save.s4399.addListener(Gaming.ME.stage);
         this.top.addListener(Gaming.ME.stage);
         this.shop.addListener(Gaming.ME.stage);
         this.union.addEvent(Gaming.ME.stage,Gaming.serviceHold);
         this.badWorld.init();
      }
      
      public function outLoginEvent() : void
      {
         this.save.outLoginEvent();
         this.top.outLoginEvent();
         this.shop.outLoginEvent();
         this.union.outLoginEvent();
      }
      
      public function shopItemsLog(da0:GoodsData) : *
      {
      }
      
      public function useMoneyMust(d0:MustDefine) : *
      {
      }
      
      public function FTimerSecond() : void
      {
         this.save.FTimerSecond();
      }
      
      public function isWhiteValentine() : Boolean
      {
         return this.save.isInTimeRange("2016-3-8","2016-3-15");
      }
   }
}

