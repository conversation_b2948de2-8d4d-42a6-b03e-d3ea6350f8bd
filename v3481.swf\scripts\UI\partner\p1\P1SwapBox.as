package UI.partner.p1
{
   import UI.UIGroup;
   import UI.bag.ItemsGripMoveCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.box.NormalBox;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.login.LoginNameBox;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._app.task.TaskData;
   import dataAll._app.task.define.TaskDefine;
   import dataAll._player.PlayerData;
   import dataAll._player.base.PlayerMainSave;
   import dataAll._player.role.RoleName;
   import dataAll.body.define.HeroDefine;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.pro.ProType;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.text.TextField;
   
   public class P1SwapBox extends AutoNormalUI
   {
      private var closeBtn:NormalBtn;
      
      private var yesBtn:NormalBtn;
      
      private var equipBtn:NormalBtn;
      
      private var titleTxt:TextField;
      
      private var infoTxt:TextField;
      
      private var numTxt:TextField;
      
      private var gripTag:Sprite;
      
      private var gripBox:NormalBox = new NormalBox();
      
      public function P1SwapBox()
      {
         super();
         mcTypeArr = ["btnSp","tag","txt"];
      }
      
      private static function get PD() : PlayerData
      {
         return Gaming.PG.da;
      }
      
      override protected function firstLoad() : void
      {
         setImgUrl("LoginUI/p1SwapBox");
         this.yesBtn.setName("切换");
         FontDeal.dealOne(this.titleTxt);
         FontDeal.dealLine(this.infoTxt);
         this.infoTxt.styleSheet = TextMethod.getLinkCss();
         this.infoTxt.addEventListener(TextEvent.LINK,this.linkClick);
         this.gripTag.addChild(this.gripBox);
         this.gripBox.arg.init(RoleName.loginArr.length,1,20,0);
         this.gripBox.setIconPro("LoginUI/roleBar");
         this.gripBox.evt.setWant(true,false);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         UIGroup.setUIMiddle(this);
         this.initGripBox();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function initGripBox() : void
      {
         var roleArr0:Array = null;
         var index0:int = 0;
         var name0:* = null;
         var d0:HeroDefine = null;
         var grip0:NormalBtn = null;
         if(this.gripBox.gripArr.length == 0)
         {
            roleArr0 = RoleName.loginArr;
            index0 = 0;
            this.gripBox.setNowGripNum(roleArr0.length);
            for each(name0 in roleArr0)
            {
               d0 = Gaming.defineGroup.body.getHeroDefine(name0);
               grip0 = this.gripBox.gripArr[index0];
               LoginNameBox.inGripData(grip0,d0);
               grip0.setName("");
               index0++;
            }
         }
         this.gripBox.setChoose_byIndex(0);
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshBoxData();
         this.chooseRole(PD.getRoleName());
         this.fleshEquipBtn();
      }
      
      private function fleshBoxData() : void
      {
         var btn0:NormalBtn = null;
         var role0:String = null;
         var winB0:Boolean = false;
         var goldB0:Boolean = false;
         var unlockB0:Boolean = false;
         var beforeB0:Boolean = false;
         var msave0:PlayerMainSave = Gaming.PG.da.main.save;
         var p1Role0:String = PD.getRoleName();
         var before0:String = PD.getBeforeRole();
         for each(btn0 in this.gripBox.gripArr)
         {
            role0 = btn0.label;
            winB0 = Boolean(Gaming.PG.loginData.haveRoleInList(role0)) || msave0.isHaveP1(role0);
            goldB0 = msave0.isUnlockP1(role0);
            unlockB0 = RoleName.loginUnlockArr.indexOf(role0) >= 0;
            beforeB0 = role0 == before0;
            if(unlockB0 == true)
            {
               if(p1Role0 == role0)
               {
                  btn0.setSmallIcon("p1");
               }
               else if(winB0 || beforeB0 || goldB0)
               {
                  btn0.setSmallIcon("");
               }
               else
               {
                  btn0.setSmallIcon("noWin");
               }
               btn0.setNumText(beforeB0 ? "原始" : "");
            }
         }
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var funName0:String = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(Boolean(btn0) && btn0.actived)
         {
            funName0 = btn0.label + "BtnClick";
            this[funName0](e);
         }
      }
      
      private function yesBtnClick(e:MouseEvent) : void
      {
         this.swapToP1();
      }
      
      private function equipBtnClick(e:MouseEvent) : void
      {
         PD.main.save.swapEquipB = !PD.main.save.swapEquipB;
         this.fleshEquipBtn();
      }
      
      private function closeBtnClick(e:MouseEvent) : void
      {
         hide();
      }
      
      private function gripOver(e:ClickEvent) : void
      {
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var btn0:NormalBtn = e.child as NormalBtn;
         this.chooseRole(e.label);
      }
      
      private function fleshNow() : void
      {
         this.fleshBoxData();
         this.chooseRole(this.gripBox.nowLabel);
         this.fleshEquipBtn();
      }
      
      private function chooseRole(role0:String) : void
      {
         var beforeB0:Boolean = false;
         var unlockB0:Boolean = false;
         var p1Role0:String = PD.getRoleName();
         var before0:String = PD.getBeforeRole();
         if(role0 == "")
         {
            role0 = p1Role0;
         }
         this.gripBox.setChoose(role0);
         var s0:String = "";
         if(role0 == p1Role0)
         {
            s0 = "请在上方选择需要切换的P1角色。";
            this.yesBtn.actived = false;
         }
         else
         {
            beforeB0 = role0 == before0;
            unlockB0 = RoleName.loginUnlockArr.indexOf(role0) >= 0;
            if(unlockB0 == false)
            {
               s0 = "该角色未解锁。";
               this.yesBtn.actived = false;
            }
            else if(beforeB0)
            {
               s0 = "这是该存档原始P1角色，可直接切换。";
               this.yesBtn.actived = true;
            }
            else
            {
               s0 = this.getUnlockStrAndSetYesBtn(role0);
            }
         }
         this.infoTxt.htmlText = FontDeal.getDealLeadingStr(this.infoTxt,s0);
         var swapMax0:int = PlayerMainSave.getSwapMax();
         var msave0:PlayerMainSave = Gaming.PG.da.main.save;
         var dayB0:Boolean = msave0.swapNum < swapMax0;
         this.numTxt.htmlText = "·每天只能切换" + ComMethod.moreRed(msave0.swapNum,swapMax0) + "次·";
         if(dayB0 == false)
         {
            this.yesBtn.actived = false;
         }
      }
      
      private function fleshEquipBtn() : void
      {
         this.equipBtn.isChosen = PD.main.save.swapEquipB;
      }
      
      private function getUnlockStrAndSetYesBtn(role0:String) : String
      {
         var msave0:PlayerMainSave = Gaming.PG.da.main.save;
         var mustD0:MustDefine = this.getOpenP1Must();
         var roleD0:HeroDefine = Gaming.defineGroup.body.getHeroDefine(role0);
         var s0:String = ComMethod.color("切换到<b>" + roleD0.getRoleCn() + "</b>P1的条件：","#FF9900",14);
         var haveB0:Boolean = Boolean(PD.moreWay.getDataByHeroName(role0));
         s0 += "\n" + ProType.booleanToUIRed(haveB0) + "1、当前存档拥有该角色。";
         var d0:TaskDefine = Gaming.defineGroup.task.getOneDefine(RoleName.winTask);
         var taskB0:Boolean = PD.task.isCompleteB(RoleName.winTask);
         s0 += "\n" + ProType.booleanToUIRed(taskB0) + "2、当前存档通关任务" + d0.getAlertTitleText() + "。";
         var winB0:Boolean = Boolean(Gaming.PG.loginData.haveRoleInList(role0)) || msave0.isHaveP1(role0);
         var goldB0:Boolean = msave0.isUnlockP1(role0);
         var unlockB0:Boolean = winB0 || goldB0;
         var canGoldUnlockB0:Boolean = taskB0 && !unlockB0 && haveB0;
         s0 += "\n" + ProType.booleanToUIRed(unlockB0) + "3、同账号下有" + ComMethod.color(roleD0.getRoleCn() + "P1","#00FF00") + "存档，并且该存档通关主线任务“末日坦克”。";
         if(winB0 == false)
         {
            if(goldB0)
            {
               s0 += ComMethod.color("(已用黄金解锁)","#FFFF00");
            }
            else
            {
               s0 += TextMethod.link("(点击使用黄金解锁)","gold");
            }
         }
         this.yesBtn.actived = taskB0 && unlockB0 && haveB0;
         return s0;
      }
      
      private function getOpenP1Must() : MustDefine
      {
         var d0:MustDefine = new MustDefine();
         d0.money = 1000;
         d0.propId = "4482";
         return d0;
      }
      
      private function linkClick(e:TextEvent) : void
      {
         var role0:String = null;
         var taskB0:Boolean = false;
         var haveB0:Boolean = false;
         var d0:TaskDefine = null;
         var must0:MustDefine = null;
         if(e.text == "gold")
         {
            role0 = this.gripBox.nowLabel;
            taskB0 = PD.task.isCompleteB(RoleName.winTask);
            haveB0 = Boolean(PD.moreWay.getDataByHeroName(role0));
            if(taskB0 == false)
            {
               d0 = Gaming.defineGroup.task.getOneDefine(RoleName.winTask);
               Gaming.uiGroup.alertBox.showError("请先通关任务" + d0.getAlertTitleText() + "。");
            }
            else if(haveB0 == false)
            {
               Gaming.uiGroup.alertBox.showError("请先获得当前角色。");
            }
            else
            {
               must0 = this.getOpenP1Must();
               PlayerMustCtrl.deductMustAlert(must0,"解锁所需：",this.yes_goldOpenP1);
            }
         }
      }
      
      private function yes_goldOpenP1() : void
      {
         var role0:String = this.gripBox.nowLabel;
         PD.main.save.addUnlockP1(role0);
         this.fleshNow();
      }
      
      private function swapToP1() : Boolean
      {
         var taskDa0:TaskData = null;
         var now0:String = null;
         var bb0:Boolean = false;
         var oneRole0:String = null;
         var oneRoleTip0:String = null;
         var oneD0:NormalBodyDefine = null;
         var error0:String = null;
         var role0:String = this.gripBox.nowLabel;
         if(role0 != "")
         {
            taskDa0 = PD.task.getNoRoleTaskIng(role0);
            if(Boolean(taskDa0))
            {
               oneRole0 = taskDa0.def.role;
               oneRoleTip0 = "";
               if(PD.getRoleName() != oneRole0 && RoleName.arr.indexOf(taskDa0.def.role) >= 0)
               {
                  oneD0 = Gaming.defineGroup.body.getDefine(taskDa0.def.role);
                  if(Boolean(oneD0))
                  {
                     oneRoleTip0 = "(在" + oneD0.getRoleCn() + "P1下)";
                  }
               }
               Gaming.uiGroup.alertBox.showError("取消或领奖以下任务才能继续：\n" + taskDa0.def.getAlertTitleText() + oneRoleTip0 + "。");
               return false;
            }
            now0 = PD.getRoleName();
            bb0 = PD.setNewP1NameUI(role0);
            if(bb0)
            {
               error0 = "";
               if(PD.main.save.swapEquipB)
               {
                  error0 = ItemsGripMoveCtrl.swapWearAllByRole(now0,role0);
               }
               Gaming.PG.da.fleshAllByEquipAll();
               Gaming.uiGroup.alertBox.showSuccess("切换成功！" + error0);
               Gaming.uiGroup.moreBox.fleshData();
               this.fleshNow();
               return true;
            }
         }
         Gaming.uiGroup.alertBox.showError("切换失败！");
         return false;
      }
   }
}

