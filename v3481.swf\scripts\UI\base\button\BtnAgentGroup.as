package UI.base.button
{
   public class BtnAgentGroup
   {
      public var name:String = "";
      
      public var title:String = "";
      
      private var arr:Array = [];
      
      public var clickFun:Function = null;
      
      public function BtnAgentGroup()
      {
         super();
      }
      
      public function getArr() : Array
      {
         return this.arr;
      }
      
      public function newNum(num0:int) : void
      {
         var a0:BtnAgent = null;
         for(var i:int = 0; i < num0; i++)
         {
            a0 = new BtnAgent();
            this.arr.push(a0);
         }
      }
   }
}

