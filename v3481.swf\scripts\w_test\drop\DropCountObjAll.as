package w_test.drop
{
   import dataAll.drop.define.DropItemsDefine;
   import dataAll.equip.device.DeviceSave;
   import dataAll.equip.weapon.WeaponSave;
   import dataAll.items.save.ItemsSave;
   import dataAll.things.define.ThingsDefine;
   import gameAll.drop.body.DropBody;
   
   public class DropCountObjAll
   {
      private var arms:DropCountObj;
      
      private var equip:DropCountObj;
      
      private var things:DropCountObj = new DropCountObj();
      
      public function DropCountObjAll()
      {
         super();
         this.init();
      }
      
      public function init() : void
      {
         this.arms = DropCountObj.getArmsObj();
         this.equip = DropCountObj.getEquipObj();
         this.things = new DropCountObj();
         this.things.name = "things";
         this.things.cn = "物品";
         this.things.childType = "name";
      }
      
      public function allDrop(drop0:DropBody) : void
      {
         var td0:ThingsDefine = null;
         var type0:String = drop0.define.type;
         var s0:ItemsSave = drop0.dat.itemsSave;
         if(type0 != DropItemsDefine.TYPE_EFFECT)
         {
            if(type0 == DropItemsDefine.TYPE_ARMS)
            {
               this.arms.addItemsSave(drop0.dat.itemsSave);
            }
            else if(type0 == DropItemsDefine.TYPE_EQUIP)
            {
               if(s0 is WeaponSave || s0 is DeviceSave)
               {
                  this.things.addNormalObj(s0.cnName);
               }
               else
               {
                  this.equip.addItemsSave(s0);
               }
            }
            else if(type0 == DropItemsDefine.TYPE_THINGS)
            {
               td0 = Gaming.defineGroup.things.getDefine(drop0.define.name);
               this.things.addNormalObj(td0.cnName,"",td0.isPartsSpecialB() || td0.isBlackChip());
            }
         }
      }
      
      public function getString() : String
      {
         var str0:String = "";
         str0 += "\n" + this.arms.getString("");
         str0 += "\n" + this.equip.getString("");
         return str0 + ("\n" + this.things.getString(""));
      }
   }
}

