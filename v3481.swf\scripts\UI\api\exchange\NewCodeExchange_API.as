package UI.api.exchange
{
   import UI.test.SaveTestBox;
   import com.adobe.crypto.MD5;
   import com.adobe.serialization.json.JSON2;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   
   public class NewCodeExchange_API
   {
      public var yesFun:Function;
      
      public var noFun:Function;
      
      internal var loader:URLLoader = new URLLoader();
      
      internal var url:URLRequest = new URLRequest("//my.4399.com/jifen/api-apply");
      
      public var gid:int = 120;
      
      public var $key:String = "290e545cfdaef97400e3293974e1a587";
      
      public function NewCodeExchange_API()
      {
         super();
         this.loader.addEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this.loader.addEventListener(Event.COMPLETE,this.loaderCompleteHandler);
      }
      
      public function startExchange(uid0:String, activation0:String, product_id0:String, _yesFun:Function = null, _noFun:Function = null) : *
      {
         var n:* = undefined;
         this.yesFun = _yesFun;
         this.noFun = _noFun;
         if(Gaming.api.save.isLocal())
         {
            uid0 = "1069144161";
         }
         var token0:String = "";
         var data0:URLVariables = new URLVariables();
         data0.activation = activation0;
         if(product_id0 == "")
         {
            data0.gid = this.gid;
            token0 = this.getToken([activation0,this.gid,"1",uid0]);
         }
         else
         {
            token0 = this.getToken([activation0,product_id0,"1",uid0]);
            data0.product_id = product_id0;
         }
         data0.uid = Number(uid0);
         data0.type = "1";
         data0.token = token0;
         this.url.data = data0;
         this.url.method = URLRequestMethod.POST;
         this.loader.load(this.url);
         SaveTestBox.addText("$key：" + this.$key);
         var arr0:Array = ["activation","gid","product_id","uid","type","token"];
         for(n in arr0)
         {
            SaveTestBox.addText(arr0[n] + "：" + this.url.data[arr0[n]]);
         }
      }
      
      private function getToken(arr0:Array) : String
      {
         var s0:* = null;
         var str0:String = "";
         for each(s0 in arr0)
         {
            str0 += s0;
         }
         str0 += this.$key;
         return MD5.hash(str0);
      }
      
      private function loaderCompleteHandler(e:Event) : void
      {
         var json0:String = this.loader.data;
         var obj0:Object = JSON2.decode(json0);
         var code0:int = int(obj0["code"]);
         var result0:String = obj0["result"];
         var msg0:String = obj0["msg"];
         if(code0 == 100)
         {
            if(this.yesFun is Function)
            {
               this.yesFun(result0);
            }
         }
         else if(this.noFun is Function)
         {
            this.noFun(msg0);
         }
      }
      
      private function errorHandler(e:IOErrorEvent) : void
      {
         if(this.noFun is Function)
         {
            this.noFun("网络连接错误");
         }
      }
   }
}

