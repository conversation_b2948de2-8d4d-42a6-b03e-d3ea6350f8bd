package UI.base.oneKey
{
   import com.sounto.utils.NumberMethod;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.must.define.MustDefine;
   
   public class OneKeyCtrl
   {
      public var ingB:Boolean = false;
      
      private var first:Number = 0;
      
      private var last:Number = 0;
      
      private var num:int = 0;
      
      private var max:int = 0;
      
      private var gift:GiftAddDefineGroup = null;
      
      private var saveB:Boolean = false;
      
      public var startFun:Function = null;
      
      public var endFun:Function = null;
      
      public function OneKeyCtrl()
      {
         super();
      }
      
      public function clear() : void
      {
         this.first = 0;
         this.last = 0;
         this.num = 0;
         this.max = 0;
         this.gift = null;
         this.ingB = false;
         this.saveB = false;
      }
      
      public function start(max0:Number, firstLv0:Number) : void
      {
         if(this.ingB)
         {
            return;
         }
         this.clear();
         this.ingB = true;
         this.first = firstLv0;
         this.last = this.first;
         this.max = max0;
         this.startFun();
      }
      
      public function useGift(g0:GiftAddDefineGroup) : void
      {
         if(this.ingB)
         {
            if(this.gift == null)
            {
               this.gift = new GiftAddDefineGroup();
            }
            this.gift.merge(g0);
         }
      }
      
      public function useMust(md0:MustDefine) : void
      {
         var g0:GiftAddDefineGroup = new GiftAddDefineGroup();
         g0.inMustDefineOnlyThings(md0);
         this.useGift(g0);
      }
      
      public function next(v0:Number, saveB0:Boolean) : void
      {
         this.last = v0;
         ++this.num;
         if(saveB0)
         {
            this.saveB = true;
         }
         if(this.num >= this.max)
         {
            this.doEnd();
         }
         else
         {
            this.startFun();
         }
      }
      
      private function doEnd() : void
      {
         this.ingB = false;
         this.endFun();
      }
      
      public function doErrorOrAlert(str0:String = "") : void
      {
         if(this.ingB)
         {
            this.doEnd();
         }
         else
         {
            Gaming.uiGroup.alertBox.showError(str0);
         }
      }
      
      public function doErrorIng(str0:String = "") : void
      {
         if(this.ingB)
         {
            this.doEnd();
         }
      }
      
      public function getLevelChangeStr() : String
      {
         return this.first + "级 → " + this.last + "级";
      }
      
      public function getPerChangeStr() : String
      {
         return NumberMethod.toPer(this.first,0) + " → " + NumberMethod.toPer(this.last,0);
      }
      
      public function getFirst() : Number
      {
         return this.first;
      }
      
      public function getLast() : Number
      {
         return this.last;
      }
      
      public function getNum() : int
      {
         return this.num;
      }
      
      public function getSaveB() : Boolean
      {
         return this.saveB;
      }
      
      public function getGift() : GiftAddDefineGroup
      {
         return this.gift;
      }
   }
}

