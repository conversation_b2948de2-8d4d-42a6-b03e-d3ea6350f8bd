package UI.gameWorld.task
{
   import UI.base.font.FontDeal;
   import dataAll._app.task.TaskData;
   import dataAll._app.union.building.cooking.CookingState;
   import dataAll._app.union.building.federal.UnionSendTaskDataGroup;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.TextEvent;
   import flash.text.TextField;
   
   public class GameWorldTaskText extends Sprite
   {
      private var stateMc:MovieClip;
      
      private var textTxt:TextField;
      
      private var taskData:TaskData = null;
      
      private var sendData:UnionSendTaskDataGroup;
      
      public var btnIsLightB:Boolean = false;
      
      public var visibleB:Boolean = true;
      
      public function GameWorldTaskText()
      {
         super();
      }
      
      public function imgInit() : void
      {
         var img0:Sprite = Gaming.swfLoaderManager.getResource("GameWorldUI","taskText");
         this.stateMc = img0["stateMc"];
         this.stateMc.gotoAndStop("ing");
         this.textTxt = img0["textTxt"];
         this.textTxt.autoSize = "left";
         this.textTxt.wordWrap = true;
         this.textTxt.addEventListener(TextEvent.LINK,this.linkClick);
         FontDeal.dealOne(this.textTxt);
         addChild(img0);
      }
      
      private function set textStr(str0:String) : void
      {
         this.textTxt.htmlText = FontDeal.getDealLeadingStr(this.textTxt,str0);
      }
      
      public function inData(da0:TaskData) : void
      {
         this.taskData = da0;
         if(Gaming.LG.state != "no" && this.taskData.def.father != "level")
         {
            this.alpha = this.taskData == Gaming.LG.getTaskData() ? 1 : 0.3;
         }
         else
         {
            this.alpha = 1;
         }
         this.fleshEach(true);
      }
      
      private function fleshTaskEach(mustFleshB:Boolean = false) : Boolean
      {
         var str0:String = null;
         var stateChangeB0:Boolean = false;
         if(!this.taskData.uiFleshB || mustFleshB)
         {
            this.taskData.uiFleshB = true;
            if(this.stateMc.currentFrameLabel != this.taskData.state)
            {
               this.stateMc.gotoAndStop(this.taskData.state);
               stateChangeB0 = true;
            }
            str0 = "";
            str0 += this.taskData.getTaskBoxText();
            this.textStr = str0;
         }
         return stateChangeB0;
      }
      
      public function inUnionSendTaskData(da0:UnionSendTaskDataGroup) : void
      {
         this.sendData = da0;
         this.fleshEach(true);
      }
      
      private function fleshUnionSendTaskEach(mustFleshB:Boolean = false) : Boolean
      {
         var str0:String = null;
         var stateChangeB0:Boolean = this.sendData.fleshB;
         var state0:String = this.sendData.getAllState();
         if(this.sendData.fleshB || state0 == "ing" || mustFleshB)
         {
            this.sendData.fleshB = false;
            if(state0 == CookingState.COMPLETE)
            {
               this.stateMc.gotoAndStop(CookingState.COMPLETE);
            }
            else
            {
               this.stateMc.gotoAndStop(CookingState.ING);
            }
            str0 = this.sendData.getIngStr();
            this.textStr = str0;
         }
         this.btnIsLightB = state0 == CookingState.ING || this.sendData.getAllGiftNum(true) > 0;
         this.visibleB = this.btnIsLightB;
         return stateChangeB0;
      }
      
      private function linkClick(e:TextEvent) : void
      {
         if(e.text == "getUnionSendTaskGift")
         {
            Gaming.uiGroup.unionUI.buildingBoard.federalBox.fleshSendBtn();
            Gaming.uiGroup.unionUI.buildingBoard.federalBox.sendGiftBtnClick(null);
         }
      }
      
      public function fleshEach(mustFleshB:Boolean = false) : Boolean
      {
         if(this.taskData is TaskData)
         {
            return this.fleshTaskEach(mustFleshB);
         }
         return this.fleshUnionSendTaskEach(mustFleshB);
      }
   }
}

