package UI.edit
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.grid.NormalGrid;
   import UI.base.grid.NormalGridIcon;
   import UI.base.text.LinkTextGroup;
   import UI.test.SaveTestBox;
   import com.sounto.utils.TextMethod;
   import dataAll._app.edit._def.EditProDefine;
   import dataAll._app.edit.boss.BossEditData;
   import dataAll._app.edit.boss.BossEditDataGroup;
   import dataAll._app.edit.boss.BossEditMethod;
   import dataAll._app.edit.boss.BossEditPro;
   import dataAll._app.edit.card.BossCardCreator;
   import dataAll._app.edit.list.EditListAgent;
   import dataAll._app.worldMap.WorldMapData;
   import dataAll._app.worldMap.save.WorldMapSave;
   import dataAll._base.IO_Define;
   import dataAll.body.define.BodyFather;
   import dataAll.body.define.HeroDefine;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.level.define.LevelDefine;
   import dataAll.pro.ProType;
   import dataAll.skill.define.SkillDefine;
   import dataAll.skill.define.SkillDescrip;
   import dataAll.ui.GatherColor;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class BosseditEditBoard extends AutoNormalUI
   {
      private var gripBox:ItemsGripBox = new ItemsGripBox();
      
      private var gripTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var iconTag:Sprite;
      
      private var iconCon:NormalGridIcon = new NormalGridIcon();
      
      private var addBtn:NormalBtn;
      
      private var gotoBtn:NormalBtn;
      
      private var mainBtn:NormalBtn;
      
      private var delBtn:NormalBtn;
      
      private var lockBtn:NormalBtn;
      
      private var codeBtn:NormalBtn;
      
      private var testBtn:NormalBtn;
      
      private var winTag:Sprite;
      
      private var lockTag:Sprite;
      
      private var baseTxt:TextField;
      
      private var skillTxt:TextField;
      
      private var levelTxt:TextField;
      
      private var txtGroup:LinkTextGroup = new LinkTextGroup();
      
      private var nowData:BossEditData = null;
      
      private var nowPro:EditProDefine = null;
      
      private var nowSkill:SkillDefine = null;
      
      public function BosseditEditBoard()
      {
         super();
         mcTypeArr = ["tag","txt","btnSp"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         this.txtGroup.init(TextMethod.getLinkCss("","#FFFF00",true),this.linkFun,this.tipFun);
         this.txtGroup.addTxtArr([this.baseTxt,this.skillTxt,this.levelTxt],true);
         this.gripBox.setIconPro("BosseditUI/bossGrip");
         this.gripBox.arg.init(1,6,0,0,false);
         this.gripBox.evt.setWant(true);
         addChild(this.gripBox);
         NormalUICtrl.setTag(this.gripBox,this.gripTag);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.gripBox.pageBox.setToSmall();
         this.gripBox.setPagePos(this.pageTag);
         addChild(this.lockTag);
         this.lockTag.mouseChildren = false;
         this.iconTag.addChild(this.iconCon);
         this.addBtn.setName("添加首领");
         this.gotoBtn.setName("挑战");
         this.codeBtn.setName("分享");
         this.codeBtn.setSmallIcon("blue");
         this.codeBtn.activedAndEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.codeBtn);
         this.mainBtn.activedAndEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.mainBtn);
         this.lockBtn.activedAndEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.lockBtn);
         this.delBtn.setName("删除");
         this.testBtn.setName("召唤\n测试");
         this.testBtn.activedAndEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.testBtn);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function get dataG() : BossEditDataGroup
      {
         return Gaming.PG.da.bossEdit;
      }
      
      public function outLoginEvent() : void
      {
         this.nowData = null;
         this.nowPro = null;
         this.nowSkill = null;
         this.txtGroup.clearMouseData();
      }
      
      private function clearShow() : void
      {
         var btn0:NormalBtn = null;
         this.iconCon.clearData();
         for each(btn0 in btnObj)
         {
            btn0.visible = false;
         }
         this.addBtn.visible = true;
         this.lockTag.visible = false;
         this.winTag.visible = false;
         this.txtGroup.clearShow();
         this.txtGroup.clearMouseData();
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
         this.txtGroup.clearMouseData();
      }
      
      private function fleshData() : void
      {
         var arr0:Array = this.dataG.getUIDataArr();
         this.gripBox.inData_byArr(arr0,this.gripFun);
         if(Boolean(this.nowData) && arr0.indexOf(this.nowData) == -1)
         {
            this.nowData = null;
         }
         if(!this.nowData && arr0.length >= 1)
         {
            this.nowData = arr0[0];
         }
         this.fleshChooseData();
      }
      
      private function fleshBoxUI() : void
      {
         var grip0:ItemsGrid = null;
         for each(grip0 in this.gripBox.gripArr)
         {
            this.gripFun(grip0,grip0.itemsData as BossEditData);
         }
      }
      
      private function fleshNowGripUI() : void
      {
         var grip0:ItemsGrid = null;
         if(Boolean(this.nowData))
         {
            grip0 = this.gripBox.findGripByData(this.nowData);
            if(Boolean(grip0))
            {
               this.gripFun(grip0,grip0.itemsData as BossEditData);
            }
         }
      }
      
      private function gripFun(grip0:NormalGrid, da0:BossEditData) : void
      {
         var mainB0:Boolean = false;
         var winB0:Boolean = false;
         var small0:String = null;
         var bodyD0:NormalBodyDefine = da0.getBodyDefine();
         grip0.itemsData = da0;
         if(Boolean(bodyD0))
         {
            mainB0 = this.dataG.mainPan(da0);
            winB0 = da0.getWinB();
            grip0.setName(da0.getCnName());
            grip0.setIconName(bodyD0.headIconUrl);
            grip0.setLevel(da0.lv);
            grip0.setNumText(da0.getScoreUI(false));
            small0 = "no";
            if(mainB0)
            {
               small0 = "main";
            }
            else if(winB0)
            {
               small0 = "win";
            }
            grip0.setSmallIcon(small0);
         }
         else
         {
            grip0.setName("未选定首领");
            grip0.setIconName("");
            grip0.setLevel(0);
            grip0.setNumText("");
            grip0.setSmallVisible("no");
         }
      }
      
      private function addClick(e:MouseEvent) : void
      {
         var error0:String = this.dataG.addBossPan();
         if(error0 == "")
         {
            this.addBossEditList();
         }
         else
         {
            UIOrder.alertError(error0);
         }
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         this.nowData = e.childData as BossEditData;
         this.fleshChooseData();
      }
      
      private function fleshChooseData() : void
      {
         var bodyD0:NormalBodyDefine = null;
         var da0:BossEditData = this.nowData;
         if(da0 == null)
         {
            this.gripBox.setChoose_byIndex(-1);
            this.clearShow();
         }
         else
         {
            this.gripBox.setChooseByItemsData(da0);
            bodyD0 = da0.getBodyDefine();
            if(bodyD0 == null)
            {
               this.clearShow();
               this.delBtn.visible = true;
            }
            else
            {
               this.fleshBossMustHave();
            }
         }
      }
      
      private function fleshBossMustHave() : void
      {
         var da0:BossEditData = this.nowData;
         var bodyD0:NormalBodyDefine = da0.getBodyDefine();
         da0.armsRangeDeal();
         this.iconCon.setIconName(bodyD0.headIconUrl);
         this.txtGroup.inDataIndex(da0.getBaseTextAgent(),0);
         this.txtGroup.inDataIndex(da0.getSkillTextAgent(),1);
         this.txtGroup.inDataIndex(da0.getLevelTextAgent(),2);
         this.fleshBtnMustHave();
         this.fleshNowGripUI();
      }
      
      private function linkFun(txt0:TextField, link0:String, data0:*) : void
      {
         var proD0:EditProDefine = null;
         var skillD0:SkillDefine = null;
         var pro0:String = null;
         var type0:String = null;
         var num0:Number = NaN;
         var max0:Number = NaN;
         var min0:Number = NaN;
         if(link0 == BossEditMethod.addSkill)
         {
            this.addSkillEditList();
         }
         else if(link0 == BossEditMethod.mp)
         {
            this.setMapEditList();
         }
         else if(link0 == BossEditMethod.changeCn)
         {
            this.setCnEditList();
         }
         else if(link0 == BossEditPro.ar)
         {
            this.setArmsEditList();
         }
         else
         {
            proD0 = data0 as EditProDefine;
            skillD0 = data0 as SkillDefine;
            if(Boolean(proD0))
            {
               if(proD0.changeB)
               {
                  this.nowPro = proD0;
                  pro0 = proD0.name;
                  type0 = proD0.type;
                  if(type0 == ProType.BOOLEAN)
                  {
                     this.nowData.swapBoolean(pro0);
                     this.fleshBossMustHave();
                  }
                  else if(type0 == ProType.NUMBER)
                  {
                     num0 = this.nowData.getValueByDefName(pro0);
                     max0 = proD0.max;
                     min0 = this.nowData.getMinByProDefine(proD0);
                     Gaming.uiGroup.alertBox.showNumChoose("修改参数：" + proD0.cnName + "(" + min0 + "~" + max0 + ")",num0,max0,min0,1,this.numberYesFun);
                  }
               }
            }
            else if(Boolean(skillD0))
            {
               if(txt0 == this.skillTxt)
               {
                  this.delSkillAlert(skillD0);
               }
            }
         }
      }
      
      private function numberYesFun(num0:Number) : void
      {
         this.nowData.outSetValue(this.nowPro.name,num0);
         this.nowPro = null;
         this.fleshBossMustHave();
      }
      
      private function tipFun(txt0:TextField, link0:String, data0:*) : void
      {
         var tip0:String = this.nowData.getTextTip(this.txtGroup.getTxtIndex(txt0),link0,data0);
         UIOrder.showTip(tip0);
      }
      
      private function addSkillEditList() : void
      {
         var a0:EditListAgent = null;
         var error0:String = this.nowData.addSkillPan();
         if(error0 == "")
         {
            a0 = this.nowData.getSkillListAgent();
            a0.linkFun = this.addSkillLink;
            a0.tipFun = this.addSkillTip;
            Gaming.uiGroup.editList.showAgent(a0);
         }
         else
         {
            UIOrder.alertError(error0);
         }
      }
      
      private function addSkillLink(name0:String) : void
      {
         if(!this.nowData)
         {
            return;
         }
         this.nowData.addSkillName(name0);
         this.fleshBossMustHave();
      }
      
      private function addSkillTip(nd0:IO_Define) : void
      {
         var tip0:String = "";
         var skillD0:SkillDefine = nd0 as SkillDefine;
         if(Boolean(skillD0))
         {
            tip0 = skillD0.getDescription(true);
         }
         UIOrder.showTip(tip0);
      }
      
      private function delSkillAlert(d0:SkillDefine) : void
      {
         this.nowSkill = d0;
         Gaming.uiGroup.alertBox.showChoose("是否删除技能：" + d0.cnName + "？",this.delSkillYesFun);
      }
      
      private function delSkillYesFun() : void
      {
         if(!this.nowData || !this.nowSkill)
         {
            return;
         }
         this.nowData.removeSkillName(this.nowSkill.name);
         this.nowSkill = null;
         this.fleshBossMustHave();
      }
      
      private function addBossEditList() : void
      {
         var a0:EditListAgent = this.dataG.getBossListAgent();
         a0.linkFun = this.addNewBoss;
         a0.tipFun = this.addBossTip;
         Gaming.uiGroup.editList.showAgent(a0);
      }
      
      private function addBossTip(nd0:IO_Define) : void
      {
         var sarr0:Array = null;
         var d0:NormalBodyDefine = nd0 as NormalBodyDefine;
         var tip0:String = "";
         if(Boolean(d0))
         {
            sarr0 = d0.getEditSkillArr();
            tip0 = SkillDescrip.getSkillArrGather(sarr0,GatherColor.blueColor,false,false,true);
         }
         UIOrder.showTip(tip0);
      }
      
      private function addNewBoss(name0:String) : void
      {
         this.nowData = this.dataG.newBossData(name0);
         this.fleshData();
      }
      
      private function setMapEditList() : void
      {
         var a0:EditListAgent = this.dataG.getMapListAgent();
         a0.linkFun = this.setMapFun;
         a0.tipFun = this.setMapTip;
         Gaming.uiGroup.editList.showAgent(a0);
      }
      
      private function setMapTip(nd0:IO_Define) : void
      {
      }
      
      private function setMapFun(name0:String) : void
      {
         if(!this.nowData)
         {
            return;
         }
         this.nowData.setMapName(name0);
         this.fleshBossMustHave();
      }
      
      private function setCnEditList() : void
      {
         var a0:EditListAgent = this.dataG.getCnListAgent();
         a0.linkFun = this.setCnFun;
         Gaming.uiGroup.editList.showAgent(a0);
      }
      
      private function setCnFun(name0:String) : void
      {
         if(!this.nowData)
         {
            return;
         }
         this.nowData.setCnFirst(name0);
         this.fleshBossMustHave();
         this.fleshBoxUI();
      }
      
      private function setArmsEditList() : void
      {
         var a0:EditListAgent = null;
         var bodyD0:NormalBodyDefine = this.nowData.getBodyDefine();
         if(bodyD0 is HeroDefine)
         {
            a0 = BossEditMethod.getArmsListAgent();
            a0.linkFun = this.setArmsFun;
            Gaming.uiGroup.editList.showAgent(a0);
         }
         else
         {
            UIOrder.alertError("持枪单位才能设置武器。");
         }
      }
      
      private function setArmsFun(name0:String) : void
      {
         if(!this.nowData)
         {
            return;
         }
         this.nowData.setArmsRange(name0);
         if(this.dataG.mainPan(this.nowData) && this.nowData.isTorArmsB())
         {
            this.dataG.clearMain();
         }
         this.fleshBossMustHave();
         this.fleshBoxUI();
      }
      
      private function fleshBtnMustHave() : void
      {
         var lockB0:Boolean = false;
         var mainB0:Boolean = this.dataG.mainPan(this.nowData);
         var winB0:Boolean = this.nowData.getWinB();
         lockB0 = this.nowData.getEditSave().lockB;
         var isTorArmsB0:Boolean = this.nowData.isTorArmsB();
         this.gotoBtn.visible = true;
         this.delBtn.visible = true;
         this.mainBtn.visible = true;
         if(mainB0)
         {
            this.mainBtn.setSmallIcon("yellowYes");
            this.mainBtn.actived = true;
            this.mainBtn.tipString = "";
            this.mainBtn.setName("主力",true,true);
         }
         else
         {
            this.mainBtn.setSmallIcon("yellowHole");
            this.mainBtn.actived = winB0 && isTorArmsB0 == false;
            if(isTorArmsB0)
            {
               this.mainBtn.tipString = "使用自制武器的首领无法设为主力。";
            }
            else
            {
               this.mainBtn.tipString = "战胜首领才能把他设为主力。主力首领才能被上传至首领大厅。";
               if(winB0 == false)
               {
                  this.mainBtn.tipString += "\n\n<orange 现在让我们先战胜它吧！/>";
               }
            }
            this.mainBtn.setName("设为\n主力",true,true);
         }
         this.lockTag.visible = lockB0;
         this.lockBtn.visible = true;
         this.lockBtn.actived = mainB0 == false;
         this.lockBtn.setName("");
         this.lockBtn.setSmallIcon(lockB0 ? "lock" : "unlock");
         if(lockB0)
         {
            if(mainB0)
            {
               this.lockBtn.tipString = "取消主力，才能解锁。";
            }
            else if(winB0)
            {
               this.lockBtn.tipString = "解锁首领参数。\n修改首领任何参数（除名字外），都将清除“已战胜”状态。";
            }
         }
         else
         {
            this.lockBtn.tipString = "锁住首领参数，让其不被意外修改。";
         }
         this.codeBtn.visible = true;
         this.codeBtn.tipString = winB0 ? "" : "战胜首领才能分享首领代码。";
         this.codeBtn.actived = winB0;
         this.winTag.visible = winB0;
         this.fleshSumTestBtn();
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0.actived)
         {
            if(btn0 == this.addBtn)
            {
               this[btn0.label + "Click"](e);
            }
            else if(Boolean(this.nowData))
            {
               this[btn0.label + "Click"](e);
            }
         }
      }
      
      private function gotoClick(e:MouseEvent) : void
      {
         BossEditMethod.gotoMap(this.nowData);
      }
      
      private function mainClick(e:MouseEvent) : void
      {
         if(this.dataG.mainPan(this.nowData))
         {
            this.dataG.clearMain();
            Gaming.soundGroup.playSound("uiSound","swapHero");
         }
         else
         {
            this.dataG.setMain(this.nowData);
            Gaming.soundGroup.playSound("uiSound","getLottery");
         }
         this.fleshBtnMustHave();
         this.fleshBoxUI();
      }
      
      private function lockClick(e:MouseEvent) : void
      {
         this.nowData.setLockB(!this.nowData.getLockB());
         Gaming.soundGroup.playSound("uiSound","changeLabel");
         this.fleshBtnMustHave();
         if(Gaming.testCtrl.canCheatingB())
         {
            SaveTestBox.addText(BossCardCreator.editToCardXML(this.nowData));
         }
      }
      
      private function codeClick(e:MouseEvent) : void
      {
         BossEditMethod.shareCode(this.nowData);
      }
      
      private function delClick(e:MouseEvent) : void
      {
         Gaming.uiGroup.alertBox.showChoose("是否删除该首领？",this.yesDel);
      }
      
      private function yesDel() : void
      {
         this.dataG.removeData(this.nowData);
         this.fleshData();
      }
      
      private function fleshSumTestBtn() : void
      {
         var bodyD0:NormalBodyDefine = null;
         var mapName0:String = null;
         var mapDa0:WorldMapData = null;
         var mapS0:WorldMapSave = null;
         var mapCn0:String = null;
         if(Boolean(this.nowData))
         {
            this.testBtn.visible = true;
            this.testBtn.actived = false;
            bodyD0 = this.nowData.getBodyDefine();
            if(this.nowData.sumPan())
            {
               mapName0 = this.nowData.getMapName();
               if(mapName0 != "")
               {
                  mapDa0 = Gaming.PG.da.worldMap.getDataByName(mapName0);
                  mapS0 = mapDa0.getSaveNull();
                  mapCn0 = "<b><yellow " + mapDa0.getMapCnName() + "/></b>";
                  this.testBtn.tipString = "无法测试：\n地图" + mapCn0 + "未解锁或者有任务。";
                  if(Boolean(mapS0))
                  {
                     if(mapS0.winB && mapDa0.taskData == null)
                     {
                        this.testBtn.actived = true;
                        this.testBtn.tipString = "在地图" + mapCn0 + "中召唤该首领进行战斗！";
                     }
                  }
               }
            }
            else
            {
               this.testBtn.tipString = BodyFather.noSumCnStr + "都不能进行召唤测试。";
            }
         }
         else
         {
            this.testBtn.actived = false;
         }
      }
      
      private function testClick(e:MouseEvent) : void
      {
         Gaming.uiGroup.alertBox.showChoose("召唤该首领参加战斗，要继续吗？",this.gotoTest);
      }
      
      private function gotoTest() : void
      {
         var mapS0:WorldMapSave = null;
         var d0:LevelDefine = this.nowData.getSumLevelDefine(Gaming.PG.da);
         if(Boolean(d0) && this.nowData.haveDataB())
         {
            mapS0 = Gaming.PG.da.worldMap.saveGroup.getSave(this.nowData.getMapName());
            if(Boolean(mapS0))
            {
               this.dataG.setTempSummonData(this.nowData);
               Gaming.LG.chooseByLevelDefine(d0,mapS0.diffUnlock - 1);
            }
         }
      }
   }
}

