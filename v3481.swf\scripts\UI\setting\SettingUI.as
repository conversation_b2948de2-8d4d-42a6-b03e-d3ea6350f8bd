package UI.setting
{
   import UI.NormalUICtrl;
   import UI.UIShow;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AppNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import UI.setting.key.SettingKeyBox;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.worldMap.WorldMapData;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.KeyboardEvent;
   import flash.events.MouseEvent;
   import gameAll.drop.BodyDropCtrl;
   
   public class SettingUI extends AppNormalUI
   {
      public var closeBtn:SimpleButton = null;
      
      public var labelBox:LabelBox = new LabelBox();
      
      private var downBox:SettingDownBox = new SettingDownBox();
      
      public var gamingBox:SettingGamingBox = new SettingGamingBox();
      
      public var ctrlBox:SettingCtrlBox = new SettingCtrlBox();
      
      public var qualityBox:SettingQualityBox = new SettingQualityBox();
      
      private var musicBox:SettingMusicBox = new SettingMusicBox();
      
      public var keyBox:SettingKeyBox = new SettingKeyBox();
      
      public var aboutBox:Sprite;
      
      public var boxArr:Array = [this.gamingBox,this.qualityBox,this.musicBox,this.keyBox,this.ctrlBox];
      
      public var labelTag:Sprite = null;
      
      public var downSp:Sprite = null;
      
      public var continueTipSp:MovieClip;
      
      public var dropBtnSp:MovieClip = null;
      
      private var dropBtn:NormalBtn = new NormalBtn();
      
      public var countBtnSp:MovieClip = null;
      
      private var countBtn:NormalBtn = new NormalBtn();
      
      public function SettingUI()
      {
         super();
         UICn = "系统设置";
         addChild(this.labelBox);
         this.labelBox.arg.init(5,1,-6,0);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ClassProperty.getProArr(this,false);
         super.setImg(img0);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         this.labelTag.x += 20;
         NormalUICtrl.setTag(this.labelBox,this.labelTag);
         this.labelBox.inData("longLabelBtn",["game","quality","music","key","ctrl"],["系统","显示","声音","按键","其他"]);
         this.labelBox.setChoose("game");
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.gamingBox.setImg(Gaming.uiGroup.getBasicMovieClip("settingGamingBox"));
         addChild(this.gamingBox);
         this.ctrlBox.setImg(Gaming.uiGroup.getBasicMovieClip("settingCtrlBox"));
         addChild(this.ctrlBox);
         this.qualityBox.setImg(Gaming.uiGroup.getBasicMovieClip("settingQualityBox"));
         addChild(this.qualityBox);
         this.musicBox.setImg(Gaming.uiGroup.getBasicMovieClip("settingMusicBox"));
         addChild(this.musicBox);
         this.keyBox.setImg(Gaming.uiGroup.getBasicMovieClip("settingKeyBox"));
         addChild(this.keyBox);
         this.downBox.setImg(this.downSp);
         addChild(this.downBox);
         addChild(this.aboutBox);
         this.aboutBox.visible = false;
         this.aboutBox["closeBtn"].addEventListener(MouseEvent.CLICK,this.aboutCloseClick);
         addChild(this.dropBtn);
         this.dropBtn.setImg(this.dropBtnSp);
         this.dropBtn.setName("掉落查看");
         ItemsGripTipCtrl.addNormalBtnTip(this.dropBtn);
         addChild(this.countBtn);
         this.countBtn.setImg(this.countBtnSp);
         this.countBtn.setName("关卡统计");
         this.countBtn.addEventListener(MouseEvent.CLICK,this.countClick);
         this.continueTipSp.parent.removeChild(this.continueTipSp);
         this.continueTipSp.mouseChildren = false;
         this.continueTipSp.mouseEnabled = false;
         this.mouseEnabled = false;
         img0.mouseEnabled = false;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.showBox(this.labelBox.nowLabel);
         this.downBox.fleshData();
         this.setContinueVisible(Gaming.LG.isGaming());
         this.fleshDropBtn(Gaming.LG.isGaming());
      }
      
      override public function hide() : void
      {
         super.hide();
         this.gamingBox.hide();
         this.aboutBox.visible = false;
      }
      
      public function outLoginEvent() : void
      {
         this.keyBox.outLoginEvent();
      }
      
      public function keyUp(e:KeyboardEvent) : void
      {
         if(visible)
         {
            this.keyBox.keyUp(e);
         }
      }
      
      private function setContinueVisible(bb0:Boolean) : void
      {
         if(bb0)
         {
            addChild(this.continueTipSp);
            if(Gaming.PG.da.level > 20)
            {
               this.continueTipSp.gotoAndStop(1);
            }
            else
            {
               this.continueTipSp.play();
            }
         }
         else if(Boolean(this.continueTipSp.parent))
         {
            this.continueTipSp.stop();
            this.continueTipSp.parent.removeChild(this.continueTipSp);
         }
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      public function showBox(label0:String) : void
      {
         var n:* = undefined;
         this.labelBox.setChoose(label0);
         for(n in this.boxArr)
         {
            this.boxArr[n].visible = false;
         }
         if(label0 == "game")
         {
            this.gamingBox.show();
         }
         else if(label0 == "ctrl")
         {
            this.ctrlBox.show();
         }
         else if(label0 == "quality")
         {
            this.qualityBox.show();
         }
         else if(label0 == "music")
         {
            this.musicBox.show();
         }
         else if(label0 == "key")
         {
            this.keyBox.show();
         }
      }
      
      private function fleshDropBtn(gamingB0:Boolean) : void
      {
         var wda0:WorldMapData = null;
         var str0:String = null;
         this.dropBtn.visible = false;
         this.dropBtn.tipString = "";
         if(gamingB0)
         {
            if(Gaming.LG.isNormalOnlyB())
            {
               wda0 = Gaming.LG.getMapDataNull();
               if(Boolean(wda0))
               {
                  str0 = "";
                  if(wda0.def.dropByDiffB())
                  {
                     str0 = wda0.getDiffTip(Gaming.LG.getMapMode(),Gaming.LG.getDiff(),true);
                  }
                  else
                  {
                     str0 = BodyDropCtrl.getMapDropTip(wda0);
                  }
                  if(str0 != "")
                  {
                     this.dropBtn.tipString = str0;
                     this.dropBtn.visible = true;
                  }
               }
            }
         }
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.hide();
      }
      
      private function aboutCloseClick(e:MouseEvent) : void
      {
         this.aboutBox.visible = false;
      }
      
      private function countClick(e:MouseEvent) : void
      {
         UIShow.showApp("levelCount");
      }
      
      public function FTimerSecond() : void
      {
         if(visible)
         {
            this.qualityBox.FTimerSecond();
         }
      }
   }
}

