package UI.task
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.StringMethod;
   import dataAll._app.task.TaskData;
   import dataAll._app.task.define.TaskDefine;
   import dataAll._app.task.define.TaskOpenDefine;
   import dataAll._app.task.define.TaskType;
   import dataAll.equip.define.EquipType;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import gameAll.level._diy.WeekTaskLevelDiy;
   
   public class TaskOpenBox extends NormalUI
   {
      private var txt:TextField;
      
      private var gripTag:Sprite;
      
      private var btnSp:MovieClip;
      
      private var upBtnSp:MovieClip;
      
      private var thingsBox:ItemsGripBox = new ItemsGripBox();
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var upBtn:NormalBtn = new NormalBtn();
      
      public var taskUI:TaskUI;
      
      public function TaskOpenBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["txt","gripTag","btnSp","upBtnSp"];
         super.setImg(img0);
         addChild(this.upBtn);
         this.upBtn.setImg(this.upBtnSp);
         this.upBtn.setName("提升掉率倍数");
         this.upBtn.addEventListener(MouseEvent.CLICK,this.upBtnClick);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("开启任务");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         addChild(this.thingsBox);
         this.thingsBox.imgType = "equipGrip";
         this.thingsBox.arg.init(8,1,5,0);
         this.thingsBox.evt.setWantEvent(true,false,false,true,true);
         NormalUICtrl.setTag(this.thingsBox,this.gripTag);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.thingsBox);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function init() : void
      {
         this.thingsBox.clearAllData();
         this.btn.actived = false;
      }
      
      private function get nowData() : TaskData
      {
         return this.taskUI.getNowData();
      }
      
      public function fleshData() : void
      {
         var da0:TaskData = this.nowData;
         var d0:TaskDefine = da0.def;
         if(da0.isOpenB(Gaming.api.save.getNowServerDate()))
         {
            this.init();
            hide();
         }
         else
         {
            show();
            this.inData(da0);
         }
      }
      
      private function inData(da0:TaskData) : void
      {
         var timeDa0:StringDate = null;
         var timeTip0:String = null;
         var tip2:String = null;
         var mustDefine0:MustDefine = null;
         var thingsB0:Boolean = false;
         var mid_x0:int = 0;
         var mustMul0:Number = NaN;
         this.init();
         var tip0:String = "";
         var d0:TaskDefine = da0.def;
         var od0:TaskOpenDefine = d0.openD;
         var timeB0:Boolean = true;
         var taskB0:Boolean = od0.taskPanB(da0.playerData.task);
         if(od0.timeLimitB())
         {
            timeDa0 = Gaming.PG.da.time.getReadTimeDate();
            timeTip0 = timeDa0.getBetweenTip(od0.start,od0.end);
            tip0 += timeTip0;
            timeB0 = od0.timePanB(timeDa0);
         }
         var unlockTkStr0:String = da0.getMustUnlockCnStr();
         if(unlockTkStr0 != "")
         {
            tip0 = StringMethod.addNewLine(tip0,"完成任务" + unlockTkStr0 + "后解锁");
         }
         if(!timeB0 || !taskB0)
         {
            this.btn.visible = false;
            this.upBtn.visible = false;
         }
         else
         {
            this.btn.visible = true;
            tip2 = this.showModel(da0);
            tip0 = StringMethod.addNewLine(tip0,tip2);
            mustDefine0 = d0.openD.getMustDefine();
            if(d0.father == TaskType.WEEK)
            {
               mustMul0 = da0.playerData.task.getWeekKeyMust();
               mustDefine0.numMul = mustMul0;
            }
            thingsB0 = this.thingsBox.inMustDefine(mustDefine0);
            mid_x0 = this.txt.x + this.txt.width / 2;
            this.thingsBox.x = mid_x0 - this.thingsBox.width / 2 + 5;
            this.btn.actived = thingsB0;
         }
         this.txt.htmlText = tip0;
      }
      
      private function showModel(da0:TaskData) : String
      {
         var mul0:Number = NaN;
         var name0:String = null;
         var blackB0:Boolean = false;
         var num0:int = 0;
         var cn0:String = null;
         var str0:String = "";
         var weekB0:Boolean = da0.def.father == TaskType.WEEK;
         if(weekB0)
         {
            mul0 = da0.playerData.task.getWeekMul();
            name0 = da0.def.remarks;
            blackB0 = da0.def.name.indexOf("Black") >= 0;
            num0 = blackB0 ? WeekTaskLevelDiy.getBlackChipNum() : 1;
            cn0 = name0 == "arms" ? "武器" : EquipType.getCnName(name0);
            str0 += "在当前任务中消灭首领必掉" + num0 + "个" + ComMethod.color(blackB0 ? "黑色" + cn0 + "碎片" : "红色稀有" + cn0,"#00FF00") + "。\n";
            str0 += "此外，还有额外概率再掉" + num0 + "个，当前额外掉率倍数：" + ComMethod.color(mul0 + "倍","#FFFF00") + "。\n";
         }
         else
         {
            str0 += "\n\n";
         }
         str0 += "要开启当前任务，必须消耗以下物品：";
         if(weekB0)
         {
            this.upBtn.visible = true;
            this.btn.x = 149;
         }
         else
         {
            this.upBtn.visible = false;
            this.btn.x = 217;
         }
         return str0;
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var mustMul0:Number = NaN;
         var da0:TaskData = this.nowData;
         var d0:TaskDefine = da0.def;
         var mustDefine0:MustDefine = d0.openD.getMustDefine();
         if(d0.father == "week")
         {
            mustMul0 = da0.playerData.task.getWeekKeyMust();
            mustDefine0.numMul = mustMul0;
         }
         PlayerMustCtrl.deductMust(mustDefine0,this.affterBtnClick);
      }
      
      private function affterBtnClick() : void
      {
         Gaming.uiGroup.alertBox.showSuccess("任务开启成功！");
         var da0:TaskData = this.nowData;
         da0.save.openB = true;
         this.taskUI.show();
      }
      
      private function upBtnClick(e:MouseEvent) : void
      {
         var da0:TaskData = this.nowData;
         da0.playerData.task.upWeekMul();
         this.fleshData();
      }
   }
}

