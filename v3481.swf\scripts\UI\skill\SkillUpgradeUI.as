package UI.skill
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.must.NormalExpendBox;
   import UI.base.must.NormalMustBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.skill.HeroSkillData;
   import dataAll.skill.define.HeroSkillDefine;
   import dataAll.skill.save.HeroSkillSave;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class SkillUpgradeUI extends NormalUI
   {
      private var btn:NormalBtn = new NormalBtn();
      
      private var addBtn:NormalBtn = new NormalBtn();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var skillGrip:ItemsGrid = new ItemsGrid();
      
      public var nowData:HeroSkillData = null;
      
      private var mustBoxSp:Sprite;
      
      private var profiBoxSp:Sprite;
      
      private var btnSp:MovieClip;
      
      private var addBtnSp:MovieClip;
      
      private var gripTag:Sprite;
      
      private var nameTxt:TextField;
      
      private var infoTxt:TextField;
      
      private var beforeTxt:TextField;
      
      private var nextTxt:TextField;
      
      private var profiExpend:ProfiExpendUIData = null;
      
      public function SkillUpgradeUI()
      {
         super();
         addChild(this.mustBox);
         addChild(this.skillGrip);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.skillGrip);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["mustBoxSp","btnSp","addBtnSp","gripTag","nameTxt","infoTxt","beforeTxt","nextTxt","profiBoxSp"];
         super.setImg(img0);
         FontDeal.dealOne(this.nameTxt);
         FontDeal.dealOne(this.infoTxt);
         FontDeal.dealOne(this.beforeTxt);
         FontDeal.dealOne(this.nextTxt);
         addChild(this.profiBoxSp);
         this.profiBoxSp["tipBtn"].addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.profiBoxSp["tipBtn"].addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
         this.mustBox.setImg(this.mustBoxSp);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("升级技能");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         addChild(this.addBtn);
         this.addBtn.setImg(this.addBtnSp);
         this.addBtn.setName("增加");
         this.addBtn.addEventListener(MouseEvent.CLICK,this.addBtnClick);
         this.skillGrip.setImgToEquipGrip();
         NormalUICtrl.setTag(this.skillGrip,this.gripTag);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function get emustBox() : NormalExpendBox
      {
         return Gaming.uiGroup.mustBox;
      }
      
      public function fleshData() : void
      {
         if(visible)
         {
            this.showUpgradeByData(this.nowData);
         }
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         this.showUpgradeByData(e.childData as HeroSkillData);
      }
      
      public function showUpgradeByData(da0:HeroSkillData) : void
      {
         var s0:HeroSkillSave = null;
         var lv_d0:HeroSkillDefine = null;
         var dg:* = undefined;
         var next_d0:HeroSkillDefine = null;
         var must_d0:MustDefine = null;
         var bb0:Boolean = false;
         var profiB0:Boolean = false;
         this.nowData = da0;
         var haveB0:Boolean = false;
         if(Boolean(da0))
         {
            haveB0 = Gaming.PG.DATA.haveSkillDataB(da0);
         }
         this.addBtn.visible = false;
         if(haveB0)
         {
            Gaming.uiGroup.skillUI.wearBox.chooseGripByData(da0);
            Gaming.uiGroup.skillUI.setCoverText("");
            s0 = da0.save;
            lv_d0 = s0.getDefine();
            dg = Gaming.defineGroup.skill;
            this.skillGrip.inData_skill(da0);
            this.nameTxt.text = lv_d0.cnName;
            this.infoTxt.htmlText = FontDeal.getDealLeadingStr(this.infoTxt,lv_d0.getDescriptionNoActiveCd());
            this.beforeTxt.htmlText = FontDeal.getDealLeadingStr(this.beforeTxt,ComMethod.color("当前第" + lv_d0.lv + "级","#FF9900") + "\n" + lv_d0.getNowChangeText());
            next_d0 = s0.getNextDefine();
            if(!next_d0)
            {
               this.mustBox.setShowState(false);
               this.clearProfiMust();
               this.btn.actived = false;
               this.nextTxt.htmlText = FontDeal.getDealLeadingStr(this.nextTxt,ComMethod.color("已升至最高等级了","#FF9900"));
            }
            else
            {
               this.nextTxt.htmlText = FontDeal.getDealLeadingStr(this.nextTxt,ComMethod.color("升级后第" + next_d0.lv + "级","#FF9900") + "\n" + next_d0.getNowChangeText());
               must_d0 = s0.getUpradeMust();
               bb0 = this.mustBox.inData(must_d0);
               profiB0 = this.showProfiMust(da0);
               this.btn.actived = bb0 && profiB0;
            }
            this.addBtn.visible = this.profiBoxSp.visible;
            if(this.addBtn.visible)
            {
               if(this.emustBox.visible)
               {
                  this.showProfiAdd();
               }
            }
            else
            {
               this.emustBox.hide();
            }
         }
         else
         {
            this.mustBox.setShowState(false);
            this.clearProfiMust();
            this.btn.actived = false;
            this.emustBox.hide();
            Gaming.uiGroup.skillUI.setCoverText("请在右边栏选择你要升级的技能");
         }
      }
      
      private function showProfiMust(da0:HeroSkillData) : Boolean
      {
         var now0:Number = NaN;
         var must0:Number = da0.save.getProfiMust();
         if(must0 > 0)
         {
            now0 = da0.save.profi;
            this.profiBoxSp.visible = true;
            this.profiBoxSp["mustTxt"].htmlText = must0 + "";
            this.profiBoxSp["nowTxt"].htmlText = "当前:" + now0;
            if(now0 >= must0)
            {
               this.profiBoxSp["hookSp"].gotoAndStop(1);
               return true;
            }
            this.profiBoxSp["hookSp"].gotoAndStop(2);
            return false;
         }
         this.clearProfiMust();
         return true;
      }
      
      private function clearProfiMust() : void
      {
         this.profiBoxSp.visible = false;
      }
      
      private function showProfiAdd() : void
      {
         if(Boolean(this.nowData))
         {
            if(this.profiExpend == null)
            {
               this.profiExpend = new ProfiExpendUIData();
            }
            this.profiExpend.setData(this.nowData,this.fleshData);
            this.emustBox.showData(this.profiExpend,this.addBtn);
         }
         else
         {
            this.emustBox.hide();
         }
      }
      
      public function btnClick(e:MouseEvent) : void
      {
         var da0:HeroSkillData = null;
         var s0:HeroSkillSave = null;
         var must_d0:MustDefine = null;
         Gaming.uiGroup.mustBox.hide();
         if(Boolean(this.nowData))
         {
            da0 = this.nowData;
            s0 = da0.save;
            must_d0 = s0.getUpradeMust();
            PlayerMustCtrl.deductMust(must_d0,this.affter_click);
         }
      }
      
      private function affter_click() : void
      {
         var da0:HeroSkillData = this.nowData;
         var s0:HeroSkillSave = da0.save;
         var mustProfi0:Number = s0.getProfiMust();
         da0.useProfi(mustProfi0);
         s0.upgrade();
         Gaming.soundGroup.playSound("uiSound","success");
         Gaming.uiGroup.alertBox.showSuccess("升级成功！");
         this.fleshData();
         Gaming.uiGroup.skillUI.wearBox.fleshData();
         Gaming.uiGroup.mainUI.fleshCoin();
      }
      
      private function addBtnClick(e:MouseEvent) : void
      {
         if(this.emustBox.visible)
         {
            this.emustBox.hide();
         }
         else
         {
            this.showProfiAdd();
         }
      }
      
      protected function tipOver(e:MouseEvent) : void
      {
         var str0:String = "";
         str0 += "1、主动技能被使用时可获得熟练度。";
         str0 += "\n2、被动技能被触发时可获得熟练度";
         str0 += "\n3、熟练度为每个技能独有，不能共享。";
         str0 += "\n4、熟练度获取有每日上限和累计上限。";
         str0 += "\n5、熟练度到达总上限，或者到达每日上限时，溢出的熟练度将拿出1/5（四舍五入），转移到该角色其他熟练度未满的技能上。";
         str0 += "\n6、队友拥有2倍的熟练度获取速度。";
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      protected function tipOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
   }
}

