package UI.bag.moreChoose
{
   import UI.bag.ItemsGripBox;
   import UI.base.AutoNormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.ItemsMoreOrder;
   import flash.events.TextEvent;
   import flash.text.StyleSheet;
   import flash.text.TextField;
   
   public class ItemsMoreCtrlBox extends AutoNormalUI
   {
      private var itemsBox:ItemsGripBox = null;
      
      private var dataGroup:ItemsDataGroup = null;
      
      private var txt:TextField;
      
      private var nowOrder:String = "";
      
      public function ItemsMoreCtrlBox()
      {
         super();
         mcTypeArr = ["txt"];
      }
      
      public function setToNormalImg() : void
      {
         setImg(Gaming.uiGroup.getBasicMovieClip("moreCtrlBox"));
         var style0:StyleSheet = ComMethod.getLinkCss("#00FFFF","#FFFFFF");
         this.txt.styleSheet = style0;
         FontDeal.dealOne(this.txt);
         this.txt.addEventListener(TextEvent.LINK,this.linkClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         INIT.showError("该方法必须被覆盖！");
      }
      
      override public function hide() : void
      {
         super.hide();
         this.clearBox();
      }
      
      public function showBox(ig0:ItemsGripBox) : void
      {
         this.clearBox();
         if(Boolean(ig0))
         {
            this.itemsBox = ig0;
            this.itemsBox.moreChooseB = true;
            this.itemsBox.addEventListener(ClickEvent.ON_CLICK,this.click);
            this.dataGroup = this.itemsBox.fatherData;
            this.dataGroup.setAllChosen(false);
            this.itemsBox.fleshMoreChoose();
            this.fleshText();
            show();
         }
      }
      
      private function clearBox() : void
      {
         if(Boolean(this.itemsBox))
         {
            this.itemsBox.moreChooseB = false;
            this.itemsBox.removeEventListener(ClickEvent.ON_CLICK,this.click);
            this.dataGroup.setAllChosen(false);
            this.itemsBox.fleshMoreChoose();
            this.dataGroup = null;
            this.itemsBox = null;
         }
      }
      
      private function fleshText() : void
      {
         var chosenNum0:int = int(this.dataGroup.getChosenArr().length);
         var linkB0:Boolean = this.dataGroup.getChosenArr().length > 0;
         var numStr0:String = "     已选" + ComMethod.color(chosenNum0 + "","#00FF00") + "个";
         var chooseStr0:String = "选择：" + ItemsMoreOrder.getTextByNameArr(this.dataGroup.getChosenChooseOrderArr());
         var ctrlStr0:String = "操作：" + ItemsMoreOrder.getTextByNameArr(this.dataGroup.getChosenCtrlOrderArr(),linkB0);
         this.txt.htmlText = FontDeal.getDealLeadingStr(this.txt,chooseStr0 + numStr0 + "\n" + ctrlStr0);
      }
      
      private function click(e:ClickEvent) : void
      {
         var da0:IO_ItemsData = e.childData as IO_ItemsData;
         if(Boolean(da0))
         {
            da0.isChosen = !da0.isChosen;
            this.itemsBox.fleshMoreChoose();
            this.fleshText();
         }
      }
      
      private function linkClick(e:TextEvent) : void
      {
         var order0:String = e.text;
         this.nowOrder = order0;
         var tip0:String = this.dataGroup.getChosenOrderTip(order0);
         if(tip0 == "")
         {
            this.doOrder();
         }
         else
         {
            Gaming.uiGroup.alertBox.showNormal(tip0,"yesAndNo",this.doOrder);
         }
      }
      
      private function doOrder() : void
      {
         var order0:String = this.nowOrder;
         var fleshB0:Boolean = this.dataGroup.doChosenOrder(order0);
         if(ItemsMoreOrder.isChooseB(order0))
         {
            this.itemsBox.fleshMoreChoose();
            Gaming.soundGroup.playSound("uiSound","click");
         }
         else
         {
            if(fleshB0)
            {
               this.itemsBox.refleshItemsDataNow();
            }
            this.itemsBox.fleshMoreChoose();
            Gaming.soundGroup.playSound("uiSound","changeLabel");
         }
         this.fleshText();
      }
   }
}

