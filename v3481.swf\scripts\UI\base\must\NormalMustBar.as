package UI.base.must
{
   import UI.base.NormalUI;
   import com.sounto.oldUtils.ComMethod;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class NormalMustBar extends NormalUI
   {
      private var txt:TextField;
      
      private var valueTxt:TextField;
      
      private var hookMc:MovieClip;
      
      public function NormalMustBar()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["txt","valueTxt","hookMc"];
         super.setImg(img0);
         this.setHook(true);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function setText(str0:String) : void
      {
         if(Boolean(this.txt))
         {
            this.txt.htmlText = str0;
         }
      }
      
      public function setValue(str0:String) : void
      {
         if(Boolean(this.valueTxt))
         {
            this.valueTxt.htmlText = str0;
         }
      }
      
      public function setHook(bb0:Boolean) : void
      {
         if(Boolean(this.hookMc))
         {
            this.hookMc.visible = true;
            this.hookMc.gotoAndStop(bb0 ? 1 : 2);
         }
      }
      
      public function inData(v0:Number, max0:Number) : Boolean
      {
         var bb0:Boolean = v0 >= max0;
         this.setValue(ComMethod.getPerCompareStr(v0,max0));
         this.setHook(bb0);
         return bb0;
      }
      
      public function clearAll() : void
      {
         this.setValue("0");
         if(Boolean(this.hookMc))
         {
            this.hookMc.visible = false;
         }
      }
   }
}

