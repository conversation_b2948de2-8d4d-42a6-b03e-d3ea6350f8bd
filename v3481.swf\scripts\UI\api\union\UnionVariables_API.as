package UI.api.union
{
   import com.adobe.serialization.json.JSON2;
   import unit4399.events.UnionEvent;
   
   public class UnionVariables_API extends UnionBase_API
   {
      public function UnionVariables_API()
      {
         super();
      }
      
      public function getVariables(idx:int, ids:Array, yesFun0:Function = null, noFun0:Function = null) : void
      {
         var arr0:Array = null;
         var id0:* = null;
         var obj2:Object = null;
         setFun(yesFun0,noFun0);
         if(Gaming.isLocal())
         {
            arr0 = [];
            for each(id0 in ids)
            {
               obj2 = {};
               obj2.id = id0;
               obj2.value = 30;
               this.yes_getVariables(JSON2.encode([obj2]));
            }
         }
         else
         {
            serviceHold.getVariables(idx,ids);
         }
      }
      
      private function yes_getVariables(jsonStr0:String) : void
      {
         doYesFun(jsonStr0);
      }
      
      public function doVariable(idx:int, id:int, yesFun0:Function = null, noFun0:Function = null) : void
      {
         setFun(yesFun0,noFun0);
         if(Gaming.isLocal())
         {
            this.yes_doVariable(true);
         }
         else
         {
            serviceHold.doVariable(idx,id);
         }
      }
      
      private function yes_doVariable(bb0:Boolean) : void
      {
         doYesFun(bb0);
      }
      
      public function onVariablesSuccess(e:UnionEvent) : void
      {
         var dataObj:Object = e.data;
         var data0:* = dataObj.data;
         switch(dataObj.apiName)
         {
            case UnionEvent.UNI_API_HQBL:
               this.yes_getVariables(data0);
               break;
            case UnionEvent.UNI_API_XGBL:
               this.yes_doVariable(data0);
         }
      }
   }
}

