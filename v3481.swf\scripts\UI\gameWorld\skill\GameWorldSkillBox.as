package UI.gameWorld.skill
{
   import UI.base.NormalUI;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import gameAll.body.skill.SkillData;
   import gameAll.body.skill.SkillDataGroup;
   
   public class GameWorldSkillBox extends NormalUI
   {
      private var gripMax:int = 10;
      
      private var skillDataGroup:SkillDataGroup = null;
      
      private var dataArr:Array = null;
      
      private var nowSilenceB:Boolean = false;
      
      private var skillGripArr:Array = [];
      
      private var iconMaxWidth:int = 0;
      
      public function GameWorldSkillBox()
      {
         super();
         elementNameArr = [];
         this.mouseChildren = false;
         this.mouseEnabled = false;
      }
      
      public function setGripMax(v0:int) : void
      {
         this.gripMax = v0;
      }
      
      public function setSkillIconMaxWidth(v0:int) : void
      {
         this.iconMaxWidth = v0;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var num0:int = 0;
         var i:int = 0;
         var n:* = undefined;
         var mc2:DisplayObject = null;
         var grip0:GameWorldSkillGrip = null;
         super.setImg(img0);
         var mc0:Sprite = img0;
         if(Boolean(mc0))
         {
            num0 = mc0.numChildren;
            for(i = 0; i < num0; i++)
            {
               mc2 = mc0.getChildAt(0);
               if(mc2 is MovieClip)
               {
                  grip0 = new GameWorldSkillGrip();
                  grip0.index = this.skillGripArr.length;
                  grip0.setImg(mc2 as MovieClip);
                  grip0.iconMaxWidth = this.iconMaxWidth;
                  this.skillGripArr.push(grip0);
               }
            }
            for(n in this.skillGripArr)
            {
               mc0.addChild(this.skillGripArr[n]);
            }
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function inSkillDataGroup(g0:SkillDataGroup) : Boolean
      {
         var changeB0:Boolean = false;
         var newArr0:Array = g0.getGWDataList();
         if(newArr0 != this.dataArr)
         {
            this.skillDataGroup = g0;
            this.changeData(newArr0);
            changeB0 = true;
         }
         this.fleshEach();
         return changeB0;
      }
      
      private function changeData(newArr0:Array) : void
      {
         var grip0:GameWorldSkillGrip = null;
         var da0:SkillData = null;
         this.dataArr = newArr0;
         var index0:int = 0;
         for each(grip0 in this.skillGripArr)
         {
            da0 = newArr0[index0];
            if(Boolean(da0))
            {
               grip0.inData(da0);
               this.setOneGripVisible(grip0,true);
            }
            else
            {
               grip0.clearData();
               this.setOneGripVisible(grip0,false);
            }
            index0++;
         }
         this.nowSilenceB = false;
         if(newArr0.length == 0 || !Gaming.LG.canUsePlayerSkillB())
         {
            visible = false;
         }
         else
         {
            visible = true;
         }
      }
      
      public function fleshBySkillDa(da0:SkillData) : void
      {
         var grip0:GameWorldSkillGrip = this.getGripBySkillDa(da0);
         if(Boolean(grip0))
         {
            grip0.inData(da0);
         }
      }
      
      public function getGripBySkillDa(da0:SkillData) : GameWorldSkillGrip
      {
         var grip0:GameWorldSkillGrip = null;
         for each(grip0 in this.skillGripArr)
         {
            if(grip0.dat == da0)
            {
               return grip0;
            }
         }
         return null;
      }
      
      public function setExpPer(per0:Number) : void
      {
      }
      
      private function setOneGripVisible(grip0:GameWorldSkillGrip, bb0:Boolean) : void
      {
         var mc0:Sprite = null;
         if(grip0.visible != bb0)
         {
            if(bb0)
            {
               mc0 = img;
               if(!grip0.parent)
               {
                  mc0.addChild(grip0);
               }
            }
            else if(Boolean(grip0.parent))
            {
               grip0.parent.removeChild(grip0);
            }
            grip0.visible = bb0;
         }
      }
      
      public function playFleshing(baseLabel0:String) : void
      {
         var n:* = undefined;
         var grip0:GameWorldSkillGrip = null;
         var da0:SkillData = null;
         for(n in this.skillGripArr)
         {
            grip0 = this.skillGripArr[n];
            da0 = grip0.itemsData as SkillData;
            if(da0.define.baseLabel == baseLabel0)
            {
               grip0.playFlashing();
            }
         }
      }
      
      public function clearAll() : void
      {
         var n:* = undefined;
         var grip0:GameWorldSkillGrip = null;
         for(n in this.skillGripArr)
         {
            grip0 = this.skillGripArr[n];
            grip0.clearData();
         }
         this.skillDataGroup = null;
         this.dataArr = null;
      }
      
      public function setAllSkillGripVisible(bb0:Boolean) : void
      {
         var n:* = undefined;
         var grip0:GameWorldSkillGrip = null;
         for(n in this.skillGripArr)
         {
            grip0 = this.skillGripArr[n];
            grip0.visible = bb0;
         }
      }
      
      public function fleshEach() : void
      {
         var n:* = undefined;
         var grip0:GameWorldSkillGrip = null;
         var bb0:Boolean = false;
         if(this.skillDataGroup is SkillDataGroup)
         {
            bb0 = !this.skillDataGroup.BB.getData().stateD.canDoSkillB();
         }
         for(n in this.skillGripArr)
         {
            grip0 = this.skillGripArr[n];
            grip0.fleshEach();
            if(bb0 != this.nowSilenceB)
            {
               grip0.setSilence(bb0);
            }
         }
         this.nowSilenceB = bb0;
      }
   }
}

