package UI.count
{
   import dataAll._player.PlayerData;
   import dataAll._player.count.HeadCountProSave;
   import dataAll._player.count.PlayerCountSave;
   import dataAll._player.count.define.CountDefine;
   import gameAll.more.DoubleCtrl;
   
   public class CountCtrl
   {
      public static var onlineTimePer:Number = 0;
      
      public function CountCtrl()
      {
         super();
      }
      
      public static function sendCount4399() : String
      {
         var urlString0:String = null;
         if(Boolean(Gaming.PG.loginData.save))
         {
            urlString0 = dealUrlBeforeNewDay();
            Gaming.api.count4399.start(Gaming.PG.loginData.uid,Gaming.PG.getSaveIndex(),urlString0);
            return urlString0;
         }
         return "";
      }
      
      private static function dealUrlBeforeNewDay() : String
      {
         var d0:CountDefine = null;
         var v0:Number = NaN;
         var str0:String = "";
         var obj0:Object = Gaming.defineGroup.count.obj;
         for each(d0 in obj0)
         {
            v0 = getValue(d0.name);
            if(PD.level < d0.mustLv)
            {
               v0 = -1;
            }
            str0 += "&" + d0.getStringBy(v0);
         }
         return str0;
      }
      
      private static function get PD() : PlayerData
      {
         return Gaming.PG.da;
      }
      
      private static function getValue(name0:String) : Number
      {
         var v0:Number = NaN;
         var taskLabel0:String = null;
         var count0:PlayerCountSave = Gaming.PG.save.getCount();
         var taskCompleteNumObj0:Object = PD.task.saveGroup.todayCompleteNumObj;
         if(count0.hasOwnProperty(name0))
         {
            v0 = Number(count0[name0]);
            if(name0 == "onlineTime" || name0 == "levelOnlineTime" || name0 == "doubleOnlineTime")
            {
               v0 = Math.ceil(v0 / 60);
            }
            return v0;
         }
         if(name0.indexOf("Task") > 0)
         {
            taskLabel0 = name0.replace("Task","");
            return PD.task.saveGroup.getTrueTodayCompleteNum(taskLabel0);
         }
         if(name0.indexOf("spaceMapWin") >= 0)
         {
            return spaceMapWin(name0);
         }
         return CountCtrl[name0]();
      }
      
      private static function lv() : Number
      {
         return PD.level;
      }
      
      private static function realName() : Number
      {
         return PD.main.save.s18;
      }
      
      private static function dps() : Number
      {
         return PD.getDps();
      }
      
      private static function mainLevel() : Number
      {
         var taskMax0:int = PD.task.getMainTaskMaxLevel();
         if(taskMax0 == 0)
         {
            taskMax0 = PD.worldMap.saveGroup.getMaxMapLevel();
         }
         return taskMax0;
      }
      
      private static function craftLv() : Number
      {
         return PD.space.craft.getCraftMaxLv();
      }
      
      private static function spaceMissionIndex() : Number
      {
         var name0:* = null;
         var num0:int = 0;
         var taskNameArr0:Array = ["YaSomewhere_1","MadmanPlot","EarthSky_1","SolarInsideEnemy","SolarInsideBoss","CeresSouthEnemy","CeresSouthBoss","GanymedeEnemy","GanymedeBoss"];
         for each(name0 in taskNameArr0)
         {
            if(PD.task.isCompleteB(name0))
            {
               num0++;
            }
         }
         return num0;
      }
      
      private static function spaceMapWin(name0:String) : Number
      {
         var map0:String = name0.replace("spaceMapWin_","");
         return PD.space.getMapWinNum(map0);
      }
      
      public static function FTimerSecond() : void
      {
         var count0:PlayerCountSave = null;
         var mustTime0:Number = NaN;
         var continuousLoginDay0:HeadCountProSave = null;
         var pd0:PlayerData = PD;
         if(Boolean(pd0))
         {
            count0 = Gaming.PG.save.getCount();
            count0.onlineTime += 1 + onlineTimePer;
            if(pd0.level >= 20)
            {
               ++count0.onlineTime20;
            }
            mustTime0 = 3600;
            if(count0.onlineTime >= mustTime0)
            {
               continuousLoginDay0 = Gaming.PG.save.headCount.continuousLoginDay;
               if(!continuousLoginDay0.todayAddB)
               {
                  continuousLoginDay0.add(Gaming.api.save.getNowServerDate().getStr());
               }
            }
            if(Gaming.LG.state == "ing")
            {
               ++count0.levelOnlineTime;
               if(DoubleCtrl.levelDoubleB())
               {
                  ++count0.doubleOnlineTime;
               }
            }
         }
      }
      
      public static function weKill() : void
      {
         var count0:PlayerCountSave = Gaming.PG.save.getCount();
         ++count0.todayKillEnemyNum;
      }
      
      public static function skillStoneDrop() : void
      {
         var count0:PlayerCountSave = Gaming.PG.save.getCount();
         ++count0.todaySkillStoneNum;
      }
      
      public static function arenaStart() : void
      {
         var count0:PlayerCountSave = Gaming.PG.save.getCount();
         ++count0.todayArenaNum;
      }
      
      public static function addUnionContribution(v0:Number) : void
      {
         var count0:PlayerCountSave = Gaming.PG.save.getCount();
         count0.todayUnionContribution += v0;
      }
   }
}

