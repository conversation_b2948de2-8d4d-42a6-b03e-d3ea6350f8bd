package UI.arena.room
{
   import UI.base.box.NormalBox;
   import UI.base.button.NormalBtn;
   
   public class ArenaRivalBox extends NormalBox
   {
      public function ArenaRivalBox()
      {
         super();
      }
      
      public function inDataByRivalDataArr(arr0:Array) : void
      {
         inData_byArr(arr0,"inDataByTopBarData");
      }
      
      override protected function getNewGrip() : NormalBtn
      {
         return new ArenaRivalGrip();
      }
   }
}

