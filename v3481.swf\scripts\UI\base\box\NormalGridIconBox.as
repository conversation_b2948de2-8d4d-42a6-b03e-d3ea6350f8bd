package UI.base.box
{
   import UI.base.grid.NormalGridIcon;
   import flash.display.Sprite;
   
   public class NormalGridIconBox extends Sprite
   {
      public var arg:BoxArrangeAddit = new BoxArrangeAddit();
      
      private var iconArr:Array = [];
      
      public function NormalGridIconBox()
      {
         super();
      }
      
      public function addByUrlArr(arr0:Array) : void
      {
         var n:* = undefined;
         var url0:String = null;
         var icon0:NormalGridIcon = null;
         this.clear();
         for(n in arr0)
         {
            url0 = arr0[n];
            icon0 = new NormalGridIcon();
            icon0.setIconName(url0);
            addChild(icon0);
            this.iconArr.push(icon0);
         }
         this.arg.inData_byArr(this.iconArr);
      }
      
      public function clear() : void
      {
         var n:* = undefined;
         var icon0:NormalGridIcon = null;
         this.arg.clear();
         for(n in this.iconArr)
         {
            icon0 = this.iconArr[n];
            icon0.clear();
            this.removeChild(icon0);
         }
         this.iconArr.length = 0;
      }
   }
}

