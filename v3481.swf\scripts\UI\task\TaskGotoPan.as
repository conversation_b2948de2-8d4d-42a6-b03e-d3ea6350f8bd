package UI.task
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.StringMethod;
   import dataAll._app.edit.arms.ArmsTorData;
   import dataAll._app.edit.boss.BossEditData;
   import dataAll._app.task.TaskData;
   import dataAll._app.task.define.TaskDefine;
   import dataAll._app.task.define.TaskType;
   import dataAll._player.PlayerData;
   import dataAll._player.more.MoreData;
   import dataAll._player.more.NormalPlayerData;
   import dataAll._player.role.RoleName;
   import dataAll.arms.ArmsData;
   import dataAll.ui.GatherColor;
   
   public class TaskGotoPan
   {
      private static var yesFun:Function = null;
      
      public function TaskGotoPan()
      {
         super();
      }
      
      private static function get PD() : PlayerData
      {
         return Gaming.PG.da;
      }
      
      public static function gotoMap(da0:TaskData, yesFun0:Function) : void
      {
         yesFun = yesFun0;
         var d0:TaskDefine = da0.def;
         var fun0:Function = TaskGotoPan[d0.name];
         if(fun0 is Function)
         {
            fun0(da0);
         }
         else if(da0.def.isMemoryB())
         {
            memoryPan(da0);
         }
         else
         {
            doYesFun();
         }
      }
      
      private static function doYesFun() : void
      {
         var fun0:Function = yesFun;
         if(fun0 is Function)
         {
            yesFun = null;
            fun0();
         }
      }
      
      private static function memoryPan(da0:TaskData) : void
      {
         var mg0:MoreData = null;
         var pd0:NormalPlayerData = null;
         var armsDa0:ArmsData = null;
         var arr0:Array = Gaming.PG.da.more.dataArr.concat(Gaming.PG.da.more.heroData);
         var limitRoleArr0:Array = RoleName.getMemoryArrByLv(da0.getLv());
         var limitLv0:int = da0.getLv();
         var partsB0:Boolean = limitLv0 >= TaskType.getPartsUnlock();
         var noRoleCnArr0:Array = [];
         for each(mg0 in arr0)
         {
            pd0 = mg0.DATA;
            if(pd0 is PlayerData || limitRoleArr0.indexOf(pd0.getRoleName()) >= 0)
            {
               for each(armsDa0 in pd0.arms.dataArr)
               {
                  if(armsDa0.save.getTrueLevel() > limitLv0)
                  {
                     noRoleCnArr0.push(pd0.getSayCn());
                     break;
                  }
               }
            }
         }
         if(noRoleCnArr0.length > 0)
         {
            Gaming.uiGroup.alertBox.showError("以下角色的武器超过了" + limitLv0 + "级：\n" + StringMethod.concatStringArr(noRoleCnArr0,4));
         }
         else
         {
            doYesFun();
         }
      }
      
      public static function sumBossTask(da0:TaskData) : void
      {
         var init0:BossEditData = null;
         var cn0:String = null;
         var s0:String = null;
         var main0:BossEditData = PD.bossEdit.getMain();
         if(Boolean(main0) && main0.sumPan())
         {
            doYesFun();
         }
         else
         {
            init0 = PD.bossEdit.getSumBossTaskInit();
            cn0 = ComMethod.color(init0.getCnName(),GatherColor.greenColor);
            s0 = "你当前没有可以召唤的主力首领，\n是否使用默认的<b>" + cn0 + "</b>挑战任务？";
            Gaming.uiGroup.alertBox.showChoose(s0,doYesFun,null,init0.getIconUrl());
         }
      }
      
      public static function armsEdit23(da0:TaskData) : void
      {
         var main0:ArmsTorData = PD.armsTor.getMain();
         if(Boolean(main0))
         {
            doYesFun();
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("你没有主力武器，请前往首领工厂创造一把。");
         }
      }
   }
}

