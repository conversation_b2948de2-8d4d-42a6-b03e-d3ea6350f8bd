package UI.gift.active
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.UIShow;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.loadBar.LoadBar;
   import UI.main.WorldMapBox;
   import dataAll._app.active.AccActiveSave;
   import dataAll._app.active.ActiveData;
   import dataAll._app.active.ActiveGiftData;
   import dataAll._app.active.ActiveTaskData;
   import dataAll._app.active.define.ActiveTask;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.pet.PetCount;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class GiftActiveBoard extends NormalUI
   {
      private var barSp:Sprite;
      
      private var taskTag:Sprite;
      
      private var taskPageTag:Sprite;
      
      private var giftTag:Sprite;
      
      private var activeCoverSp:Sprite;
      
      private var activeTxt:TextField;
      
      private var activeBtnSp:MovieClip;
      
      private var activeGripSp:MovieClip;
      
      private var activeGrip:ItemsGrid = new ItemsGrid();
      
      private var activeBtn:NormalBtn = new NormalBtn();
      
      private var oneBtnSp:MovieClip;
      
      private var oneBtn:NormalBtn = new NormalBtn();
      
      private var taskBox:ItemsGripBox = new ItemsGripBox();
      
      private var giftBox:ActiveGiftBox = new ActiveGiftBox();
      
      private var activeBar:LoadBar = new LoadBar();
      
      public function GiftActiveBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["barSp","taskTag","taskPageTag","giftTag","activeCoverSp","activeTxt","activeBtnSp","activeGripSp","oneBtnSp"];
         super.setImg(img0);
         addChild(this.activeBar);
         this.activeBar.setImg(this.barSp);
         addChild(this.taskBox);
         NormalUICtrl.setTag(this.taskBox,this.taskTag);
         this.taskBox.setIconPro("GiftUI/activeTaskGrip");
         this.taskBox.arg.init(1,12,0,6);
         this.taskBox.addEventListener(ClickEvent.ON_CLICK,this.taskBarClick);
         this.taskBox.pageBox.setToSmall();
         this.taskBox.pageBox.setXY_bySp(this.taskPageTag,this.taskBox);
         addChild(this.giftBox);
         NormalUICtrl.setTag(this.giftBox,this.giftTag);
         this.giftBox.setIconPro("GiftUI/activeGiftGrip");
         this.giftBox.arg.init(3,2,4,4);
         this.giftBox.evt.setWantEvent(true,false,false,true,true);
         this.giftBox.evt.setToCurrentTarget(true);
         this.giftBox.addEventListener(ClickEvent.ON_CLICK,this.btnClick);
         this.giftBox.addEventListener(ClickEvent.ON_OVER,this.btnOver);
         addChild(this.oneBtn);
         this.oneBtn.setImg(this.oneBtnSp);
         this.oneBtn.setName("一键领取");
         this.oneBtn.addEventListener(MouseEvent.CLICK,this.oneBtnClick);
         addChild(this.activeGrip);
         this.activeGrip.setImg(this.activeGripSp);
         this.activeGrip.addEventListener(MouseEvent.MOUSE_OVER,this.activityGripOver);
         addChild(this.activeBtn);
         this.activeBtn.setImg(this.activeBtnSp);
         this.activeBtn.setName("获得奖励");
         FontDeal.dealOne(this.activeTxt);
         addChild(this.activeCoverSp);
         this.activeBtn.addEventListener(MouseEvent.CLICK,this.activeBtnClick);
      }
      
      private function get activeData() : ActiveData
      {
         return Gaming.PG.da.active;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      private function fleshData() : void
      {
         var activeData0:ActiveData = this.activeData;
         var taskArr0:Array = activeData0.getTaskDataArr();
         var giftArr0:Array = activeData0.getGiftDataArr();
         this.taskBox.inData_byArr(taskArr0,"inData_byActiveTaskData");
         this.giftBox.inData_byArr(giftArr0,"inData_byActiveGiftData");
         this.oneBtn.actived = this.getCanGiftNum() > 0;
         var nowActive0:int = activeData0.nowActive;
         var maxActive0:int = activeData0.getMaxActiveValue();
         this.activeBar.setPer(nowActive0 / maxActive0);
         this.activeBar.setText(nowActive0 + " / " + maxActive0);
         this.fleshActivity();
      }
      
      private function btnClick(e:ClickEvent) : void
      {
         var da0:ActiveGiftData = null;
         var bb0:Boolean = false;
         var grip0:ActiveGiftGrip = e.child as ActiveGiftGrip;
         if(grip0.canGetGiftB)
         {
            da0 = grip0.itemsData as ActiveGiftData;
            bb0 = GiftAddit.addAndAutoBagSpacePan(da0.getGift());
            if(bb0)
            {
               Gaming.PG.da.active.save.setHaveGiftB(da0.name,true);
               this.fleshData();
            }
         }
      }
      
      private function getCanGiftNum() : int
      {
         var grip0:ActiveGiftGrip = null;
         var num0:int = 0;
         for each(grip0 in this.giftBox.gripArr)
         {
            if(grip0.canGetGiftB)
            {
               num0++;
            }
         }
         return num0;
      }
      
      private function getCanGiftMeger() : GiftAddDefineGroup
      {
         var grip0:ActiveGiftGrip = null;
         var da0:ActiveGiftData = null;
         var g0:GiftAddDefineGroup = new GiftAddDefineGroup();
         for each(grip0 in this.giftBox.gripArr)
         {
            if(grip0.canGetGiftB)
            {
               da0 = grip0.itemsData as ActiveGiftData;
               g0.merge(da0.getGift());
            }
         }
         return g0;
      }
      
      private function oneBtnClick(e:MouseEvent) : void
      {
         var error0:String = null;
         var grip0:ActiveGiftGrip = null;
         var da0:ActiveGiftData = null;
         var g0:GiftAddDefineGroup = this.getCanGiftMeger();
         if(g0.haveDataB())
         {
            error0 = GiftAddit.bagSpacePan(g0);
            if(error0 == "")
            {
               for each(grip0 in this.giftBox.gripArr)
               {
                  if(grip0.canGetGiftB)
                  {
                     da0 = grip0.itemsData as ActiveGiftData;
                     Gaming.PG.da.active.save.setHaveGiftB(da0.name,true);
                  }
               }
               GiftAddit.addAndTip(g0);
               this.fleshData();
            }
            else
            {
               UIOrder.alertError(error0);
            }
         }
      }
      
      private function btnOver(e:ClickEvent) : void
      {
         Gaming.uiGroup.giftTip.showTip((e.childData as ActiveGiftData).getGift(),e.child as DisplayObject);
      }
      
      private function btnOut(e:ClickEvent) : void
      {
      }
      
      private function taskBarClick(e:ClickEvent) : void
      {
         var da0:ActiveTaskData = null;
         var label0:String = null;
         var taskLabel0:String = null;
         var grip0:NormalBtn = e.child as NormalBtn;
         if(grip0.getSmallIconStr() != "")
         {
            da0 = grip0.itemsData as ActiveTaskData;
            label0 = da0.def.name;
            if(label0 == ActiveTask.dailySign)
            {
               Gaming.uiGroup.giftUI.showBox(label0);
            }
            else if(label0 == ActiveTask.vipDay)
            {
               UIShow.showByLabel("vip");
            }
            else if(label0 != ActiveTask.loveGift)
            {
               if(label0 == ActiveTask.endlessLevel)
               {
                  WorldMapBox.gotoLevelByWorldMapId("ShuangTa",true);
               }
               else if(label0 == ActiveTask.ask)
               {
                  UIShow.showByLabel("ask");
               }
               else if(label0.indexOf(ActiveTask.Task) > 0)
               {
                  taskLabel0 = label0.replace(ActiveTask.Task,"");
                  Gaming.uiGroup.taskUI.nowLabel = taskLabel0;
                  UIShow.showByLabel("task");
               }
               else if(label0 == ActiveTask.arena)
               {
                  UIShow.showByLabel(label0);
               }
               else if(ActiveTask.unionArr.indexOf(label0) >= 0)
               {
                  UIShow.showByLabel("union");
               }
               else if(label0 == ActiveTask.wilder)
               {
                  UIShow.showByLabel("wilder");
               }
               else if(label0 == ActiveTask.smelt)
               {
                  UIShow.showByLabel("city");
                  Gaming.uiGroup.cityUI.showBox("smelt");
               }
               else if(label0 == ActiveTask.petDispatch)
               {
                  if(PetCount.levelCanB())
                  {
                     Gaming.uiGroup.petUI.showAndLabel("dispatch");
                  }
                  else
                  {
                     Gaming.uiGroup.alertBox.showError("宠物系统未开放。");
                  }
               }
               else if(label0 == ActiveTask.useArenaStamp)
               {
                  UIShow.showByLabel("arena");
               }
               else if(label0 == ActiveTask.gm3)
               {
                  UIShow.showByLabel("anniverGm");
               }
            }
         }
      }
      
      private function activityGripOver(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var g0:GiftAddDefineGroup = AccActiveSave.getActivityGiftGroup();
         Gaming.uiGroup.giftTip.showTip(g0,btn0);
      }
      
      private function fleshActivity() : void
      {
         var btnState0:int = 0;
         if(Boolean(this.activeData.save.getAcc()))
         {
            this.activeCoverSp.visible = !this.activeData.save.getAccShowB();
            this.activeGrip.setIconName("ThingsIcon/postNormalChest");
            this.activeTxt.htmlText = FontDeal.getDealLeadingStr(this.activeTxt,this.activeData.getActivityText());
            btnState0 = this.activeData.getActivityBtnState();
            if(Boolean(Gaming.PG.da.findEquipDataByName("snowShadow2",true)))
            {
               btnState0 = 2;
            }
            if(btnState0 == 0)
            {
               this.activeBtn.actived = false;
               this.activeBtn.setName("领取");
            }
            else if(btnState0 == 1)
            {
               this.activeBtn.actived = true;
               this.activeBtn.setName("领取");
            }
            else
            {
               this.activeBtn.actived = false;
               this.activeBtn.setName("已领取");
            }
         }
         else
         {
            this.activeCoverSp.visible = true;
         }
      }
      
      private function activeBtnClick(e:MouseEvent) : void
      {
         var gift0:GiftAddDefineGroup = null;
         var bb0:Boolean = false;
         var btnState0:int = this.activeData.getActivityBtnState();
         if(btnState0 == 1)
         {
            gift0 = AccActiveSave.getActivityGiftGroup();
            bb0 = GiftAddit.addAndAutoBagSpacePan(gift0);
            if(bb0)
            {
               this.activeData.getActivityGiftEvent();
               this.fleshActivity();
            }
         }
      }
   }
}

