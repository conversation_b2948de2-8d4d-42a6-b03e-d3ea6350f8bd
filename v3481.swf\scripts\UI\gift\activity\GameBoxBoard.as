package UI.gift.activity
{
   import UI.UIOrder;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.gift.exchange.ExchangeGiftShowBox;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.StringDate;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.ExchangeGiftAddDefineGroup;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import flash.text.TextField;
   
   public class GameBoxBoard extends NormalUI
   {
      private var codeTxt:TextField;
      
      private var giftSp:Sprite;
      
      private var canGetCodeB:Boolean = true;
      
      private var nowProduct_id:String = "";
      
      private var giftBox:ExchangeGiftShowBox = new ExchangeGiftShowBox();
      
      private var uploadBtnSp:MovieClip;
      
      private var activeBtnSp:MovieClip;
      
      private var uploadBtn:NormalBtn = new NormalBtn();
      
      private var activeBtn:NormalBtn = new NormalBtn();
      
      public function GameBoxBoard()
      {
         super();
         this.giftBox.tileGripResoureName = "exchangeTitleGrip2";
         this.giftBox.giftFather = "gameBox";
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["codeTxt","giftSp","uploadBtnSp","activeBtnSp"];
         super.setImg(img0);
         this.codeTxt.text = "";
         addChild(this.giftBox);
         this.giftBox.setImg(this.giftSp);
         this.giftBox.getGiftBox().arg.init(5,2,8,8);
         this.giftBox.btn.addEventListener(MouseEvent.CLICK,this.click);
         addChild(this.uploadBtn);
         this.uploadBtn.setImg(this.uploadBtnSp);
         this.uploadBtn.setName("下载游戏盒");
         this.uploadBtn.addEventListener(MouseEvent.CLICK,this.uploadBtnClick);
         addChild(this.activeBtn);
         this.activeBtn.setImg(this.activeBtnSp);
         this.activeBtn.setName("活动详情");
         this.activeBtn.addEventListener(MouseEvent.CLICK,this.activeBtnClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.connectUI.show("与服务器连接中……");
         Gaming.api.save.getServerTime(this.affter_getDate,this.no_getDate);
      }
      
      private function no_getDate(str:* = null) : *
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.giftUI.hide();
      }
      
      private function affter_getDate(time00:String) : *
      {
         Gaming.uiGroup.connectUI.hide();
         var nowD0:StringDate = Gaming.api.save.getNowServerDate();
         this.giftBox.fleshByTime(nowD0);
      }
      
      private function uploadBtnClick(e:MouseEvent) : void
      {
         navigateToURL(new URLRequest("https://app.4399.cn/app-qd-newshd.html"),"blank");
      }
      
      private function activeBtnClick(e:MouseEvent) : void
      {
         navigateToURL(new URLRequest("https://huodong2.4399.com/n/comm/pcsign/index.php?strcode=Ng=="),"blank");
      }
      
      private function click(e:MouseEvent) : void
      {
         var bb0:Boolean = false;
         this.nowProduct_id = this.giftBox.getNowGiftDefine().codeExchangeId;
         var gift0:GiftAddDefineGroup = Gaming.defineGroup.gift.getOneByExchangeCode(this.nowProduct_id);
         var bagStr0:String = GiftAddit.bagSpacePan(gift0,1,Gaming.PG.da);
         if(bagStr0 != "")
         {
            Gaming.uiGroup.alertBox.showError(bagStr0);
         }
         else
         {
            bb0 = UIOrder.isLoginByJS();
            if(bb0)
            {
               Gaming.uiGroup.connectUI.show("数据处理中……");
               Gaming.api.save.getStoreState(this.affter_getStoreState);
            }
            else
            {
               Gaming.uiGroup.connectUI.hide();
            }
         }
      }
      
      private function affter_getStoreState(state0:int) : void
      {
         var uid0:String = null;
         var ac0:String = null;
         var giftDefine0:ExchangeGiftAddDefineGroup = null;
         if(state0 == 1 || state0 == -2)
         {
            uid0 = Gaming.PG.loginData.uid;
            ac0 = TextWay.toHan2(TextWay.toHanSpace(this.codeTxt.text));
            giftDefine0 = this.giftBox.getNowGiftDefine();
            if(giftDefine0.apiType == "")
            {
               Gaming.api.newExchange.startExchange(uid0,ac0,this.nowProduct_id,this.yes_code,this.no_code);
            }
            else if(giftDefine0.apiType == "MyCreditExchange")
            {
               Gaming.api.myCreditExchange.startExchange(uid0,ac0,this.nowProduct_id,this.yes_code,this.no_code);
            }
         }
      }
      
      private function yes_code(result0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         result0 = this.nowProduct_id;
         var gift0:GiftAddDefineGroup = Gaming.defineGroup.gift.getOneByExchangeCode(result0);
         if(!gift0)
         {
            INIT.showErrorMust("找不到指定id礼包：" + result0);
         }
         var tipStr0:String = "兑换成功！你获得了" + gift0.cnName + "：\n" + ComMethod.color(gift0.getDescription(3),"#00FF00");
         GiftAddit.add(gift0,tipStr0,false);
         UIOrder.save(false,false);
      }
      
      private function no_code(str0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError(str0);
      }
   }
}

