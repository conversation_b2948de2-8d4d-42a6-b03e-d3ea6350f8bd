package UI.api.union
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import unit4399.events.UnionEvent;
   
   public class UnionGrow_API extends UnionBase_API
   {
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public function UnionGrow_API()
      {
         super();
         this.tempMoney = 0;
      }
      
      public function get tempMoney() : Number
      {
         return this.CF.getAttribute("tempMoney");
      }
      
      public function set tempMoney(v0:Number) : void
      {
         this.CF.setAttribute("tempMoney",v0);
      }
      
      public function doTask(idx:int, task:String, yesFun0:Function = null, noFun0:Function = null) : void
      {
         setFun(yesFun0,noFun0,"doTask");
         if(Gaming.isLocal())
         {
            this.yes_doTask(true);
         }
         else
         {
            serviceHold.doTask(idx,task);
         }
      }
      
      private function yes_doTask(bb0:Boolean) : void
      {
         doYesFun(bb0,"doTask");
      }
      
      public function doExchange(idx:int, money:int, yesFun0:Function = null, noFun0:Function = null) : void
      {
         setFun(yesFun0,noFun0,"doExchange");
         if(Gaming.isLocal())
         {
            this.yes_doExchange(true);
         }
         else
         {
            this.tempMoney = money;
            serviceHold.doExchange(idx,money);
         }
      }
      
      private function yes_doExchange(bb0:Boolean) : void
      {
         Gaming.PG.save.getCount().todayUseMoney = Gaming.PG.save.getCount().todayUseMoney + this.tempMoney;
         doYesFun(bb0,"doExchange");
      }
      
      public function getTaskValuegetTaskValue(idx:int, money:int) : void
      {
         serviceHold.getTaskValue(idx,money);
      }
      
      private function yes_getTaskValuegetTaskValue(jsonStr0:String) : void
      {
      }
      
      public function onGrowSuccess(e:UnionEvent) : void
      {
         var dataObj:Object = e.data;
         var data0:* = dataObj.data;
         switch(dataObj.apiName)
         {
            case UnionEvent.UNI_API_BHRW:
               this.yes_doTask(data0);
               break;
            case UnionEvent.UNI_API_BHDH:
               this.yes_doExchange(data0);
               break;
            case UnionEvent.UNI_API_BHRWWC:
               this.yes_getTaskValuegetTaskValue(data0);
         }
      }
   }
}

