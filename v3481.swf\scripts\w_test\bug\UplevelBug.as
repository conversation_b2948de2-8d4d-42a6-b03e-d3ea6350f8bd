package w_test.bug
{
   import dataAll._app.worldMap.WorldMapData;
   import dataAll._app.worldMap.define.MapMode;
   import gameAll.hero.HeroBody;
   
   public class UplevelBug
   {
      private static const NO:String = "no";
      
      private static const SET:String = "set";
      
      private static const START:String = "start";
      
      private static const GAMING:String = "gaming";
      
      public var enabled:Boolean = false;
      
      private var num:int = 0;
      
      private var state:String = "no";
      
      public function UplevelBug()
      {
         super();
      }
      
      public function start() : void
      {
         this.enabled = true;
         this.state = SET;
      }
      
      public function test2() : void
      {
         for(var i:int = 0; i < 1000; i++)
         {
            Gaming.uiGroup.testUI.save4399.inputBeforeTxt();
            Gaming.PG.da.base.addExp(215330000);
            Gaming.PG.da.base.addExp(43818);
            if(Gaming.PG.da.level == 95)
            {
               Gaming.uiGroup.alertBox.showError("测试" + this.num + "次，等级为95！");
               return;
            }
         }
      }
      
      private function setEvent() : void
      {
         ++this.num;
         trace("▇▇▇▇▇▇▇▇测试升级bug第【" + this.num + "】次▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇");
         Gaming.uiGroup.testUI.save4399.inputBeforeTxt();
         Gaming.PG.da.base.addExp(215330000);
         Gaming.PG.da.thingsBag.useThings("doubleCard",1,true,false);
         var da0:WorldMapData = Gaming.PG.da.worldMap.getDataByName("NanTang");
         Gaming.LG.chooseLevel("NanTang",0,"normal",MapMode.CHALLENGE);
      }
      
      public function FTimer() : void
      {
         var hero0:HeroBody = null;
         if(this.enabled)
         {
            if(this.state == SET)
            {
               this.setEvent();
               this.state = START;
            }
            else if(this.state == START)
            {
               if(Gaming.LG.isGaming())
               {
                  this.state = GAMING;
               }
            }
            else if(this.state == GAMING)
            {
               if(Gaming.PG.save.base.exp > 3536094000)
               {
                  this.state = SET;
               }
               hero0 = Gaming.PG.ctrlHero;
               if(Boolean(hero0))
               {
                  if(!Gaming.LG.autoTestB)
                  {
                     hero0.openAi();
                     Gaming.LG.autoTestB = true;
                  }
               }
               if(Gaming.PG.da.level == 93)
               {
                  this.state = SET;
               }
               else if(Gaming.PG.da.level == 95)
               {
                  Gaming.LG.pauseLevel();
                  Gaming.uiGroup.alertBox.showError("测试" + this.num + "次，等级为95！");
                  this.state = NO;
               }
            }
         }
      }
   }
}

