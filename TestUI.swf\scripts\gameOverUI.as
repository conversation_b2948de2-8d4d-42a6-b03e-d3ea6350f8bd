package
{
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol194")]
   public dynamic class gameOverUI extends MovieClip
   {
      public var txt:TextField;
      
      public var starTxt:TextField;
      
      public var endlessSp:MovieClip;
      
      public var arenaSp:MovieClip;
      
      public var titleTxt:TextField;
      
      public var closeBtn:SimpleButton;
      
      public var starMc:MovieClip;
      
      public function gameOverUI()
      {
         super();
      }
   }
}

