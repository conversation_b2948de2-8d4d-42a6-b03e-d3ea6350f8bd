package UI.city.smelt
{
   import UI.UIOrder;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.alert.AlertBox;
   import UI.base.button.NormalBtn;
   import UI.base.drag.ItemsDragController;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.numChoose.NumChooseBox;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.StringMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._app.city.CityData;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.items.IO_ItemsData;
   import dataAll.things.ThingsData;
   import dataAll.things.ThingsDataGroup;
   import dataAll.things.creator.ThingsSemltCreator;
   import dataAll.things.define.ThingsDefine;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.text.TextField;
   
   public class CitySmeltBoard extends AutoNormalUI
   {
      private var tipBtn:SimpleButton;
      
      private var btn:NormalBtn;
      
      private var conSp:MovieClip;
      
      private var conTxt:TextField;
      
      private var errorTxt:TextField;
      
      private var numTxt:TextField;
      
      private var infoTxt:TextField;
      
      private var numSp:Sprite;
      
      private var numBox:NumChooseBox = new NumChooseBox();
      
      private var itemsBox:ItemsGripBox = new ItemsGripBox();
      
      private var planTag:Sprite;
      
      private var planTxt:TextField;
      
      private var groupTxt:TextField;
      
      private var ruleBtn:NormalBtn;
      
      private var listBtn:NormalBtn;
      
      private var saveBtn:NormalBtn;
      
      private var inBtn:NormalBtn;
      
      private var editBtn:NormalBtn;
      
      private var editB:Boolean = false;
      
      private var effectSp:MovieClip;
      
      private var nowGift:GiftAddDefineGroup = new GiftAddDefineGroup();
      
      private var smeltGift:GiftAddDefineGroup = null;
      
      private const tipStr:String = "";
      
      public function CitySmeltBoard()
      {
         super();
         mcTypeArr = ["btnSp","txt","tag"];
      }
      
      private function get alert() : AlertBox
      {
         return Gaming.uiGroup.alertBox;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["conSp","tipBtn"];
         super.setImg(img0);
      }
      
      override protected function firstLoad() : void
      {
         FontDeal.dealOne(this.conTxt);
         FontDeal.dealOne(this.errorTxt);
         FontDeal.dealStaticLine(this.infoTxt);
         this.conTxt.mouseEnabled = false;
         this.conSp.stop();
         this.conSp.addChild(this.itemsBox);
         this.conSp.addEventListener(MouseEvent.MOUSE_UP,this.conUp);
         this.conSp.addEventListener(MouseEvent.MOUSE_OVER,this.conOver);
         this.conSp.addEventListener(MouseEvent.MOUSE_OUT,this.conOut);
         this.errorTxt.addEventListener(MouseEvent.MOUSE_OVER,this.errorTxtOver);
         this.errorTxt.addEventListener(MouseEvent.MOUSE_OUT,this.errorTxtOut);
         addChild(this.numBox);
         this.numBox.setImg(this.numSp);
         this.numBox.init(9999,1);
         this.numBox.addEventListener(Event.CHANGE,this.fleshNum);
         this.itemsBox.setIconPro("equipGrip",60,60);
         this.itemsBox.arg.init(6,1,10,0);
         this.itemsBox.x = 10;
         this.itemsBox.y = 20;
         this.itemsBox.evt.setWant(true,true);
         this.itemsBox.setCtrlBtnImgType("BasicUI/gripDelBtn");
         this.itemsBox.addEventListener(ClickEvent.ON_CTRL_CLICK,this.boxCtrlClick);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.itemsBox);
         this.numTxt.addEventListener(MouseEvent.MOUSE_OVER,this.numTxtOver);
         this.numTxt.addEventListener(MouseEvent.MOUSE_OUT,this.numTxtOut);
         this.ruleBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.ruleBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
         this.listBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.listBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
         this.listBtn.setName("物品组");
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
         this.inBtn.setName("输入方案");
         this.saveBtn.setName("保存方案");
         this.editBtn.setName("编辑方案");
         this.groupTxt.styleSheet = ComMethod.getLinkCss("#00FFFF","#FFFFFF");
         FontDeal.dealOne(this.groupTxt);
         this.groupTxt.addEventListener(TextEvent.LINK,this.linkClick);
         this.groupTxt.htmlText = "";
         this.groupTxt.maxChars = 47;
         this.effectSp.mouseChildren = false;
         this.effectSp.mouseEnabled = false;
         addChild(this.effectSp);
         firstB = true;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.bagUI.showAndLabel("things",true);
         Gaming.uiGroup.bagUI.setLeftByMe(this);
         this.editB = false;
         this.fleshThings();
         this.fleshPlan();
      }
      
      override public function hide() : void
      {
         Gaming.uiGroup.bagUI.hideWhenVisible();
         super.hide();
      }
      
      private function get thingsBag() : ThingsDataGroup
      {
         return Gaming.PG.da.thingsBag;
      }
      
      private function get cityData() : CityData
      {
         return Gaming.PG.da.city;
      }
      
      private function fleshThings(fleshPlanTxtB:Boolean = true) : void
      {
         var result0:String = null;
         var surpluse0:int = this.cityData.getSurplusNum();
         this.numBox.setMax(surpluse0);
         var giftNumB0:Boolean = this.fleshGift();
         var len0:int = int(this.nowGift.arr.length);
         this.conTxt.visible = len0 == 0;
         if(fleshPlanTxtB)
         {
            this.outToPlanTxt();
         }
         var num0:int = this.numBox.nowNum;
         var bagSpace0:int = this.thingsBag.getSpaceSiteNum();
         var numError0:String = ThingsSemltCreator.getMinMaxError(len0);
         var actived0:Boolean = false;
         this.setError("");
         if(numError0 != "")
         {
            this.setError(numError0);
         }
         else if(bagSpace0 < num0)
         {
            this.setError("物品背包至少有" + num0 + "个空位");
         }
         else
         {
            result0 = ThingsSemltCreator.getSmeltResultStr(this.nowGift,num0);
            if(result0 == "")
            {
               this.setError("当前组合无法熔炼");
            }
            else
            {
               this.setError(result0,65280);
               actived0 = true;
            }
         }
         this.btn.actived = giftNumB0 && actived0 && num0 <= surpluse0;
         this.btn.setName(giftNumB0 ? "熔炼" + num0 + "次" : "物品数量不足");
         this.numTxt.htmlText = "今日还可熔炼" + TextMethod.redZeroOrGreen(surpluse0) + "次";
      }
      
      private function fleshGift() : Boolean
      {
         var gd0:GiftAddDefine = null;
         var td0:ThingsDefine = null;
         var smeltNum0:int = 0;
         var min0:int = 0;
         var num0:int = 0;
         var grip0:NormalBtn = null;
         this.itemsBox.inData_byArr(this.nowGift.arr,"inData_giftSec");
         var index0:int = 0;
         var enoughB0:Boolean = true;
         for each(gd0 in this.nowGift.arr)
         {
            td0 = Gaming.defineGroup.things.getDefine(gd0.name);
            smeltNum0 = this.numBox.nowNum;
            min0 = td0.smeltD.getMinMust() * smeltNum0;
            num0 = this.thingsBag.getThingsNum(gd0.name);
            gd0.num = min0;
            grip0 = this.itemsBox.getBtnBySecData(gd0);
            if(Boolean(grip0))
            {
               if(num0 < min0 * 10)
               {
                  grip0.setNumText(ComMethod.lessRedB(num0,min0));
               }
               else
               {
                  grip0.setNumText("<b>" + min0 + "</b>");
               }
               if(num0 < min0)
               {
                  grip0.setIconGrayFilter(true);
               }
               else
               {
                  grip0.setIconGrayFilter(false);
               }
            }
            if(num0 < min0)
            {
               enoughB0 = false;
            }
            index0++;
         }
         return enoughB0;
      }
      
      private function setError(str0:String, color0:uint = 16744512) : void
      {
         this.errorTxt.visible = str0 != "";
         this.errorTxt.textColor = color0;
         this.errorTxt.htmlText = str0;
      }
      
      private function getError() : String
      {
         return this.errorTxt.htmlText;
      }
      
      private function intoThings(da0:ThingsData) : void
      {
         var gd0:GiftAddDefine = null;
         var d0:ThingsDefine = da0.save.getDefine();
         var num0:int = d0.smeltD.getMinMust();
         var smeltNum0:int = this.numBox.nowNum;
         num0 *= smeltNum0;
         var nowGd0:GiftAddDefine = this.nowGift.getGiftByName(d0.name);
         if(!nowGd0)
         {
            gd0 = new GiftAddDefine();
            gd0.inData_byStr("things;" + d0.name + ";" + num0);
            this.nowGift.mergeOne(gd0);
            this.fleshThings();
         }
      }
      
      private function fleshNum(e:* = null) : void
      {
         this.fleshThings();
      }
      
      private function numTxtOver(e:MouseEvent) : void
      {
         var tip0:String = this.cityData.getNumTip();
         Gaming.uiGroup.tipBox.textTip.showFollowText(tip0);
      }
      
      private function numTxtOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function errorTxtOver(e:MouseEvent) : void
      {
         var str0:String = this.getError();
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
      }
      
      private function errorTxtOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function boxCtrlClick(e:ClickEvent) : void
      {
         var index0:int = e.index;
         this.nowGift.arr.splice(index0,1);
         this.fleshThings();
      }
      
      private function conOver(e:MouseEvent) : void
      {
         var dragCtrl:ItemsDragController = Gaming.uiGroup.dragCtrl;
         var da0:IO_ItemsData = null;
         if(Boolean(dragCtrl.dragChild))
         {
            da0 = dragCtrl.dragChild.itemsData as IO_ItemsData;
         }
         if(da0 is IO_ItemsData)
         {
            this.conSp.gotoAndStop("yes");
         }
         else
         {
            this.conSp.gotoAndStop("normal");
         }
      }
      
      private function conOut(e:MouseEvent) : void
      {
         this.conSp.gotoAndStop("normal");
      }
      
      private function conUp(e:MouseEvent) : void
      {
         var da0:IO_ItemsData = null;
         var tDa0:ThingsData = null;
         var inMax0:int = 0;
         this.conSp.gotoAndStop("normal");
         var dragCtrl:ItemsDragController = Gaming.uiGroup.dragCtrl;
         if(Boolean(dragCtrl.dragChild))
         {
            da0 = dragCtrl.dragChild.itemsData as IO_ItemsData;
            tDa0 = da0 as ThingsData;
            if(tDa0 is ThingsData)
            {
               if(tDa0.save.getDefine().isSmeltMaterialB())
               {
                  inMax0 = ThingsSemltCreator.getThingsMax();
                  if(this.nowGift.arr.length >= inMax0)
                  {
                     Gaming.uiGroup.alertBox.showError("最多放入" + inMax0 + "种物品。");
                  }
                  else
                  {
                     this.intoThings(tDa0);
                  }
               }
            }
         }
      }
      
      protected function click() : void
      {
         var smeltNum0:int = 0;
         this.fleshThings();
         if(this.btn.actived)
         {
            smeltNum0 = this.numBox.nowNum;
            this.smeltGift = ThingsSemltCreator.smeltNum(this.nowGift,smeltNum0);
            if(!this.smeltGift)
            {
               Gaming.uiGroup.alertBox.showError("这些物品组合无法进行熔炼。");
            }
            else if(this.smeltGift.getTipB())
            {
               UIOrder.getStoreState(this.affterGetStoreState);
            }
            else
            {
               this.affterGetStoreState(1);
            }
         }
      }
      
      private function affterGetStoreState(v0:int) : void
      {
         var smeltNum0:int = 0;
         var gd0:GiftAddDefine = null;
         if(v0 == 1 || v0 == -2)
         {
            Gaming.uiGroup.connectUI.hide();
            smeltNum0 = this.numBox.nowNum;
            for each(gd0 in this.nowGift.arr)
            {
               this.thingsBag.useThings(gd0.name,gd0.num);
            }
            this.cityData.useNum(this.smeltGift,smeltNum0);
            GiftAddit.addByDefineGroup(this.smeltGift,Gaming.PG.da);
            this.fleshThings();
            if(this.smeltGift.getTipB())
            {
               UIOrder.save(true,false,false,this.yesSave,this.noSave);
            }
            else
            {
               Gaming.soundGroup.playSound("uiSound","success");
               this.yesSave();
            }
         }
      }
      
      private function yesSave(v:* = null) : void
      {
         var td0:ThingsDefine = null;
         var str0:String = "获得物品：\n" + this.smeltGift.getDescription(3);
         var iconUrl0:String = "";
         if(this.smeltGift.arr.length == 1)
         {
            td0 = Gaming.defineGroup.things.getDefine(this.smeltGift.arr[0].name);
            iconUrl0 = td0.iconUrl;
         }
         Gaming.uiGroup.alertBox.showCheck(str0,"yes",0,null,null,iconUrl0,"equip");
         Gaming.uiGroup.bagUI.fleshNowBox();
      }
      
      private function noSave(v:* = null) : void
      {
         this.hide();
      }
      
      override public function get width() : Number
      {
         return 482;
      }
      
      private function playEffect(label0:String) : void
      {
         if(label0 == "")
         {
            label0 = "no";
         }
         this.effectSp.gotoAndPlay(label0);
      }
      
      private function inputPlan(str0:String) : Boolean
      {
         var back0:* = undefined;
         var gg0:GiftAddDefineGroup = null;
         var afterLen0:int = 0;
         var lessArr0:Array = null;
         var gd0:GiftAddDefine = null;
         var bagNum0:int = 0;
         var td0:ThingsDefine = null;
         back0 = ThingsSemltCreator.planToGift(str0);
         if(str0 == "")
         {
            this.alert.showError("方案内容不能为空。");
         }
         else
         {
            back0 = ThingsSemltCreator.planToGift(str0);
            gg0 = back0 as GiftAddDefineGroup;
            if(Boolean(gg0))
            {
               this.nowGift = gg0.clone();
               this.numBox.setNum(1);
               this.fleshThings(false);
               afterLen0 = int(this.nowGift.arr.length);
               if(afterLen0 < gg0.arr.length)
               {
                  lessArr0 = [];
                  for each(gd0 in gg0.arr)
                  {
                     bagNum0 = Gaming.PG.da.thingsBag.getThingsNum(gd0.name);
                     if(bagNum0 < gd0.num)
                     {
                        td0 = Gaming.defineGroup.things.getDefine(gd0.name);
                        lessArr0.push(td0.cnName);
                     }
                  }
                  this.alert.showError("背包缺少" + (gg0.arr.length - afterLen0) + "样配方物品：\n" + StringMethod.concatStringArr(lessArr0,3));
               }
               return true;
            }
            this.alert.showError(String(back0));
         }
         return false;
      }
      
      private function outToPlanTxt() : void
      {
         this.planTxt.text = ThingsSemltCreator.giftToPlan(this.nowGift,this.numBox.nowNum);
      }
      
      private function inClick() : void
      {
         var bb0:Boolean = false;
         var str0:String = TextWay.toHanSpace(this.planTxt.text);
         if(str0 == "")
         {
            this.alert.showError("文本框内不能为空。");
         }
         else
         {
            bb0 = this.inputPlan(str0);
            if(bb0)
            {
               this.playEffect("in");
            }
         }
      }
      
      private function saveClick() : void
      {
         var back0:* = undefined;
         var gg0:GiftAddDefineGroup = null;
         var title0:String = null;
         var error0:String = null;
         var str0:String = TextWay.toHanSpace(this.planTxt.text);
         if(str0 == "")
         {
            this.alert.showError("文本框内不能为空。");
         }
         else
         {
            back0 = ThingsSemltCreator.planToGift(str0);
            gg0 = back0 as GiftAddDefineGroup;
            if(Boolean(gg0))
            {
               this.planTxt.text = str0;
               title0 = ThingsSemltCreator.getPlanTitle(gg0);
               error0 = this.cityData.save.savePlan(title0,str0);
               if(error0 == "")
               {
                  this.fleshPlan();
                  this.playEffect("save");
               }
               else
               {
                  this.alert.showError(error0);
               }
            }
            else
            {
               this.alert.showError(String(back0));
            }
         }
      }
      
      private function editClick() : void
      {
         this.editB = !this.editB;
         this.fleshPlan();
      }
      
      private function linkClick(e:TextEvent) : void
      {
         var bb0:Boolean = false;
         var label0:String = e.text;
         if(this.editB)
         {
            this.cityData.save.delPlan(label0);
            this.fleshPlan();
         }
         else
         {
            this.planTxt.text = label0;
            bb0 = this.inputPlan(label0);
            if(bb0)
            {
               this.playEffect("link");
            }
         }
      }
      
      private function fleshPlan() : void
      {
         var str0:String = this.cityData.save.getPlanTxt(this.editB);
         this.groupTxt.htmlText = str0;
         this.editBtn.setName(this.editB ? "完成" : "开始删除方案");
         this.editBtn.actived = this.editB || str0 != "";
      }
      
      protected function tipOver(e:MouseEvent) : void
      {
         var str0:String = null;
         var btn0:DisplayObject = e.target as NormalBtn;
         if(btn0 == this.tipBtn)
         {
            Gaming.uiGroup.tipBox.showText(str0);
         }
         else if(btn0 == this.ruleBtn)
         {
            str0 = "1、每样物品拥有不同类别，和不同优先级(分为0级、1级、2级、3级四种)。";
            str0 += "\n2、相同类别、且相同优先级的物品视为1组，如左边《物品组》表。";
            str0 += "\n<orange 3、如果你想产出某样物品，那你可以把它所属那1组的其他物品作为熔炼材料。/>";
            str0 += "\n4、如果想增加产量，可以额外加入更低优先级的物品（类别不限），或者加入纯增产物品。";
            str0 += "\n5、纯增产物品只能作为材料，无法产出。";
            str0 += "\n6、熔炼材料最少放2种，最多放6种。";
            Gaming.uiGroup.tipBox.showText(str0);
         }
         else if(btn0 == this.listBtn)
         {
            str0 = Gaming.defineGroup.things.getSmeltGatherTip();
            Gaming.uiGroup.tipBox.showText(str0,16);
         }
      }
      
      protected function tipOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         if(!btn0.actived)
         {
            return;
         }
         if(btn0 != this.editBtn)
         {
            if(this.editB)
            {
               this.editB = false;
               this.fleshPlan();
            }
         }
         if(btn0 == this.btn)
         {
            this.click();
         }
         if(btn0 == this.inBtn)
         {
            this.inClick();
         }
         if(btn0 == this.saveBtn)
         {
            this.saveClick();
         }
         if(btn0 == this.editBtn)
         {
            this.editClick();
         }
      }
   }
}

