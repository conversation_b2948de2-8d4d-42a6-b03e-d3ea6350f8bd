package UI.bag.wear
{
   import UI.base.NormalUI;
   import dataAll.equip.define.EquipDefine;
   import flash.display.MovieClip;
   
   public class FashionHDBox extends NormalUI
   {
      public var tempDef:EquipDefine = null;
      
      private var mc:MovieClip = null;
      
      private var def:EquipDefine = null;
      
      private var t:Number = -1;
      
      public function FashionHDBox()
      {
         super();
         this.mouseChildren = false;
         this.mouseEnabled = false;
      }
      
      override protected function firstLoad() : void
      {
         setImgUrl("BasicUI/fashionMc");
      }
      
      public function setFashion(d0:EquipDefine, hideT0:Number = -1) : void
      {
         var mc0:MovieClip = null;
         if(d0 != this.def)
         {
            this.clearMc();
            mc0 = Gaming.swfLoaderManager.getResourceFull(d0.getHDShowUrl());
            if(<PERSON><PERSON><PERSON>(mc0))
            {
               this.mc = mc0;
               mc0.x = 124;
               mc0.y = 487;
               mc0.scaleX = 4;
               mc0.scaleY = 4;
               this.addChild(mc0);
            }
         }
         this.t = hideT0;
         this.def = d0;
      }
      
      private function clearMc() : void
      {
         if(Boolean(this.mc))
         {
            if(Boolean(this.mc.parent))
            {
               this.mc.parent.removeChild(this.mc);
            }
         }
         this.mc = null;
      }
      
      override public function hide() : void
      {
         super.hide();
         this.t = -1;
      }
      
      public function outLoginEvent() : void
      {
         this.clearMc();
         this.def = null;
         this.t = -1;
      }
      
      public function getDef() : EquipDefine
      {
         return this.def;
      }
      
      public function FTimer() : void
      {
         if(Boolean(this.parent) && this.visible)
         {
            if(this.t > 0)
            {
               this.t -= 0.03333333333333333;
            }
            else if(this.t > -1)
            {
               this.hide();
               this.t = -1;
            }
         }
      }
   }
}

