package UI.bag.wear
{
   import UI.UIOrder;
   import UI.UIShow;
   import UI.base.AutoNormalUI;
   import UI.base.font.FontDeal;
   import UI.base.scroll.NormalScrollBar;
   import com.sounto.utils.StringMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._player.define.PlayerDataName;
   import dataAll.equip.add.EquipAddAgent;
   import dataAll.equip.add.EquipAddAgentGroup;
   import dataAll.equip.add.EquipAddChild;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class WearProBox extends AutoNormalUI
   {
      private var equipProArr:Array = null;
      
      private var dropB:Boolean = false;
      
      private var proTxt:TextField;
      
      private var valueTxt:TextField;
      
      private var txtCon:Sprite = new Sprite();
      
      private var backSp:Sprite;
      
      private var maskTargetSp:Sprite;
      
      private var scrollBarSp:Sprite;
      
      private var scrollLineSp:Sprite;
      
      private var scrollBar:NormalScrollBar;
      
      private var nowG:EquipAddAgentGroup = null;
      
      private var moveLink:String = "";
      
      public function WearProBox()
      {
         super();
         mcTypeArr = ["btnSp","txt","tag"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = elementNameArr.concat([]);
         super.setImg(img0);
         TextMethod.setAutoFormat(this.proTxt);
         FontDeal.dealLine(this.proTxt);
         this.proTxt.styleSheet = TextMethod.getLinkCss("#CCCCCC","#FFFFFF",false);
         TextMethod.setAutoFormat(this.valueTxt);
         FontDeal.dealLine(this.valueTxt);
         this.valueTxt.styleSheet = TextMethod.getLinkCss("#FFFF00","#FFFFFF",false);
         this.proTxt.addEventListener(TextEvent.LINK,this.proLinkClick);
         this.proTxt.addEventListener(MouseEvent.MOUSE_MOVE,this.txtMove);
         this.valueTxt.addEventListener(TextEvent.LINK,this.valueLinkClick);
         this.valueTxt.addEventListener(MouseEvent.MOUSE_MOVE,this.txtMove);
         this.txtCon.addChild(this.backSp);
         this.txtCon.addChild(this.proTxt);
         this.txtCon.addChild(this.valueTxt);
         this.addChild(this.txtCon);
         this.scrollBar = new NormalScrollBar(this.txtCon,this.maskTargetSp,this.scrollBarSp,this.scrollLineSp,1,false,true,true);
         this.scrollBar.speed = 30;
         this.scrollBar.refresh();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      public function setEquipProArr(arr0:Array, dropB0:Boolean = false) : void
      {
         this.equipProArr = arr0;
         this.dropB = dropB0;
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      private function fleshData() : void
      {
         var g0:EquipAddAgentGroup = Gaming.PG.DATA.getEquipAddAgentGroup(this.equipProArr);
         this.nowG = g0;
         var textObj0:Object = g0.getTextObj(this.dropB);
         var titleArr0:Array = textObj0.title;
         var valueArr0:Array = textObj0.value;
         this.proTxt.htmlText = FontDeal.getDealLeadingStr(this.proTxt,StringMethod.concatStringArr(titleArr0,1,"\n"));
         this.valueTxt.htmlText = FontDeal.getDealLeadingStr(this.valueTxt,StringMethod.concatStringArr(valueArr0,1,"\n"));
         this.backSp.height = this.valueTxt.y + this.valueTxt.height + 20;
         if(this.backSp.scaleY < 1)
         {
            this.backSp.scaleY = 1;
         }
         this.scrollBar.refresh();
      }
      
      private function txtMove(e:MouseEvent) : void
      {
         var charIndex:Number = NaN;
         var format:TextFormat = null;
         var url0:String = null;
         var f0:int = 0;
         var link0:String = "";
         var txt0:TextField = e.target as TextField;
         if(Boolean(txt0))
         {
            charIndex = txt0.getCharIndexAtPoint(e.localX,e.localY);
            if(charIndex >= 0)
            {
               format = txt0.getTextFormat(charIndex,charIndex + 1);
               url0 = format.url;
               if(url0 != "")
               {
                  f0 = url0.indexOf(":");
                  link0 = url0.substr(f0 + 1);
               }
            }
         }
         if(link0 != this.moveLink)
         {
            this.txtLinkOver(link0,txt0);
            this.moveLink = link0;
         }
      }
      
      private function txtLinkOver(link0:String, txt0:TextField) : void
      {
         var a0:EquipAddAgent = null;
         var c0:EquipAddChild = null;
         var tip0:String = "";
         if(link0 != "")
         {
            if(Boolean(this.nowG))
            {
               if(txt0 == this.proTxt)
               {
                  a0 = this.nowG.getAgent(link0);
                  tip0 = a0.getTip();
               }
               else if(txt0 == this.valueTxt)
               {
                  c0 = this.nowG.getChildByLink(link0);
                  tip0 = c0.getTip();
               }
            }
         }
         UIOrder.showTip(tip0);
      }
      
      private function proLinkClick(e:TextEvent) : void
      {
         var a0:EquipAddAgent = null;
         if(Boolean(this.nowG))
         {
            a0 = this.nowG.getAgent(e.text);
         }
      }
      
      private function valueLinkClick(e:TextEvent) : void
      {
         var c0:EquipAddChild = null;
         var maxName0:String = null;
         if(Boolean(this.nowG))
         {
            c0 = this.nowG.getChildByLink(e.text);
            if(Boolean(c0))
            {
               if(Gaming.LG.isGaming() == false)
               {
                  maxName0 = c0.getMaxName();
                  if(c0.dataFrom == PlayerDataName.achieve)
                  {
                     if(maxName0 != "")
                     {
                        if(c0.dataFrom == PlayerDataName.achieve)
                        {
                           Gaming.uiGroup.achieveUI.gotoName(maxName0);
                        }
                     }
                  }
                  else if(PlayerDataName.proGotoArr.indexOf(c0.dataFrom) >= 0)
                  {
                     UIShow.showByLabel(c0.dataFrom);
                  }
               }
            }
         }
      }
   }
}

