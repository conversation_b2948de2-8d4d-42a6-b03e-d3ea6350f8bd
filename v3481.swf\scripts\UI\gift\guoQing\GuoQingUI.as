package UI.gift.guoQing
{
   import UI.base.AppNormalUI;
   import UI.base.NormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import UI.gift.anniver.other.AnniverOtherBoard;
   import com.sounto.oldUtils.StringDate;
   import dataAll.gift.guoQing.GuoQingSave;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class GuoQingUI extends AppNormalUI
   {
      public var labelBox:LabelBox;
      
      private var tipBtn:SimpleButton;
      
      private var labelArr:Array;
      
      private var labelCnArr:Array;
      
      private var signBoard:GuoQingSignBoard;
      
      private var eggBoard:GuoQingEggBoard;
      
      private var otherBoard:AnniverOtherBoard;
      
      private var boxArr:Array;
      
      private var coverSp:Sprite;
      
      private var coverTxt:TextField;
      
      private var labelTag:Sprite;
      
      private var closeBtn:SimpleButton;
      
      private var timeTxt:TextField;
      
      private var timeSp:Sprite;
      
      public function GuoQingUI()
      {
         var label0:* = null;
         var box0:NormalUI = null;
         this.labelBox = new LabelBox();
         this.labelArr = ["sign","egg"];
         this.labelCnArr = ["签到礼包","砸金蛋","其他更新"];
         this.signBoard = new GuoQingSignBoard();
         this.eggBoard = new GuoQingEggBoard();
         this.otherBoard = new AnniverOtherBoard();
         this.boxArr = [];
         super();
         UICn = "十月签到";
         for each(label0 in this.labelArr)
         {
            box0 = this[label0 + "Board"];
            this.boxArr.push(box0);
            box0.UILabel = label0;
            if(box0.hasOwnProperty("setCoverText"))
            {
               box0["setCoverText"] = this.setCoverText;
            }
         }
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var label0:String = null;
         var normalUI0:NormalUI = null;
         elementNameArr = ["labelTag","closeBtn","coverSp","timeSp","timeTxt","tipBtn"];
         super.setImg(img0);
         var cnNameArr0:Array = this.labelCnArr;
         this.labelBox.arg.init(10,1,-9,0);
         this.labelBox.inData("longLabelBtn",this.labelArr,cnNameArr0);
         this.labelBox.setChoose(this.labelArr[0]);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.labelBox.x = this.labelTag.x;
         this.labelBox.y = this.labelTag.y;
         addChild(this.labelBox);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         var boardArr0:Array = this.labelArr;
         for each(label0 in boardArr0)
         {
            label0 += "Board";
            normalUI0 = this[label0];
            addChild(normalUI0);
            normalUI0.visible = false;
            normalUI0.setImg(Gaming.swfLoaderManager.getResource("GuoQingUI",label0));
         }
         addChild(this.coverSp);
         this.coverTxt = this.coverSp["txt"];
         this.setCoverText("");
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.showBox(this.labelArr[0]);
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      public function showBox(label0:String) : void
      {
         var n:* = undefined;
         var ui0:NormalUI = null;
         var timeStr0:String = null;
         var day0:int = 0;
         if(label0 == "")
         {
            label0 = this.labelArr[0];
         }
         this.labelBox.setChoose(label0);
         for(n in this.boxArr)
         {
            this.boxArr[n].hide();
         }
         ui0 = this[label0 + "Board"];
         if(Boolean(ui0))
         {
            ui0.show();
         }
         this.setCoverText("");
         this.timeTxt.visible = false;
         this.timeSp.visible = false;
         if(this.labelBox.nowLabel == "other")
         {
            this.timeTxt.visible = false;
            this.timeSp.visible = false;
         }
         else
         {
            this.timeTxt.visible = true;
            this.timeSp.visible = true;
            timeStr0 = Gaming.PG.da.time.getReadTime();
            if(StringDate.compareDateByStr(GuoQingSave.signStart,timeStr0) < 0)
            {
               this.timeTxt.htmlText = "活动还未开始";
               this.setCoverText(GuoQingSave.getSignTimeStr());
            }
            else
            {
               day0 = StringDate.compareDateByStr(timeStr0,GuoQingSave.signEnd);
               if(day0 >= 0)
               {
                  this.timeTxt.htmlText = "距离活动结束还剩余" + (day0 + 1) + "天";
               }
               else
               {
                  this.timeTxt.htmlText = "活动已结束！";
                  this.setCoverText("活动已结束！");
               }
            }
         }
      }
      
      public function setCoverText(str0:String) : void
      {
         this.coverSp.visible = str0 != "";
         this.coverTxt.htmlText = str0;
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         hide();
      }
      
      private function tipOver(e:MouseEvent) : void
      {
         if(!this.signBoard.visible)
         {
            if(this.eggBoard.visible)
            {
            }
         }
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function tipOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      public function outLoginEvent() : void
      {
         this.eggBoard.outLoginEvent();
      }
      
      public function FTimerSecond() : void
      {
         if(visible)
         {
         }
      }
   }
}

