package UI.base
{
   import UI.NormalUICtrl;
   import UI.base.button.NormalBtn;
   import UI.base.grid.NormalGridIcon;
   import UI.base.loadBar.LoadBar;
   import UI.base.must.NormalMustBox;
   import UI.base.numChoose.NumChooseBox;
   import com.sounto.oldUtils.ComMethod;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class AutoNormalUI extends NormalUI
   {
      protected var btnObj:Object = {};
      
      protected var btnSetB:Boolean = true;
      
      protected var mcTypeArr:Array = ["btnSp","txt","tag","pos","Bx","iconCon","numSp","mustBoxSp","loadBarSp"];
      
      protected var McTypeArr:Array = [];
      
      public function AutoNormalUI()
      {
         super();
      }
      
      protected function getBtnByName(name0:String) : NormalBtn
      {
         return this.btnObj[name0];
      }
      
      protected function fleshMcTypeArr() : void
      {
         var type0:* = null;
         this.McTypeArr.length = 0;
         for each(type0 in this.mcTypeArr)
         {
            this.McTypeArr.push(ComMethod.firstUpperCase(type0));
         }
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var mc0:DisplayObject = null;
         var haveB0:Boolean = false;
         var n:* = undefined;
         var type0:String = null;
         var type2:String = null;
         var index0:int = 0;
         var name0:String = null;
         this.fleshMcTypeArr();
         super.setImg(img0);
         var arr0:Array = this.getAllMc(img0);
         for each(mc0 in arr0)
         {
            haveB0 = false;
            for(n in this.mcTypeArr)
            {
               type0 = this.McTypeArr[n];
               type2 = this.mcTypeArr[n];
               index0 = int(mc0.name.indexOf(type0));
               if(index0 == -1)
               {
                  index0 = int(mc0.name.indexOf(type2));
               }
               if(index0 != -1)
               {
                  name0 = mc0.name.substr(0,index0);
                  this[type2 + "Set"](mc0,name0);
                  haveB0 = true;
                  break;
               }
            }
            if(!haveB0)
            {
               if(mc0.name.indexOf("Sp") >= 0)
               {
                  this.SET(mc0.name,mc0);
               }
            }
         }
      }
      
      private function getAllMc(img0:Sprite) : Array
      {
         var mc0:DisplayObject = null;
         var arr0:Array = [];
         var num0:int = img0.numChildren;
         for(var i:int = 0; i < num0; i++)
         {
            mc0 = img0.getChildAt(i);
            if(mc0 is Sprite || mc0 is TextField)
            {
               arr0.push(mc0);
            }
         }
         return arr0;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      protected function GET(str0:String) : *
      {
         INIT.showError("该方法必须被覆盖！");
      }
      
      private function btnSpSet(mc0:DisplayObject, name0:String) : void
      {
         var btn0:NormalBtn = new NormalBtn();
         addChild(btn0);
         btn0.setImg(mc0 as MovieClip);
         btn0.label = name0;
         btn0.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.btnObj[name0] = btn0;
         if(this.btnSetB)
         {
            this.SET(ComMethod.firstLowerCase(name0 + "Btn"),btn0);
         }
      }
      
      private function loadBarSpSet(mc0:DisplayObject, name0:String) : void
      {
         var loadBar0:LoadBar = new LoadBar();
         addChild(loadBar0);
         loadBar0.setImg(mc0 as Sprite);
         this.SET(ComMethod.firstLowerCase(name0 + "LoadBar"),loadBar0);
      }
      
      private function mustBoxSpSet(mc0:DisplayObject, name0:String) : void
      {
         var loadBar0:NormalMustBox = new NormalMustBox();
         addChild(loadBar0);
         loadBar0.setImg(mc0 as Sprite);
         this.SET(ComMethod.firstLowerCase(name0 + "MustBox"),loadBar0);
      }
      
      private function txtSet(mc0:DisplayObject, name0:String) : void
      {
         this.SET(ComMethod.firstLowerCase(name0 + "Txt"),mc0);
      }
      
      private function tagSet(mc0:DisplayObject, name0:String) : void
      {
         this.SET(ComMethod.firstLowerCase(name0 + "Tag"),mc0);
      }
      
      private function posSet(mc0:DisplayObject, name0:String) : void
      {
         var bx0:DisplayObject = this.GET(name0) as DisplayObject;
         if(Boolean(bx0))
         {
            addChild(bx0);
            NormalUICtrl.setTag(bx0,mc0);
         }
      }
      
      private function numBoxSpSet(mc0:DisplayObject, name0:String) : void
      {
         var loadBar0:NumChooseBox = new NumChooseBox();
         addChild(loadBar0);
         loadBar0.setImg(mc0 as Sprite);
         this.SET(ComMethod.firstLowerCase(name0 + "NumBox"),loadBar0);
      }
      
      private function BxSet(mc0:DisplayObject, name0:String) : void
      {
         var bx0:DisplayObject = this.GET(name0) as DisplayObject;
         if(Boolean(bx0))
         {
            addChild(bx0);
            bx0["setImg"](mc0);
         }
      }
      
      private function iconConSet(mc0:DisplayObject, name0:String) : void
      {
         var iconCon0:NormalGridIcon = null;
         iconCon0 = new NormalGridIcon();
         addChild(iconCon0);
         iconCon0.x = mc0.x;
         iconCon0.y = mc0.y;
         this.SET(ComMethod.firstLowerCase(name0 + "Icon"),iconCon0);
      }
      
      protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
      }
   }
}

