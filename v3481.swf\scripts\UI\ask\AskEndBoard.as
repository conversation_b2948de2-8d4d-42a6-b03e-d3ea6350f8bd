package UI.ask
{
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class AskEndBoard extends NormalUI
   {
      private var txt:TextField;
      
      private var giftBtnSp:MovieClip;
      
      public var giftBtn:NormalBtn = new NormalBtn();
      
      private var giftTag:Sprite;
      
      private var giftBox:ItemsGripBox = new ItemsGripBox();
      
      public function AskEndBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["txt","giftBtnSp","giftTag"];
         super.setImg(img0);
         addChild(this.giftBox);
         this.giftBox.x = this.giftTag.x;
         this.giftBox.y = this.giftTag.y;
         this.giftBox.setIconPro("equipGrip");
         this.giftBox.arg.init(5,4,4,4);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.giftBox);
         addChild(this.giftBtn);
         this.giftBtn.setImg(this.giftBtnSp);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function showInfo(str0:String, gift0:GiftAddDefineGroup, getGiftB0:Boolean) : void
      {
         this.txt.htmlText = str0;
         if(Boolean(gift0))
         {
            this.giftBox.inData_byArr(gift0.arr,"inData_gift");
            this.giftBtn.visible = true;
            this.giftBtn.actived = !getGiftB0;
            this.giftBtn.setName(getGiftB0 ? "已领取" : "领取奖励");
         }
         else
         {
            this.giftBox.clearAllData();
            this.giftBtn.visible = false;
         }
      }
   }
}

