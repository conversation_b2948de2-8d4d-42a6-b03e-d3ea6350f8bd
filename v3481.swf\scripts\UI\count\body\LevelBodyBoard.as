package UI.count.body
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.base.AutoNormalUI;
   import UI.base.box.NormalBox;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.grid.NormalGrid;
   import UI.base.grid.NormalGridIcon;
   import UI.base.scroll.NormalScrollBar;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.TextMethod;
   import dataAll.body.define.BodyCamp;
   import dataAll.body.define.BodyFather;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.level.define.unit.UnitType;
   import dataAll.skill.IO_SkillDefineGetter;
   import dataAll.skill.define.SkillDefine;
   import dataAll.skill.define.SkillName;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextField;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.data.NormalBodyData;
   import gameAll.body.skill.SkillData;
   import gameAll.body.skill.StateData;
   
   public class LevelBodyBoard extends AutoNormalUI
   {
      private var bodyTag:Sprite;
      
      private var bodyTxt:TextField;
      
      private var bodyBox:NormalBox = new NormalBox();
      
      private var pageTag:Sprite;
      
      private var iconTag:Sprite;
      
      private var icon:NormalGridIcon = new NormalGridIcon();
      
      private var levelTxt:TextField;
      
      private var titleTxt:TextField;
      
      private var eleMc:MovieClip = null;
      
      private var infoTxt:TextField;
      
      private var skillTxt:TextField;
      
      private var buffTxt:TextField;
      
      private var skillBox:NormalBox = new NormalBox();
      
      private var buffBox:NormalBox = new NormalBox();
      
      private var boxSp:Sprite;
      
      private var maskTargetSp:Sprite;
      
      private var scrollBarSp:Sprite;
      
      private var scrollLineSp:Sprite;
      
      private var scrollBar:NormalScrollBar;
      
      private var tempBody:IO_NormalBody = null;
      
      private var idNumObj:Object = null;
      
      public function LevelBodyBoard()
      {
         super();
         mcTypeArr = ["btnSp","txt","tag"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         FontDeal.dealOne(this.bodyTxt);
         this.bodyBox.btnClass = NormalGrid;
         this.bodyBox.setIconPro("OtherUI/bodyBtn");
         this.bodyBox.arg.init(4,6,8,8);
         this.bodyBox.evt.setWant(true,true);
         addChild(this.bodyBox);
         NormalUICtrl.setTag(this.bodyBox,this.bodyTag);
         this.bodyBox.addEventListener(ClickEvent.ON_CLICK,this.bodyBoxClick);
         this.bodyBox.addEventListener(ClickEvent.ON_OVER,this.bodyBoxOver);
         this.bodyBox.addEventListener(ClickEvent.ON_OUT,Gaming.uiGroup.tipBox.hide);
         this.bodyBox.pageBox.setToSmall();
         this.bodyBox.setPagePos(this.pageTag);
         this.boxSp.addChild(this.iconTag);
         this.iconTag.addChild(this.icon);
         this.boxSp.addChild(this.levelTxt);
         this.levelTxt.htmlText = "";
         this.boxSp.addChild(this.titleTxt);
         FontDeal.dealOne(this.titleTxt);
         this.titleTxt.htmlText = "";
         this.eleMc = Gaming.swfLoaderManager.getResourceFull("GameWorldUI/eleMc");
         this.boxSp.addChild(this.eleMc);
         this.eleMc.stop();
         this.eleMc.x = 313;
         this.eleMc.y = 110;
         this.boxSp.addChild(this.infoTxt);
         TextMethod.setAutoFormat(this.infoTxt);
         FontDeal.dealLine(this.infoTxt);
         this.infoTxt.htmlText = "";
         this.boxSp.addChild(this.skillTxt);
         FontDeal.dealOne(this.skillTxt);
         this.boxSp.addChild(this.buffTxt);
         FontDeal.dealOne(this.buffTxt);
         this.skillBox.btnClass = NormalGrid;
         this.skillBox.setIconPro("OtherUI/buffBar");
         this.skillBox.arg.init(1,99,0,2);
         this.skillBox.evt.setWant(false,true);
         this.boxSp.addChild(this.skillBox);
         this.skillBox.x = 428;
         this.skillBox.y = 110;
         this.skillBox.addEventListener(ClickEvent.ON_OVER,this.skillBoxOver);
         this.skillBox.addEventListener(ClickEvent.ON_OUT,Gaming.uiGroup.tipBox.hide);
         this.buffBox.btnClass = NormalGrid;
         this.buffBox.setIconPro("OtherUI/buffBar");
         this.buffBox.arg.init(1,99,0,2);
         this.buffBox.evt.setWant(false,true);
         this.boxSp.addChild(this.buffBox);
         this.buffBox.x = 618;
         this.buffBox.y = 110;
         this.buffBox.addEventListener(ClickEvent.ON_OVER,this.buffBoxOver);
         this.buffBox.addEventListener(ClickEvent.ON_OUT,Gaming.uiGroup.tipBox.hide);
         this.scrollBar = new NormalScrollBar(this.boxSp,this.maskTargetSp,this.scrollBarSp,this.scrollLineSp,1,false,true,true);
         this.scrollBar.speed = 30;
         this.scrollBar.refresh();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      private function fleshData() : void
      {
         var we0:int = Gaming.BG.filter.getExistLiveNum(BodyCamp.WE);
         var enemy0:int = Gaming.BG.filter.getExistLiveNum(BodyCamp.WE);
         this.bodyTxt.htmlText = "我方：" + ComMethod.green(we0) + "  敌人：" + ComMethod.orange(enemy0);
         var arr0:Array = this.getBodyArr();
         this.bodyBox.inData_byArr(arr0,this.inBodyBtnFun);
         var chooseBtn0:NormalBtn = this.bodyBox.getChooseBtn();
         if(Boolean(chooseBtn0))
         {
            this.inBody(chooseBtn0.itemsData as IO_NormalBody);
         }
         else
         {
            this.clearBody();
         }
      }
      
      private function inBodyBtnFun(btn0:NormalGrid, b0:IO_NormalBody) : void
      {
         btn0.itemsData = b0;
         var dat0:NormalBodyData = b0.getData();
         var d0:NormalBodyDefine = dat0.define;
         btn0.setIconName(dat0.getHeadIconUrl());
         btn0.setName("");
         if(Boolean(btn0.icon))
         {
            if(btn0.icon.haveDataB() == false)
            {
               btn0.setName(d0.getRoleCn());
            }
         }
         btn0.setBackLabel(dat0.camp);
         btn0.setSmallIconFrame(dat0.isEnemy() ? 1 : 2);
         btn0.setSmallIconPer(dat0.getLifePer());
         var typeStr0:String = UnitType.getColorCnName(dat0.getDetailUnitType());
         btn0.setLevelText(typeStr0);
         var num0:int = this.getBodyNum(b0);
         if(num0 > 1)
         {
            btn0.setNumText("x" + num0);
         }
         else
         {
            btn0.setNumText("");
         }
      }
      
      private function getBodyArr() : Array
      {
         var b0:IO_NormalBody = null;
         var newArr0:Array = null;
         var ar2:Array = null;
         var showB0:Boolean = false;
         var index0:int = 0;
         var idNumMax0:int = 0;
         var dat0:NormalBodyData = null;
         var sumFather0:IO_NormalBody = null;
         var id0:String = null;
         var arr0:Array = Gaming.BG.getAllArr();
         var arrArr0:Array = [];
         var idNumObj0:Object = {};
         for each(b0 in arr0)
         {
            showB0 = true;
            index0 = 1;
            idNumMax0 = 1;
            dat0 = b0.getData();
            if(dat0.isEnemy())
            {
               if(dat0.existB == false)
               {
                  showB0 = false;
               }
               if(b0.getDieCtrl().isTrueBody() == false)
               {
                  sumFather0 = dat0.summoned.fatherBody;
                  if(Boolean(sumFather0))
                  {
                     if(sumFather0.getData().isNormalUnitB())
                     {
                        showB0 = false;
                     }
                     else if(dat0.stateD.out_noAiFindB == true)
                     {
                        showB0 = false;
                     }
                  }
                  else
                  {
                     showB0 = false;
                  }
               }
               else if(dat0.isNormalUnitB())
               {
               }
               if(showB0)
               {
                  if(dat0.isSuperB() || dat0.isBossB())
                  {
                     index0 = 0;
                  }
                  else
                  {
                     index0 = 3;
                  }
               }
            }
            else if(dat0.getExistOrNoEverParasitic())
            {
               if(dat0.isSaveDataPlayerB())
               {
                  index0 = 1;
               }
               else
               {
                  index0 = 2;
               }
            }
            else
            {
               showB0 = false;
            }
            if(Gaming.isLocal() == false)
            {
               if(dat0.define.father == BodyFather.other)
               {
                  showB0 = false;
               }
               else if(Gaming.swfLoaderManager.haveResourceFull(dat0.getHeadIconUrl()) == false)
               {
                  showB0 = false;
               }
            }
            if(showB0)
            {
               if((dat0.isSuperB() || dat0.isBossB()) == false)
               {
                  id0 = dat0.camp + "_" + dat0.getDetailUnitType() + "_" + dat0.id;
                  if(idNumObj0.hasOwnProperty(id0) == false)
                  {
                     idNumObj0[id0] = 0;
                  }
                  ++idNumObj0[id0];
                  if(idNumObj0[id0] > idNumMax0)
                  {
                     showB0 = false;
                  }
               }
            }
            if(showB0)
            {
               if(arrArr0[index0] == null)
               {
                  arrArr0[index0] = [];
               }
               arrArr0[index0].push(b0);
            }
         }
         this.idNumObj = idNumObj0;
         newArr0 = [];
         for each(ar2 in arrArr0)
         {
            newArr0 = newArr0.concat(ar2);
         }
         return newArr0;
      }
      
      private function getBodyNum(b0:IO_NormalBody) : int
      {
         var dat0:NormalBodyData = b0.getData();
         var id0:String = dat0.camp + "_" + dat0.getDetailUnitType() + "_" + dat0.id;
         if(this.idNumObj.hasOwnProperty(id0))
         {
            return this.idNumObj[id0];
         }
         return 0;
      }
      
      private function inBody(b0:IO_NormalBody) : void
      {
         var dat0:NormalBodyData = null;
         var d0:NormalBodyDefine = null;
         var secSp0:Sprite = null;
         this.tempBody = b0;
         if(Boolean(b0))
         {
            this.bodyBox.setChooseByItemsData(b0);
            dat0 = b0.getData();
            d0 = dat0.define;
            this.icon.setIconName(dat0.getHeadIconUrl());
            this.titleTxt.htmlText = d0.getRoleCn();
            this.levelTxt.htmlText = dat0.bodyLevel + "";
            this.eleMc.gotoAndStop(d0.shell);
            this.infoTxt.htmlText = dat0.getLevelBodyInfo();
            this.inSkill(b0);
            this.inBuff(b0);
            secSp0 = this.boxSp["secSp"];
            secSp0.scaleY = 1;
            secSp0.height = this.boxSp.height + 10;
            if(secSp0.scaleY < 1)
            {
               secSp0.scaleY = 1;
            }
            this.scrollBar.refresh();
         }
         else
         {
            this.clearBody();
         }
      }
      
      private function bodyBoxClick(e:ClickEvent) : void
      {
         this.inBody(e.childData as IO_NormalBody);
      }
      
      private function bodyBoxOver(e:ClickEvent) : void
      {
      }
      
      private function clearBody() : void
      {
         this.tempBody = null;
         this.bodyBox.setChoose_byIndex(-1);
         this.icon.clearData();
         this.titleTxt.htmlText = "";
         this.levelTxt.htmlText = "";
         this.infoTxt.htmlText = "";
         this.skillBox.clearAllData();
         this.buffBox.clearAllData();
         var secSp0:Sprite = this.boxSp["secSp"];
         secSp0.scaleY = 1;
         this.scrollBar.refresh();
      }
      
      private function inSkill(b0:IO_NormalBody) : void
      {
         var da0:SkillData = null;
         var arr0:Array = null;
         var obj0:Object = b0.getSkill().obj;
         var arr1:Array = [];
         var arr2:Array = [];
         var firstArr0:Array = [];
         for each(da0 in obj0)
         {
            if(SkillName.countFirstArr.indexOf(da0.define.baseLabel) >= 0)
            {
               firstArr0.push(da0);
            }
            else if(da0.define.isActiveB())
            {
               if(b0.getData().isSaveDataPlayerB() == false)
               {
                  arr1.push(da0);
               }
            }
            else
            {
               arr2.push(da0);
            }
         }
         arr1.sort(this.sortBySkillFather);
         arr2.sort(this.sortBySkillFather);
         arr0 = firstArr0.concat(arr1).concat(arr2);
         this.skillBox.inData_byArr(arr0,this.inSkillFun);
      }
      
      private function sortBySkillFather(a0:IO_SkillDefineGetter, b0:IO_SkillDefineGetter) : int
      {
         return ArrayMethod.sortNumberFun(a0.getSkillDefine().getFatherCn(),b0.getSkillDefine().getFatherCn());
      }
      
      private function inSkillFun(btn0:NormalGrid, da0:SkillData) : void
      {
         var st0:Number = NaN;
         var uiNum0:Number = NaN;
         var passStr0:String = null;
         var d0:SkillDefine = da0.define;
         btn0.itemsData = da0;
         if(this.tempBody.getData().isSaveDataPlayerB())
         {
            btn0.setName(d0.getCountCn("we",true));
         }
         else
         {
            btn0.setName(d0.getCnName());
         }
         if(d0.isActiveB())
         {
            st0 = da0.getUITrueSurplusCd();
            btn0.setShopBtnBackMc(1);
            btn0.setSmallIconPer(da0.getCDPer());
            btn0.setLevelText(st0 > 9999 ? "∞" : Number(st0).toFixed(1));
         }
         else
         {
            btn0.setShopBtnBackMc(2);
            if(da0.getMinTriggerT() > 0)
            {
               btn0.setLevelText(NumberMethod.toFixed(da0.getSurplusMinTriggerT()) + "");
               btn0.setSmallIconPer(da0.minTrigger_t / da0.getMinTriggerT());
            }
            else
            {
               uiNum0 = da0.getUINum();
               passStr0 = da0.getPassiveCdText();
               if(uiNum0 < 9999)
               {
                  btn0.setLevelText(uiNum0 + "");
               }
               else if(passStr0 != "")
               {
                  btn0.setLevelText(passStr0);
               }
               else
               {
                  btn0.setLevelText("");
               }
               btn0.setSmallIconPer(0);
            }
         }
      }
      
      private function skillBoxOver(e:ClickEvent) : void
      {
         var d0:SkillDefine = null;
         var s0:String = null;
         var st0:Number = NaN;
         var da0:SkillData = e.childData as SkillData;
         if(Boolean(da0))
         {
            d0 = da0.define;
            s0 = "";
            if(d0.isActiveB())
            {
               st0 = da0.getUITrueSurplusCd();
               s0 += "<green 主动技能/>";
               s0 += "\n<orange 冷却剩余：" + (st0 > 9999 ? "∞" : Number(st0).toFixed(1) + "秒") + "/>";
            }
            else if(da0.getMinTriggerT() > 0)
            {
               s0 += "\n<orange 最短触发间隔剩余：" + NumberMethod.toFixed(da0.getSurplusMinTriggerT()) + "秒/>";
            }
            if(s0 != "")
            {
               s0 += "\n";
            }
            s0 += d0.getTipStr();
            UIOrder.showTip(s0);
         }
      }
      
      private function inBuff(b0:IO_NormalBody) : void
      {
         var da0:StateData = null;
         var arr0:Array = null;
         var obj0:Object = b0.getState().obj;
         var arr1:Array = [];
         var arr2:Array = [];
         for each(da0 in obj0)
         {
            if(da0.extra.visible)
            {
               if(da0.define.isActiveB())
               {
                  arr1.push(da0);
               }
               else
               {
                  arr2.push(da0);
               }
            }
         }
         arr1.sort(this.sortBySkillFather);
         arr2.sort(this.sortBySkillFather);
         arr0 = arr1.concat(arr2);
         this.buffBox.inData_byArr(arr0,this.inBuffFun);
      }
      
      private function inBuffFun(btn0:NormalGrid, da0:StateData) : void
      {
         var d0:SkillDefine = da0.define;
         var st0:Number = da0.getSurplusT();
         btn0.itemsData = da0;
         var camp0:String = da0.BB.getData().camp == da0.getPuCamp() ? BodyCamp.WE : BodyCamp.ENEMY;
         btn0.setName(d0.getCountCn(camp0,true));
         if(d0.isActiveB())
         {
            btn0.setShopBtnBackMc(1);
         }
         else
         {
            btn0.setShopBtnBackMc(2);
         }
         btn0.setSmallIconPer(da0.getSurplusTPer());
         btn0.setLevelText(st0 > 9999 ? "∞" : Number(st0).toFixed(1));
      }
      
      private function buffBoxOver(e:ClickEvent) : void
      {
         var s0:String = null;
         var st0:Number = NaN;
         var da0:StateData = e.childData as StateData;
         if(Boolean(da0))
         {
            s0 = "";
            st0 = da0.getSurplusT();
            s0 += "<orange 持续时间剩余：" + (st0 > 9999 ? "∞" : Number(st0).toFixed(1) + "秒") + "/>";
            s0 += "\n" + da0.define.getTipStr();
            UIOrder.showTip(s0);
         }
      }
   }
}

