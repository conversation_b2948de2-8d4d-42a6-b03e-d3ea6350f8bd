package UI.forging.armsElement
{
   import UI.bag.BagUI;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.grid.NormalGrid;
   import UI.base.must.NormalMustBox;
   import dataAll._player.PlayerData;
   import dataAll.arms.ArmsData;
   import dataAll.arms.creator.ArmsElementCtrl;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class EleMoveBoard extends AutoNormalUI
   {
      private var beforeTxt:TextField;
      
      private var beforeGrip:ItemsGrid = new ItemsGrid();
      
      private var beforeGripTag:Sprite;
      
      private var afterTxt:TextField;
      
      private var afterGrip:ItemsGrid = new ItemsGrid();
      
      private var afterGripTag:Sprite;
      
      private var mustBox:NormalMustBox;
      
      private var btn:NormalBtn;
      
      private var delBtnObj:Object = {};
      
      private var beforeData:ArmsData = null;
      
      private var afterData:ArmsData = null;
      
      public function EleMoveBoard()
      {
         super();
         mcTypeArr = ["btnSp","mustBoxSp","txt","Bx","tag"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         img0.removeChild(img0["eleBtnSp"]);
         super.setImg(img0);
         this.initIcon("before");
         this.initIcon("after");
         this.btn.setName("转移");
         this.showNone();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function initIcon(type0:String) : void
      {
         var delBtn0:NormalBtn = null;
         var grip0:ItemsGrid = this[type0 + "Grip"];
         var tag0:Sprite = this[type0 + "GripTag"];
         grip0.setImgToEquipGrip();
         grip0.iconMaxHeight = 50;
         grip0.iconMaxWidth = 50;
         ItemsGripTipCtrl.addEvent_byItemsGrip(grip0);
         grip0.label = type0;
         grip0.addEventListener(MouseEvent.MOUSE_UP,this.gripUp);
         tag0.addChild(grip0);
         delBtn0 = new NormalBtn();
         delBtn0.setImg(Gaming.swfLoaderManager.getResourceFull("BasicUI/gripDelBtn"));
         delBtn0.x = grip0.x + grip0.width;
         delBtn0.y = grip0.y;
         delBtn0.label = type0;
         delBtn0.addEventListener(MouseEvent.CLICK,this.delBtnClick);
         tag0.addChild(delBtn0);
         this.delBtnObj[type0] = delBtn0;
      }
      
      override public function show() : void
      {
         super.show();
         this.flesh();
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      private function flesh() : void
      {
         var tipStr0:String = null;
         this.filterData();
         this.fleshBag();
         this.showIcon();
         this.mustBox.setShowState(false);
         this.btn.actived = false;
         if(Boolean(this.beforeData) && Boolean(this.afterData))
         {
            tipStr0 = ArmsElementCtrl.canMoveB(this.beforeData,this.afterData);
            if(tipStr0 == "")
            {
               this.showMust();
            }
            else
            {
               this.afterTxt.htmlText = tipStr0;
            }
         }
      }
      
      private function filterData() : void
      {
         var pd0:PlayerData = Gaming.PG.da;
         if(Boolean(this.beforeData))
         {
            if(!this.beforeData.isChosen)
            {
               this.beforeData = null;
            }
            if(Boolean(this.beforeData))
            {
               if(!pd0.findItemsData(this.beforeData))
               {
                  this.beforeData = null;
               }
            }
         }
         if(Boolean(this.afterData))
         {
            if(!this.afterData.isChosen)
            {
               this.afterData = null;
            }
            if(Boolean(this.afterData))
            {
               if(!pd0.findItemsData(this.afterData))
               {
                  this.afterData = null;
               }
            }
         }
      }
      
      private function fleshBag() : void
      {
         var bagUI0:BagUI = Gaming.uiGroup.bagUI;
         var showLabel0:String = "";
         if(Boolean(this.beforeData))
         {
            showLabel0 = this.beforeData.getDataType();
         }
         if(Boolean(this.afterData))
         {
            showLabel0 = this.afterData.getDataType();
         }
         if(!bagUI0.visible || showLabel0 != bagUI0.labelBox.nowLabel && showLabel0 != "")
         {
            bagUI0.showAndLabel(showLabel0);
         }
         if(!this.beforeData && !this.afterData)
         {
            bagUI0.unlockAllLabel();
         }
         else
         {
            bagUI0.lockOtherLabel();
         }
         bagUI0.fleshNowBoxChoose();
      }
      
      private function showIcon() : void
      {
         this.showOneItems("before","请拖入源物品");
         this.showOneItems("after","请拖入目标物品");
      }
      
      private function showOneItems(type0:String, noStr0:String) : void
      {
         var tip0:String = null;
         var grip0:ItemsGrid = this[type0 + "Grip"];
         var da0:ArmsData = this[type0 + "Data"];
         var txt0:TextField = this[type0 + "Txt"];
         var del0:NormalBtn = this.delBtnObj[type0];
         if(Boolean(da0))
         {
            grip0.inDataByAllItems(da0);
            tip0 = da0.getElementTip();
            txt0.htmlText = tip0;
            del0.visible = true;
         }
         else
         {
            this.clearOneIcon(type0);
         }
      }
      
      private function clearOneIcon(type0:String) : void
      {
         var grip0:ItemsGrid = this[type0 + "Grip"];
         var txt0:TextField = this[type0 + "Txt"];
         var del0:NormalBtn = this.delBtnObj[type0];
         this.clearOneData(type0);
         del0.visible = false;
         grip0.clearData();
         txt0.htmlText = type0 == "before" ? "请拖入源物品" : "请拖入目标物品";
      }
      
      private function clearOneData(type0:String) : void
      {
         var da0:ArmsData = this[type0 + "Data"];
         if(Boolean(da0))
         {
            da0.isChosen = false;
            this[type0 + "Data"] = null;
         }
      }
      
      private function showMust() : void
      {
         var must_d0:MustDefine = ArmsElementCtrl.getMoveMust(this.beforeData,this.afterData);
         var mustB0:Boolean = this.mustBox.inData(must_d0);
         this.btn.actived = mustB0;
      }
      
      private function gripUp(e:MouseEvent) : void
      {
         var targetDa0:ArmsData = null;
         var grip0:ItemsGrid = null;
         var type0:String = null;
         var tgrip0:NormalGrid = Gaming.uiGroup.dragCtrl.dragChild;
         if(Boolean(tgrip0))
         {
            targetDa0 = tgrip0.itemsData as ArmsData;
            if(Boolean(targetDa0))
            {
               if(targetDa0 != this.beforeData && targetDa0 != this.afterData)
               {
                  grip0 = e.target as ItemsGrid;
                  type0 = grip0.label;
                  this.clearOneData(type0);
                  targetDa0.isChosen = true;
                  this[type0 + "Data"] = targetDa0;
                  this.flesh();
               }
            }
         }
      }
      
      private function delBtnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var type0:String = btn0.label;
         this.clearOneData(type0);
         this.flesh();
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var must_d0:MustDefine = null;
         this.filterData();
         if(Boolean(this.beforeData) && Boolean(this.afterData))
         {
            if(this.btn.actived)
            {
               must_d0 = ArmsElementCtrl.getMoveMust(this.beforeData,this.afterData);
               PlayerMustCtrl.deductMust(must_d0,this.afterMove);
            }
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("数据不存在！");
         }
      }
      
      private function afterMove() : void
      {
         ArmsElementCtrl.move(this.beforeData,this.afterData);
         Gaming.uiGroup.bagUI.labelBox.nowLabel = "";
         this.flesh();
         Gaming.uiGroup.alertBox.showSuccess("转移成功！");
      }
      
      private function showNone() : void
      {
         this.clearOneIcon("before");
         this.clearOneIcon("after");
         this.mustBox.setShowState(false);
         this.btn.actived = false;
      }
   }
}

