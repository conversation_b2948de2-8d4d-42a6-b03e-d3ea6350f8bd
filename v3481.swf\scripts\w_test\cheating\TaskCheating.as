package w_test.cheating
{
   import dataAll._app.task.define.TaskDefine;
   import dataAll._app.worldMap.save.WorldMapSaveGroup;
   
   public class TaskCheating extends OneCheating
   {
      public function TaskCheating()
      {
         super();
      }
      
      public function unlockTask(str0:String, v0:int) : String
      {
         var s0:WorldMapSaveGroup = Gaming.PG.da.worldMap.saveGroup;
         s0.winOne("BaiLu",0);
         Gaming.uiGroup.mainUI.fleshBtn();
         return "解锁任务";
      }
      
      public function unlockMainTask(str0:String, v0:int) : String
      {
         Gaming.PG.da.task.unlockAllByType("main");
         return "解锁所有主线任务";
      }
      
      public function delMainTaskMore(str0:String, v0:int) : String
      {
         var num0:int = 0;
         var d0:TaskDefine = Gaming.defineGroup.task.getOneDefine(str0);
         if(Boolean(d0) && d0.isMainB())
         {
            num0 = Gaming.PG.da.task.delMainMore(str0);
            return "删除了【" + d0.cnName + "】之后的所有主线任务" + num0 + "个";
         }
         return "";
      }
      
      public function nowTaskComplete(str0:String, v0:int) : String
      {
         Gaming.uiGroup.taskUI.test_nowTaskComplete();
         return "当前任务完成";
      }
      
      public function nowTaskGet(str0:String, v0:int) : String
      {
         Gaming.uiGroup.taskUI.test_nowTaskGet();
         return "当前任务接取";
      }
      
      public function setNowTaskNum(str0:String, v0:int) : String
      {
         Gaming.uiGroup.taskUI.test_setTaskNum(v0);
         return "设置当前任务目标次数：" + v0;
      }
   }
}

