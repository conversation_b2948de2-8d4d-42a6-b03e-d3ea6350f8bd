package UI.count.hurt
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.box.NormalBox;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.grid.NormalGrid;
   import UI.base.scroll.NormalScrollBar;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._player.count.PlayerCountSave;
   import dataAll.body.attack.HurtData;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.text.TextField;
   import gameAll.trigger.HurtTrigger;
   import gameAll.trigger.hurt.HurtCountData;
   import gameAll.trigger.hurt.HurtCountDataGroup;
   
   public class LevelHurtBoard extends AutoNormalUI
   {
      private var openBtn:NormalBtn;
      
      private var oneBtn:NormalBtn;
      
      private var infoTxt:TextField;
      
      private var boxSp:Sprite;
      
      private var oneTxt:TextField;
      
      private var oneTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var oneBox:NormalBox = new NormalBox();
      
      private var maskTargetSp:Sprite;
      
      private var scrollBarSp:Sprite;
      
      private var scrollLineSp:Sprite;
      
      private var scrollBar:NormalScrollBar;
      
      private var cumBox:NormalBox = new NormalBox();
      
      private var childBox:NormalBox = new NormalBox();
      
      private var oneLabelArr:Array = ["all","die"];
      
      private var oneCnArr:Array = ["近50条","死亡"];
      
      private var oneLabel:String = this.oneLabelArr[0];
      
      private var oneMaxHurt:Number = 0;
      
      public function LevelHurtBoard()
      {
         super();
         mcTypeArr = ["btnSp","txt","tag"];
      }
      
      private static function get SAVE() : PlayerCountSave
      {
         return Gaming.PG.save.getCount();
      }
      
      private static function get hurtTrriger() : HurtTrigger
      {
         return Gaming.TG.hurt;
      }
      
      private static function get cumG() : HurtCountDataGroup
      {
         return Gaming.TG.hurt.count.getCumObj();
      }
      
      private static function inBarFun(grip0:NormalGrid, da0:HurtCountData) : void
      {
         grip0.label = da0.cn;
         grip0.itemsData = da0;
         grip0.setName(da0.getCn());
         grip0.setNumText(da0.getHurtSort());
         grip0.setLevelText(NumberMethod.toPer(da0.per));
         grip0.setSmallIconPer(da0.per);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         this.openBtn.setName("累计伤害统计");
         this.oneBtn.setName("单次伤害统计");
         this.oneBtn.tipString = "开启单次伤害统计将会影响游戏效率，不建议在平时刷图时开启。";
         ItemsGripTipCtrl.addNormalBtnTip(this.openBtn);
         ItemsGripTipCtrl.addNormalBtnTip(this.oneBtn);
         FontDeal.dealOne(this.infoTxt);
         this.infoTxt.htmlText = "";
         this.addHurtBox(this.cumBox);
         this.addHurtBox(this.childBox,false);
         FontDeal.dealOne(this.oneTxt);
         this.oneTxt.styleSheet = TextMethod.getLinkCss();
         this.oneTxt.addEventListener(TextEvent.LINK,this.oneTextClick);
         this.scrollBar = new NormalScrollBar(this.boxSp,this.maskTargetSp,this.scrollBarSp,this.scrollLineSp,1,false,true,true);
         this.scrollBar.speed = 30;
         this.scrollBar.refresh();
         this.oneBox.setIconPro("OtherUI/levelHurtOneBar");
         this.oneBox.arg.init(1,13,0,0,false);
         this.oneBox.evt.setWant(false,true);
         addChild(this.oneBox);
         NormalUICtrl.setTag(this.oneBox,this.oneTag);
         this.oneBox.addEventListener(ClickEvent.ON_OVER,this.oneBoxOver);
         this.oneBox.addEventListener(ClickEvent.ON_OUT,Gaming.uiGroup.tipBox.hide);
         this.oneBox.pageBox.setToSmall();
         this.oneBox.setPagePos(this.pageTag);
      }
      
      private function addHurtBox(b0:NormalBox, cumB0:Boolean = true) : void
      {
         this.boxSp.addChild(b0);
         b0.arg.init(1,99,0,0);
         b0.setIconPro("OtherUI/levelHurtBar");
         b0.btnClass = NormalGrid;
         if(cumB0)
         {
            b0.evt.setWant(true);
            b0.x = 5;
            b0.y = 10;
            b0.addEventListener(ClickEvent.ON_CLICK,this.cumBoxClick);
         }
         else
         {
            b0.evt.setWant(false,true);
            b0.x = 287;
            b0.y = 10;
            b0.addEventListener(ClickEvent.ON_OVER,this.childBoxOver);
            b0.addEventListener(ClickEvent.ON_OUT,Gaming.uiGroup.tipBox.hide);
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      private function fleshData() : void
      {
         this.fleshBtn();
         this.fleshInfo();
         this.fleshCumBox();
         this.fleshOne();
      }
      
      private function fleshBtn() : void
      {
         this.openBtn.isChosen = SAVE.getHurtCumB();
         this.oneBtn.isChosen = SAVE.getHurtTestB();
      }
      
      private function fleshInfo() : void
      {
         var s0:String = null;
         var sum0:Number = NaN;
         if(Boolean(cumG))
         {
            s0 = "";
            sum0 = cumG.getHurtSum();
            s0 += this.infoTitle("伤害累计：") + ComMethod.numberToSmall(sum0);
            this.infoTxt.htmlText = s0;
         }
      }
      
      private function infoTitle(s0:String) : String
      {
         return ComMethod.color(s0,"#33CCFF");
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0 == this.openBtn)
         {
            SAVE.setHurtCumB(!SAVE.getHurtCumB());
            hurtTrriger.fleshTestCount();
            this.fleshBtn();
         }
         else if(btn0 == this.oneBtn)
         {
            SAVE.setHurtTestB(!SAVE.getHurtTestB());
            hurtTrriger.fleshTestCount();
            this.fleshBtn();
         }
      }
      
      private function fleshCumBox() : void
      {
         if(cumG == null)
         {
            return;
         }
         var arr0:Array = cumG.getPuCumArr();
         this.cumBox.inData_byArr(arr0,inBarFun);
         this.cumBox.setChoose(this.cumBox.nowLabel);
         this.fleshChildBox();
      }
      
      private function fleshChildBox() : void
      {
         var puDa0:HurtCountData = null;
         var arr0:Array = null;
         if(cumG == null)
         {
            return;
         }
         var choose0:NormalBtn = this.cumBox.getChooseBtn();
         if(Boolean(choose0))
         {
            puDa0 = choose0.itemsData as HurtCountData;
            if(Boolean(puDa0))
            {
               arr0 = cumG.getSortArrByPu(puDa0.puCn);
               this.childBox.inData_byArr(arr0,inBarFun);
            }
            else
            {
               this.cumBox.setChoose_byIndex(-1);
               this.childBox.clearAllData();
            }
         }
         else
         {
            this.childBox.clearAllData();
         }
         var secSp0:Sprite = this.boxSp["secSp"];
         secSp0.height = this.boxSp.height;
         this.scrollBar.refresh();
      }
      
      private function cumBoxClick(e:ClickEvent) : void
      {
         this.cumBox.setChoose(e.label);
         this.fleshChildBox();
      }
      
      private function childBoxOver(e:ClickEvent) : void
      {
         var trace0:String = null;
         var da0:HurtCountData = e.childData as HurtCountData;
         if(Boolean(da0))
         {
            trace0 = hurtTrriger.count.getOneTraceByHurtCn(da0.name);
            if(trace0 != "")
            {
               trace0 = "<i1>|<b><blue 最近一次伤害：/></b>\n" + trace0;
               Gaming.uiGroup.tipBox.showText(trace0);
            }
         }
      }
      
      private function fleshOne() : void
      {
         var obj0:Object = null;
         var hurt0:Number = NaN;
         var arr0:Array = hurtTrriger.count.getOneObjArr(this.oneLabel);
         this.oneMaxHurt = 0;
         for each(obj0 in arr0)
         {
            hurt0 = Number(obj0.hurt);
            if(hurt0 > this.oneMaxHurt)
            {
               this.oneMaxHurt = hurt0;
            }
         }
         this.oneBox.inData_byArr(arr0,this.inOneBarFun);
         this.oneTxt.htmlText = "单次伤害 " + TextMethod.getLabelArrLink(this.oneLabelArr,this.oneCnArr,this.oneLabel);
      }
      
      private function oneTextClick(e:TextEvent) : void
      {
         this.oneLabel = e.text;
         this.fleshOne();
      }
      
      private function inOneBarFun(grip0:NormalBtn, obj0:Object) : void
      {
         var str0:String = null;
         var hurt0:Number = NaN;
         var h0:HurtData = obj0.hurtData;
         str0 = obj0.str;
         hurt0 = Number(obj0.hurt);
         var maxHurt0:Number = hurtTrriger.count.getMaxHurt();
         var cn0:String = h0.getCountOneCn(false,obj0.dieB);
         grip0.label = cn0;
         grip0.itemsData = obj0;
         grip0.setName(cn0);
         grip0.setNumText(ComMethod.numberToSmall(hurt0));
         grip0.tipString = str0;
         if(this.oneMaxHurt > 0)
         {
            grip0.setSmallVisible(true);
            if(Boolean(h0.producter) && Boolean(h0.producter.getData().isEnemy()))
            {
               grip0.setSmallIconPer(hurt0 / h0.producter.getData().maxLife);
            }
            else
            {
               grip0.setSmallIconPer(hurt0 / this.oneMaxHurt);
            }
         }
         else
         {
            grip0.setSmallVisible(false);
         }
      }
      
      private function oneBoxOver(e:ClickEvent) : void
      {
         var grip0:NormalBtn = e.child as NormalBtn;
         if(Boolean(grip0))
         {
            Gaming.uiGroup.tipBox.showText(grip0.tipString);
         }
      }
   }
}

