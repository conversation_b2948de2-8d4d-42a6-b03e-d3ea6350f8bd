package UI.base.label
{
   import UI.base.box.NormalBox;
   import UI.base.button.NormalBtn;
   import dataAll.ui.label.LabelAddData;
   
   public class LabelBox extends NormalBox
   {
      public var nowCnName:String = "";
      
      public var addData:LabelAddData = new LabelAddData();
      
      public var firstUpB:Boolean = true;
      
      public function LabelBox()
      {
         super();
      }
      
      public function inData(imgType0:String, labelArr0:Array, cnNameArr0:Array, setSmallIconB0:Boolean = false) : void
      {
         var da0:LabelAddData = new LabelAddData();
         da0.inDataOther(imgType0,labelArr0,cnNameArr0);
         da0.setSmallIconB = setSmallIconB0;
         this.inDataByLabelAddData(da0);
      }
      
      public function inDataByLabelAddData(da0:LabelAddData) : void
      {
         var n:* = undefined;
         var btn0:NormalBtn = null;
         var childDa0:LabelAddData = null;
         this.clear();
         label = da0.label;
         this.addData = da0;
         setGripNum(da0.childArr.length,da0.imgType);
         for(n in gripArr)
         {
            btn0 = gripArr[n];
            childDa0 = da0.childArr[n];
            btn0.label = childDa0.label;
            btn0.setName(childDa0.cnName);
            if(da0.setSmallIconB)
            {
               btn0.setSmallIcon(btn0.label);
            }
         }
      }
      
      override public function setChoose(label0:String) : void
      {
         var da0:LabelAddData = this.addData.getChildByLabel(label0);
         if(Boolean(da0))
         {
            this.setChoose_byIndex(da0.index);
         }
      }
      
      override public function setChoose_byIndex(index0:int) : NormalBtn
      {
         var n:* = undefined;
         var btn0:NormalBtn = null;
         var btn2:NormalBtn = null;
         if(gripArr.length == 0)
         {
            return null;
         }
         var clearB0:Boolean = index0 < 0 || index0 > gripArr.length - 1;
         nowIndex = index0;
         for(n in gripArr)
         {
            btn0 = gripArr[n];
            btn0.isChosen = false;
            if(!btn0.lockActivedB)
            {
               btn0.actived = true;
            }
            if(this.firstUpB)
            {
               addChildAt(btn0,0);
            }
            else
            {
               addChild(btn0);
            }
         }
         if(!clearB0)
         {
            btn2 = gripArr[index0];
            this.setChooseBtn(btn2);
            return btn2;
         }
         return null;
      }
      
      protected function setChooseBtn(btn2:NormalBtn) : void
      {
         addChild(btn2);
         btn2.isChosen = true;
         btn2.actived = false;
         nowLabel = btn2.label;
         this.nowCnName = btn2.getNameString();
      }
      
      public function getFirstLabel() : String
      {
         var btn0:NormalBtn = gripArr[0];
         if(Boolean(btn0))
         {
            return btn0.label;
         }
         return "";
      }
      
      override public function clear() : void
      {
         super.clear();
         this.addData = new LabelAddData();
      }
   }
}

