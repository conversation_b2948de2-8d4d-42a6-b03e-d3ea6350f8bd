package w_test.cheating
{
   import UI.test.SaveTestBox;
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.StringMethod;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll.equip.weapon.WeaponDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.level.LevelDiffGetting;
   import dataAll.level.define.LevelDefine;
   import dataAll.level.modeDiy.ModeDiyDefine;
   import gameAll.body.IO_NormalBody;
   import gameAll.hero.HeroBody;
   import gameAll.level.unit.UnitCreator;
   
   public class LevelCheating extends OneCheating
   {
      public function LevelCheating()
      {
         super();
      }
      
      public function unlockGoto(str0:String, v0:int) : String
      {
         var arr0:Array = null;
         var d2:WorldMapDefine = null;
         var d0:WorldMapDefine = Gaming.defineGroup.worldMap.getDefineByCn(str0);
         if(<PERSON><PERSON><PERSON>(d0))
         {
            if(d0.isShowMapB())
            {
               arr0 = Gaming.defineGroup.worldMap.getShowMapArr();
               if(arr0.indexOf(d0) >= 0)
               {
                  for each(d2 in arr0)
                  {
                     Gaming.PG.da.worldMap.saveGroup.unlockOne(d2.name);
                     if(d0 == d2)
                     {
                        return "解锁 " + d0.cnName + " 成功！";
                     }
                  }
               }
            }
            return "地图 " + d0.cnName + " 无法解锁";
         }
         return "找不到地图 " + str0 + "";
      }
      
      public function lottery(str0:String, v0:int) : String
      {
         var g0:GiftAddDefineGroup = null;
         var m0:GiftAddDefineGroup = new GiftAddDefineGroup();
         for(var i:int = 0; i < v0; i++)
         {
            g0 = Gaming.LG.getLotteryGiftNull(1);
            m0.merge(g0);
         }
         return "获得奖励\n" + m0.getDescription(2);
      }
      
      public function showPlayerSkillText(str0:String, v0:int) : String
      {
         var b0:IO_NormalBody = null;
         var skillArr0:Array = null;
         var skillCnArr0:Array = null;
         var barr0:Array = Gaming.PG.da.getAllFightBodyArr(false);
         var s0:String = "";
         for each(b0 in barr0)
         {
            skillArr0 = b0.getSkill().getNowSkillNameArr();
            skillCnArr0 = Gaming.defineGroup.skill.getCnArrByNameArr(skillArr0);
            s0 += b0.getDefine().cnName + "：" + StringMethod.concatStringArr(skillCnArr0,99) + "\n";
         }
         SaveTestBox.addText(s0);
         return "已输入到 SaveTestBox";
      }
      
      public function gotoLevelDefine(str0:String, v0:int) : String
      {
         var diff0:int = 0;
         var ar0:Array = str0.split(" ");
         var name0:String = ar0[0];
         var d0:LevelDefine = Gaming.defineGroup.level.getDefineBy(str0);
         if(Boolean(d0))
         {
            diff0 = 0;
            if(ar0.length >= 2)
            {
               diff0 = int(ar0[1]);
            }
            Gaming.LG.chooseByLevelDefine(d0,diff0);
            return "";
         }
         return "找不到地图定义：" + str0;
      }
      
      public function setAllDemDiy(str0:String, v0:int) : String
      {
         var darr0:Array = null;
         var num0:int = 0;
         var d0:WorldMapDefine = null;
         var diy0:ModeDiyDefine = Gaming.defineGroup.modelDiy.getDefine(str0);
         if(Boolean(diy0))
         {
            darr0 = Gaming.defineGroup.worldMap.getShowMapArr();
            num0 = 0;
            for each(d0 in darr0)
            {
               if(d0.getDemonDiy() != ModeDiyDefine.CLOSE)
               {
                  if(diy0.canMapB(d0))
                  {
                     d0.setDemonDiy(str0,"");
                     num0++;
                  }
               }
            }
            return "设置" + num0 + "个地图模式：" + str0;
         }
         return "找不到模式：" + str0;
      }
      
      public function stopAccOpen(str0:String, v0:int) : String
      {
         Gaming.uiGroup.stopHandUpBox.setStopAccB(true);
         return "开启防加速检测";
      }
      
      public function unlockAllMap(str0:String, v0:int) : String
      {
         Gaming.PG.da.worldMap.unlockAll();
         Gaming.uiGroup.mainUI.show();
         return "解锁所有地图";
      }
      
      public function winAllMap(str0:String, v0:int) : String
      {
         Gaming.PG.da.worldMap.unlockAll();
         Gaming.PG.da.worldMap.saveGroup.winAll();
         Gaming.uiGroup.mainUI.show();
         return "通关所有地图";
      }
      
      public function unlockAllDiff(str0:String, v0:int) : String
      {
         Gaming.PG.da.worldMap.saveGroup.unlockAllDiff();
         return "解锁所有关卡难度";
      }
      
      public function setDpsMul(str0:String, v0:int) : String
      {
         var b0:IO_NormalBody = null;
         var hero0:HeroBody = Gaming.PG.ctrlHero;
         if(Boolean(hero0))
         {
            b0 = hero0.dat.getCtrlBody();
            b0.getData().dpsMul = v0;
            return b0.getDefine().cnName + "设置战斗力倍数：" + v0;
         }
         return "";
      }
      
      public function testEnemy(str0:String, v0:int) : String
      {
         var uc0:UnitCreator = Gaming.LG.nowLevel.unitCtreator;
         uc0.testEnemyB = !uc0.testEnemyB;
         return (uc0.testEnemyB ? "关闭" : "开启") + "小怪出兵";
      }
      
      public function winLevel(str0:String, v0:int) : String
      {
         Gaming.LG.levelWin("r_over");
         return "";
      }
      
      public function testLevel(str0:String, v0:int) : String
      {
         Gaming.PG.save.worldMap.setOneName("WoTu","WoTu_test");
         return "沃土镇设为测试状态";
      }
      
      public function setAnger(str0:String, v0:int) : String
      {
         var d0:WeaponDefine = null;
         for each(d0 in Gaming.defineGroup.weapon.obj)
         {
            d0.anger = 0;
         }
         return "设置所有副手所需怒气值：" + v0;
      }
      
      public function restart(str0:String, v0:int) : String
      {
         Gaming.LG.restartLevel();
         return "";
      }
      
      public function setEndlessLv(str0:String, v0:int) : String
      {
         Gaming.testCtrl.cheating.endlessGrade = v0;
         return "设置当前层级为：" + v0;
      }
      
      public function setBossLifePer(str0:String, v0:int) : String
      {
         var b0:IO_NormalBody = null;
         var enemyArr0:Array = Gaming.BG.ENEMY_ARR;
         for each(b0 in enemyArr0)
         {
            b0.getData().setLifePer(v0 / 100);
         }
         return "设置所有敌人生命值为百分之：" + NumberMethod.toPer(v0 / 100);
      }
      
      public function setWeLifePer(str0:String, v0:int) : String
      {
         var b0:IO_NormalBody = null;
         var arr0:Array = Gaming.BG.WE_ARR;
         for each(b0 in arr0)
         {
            b0.getData().setLifePer(v0 / 100);
         }
         return "设置所有我方单位生命值为百分之：" + NumberMethod.toPer(v0 / 100);
      }
      
      public function killAllPartner(str0:String, v0:int) : String
      {
         var b0:IO_NormalBody = null;
         var arr0:Array = Gaming.BG.WE_ARR.concat();
         for each(b0 in arr0)
         {
            if(!b0.getData().isWeMainPlayerB())
            {
               Gaming.TG.hurt.toDie(b0,Gaming.BG.filter.getOneBoss());
            }
         }
         return "杀死我方所有队友";
      }
      
      public function openAllSweeping(str0:String, v0:int) : String
      {
         LevelDiffGetting.openAllSweepingB = !LevelDiffGetting.openAllSweepingB;
         if(LevelDiffGetting.openAllSweepingB)
         {
            Gaming.PG.da.worldMap.saveGroup.sweepingNum = -1000;
         }
         return "开启所有难度扫荡：" + LevelDiffGetting.openAllSweepingB;
      }
      
      public function clearMainCharger(str0:String, v0:int) : String
      {
         var hero0:HeroBody = Gaming.PG.ctrlHero;
         if(Boolean(hero0))
         {
            hero0.dat.clearAllCapacityCharger();
            return "清空" + hero0.dat.getCnName() + "的所有弹夹和携弹量";
         }
         return "找不到P1";
      }
      
      public function moveBody(str0:String, v0:int) : String
      {
         var sarr0:Array = str0.split(",");
         var id0:String = sarr0[0];
         var x0:Number = int(sarr0[1]);
         var y0:Number = int(sarr0[2]);
         var b0:IO_NormalBody = Gaming.BG.filter.getBody_byId(id0);
         if(Boolean(b0))
         {
            b0.setXY(x0,y0);
            return "移动" + id0 + "到 " + x0 + "," + y0;
         }
         return "找不到id：" + id0;
      }
   }
}

