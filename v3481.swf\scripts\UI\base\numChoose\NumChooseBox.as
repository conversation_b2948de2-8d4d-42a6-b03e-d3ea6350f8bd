package UI.base.numChoose
{
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.NumberMethod;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class NumChooseBox extends NormalUI
   {
      private var CF:NiuBiCF = new NiuBiCF();
      
      private var prevSp:MovieClip;
      
      private var nextSp:MovieClip;
      
      private var valueTxt:TextField;
      
      private var prev_btn:NormalBtn = new NormalBtn();
      
      private var next_btn:NormalBtn = new NormalBtn();
      
      public var dealNumFun:Function;
      
      public var textChangeFleshB:Boolean = true;
      
      public var fixed:int = 0;
      
      public function NumChooseBox()
      {
         super();
         this.nowNum = 0;
         this.max = 9999;
         this.min = 0;
         this.gapNum = 1;
      }
      
      public function get max() : Number
      {
         return this.CF.getAttribute("max");
      }
      
      public function set max(v0:Number) : void
      {
         this.CF.setAttribute("max",v0);
      }
      
      public function get min() : Number
      {
         return this.CF.getAttribute("min");
      }
      
      public function set min(v0:Number) : void
      {
         this.CF.setAttribute("min",v0);
      }
      
      public function get gapNum() : Number
      {
         return this.CF.getAttribute("gapNum");
      }
      
      public function set gapNum(v0:Number) : void
      {
         this.CF.setAttribute("gapNum",v0);
      }
      
      public function get nowNum() : Number
      {
         return this.CF.getAttribute("nowNum");
      }
      
      public function set nowNum(v0:Number) : void
      {
         this.CF.setAttribute("nowNum",v0);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["valueTxt","nextSp","prevSp"];
         super.setImg(img0);
         FontDeal.dealOne(this.valueTxt);
         addChild(this.prev_btn);
         this.prev_btn.setImg(this.prevSp);
         this.prev_btn.setName("-",false);
         this.prev_btn.addEventListener(MouseEvent.CLICK,this.prevClick);
         addChild(this.next_btn);
         this.next_btn.setImg(this.nextSp);
         this.next_btn.setName("+",false);
         this.next_btn.addEventListener(MouseEvent.CLICK,this.nextClick);
         this.valueTxt.addEventListener(Event.CHANGE,this.valueTextChange);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function init(max0:Number, min0:Number) : void
      {
         this.max = max0;
         this.min = min0;
         this.nowNum = this.min;
         this.fleshPriceAndNum(0,false,false);
      }
      
      public function setNum(num0:Number) : void
      {
         this.nowNum = num0;
         this.fleshPriceAndNum(0,false,false);
      }
      
      public function setMax(max0:Number) : void
      {
         if(max0 < this.min)
         {
            max0 = this.min;
         }
         this.max = max0;
         if(this.nowNum > max0)
         {
            this.setNum(max0);
         }
      }
      
      public function fleshPriceAndNum(numAdd0:Number, fromTextB0:Boolean = true, eventB0:Boolean = true) : void
      {
         var text0:String = null;
         var v0:Number = NaN;
         var beforeNum0:Number = this.nowNum;
         this.nowNum += numAdd0;
         var num0:Number = 0;
         if(fromTextB0)
         {
            text0 = this.valueTxt.text;
            v0 = Number(text0);
            if(isNaN(v0))
            {
               v0 = 0;
            }
            num0 = v0;
            num0 = this.dealNuyNum(num0);
            this.nowNum = num0;
            if(text0 != "")
            {
               this.valueTxt.text = num0 + "";
            }
         }
         else
         {
            num0 = this.nowNum;
            num0 = this.dealNuyNum(num0);
            this.nowNum = num0;
            this.valueTxt.text = num0 + "";
         }
         if(this.nowNum != beforeNum0 && eventB0)
         {
            this.changeEvent();
         }
      }
      
      private function valueTextChange(e:Event) : void
      {
         if(this.textChangeFleshB)
         {
            this.fleshPriceAndNum(0);
         }
      }
      
      private function prevClick(e:MouseEvent) : *
      {
         this.fleshPriceAndNum(-this.gapNum,false);
      }
      
      private function nextClick(e:MouseEvent) : *
      {
         this.fleshPriceAndNum(this.gapNum,false);
      }
      
      private function dealNuyNum(num0:Number) : Number
      {
         if(this.dealNumFun is Function)
         {
            num0 = this.dealNumFun(num0);
         }
         var max0:Number = this.max;
         var min0:Number = this.min;
         num0 = NumberMethod.toFixed(num0,this.fixed);
         if(num0 > max0)
         {
            num0 = max0;
         }
         if(num0 < min0)
         {
            num0 = min0;
         }
         return num0;
      }
      
      public function getFleshNum() : Number
      {
         if(this.textChangeFleshB == false)
         {
            this.fleshPriceAndNum(0,true,false);
            return this.nowNum;
         }
         return this.dealNuyNum(this.nowNum);
      }
      
      private function changeEvent() : void
      {
         var e0:Event = new Event(Event.CHANGE);
         dispatchEvent(e0);
      }
   }
}

