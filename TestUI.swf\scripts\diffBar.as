package
{
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol619")]
   public dynamic class diffBar extends MovieClip
   {
      public var sizeSp:MovieClip;
      
      public var numTxt:TextField;
      
      public var numBackSp:MovieClip;
      
      public var nameTxt:TextField;
      
      public function diffBar()
      {
         super();
      }
   }
}

