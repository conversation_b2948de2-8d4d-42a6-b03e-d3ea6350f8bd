package UI.base.cheating
{
   import UI.UIOrder;
   import UI.base.HaveConSprite;
   import UI.base.box.NormalBox;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextField;
   import gameAll.more.DoubleCtrl;
   
   public class StopHandUpBox extends HaveConSprite
   {
      private var CF:NiuBiCF = new NiuBiCF();
      
      private var noSp:Sprite;
      
      private var btnTag:Sprite;
      
      private var txt:TextField;
      
      private var titleTxt:TextField;
      
      private var infoTxt:TextField;
      
      private var btnBox:NormalBox = new NormalBox();
      
      private var nowAskIndex:int = 0;
      
      private var img:Sprite;
      
      private var nowAF:int = 0;
      
      private var stopAccB:Boolean = false;
      
      public function StopHandUpBox()
      {
         super();
      }
      
      public function get sec_i() : Number
      {
         return this.CF.getAttribute("sec_i");
      }
      
      public function set sec_i(v0:Number) : void
      {
         this.CF.setAttribute("sec_i",v0);
      }
      
      public function get local_t() : Number
      {
         return this.CF.getAttribute("local_t");
      }
      
      public function set local_t(v0:Number) : void
      {
         this.CF.setAttribute("local_t",v0);
      }
      
      public function get overNum() : Number
      {
         return this.CF.getAttribute("overNum");
      }
      
      public function set overNum(v0:Number) : void
      {
         this.CF.setAttribute("overNum",v0);
      }
      
      public function imgInit() : void
      {
         this.setImg(Gaming.uiGroup.getBasicMovieClip("stopHandUpBox"));
         visible = false;
      }
      
      private function setImg(img0:Sprite) : void
      {
         var n:* = undefined;
         var name0:String = null;
         this.img = img0;
         var elementNameArr:Array = ["btnTag","txt","noSp","titleTxt","infoTxt"];
         for(n in elementNameArr)
         {
            name0 = elementNameArr[n];
            this.SET(name0,this.img[name0]);
            if(this.img[name0] is MovieClip)
            {
               this.img[name0].stop();
            }
         }
         addChildAt(this.img,0);
         this.x = this.img.x;
         this.y = this.img.y;
         this.img.x = 0;
         this.img.y = 0;
         this.btnBox.setIconPro("BasicUI/stopHandUpBtn");
         this.btnBox.setNowGripNum(5);
         this.btnBox.addEventListener(ClickEvent.ON_CLICK,this.btnClick);
         this.btnTag.addChild(this.btnBox);
         addChild(this.noSp);
         this.noSp.visible = false;
         this.titleTxt.text = "防加速算术题";
         this.infoTxt.htmlText = "系统检测到您使用了加速功能，请关闭该功能，并正确回答算术题，即可正常游戏。如果题目回答错误超过4次，请您重新登录游戏。";
      }
      
      protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function show() : void
      {
         visible = true;
         inCon();
         this.nowAskIndex = 0;
         this.newAsk();
      }
      
      public function hide() : void
      {
         visible = false;
         outCon();
      }
      
      private function getNumberArr(num0:int) : Array
      {
         var arr0:Array = [];
         for(var i:int = 0; i < num0; i++)
         {
            arr0.push(i);
         }
         return arr0;
      }
      
      private function newAsk() : void
      {
         Gaming.LG.pauseLevel();
         DoubleCtrl.stopAll();
         this.nowAF = 0;
         this.noSp.visible = false;
         ++this.nowAskIndex;
         var n1:int = Math.random() * 10;
         var n2:int = Math.random() * 10;
         var a0:int = n1 + n2;
         this.txt.text = "问题： " + n1 + " + " + n2 + " = ？";
         var randomAskArr0:Array = ComMethod.getRandomArray(this.getNumberArr(20),5);
         var f0:int = int(randomAskArr0.indexOf(a0));
         if(f0 == -1)
         {
            randomAskArr0.splice(int(randomAskArr0.length * Math.random()),1);
            randomAskArr0.push(a0);
         }
         var indexArr0:Array = ComMethod.getRandomArray(this.getNumberArr(18),5);
         ComMethod.sortRandomArray(indexArr0);
         this.inputAsk(randomAskArr0,a0,indexArr0);
      }
      
      private function inputAsk(askArr0:Array, correctAsk0:int, indexArr0:Array) : void
      {
         var n:* = undefined;
         var btn0:NormalBtn = null;
         var a0:int = 0;
         var index0:int = 0;
         var x0:int = 0;
         var y0:int = 0;
         var btnArr0:Array = this.btnBox.gripArr;
         for(n in btnArr0)
         {
            btn0 = btnArr0[n];
            a0 = int(askArr0[n]);
            index0 = int(indexArr0[n]);
            x0 = index0 % 6 * btn0.width + 3;
            y0 = int(index0 / 6) * btn0.height + 3;
            btn0.setName(a0 + "");
            btn0.fatherUrl = a0 + "";
            btn0.x = x0;
            btn0.y = y0;
            btn0.label = a0 == correctAsk0 ? "correct" : "";
         }
      }
      
      private function btnClick(e:ClickEvent) : void
      {
         if(e.label == "correct")
         {
            this.nowAskIndex = 0;
            this.hide();
            Gaming.LG.resumeLevel();
            Gaming.PG.da.moreWay.addLifeLiveMore(1);
         }
         else if(this.nowAskIndex < 4)
         {
            Gaming.uiGroup.alertBox.showError("回答错误！\n你还有" + (3 - this.nowAskIndex) + "次机会。",this.newAsk);
         }
         else
         {
            this.nowAskIndex = 0;
            UIOrder.save();
            this.noSp.visible = true;
         }
      }
      
      public function setStopAccB(bb0:Boolean) : void
      {
         this.stopAccB = bb0;
      }
      
      public function getStopAccB() : Boolean
      {
         return this.stopAccB;
      }
      
      public function startLevel() : void
      {
         this.stopAccB = false;
         if(Gaming.LG.isUnionBattleB())
         {
         }
         if(Gaming.testCtrl.enabled)
         {
         }
         this.sec_i = 1;
         this.local_t = 0;
         this.overNum = 0;
      }
      
      public function overLevel() : void
      {
         this.stopAccB = false;
      }
      
      public function levelIngTimer() : void
      {
         var fram0:Number = NaN;
         var i0:int = 0;
         var localNew0:Number = NaN;
         var now0:Number = NaN;
         var c0:Number = NaN;
         if(this.stopAccB)
         {
            if(!visible)
            {
               fram0 = 30;
               i0 = this.sec_i;
               if(i0 == 1)
               {
                  this.local_t = this.getNowLocalTime();
               }
               i0++;
               if(i0 > fram0 / 2)
               {
                  i0 = 1;
                  localNew0 = (this.local_t + 0.5 - 1 / fram0) % 60;
                  now0 = this.getNowLocalTime();
                  c0 = localNew0 - now0;
                  if(c0 > 58)
                  {
                     c0 -= 60;
                  }
                  if(c0 < -58)
                  {
                     c0 += 60;
                  }
                  if(c0 > 0.1)
                  {
                     ++this.overNum;
                     if(this.overNum >= 3)
                     {
                        this.overNum = 1;
                        this.show();
                     }
                  }
                  else
                  {
                     this.overNum = 0;
                  }
               }
               this.sec_i = i0;
            }
         }
      }
      
      private function setLocalTime() : void
      {
      }
      
      private function getNowLocalTime() : Number
      {
         var de0:Date = new Date();
         return de0.seconds + de0.milliseconds / 1000;
      }
      
      public function levelIngTimerSecond() : void
      {
      }
      
      public function FTimer() : void
      {
         var btnArr0:Array = null;
         var btn0:NormalBtn = null;
         if(visible)
         {
            btnArr0 = this.btnBox.gripArr;
            ++this.nowAF;
            if(this.nowAF > 60)
            {
               if(this.nowAF > 180)
               {
                  this.nowAF = 0;
               }
               else
               {
                  for each(btn0 in btnArr0)
                  {
                     btn0.setName(btn0.fatherUrl);
                  }
               }
            }
            else if(this.nowAF % 3 == 0)
            {
               for each(btn0 in btnArr0)
               {
                  btn0.setName(String(int(Math.random() * 20)));
                  btn0.setSmallIconFrame(int(Math.random() * 2 + 1));
               }
            }
         }
      }
   }
}

