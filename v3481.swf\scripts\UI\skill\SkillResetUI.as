package UI.skill
{
   import UI.bag.ItemsGripBox;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.skill.HeroSkillReset;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class SkillResetUI extends NormalUI
   {
      private var btn:NormalBtn = new NormalBtn();
      
      private var addBtn:NormalBtn = new NormalBtn();
      
      private var box:ItemsGripBox = new ItemsGripBox();
      
      private var gripTag:Sprite;
      
      private var coinTxt:TextField;
      
      private var numTxt:TextField;
      
      private var btnSp:MovieClip;
      
      private var addBtnSp:MovieClip;
      
      public function SkillResetUI()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["numTxt","coinTxt","gripTag","btnSp","addBtnSp"];
         super.setImg(img0);
         FontDeal.dealOne(this.coinTxt);
         FontDeal.dealOne(this.numTxt);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("重置技能");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         addChild(this.addBtn);
         this.addBtn.setImg(this.addBtnSp);
         this.addBtn.addEventListener(MouseEvent.CLICK,this.addBtnClick);
         this.addBtn.addEventListener(MouseEvent.MOUSE_OVER,this.addBtnOver);
         this.addBtn.addEventListener(MouseEvent.MOUSE_OUT,this.addBtnOut);
         this.gripTag.addChild(this.box);
         this.box.imgType = "equipGrip";
         this.box.arg.init(3,1,5,0);
         this.box.evt.setWantEvent(true,false,false,true,true);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function fleshData() : void
      {
         var gift_d0:GiftAddDefineGroup = null;
         var canNum0:int = 0;
         var pd0:NormalPlayerData = Gaming.PG.DATA;
         if(pd0.skill.getNoLockNum() == 0 && pd0.skillBag.getNoLockNum() == 0)
         {
            this.btn.actived = false;
            Gaming.uiGroup.skillUI.setCoverText("你还没有可用于重置的技能。");
         }
         else
         {
            gift_d0 = HeroSkillReset.getResetByPlayerData(pd0);
            this.inGiftAddDefineGroup(gift_d0);
            canNum0 = HeroSkillReset.getCanResetNum(pd0) - pd0.base.save.skillResetedNum;
            this.numTxt.htmlText = "你还可以重置 " + ComMethod.color(canNum0 + "",canNum0 > 0 ? "#00FF00" : "#FF0000") + " 次";
            this.btn.actived = canNum0 > 0;
         }
      }
      
      private function inGiftAddDefineGroup(dg0:GiftAddDefineGroup) : void
      {
         var n:* = undefined;
         var d0:GiftAddDefine = null;
         var arr0:Array = dg0.arr;
         var arr2:Array = [];
         for(n in arr0)
         {
            d0 = arr0[n];
            if(d0.type == "base")
            {
               if(d0.name == "coin")
               {
                  this.coinTxt.text = d0.num + "";
               }
            }
            else
            {
               arr2.push(d0);
            }
         }
         this.box.inData_byArr(arr2,"inData_gift");
      }
      
      public function btnClick(e:MouseEvent) : void
      {
         Gaming.uiGroup.alertBox.showChoose("重置技能会清空技能的熟练度，\n是否继续？",this.doReset);
      }
      
      private function doReset() : void
      {
         var pd0:NormalPlayerData = Gaming.PG.DATA;
         var gift_d0:GiftAddDefineGroup = HeroSkillReset.getResetByPlayerData(pd0);
         var bagStr0:String = GiftAddit.bagSpacePan(gift_d0);
         if(bagStr0 != "")
         {
            Gaming.uiGroup.alertBox.showError(bagStr0);
         }
         else
         {
            pd0.skill.resetAllSkill();
            pd0.skillBag.resetAllSkill();
            ++pd0.base.save.skillResetedNum;
            this.fleshData();
            Gaming.uiGroup.skillUI.wearBox.fleshData();
            GiftAddit.add(gift_d0,"已获得所有返还的物品和银币。");
         }
      }
      
      private function addBtnClick(e:MouseEvent) : void
      {
         var must_d0:MustDefine = Gaming.defineGroup.must.skillResetNum;
         PlayerMustCtrl.numBuy(must_d0,"技能重置次数",-1,this.yes_addNum);
      }
      
      private function yes_addNum() : void
      {
         Gaming.soundGroup.playSound("uiSound","getItems");
         var pd0:NormalPlayerData = Gaming.PG.DATA;
         --pd0.base.save.skillResetedNum;
         this.fleshData();
      }
      
      private function addBtnOver(e:MouseEvent) : void
      {
         var must_d0:MustDefine = Gaming.defineGroup.must.skillResetNum;
         Gaming.uiGroup.tipBox.textTip.showFollowText(must_d0.getNumBuyText("技能重置次数"));
      }
      
      private function addBtnOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
   }
}

