package UI.skill
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.must.NormalMustBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.skill.define.HeroSkillDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.system.System;
   
   public class SkillStudyUI extends NormalUI
   {
      public var gripBox:ItemsGripBox = new ItemsGripBox();
      
      public var btn:NormalBtn = new NormalBtn();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var nowGrip:ItemsGrid = null;
      
      private var gripTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var thingsGripTag:Sprite;
      
      private var mustBoxSp:Sprite;
      
      private var btnSp:MovieClip;
      
      public function SkillStudyUI()
      {
         super();
         addChild(this.gripBox);
         addChild(this.btn);
         addChild(this.mustBox);
         this.gripBox.imgType = "equipGrip";
         this.gripBox.evt.setWantEvent(true,false,false,true,true);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.gripBox);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["gripTag","mustBoxSp","btnSp","pageTag"];
         super.setImg(img0);
         NormalUICtrl.setTag(this.gripBox,this.gripTag);
         this.mustBox.setImg(this.mustBoxSp);
         this.btn.setImg(this.btnSp);
         this.btn.setName("学习技能");
         this.showMustByGrip(null);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function fleshData() : void
      {
         var arr1:Array = null;
         var showNum0:int = 0;
         var pd0:NormalPlayerData = Gaming.PG.DATA;
         var num0:int = int(pd0.skill.dataArr.length + pd0.skillBag.dataArr.length);
         var max0:int = int(Gaming.defineGroup.skill.heroOriginalArr.length);
         if(num0 < max0)
         {
            arr1 = Gaming.PG.DATA.getNoStudyOriginalSkillDefineArr();
            showNum0 = pd0.getSkillChooseNum() + 1;
            this.gripBox.arg.init(showNum0 >= 6 ? 6 : showNum0,int((showNum0 - 1) / 6) + 1,6,6);
            this.gripBox.inData_byArr(arr1,"inData_SkillDefine");
            this.showMustByGrip(this.nowGrip);
         }
         else
         {
            Gaming.uiGroup.skillUI.setCoverText("你已经学完了所有技能");
         }
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         this.gripBox.setChoose_byIndex(e.index);
         this.showMustByGrip(e.child as ItemsGrid);
      }
      
      private function showMustByGrip(grip0:ItemsGrid) : void
      {
         var d0:HeroSkillDefine = null;
         var mustLv0:int = 0;
         var must_d0:MustDefine = null;
         var bb0:Boolean = false;
         if(Boolean(grip0))
         {
            if(this.gripBox.gripArr.indexOf(grip0) == -1)
            {
               grip0 = this.gripBox.findGripByData(grip0.itemsData);
            }
         }
         this.nowGrip = grip0;
         if(Boolean(grip0))
         {
            d0 = grip0.itemsData as HeroSkillDefine;
            if(Boolean(d0))
            {
               mustLv0 = Gaming.PG.DATA.getStudyMustLv();
               must_d0 = d0.getThisStudyMust(mustLv0);
               bb0 = this.mustBox.inData(must_d0);
               this.btn.actived = bb0;
               this.gripBox.setChoose_byIndex(grip0.index);
               if(Gaming.testCtrl.cheating.enabled)
               {
                  System.setClipboard(d0.getProjectText());
               }
            }
         }
         else
         {
            this.mustBox.setShowState(false);
            this.btn.actived = false;
         }
      }
      
      public function btnClick(e:MouseEvent) : void
      {
         var d0:HeroSkillDefine = null;
         var pd0:NormalPlayerData = null;
         var mustLv0:int = 0;
         var must_d0:MustDefine = null;
         if(Boolean(this.nowGrip))
         {
            d0 = this.nowGrip.itemsData as HeroSkillDefine;
            pd0 = Gaming.PG.DATA;
            mustLv0 = pd0.getStudyMustLv();
            must_d0 = d0.getThisStudyMust(mustLv0);
            PlayerMustCtrl.deductMust(must_d0,this.affter_click);
         }
      }
      
      private function affter_click() : void
      {
         var d0:HeroSkillDefine = this.nowGrip.itemsData as HeroSkillDefine;
         var pd0:NormalPlayerData = Gaming.PG.DATA;
         var mustLv0:int = pd0.getStudyMustLv();
         pd0.skillBag.addSkillByLabel(d0.baseLabel,mustLv0);
         Gaming.soundGroup.playSound("uiSound","success");
         var tipText0:String = "获得新技能：" + ComMethod.color(d0.cnName,"#FF6600") + "。";
         Gaming.uiGroup.alertBox.showCheck(tipText0,"yes",0.5,null,null,d0.iconUrl,"equip");
         this.fleshData();
         this.gripBox.setChoose_byIndex(-1);
         this.showMustByGrip(null);
         Gaming.uiGroup.skillUI.wearBox.fleshData();
         Gaming.uiGroup.mainUI.fleshCoin();
      }
   }
}

