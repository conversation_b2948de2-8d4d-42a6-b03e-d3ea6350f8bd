package UI.base.scroll
{
   import UI.base.button.NormalBtn;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.geom.Rectangle;
   
   public class SliderBar extends Sprite
   {
      public var label:String = "";
      
      private var changeFun:Function;
      
      private var bar:NormalScrollBar;
      
      private var img:Sprite = null;
      
      private var scrollBtn:NormalBtn = new NormalBtn();
      
      private var scrollBar:MovieClip;
      
      private var scrollLine:Sprite;
      
      private var maskSprite:Sprite = new Sprite();
      
      public function SliderBar()
      {
         super();
         this.maskSprite.graphics.beginFill(0,0);
         this.maskSprite.graphics.drawRect(0,0,2,2);
         this.maskSprite.graphics.endFill();
      }
      
      public function setImg(img0:Sprite) : void
      {
         this.img = img0;
         this.scrollBar = this.img["scrollBar"];
         this.scrollLine = this.img["scrollLine"];
         addChild(this.scrollLine);
         addChild(this.scrollBtn);
         this.scrollBtn.setImg(this.scrollBar);
         this.bar = new NormalScrollBar(this.maskSprite,new Rectangle(0,0,1,1),this.scrollBtn,this.scrollLine);
         this.bar.direction = "H";
         this.bar.stepNumber = 5;
         this.bar.lineAbleClick = true;
         this.bar.refresh();
         addChildAt(this.img,0);
         this.x = this.img.x;
         this.img.x = 0;
         this.y = this.img.y;
         this.img.y = 0;
         this.bar.changeFun = this.doChangeFun;
      }
      
      public function setToNormalImg() : void
      {
      }
      
      public function getPer() : Number
      {
         return this.bar.getPer();
      }
      
      public function setPer(v0:Number) : void
      {
         this.bar.setPer(v0);
      }
      
      public function setChangeFun(fun0:Function) : void
      {
         this.changeFun = fun0;
      }
      
      private function doChangeFun(v0:Number) : void
      {
         if(this.changeFun is Function)
         {
            this.changeFun(v0,this.label);
         }
      }
   }
}

