package w_test.cheating
{
   import UI.UIOrder;
   import com.adobe.serialization.json.JSON2;
   import com.sounto.cf.ObjectToXml;
   import dataAll._player.supple.PlayerDataSupple;
   import flash.system.System;
   import gameAll.level.data.LevelData;
   
   public class SaveCheating extends OneCheating
   {
      public function SaveCheating()
      {
         super();
      }
      
      public function initPlayerSave(str0:String, v0:int) : String
      {
         Gaming.PG.initSave();
         Gaming.uiGroup.mainUI.show();
         return "初始化存档";
      }
      
      public function autoSaveTime(str0:String, v0:int) : String
      {
         LevelData.autoSaveTime = v0;
         return "自动存档时间间隔：" + LevelData.autoSaveTime + "秒";
      }
      
      public function getSaveData(str0:String, v0:int) : String
      {
         System.setClipboard(JSON2.encode(Gaming.PG.save.getCopyObj()));
         return "已复制存档数据到剪贴板";
      }
      
      public function getSaveNoThin(str0:String, v0:int) : String
      {
         System.setClipboard(JSON2.encode(Gaming.PG.save.getCopyObjNoThin()));
         return "已复制未缩减存档数据到剪贴板";
      }
      
      public function getSaveThin(str0:String, v0:int) : String
      {
         System.setClipboard(JSON2.encode(Gaming.PG.save.getCopyObjThin()));
         return "已复制缩减存档数据到剪贴板";
      }
      
      public function get4399Save(str0:String, v0:int) : String
      {
         System.setClipboard(ObjectToXml.decode4399Two(Gaming.PG.save.getCopyObj()));
         return "已复制4399存档数据到剪贴板";
      }
      
      public function get4399SaveNoThin(str0:String, v0:int) : String
      {
         System.setClipboard(ObjectToXml.decode4399Two(Gaming.PG.save.getCopyObjNoThin()));
         return "已复制未缩减4399存档数据到剪贴板";
      }
      
      public function get4399SaveThin(str0:String, v0:int) : String
      {
         System.setClipboard(ObjectToXml.decode4399Two(Gaming.PG.save.getCopyObjThin()));
         return "已复制缩减4399存档数据到剪贴板";
      }
      
      public function savePlayerSave(str0:String, v0:int) : String
      {
         UIOrder.saveCanStop();
         return "";
      }
      
      public function supplePlayerSave(str0:String, v0:int) : String
      {
         PlayerDataSupple.supple(Gaming.PG.da);
         return "载入存档的补充处理。";
      }
      
      public function setSuppleSummer(str0:String, v0:int) : String
      {
         return "";
      }
      
      public function suppleSummer(str0:String, v0:int) : String
      {
         PlayerDataSupple.dealSummer(Gaming.PG.da);
         return "修复成功";
      }
      
      public function recoverSummer(str0:String, v0:int) : String
      {
         return "返回：" + str0;
      }
      
      public function seeSave(str0:String, v0:int) : String
      {
         Gaming.PG.test();
         return "";
      }
      
      public function logout(str0:String, v0:int) : String
      {
         Gaming.uiGroup.loginUI.outLoginEvent(false);
         return "";
      }
   }
}

