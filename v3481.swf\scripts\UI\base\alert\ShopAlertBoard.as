package UI.base.alert
{
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._app.goods.define.PriceType;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class ShopAlertBoard extends NormalUI
   {
      public var txt:TextField;
      
      public var otherTxt:TextField;
      
      public var valueTxt:TextField;
      
      public var valueBackSp:Sprite;
      
      public var prevSp:MovieClip;
      
      public var nextSp:MovieClip;
      
      public var prev_btn:NormalBtn = new NormalBtn();
      
      public var next_btn:NormalBtn = new NormalBtn();
      
      public var box:AlertBox = null;
      
      public var nowData:GoodsData = null;
      
      public function ShopAlertBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["txt","otherTxt","valueTxt","nextSp","prevSp","valueBackSp"];
         super.setImg(img0);
         FontDeal.dealOne(this.txt);
         FontDeal.dealOne(this.valueTxt);
         FontDeal.dealOne(this.otherTxt);
         addChild(this.prev_btn);
         this.prev_btn.setImg(this.prevSp);
         this.prev_btn.setName("<",false);
         this.prev_btn.addEventListener(MouseEvent.CLICK,this.prevClick);
         addChild(this.next_btn);
         this.next_btn.setImg(this.nextSp);
         this.next_btn.setName(">",false);
         this.next_btn.addEventListener(MouseEvent.CLICK,this.nextClick);
         this.valueTxt.addEventListener(Event.CHANGE,this.valueTextChange);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function showCheck(da0:GoodsData, yesFun0:Function = null, noFun0:Function = null, setNum0:int = 0) : void
      {
         var d0:GoodsDefine = null;
         d0 = da0.def;
         this.nowData = da0.copy();
         this.valueTxt.text = "1";
         this.box.showCheck("","yesAndNo",0,yesFun0,noFun0);
         show();
         this.txt.htmlText = this.nowData.getAlertInfoText();
         this.prev_btn.visible = d0.chooseNumB;
         this.next_btn.visible = d0.chooseNumB;
         this.valueTxt.visible = d0.chooseNumB;
         this.valueBackSp.visible = d0.chooseNumB;
         if(setNum0 > 0 && da0.def.chooseNumB)
         {
            this.valueTxt.text = setNum0 + "";
         }
         this.fleshPriceAndNum();
      }
      
      private function fleshPriceAndNum(fromTextB0:Boolean = true) : void
      {
         var text0:String = null;
         var num0:int = 0;
         if(fromTextB0)
         {
            text0 = this.valueTxt.text;
            num0 = int(text0);
            num0 = this.nowData.dealNuyNum(num0);
            this.nowData.nowNum = num0;
            if(text0 != "")
            {
               this.valueTxt.text = num0 + "";
            }
         }
         else
         {
            num0 = int(this.nowData.nowNum);
            num0 = this.nowData.dealNuyNum(num0);
            this.nowData.nowNum = num0;
            this.valueTxt.text = num0 + "";
         }
         var d0:GoodsDefine = this.nowData.def;
         var price0:Number = this.nowData.getAlertPrice();
         var numDisStr0:String = this.nowData.getNumDiscountStr();
         var mustBagSpace0:int = d0.noOverlayB ? num0 : 1;
         var surplusSpace0:int = Gaming.PG.da.getBagSpaceByType(d0.inBagType) - mustBagSpace0;
         var enoughB0:Boolean = Gaming.PG.da.compareCrrency(price0,d0.priceType);
         var str0:String = "";
         str0 += this.nowData.getAlertOtherText(num0,surplusSpace0,this.nowData.getMaxNumLimit(),this.nowData.getAllMaxNumLimit());
         if(d0.chooseNumB)
         {
            str0 += "\n";
         }
         str0 += "\n" + ComMethod.color("适度娱乐 理性消费","#00FF00") + "    价格：" + ComMethod.color(price0 + "","#FFFF00") + " " + PriceType.getCnName(d0.priceType);
         if(!enoughB0)
         {
            str0 += ComMethod.color("（不足）","#FF0000");
         }
         if(numDisStr0 != "")
         {
            str0 += "    " + numDisStr0;
         }
         this.otherTxt.htmlText = FontDeal.getDealLeadingStr(this.otherTxt,str0);
         this.box.yesBtn.actived = !(num0 <= 0 || !enoughB0 || surplusSpace0 < 0);
      }
      
      private function valueTextChange(e:Event) : void
      {
         this.fleshPriceAndNum();
      }
      
      private function prevClick(e:MouseEvent) : *
      {
         --this.nowData.nowNum;
         this.fleshPriceAndNum(false);
      }
      
      private function nextClick(e:MouseEvent) : *
      {
         ++this.nowData.nowNum;
         this.fleshPriceAndNum(false);
      }
   }
}

