package UI.vehicle
{
   import UI.bag.ItemsGrid;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.must.NormalMustBox;
   import dataAll.equip.vehicle.VehicleData;
   import dataAll.equip.vehicle.VehicleDataCreator;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class VehicleUpgradeBoard extends NormalUI
   {
      public var vehicleUI:VehicleUI;
      
      private var coverSp:Sprite;
      
      private var mustTag:Sprite;
      
      private var btnSp:MovieClip;
      
      private var beforeSp:Sprite;
      
      private var afterSp:Sprite;
      
      private var beforeBox:VehicleUpgradeOneBox = new VehicleUpgradeOneBox();
      
      private var afterBox:VehicleUpgradeOneBox = new VehicleUpgradeOneBox();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var btn:NormalBtn = new NormalBtn();
      
      public var nowData:VehicleData;
      
      public function VehicleUpgradeBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["coverSp","mustTag","btnSp","beforeSp","afterSp"];
         super.setImg(img0);
         addChild(this.beforeBox);
         this.beforeBox.setImg(this.beforeSp);
         addChild(this.afterBox);
         this.afterBox.setImg(this.afterSp);
         addChild(this.mustBox);
         this.mustBox.setLongImg();
         this.mustBox.x = this.mustTag.x;
         this.mustBox.y = this.mustTag.y;
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.btn.setName("升级");
         this.btn.actived = false;
         this.setCoverText("1");
         addChild(this.coverSp);
         FontDeal.dealOne(this.coverSp["txt"]);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      public function outLoginEvent() : void
      {
         this.nowData = null;
      }
      
      protected function setCoverText(str0:String) : void
      {
         if(str0 == "")
         {
            this.coverSp.visible = false;
         }
         else
         {
            this.coverSp.visible = true;
         }
      }
      
      private function fleshData() : void
      {
         this.showData(this.nowData);
      }
      
      public function gripClick(e:ClickEvent) : void
      {
         if(visible)
         {
            this.showData(e.childData as VehicleData);
         }
      }
      
      private function showData(da0:VehicleData) : void
      {
         var afterDa0:VehicleData = null;
         var must_d0:MustDefine = null;
         var bb0:Boolean = false;
         this.nowData = da0;
         var grip0:ItemsGrid = this.vehicleUI.itemsBox.findGripByData(this.nowData);
         if(grip0 is ItemsGrid)
         {
            this.setCoverText("");
            this.vehicleUI.itemsBox.setChoose_byIndex(grip0.index);
            afterDa0 = null;
            if(da0.canUpgradeVehicleB())
            {
               afterDa0 = da0.clone() as VehicleData;
               afterDa0.upgradeVehicle();
            }
            this.beforeBox.inData(da0,"升级前：");
            this.afterBox.inData(afterDa0,"升级后：");
            must_d0 = VehicleDataCreator.getUpgradeMust(da0);
            bb0 = this.mustBox.inData(must_d0,da0.normalPlayerData.level);
            this.btn.actived = bb0 && Boolean(afterDa0);
         }
         else
         {
            this.setCoverText("1");
            this.vehicleUI.itemsBox.setChoose_byIndex(-1);
         }
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var must_d0:MustDefine = VehicleDataCreator.getUpgradeMust(this.nowData);
         PlayerMustCtrl.deductMust(must_d0,this.affterBtnClick);
      }
      
      private function affterBtnClick() : void
      {
         var da0:VehicleData = this.nowData;
         da0.upgradeVehicle();
         Gaming.uiGroup.alertBox.showSuccess("升级成功！");
         this.vehicleUI.fleshBag();
         this.fleshData();
      }
   }
}

