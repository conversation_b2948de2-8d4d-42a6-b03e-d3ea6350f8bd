package UI.gift.activity
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.StringDate;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.ExchangeGiftAddDefineGroup;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   import flash.net.navigateToURL;
   import flash.text.TextField;
   
   public class Spring2017Board extends NormalUI
   {
      private var btnSp:MovieClip;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var giftTag:Sprite;
      
      private var linkTxt:TextField;
      
      private var loader:URLLoader = new URLLoader();
      
      private var url:URLRequest = new URLRequest("https://huodong2.4399.com/2016/newyear2017/api.php");
      
      private var gift_id:String = "spring2017";
      
      private var giftBox:ItemsGripBox = new ItemsGripBox();
      
      private var nowGiftType:int = 1;
      
      public function Spring2017Board()
      {
         super();
         this.loader.addEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this.loader.addEventListener(Event.COMPLETE,this.loaderCompleteHandler);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["btnSp","giftTag","linkTxt"];
         super.setImg(img0);
         this.linkTxt.styleSheet = ComMethod.getLinkCss("#FF9900","#FFFFFF");
         this.linkTxt.addEventListener(TextEvent.LINK,this.linkClick);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("获取礼包");
         this.btn.addEventListener(MouseEvent.CLICK,this.click);
         addChild(this.giftBox);
         NormalUICtrl.setTag(this.giftBox,this.giftTag);
         this.giftBox.imgType = "equipGrip";
         this.giftBox.evt.setWantEvent(true,false,false,true,true);
         this.giftBox.arg.init(6,2,6,6);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.giftBox);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         var gift0:ExchangeGiftAddDefineGroup = null;
         super.show();
         if(this.giftBox.gripArr.length == 0)
         {
            gift0 = Gaming.defineGroup.gift.getOne(this.gift_id) as ExchangeGiftAddDefineGroup;
            this.giftBox.inData_byArr(gift0.arr,"inData_gift");
         }
         this.pageLoad();
      }
      
      private function pageLoad() : void
      {
         Gaming.uiGroup.connectUI.show();
         var data0:URLVariables = new URLVariables();
         data0.type = 1;
         data0.uid = Gaming.PG.getUid();
         this.url.data = data0;
         this.url.method = URLRequestMethod.POST;
         this.loader.load(this.url);
      }
      
      private function loaderCompleteHandler(e:Event) : void
      {
         var tipStr0:String = null;
         Gaming.uiGroup.connectUI.hide();
         var v0:Number = Number(this.loader.data);
         if(v0 == 10001)
         {
            this.pageFlesh(false);
         }
         else if(v0 == 20000)
         {
            this.pageFlesh(true);
         }
         else
         {
            tipStr0 = "未知错误";
            if(v0 == 10000)
            {
               tipStr0 = "参数填写不完整或者不正确";
            }
            this.errorHandler(null,tipStr0);
         }
      }
      
      private function pageFlesh(canB0:Boolean) : void
      {
         var haveGetB0:Boolean = false;
         var gift0:ExchangeGiftAddDefineGroup = Gaming.defineGroup.gift.getOne(this.gift_id) as ExchangeGiftAddDefineGroup;
         var now0:StringDate = Gaming.api.save.getNowServerDate();
         var thisD0:StringDate = new StringDate();
         thisD0.inData_byStr(gift0.startTime);
         var canGetCodeB:Boolean = gift0.startTime == "" ? true : thisD0.compareDateValue(now0) >= 0;
         this.btn.setName("领取礼包");
         if(canGetCodeB)
         {
            this.linkTxt.htmlText = "点击进入活动地址：" + ComMethod.link(gift0.codeUrl,"url");
            haveGetB0 = true;
            this.btn.actived = canB0 && !haveGetB0;
            if(haveGetB0)
            {
               this.btn.setName("已领取");
            }
            else if(!canB0)
            {
               this.btn.setName("请参加活动");
            }
         }
         else
         {
            this.linkTxt.htmlText = "活动开始时间：" + gift0.startTime + ComMethod.color("（日期未到）","#FF0000");
            this.btn.actived = false;
         }
      }
      
      private function errorHandler(e:* = null, str0:String = "") : void
      {
         if(str0 == "")
         {
            str0 = "网络连接错误！";
         }
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError(str0);
         this.pageFlesh(false);
      }
      
      private function linkClick(e:TextEvent) : void
      {
         var gift0:ExchangeGiftAddDefineGroup = null;
         if(e.text == "url")
         {
            gift0 = Gaming.defineGroup.gift.getOne(this.gift_id) as ExchangeGiftAddDefineGroup;
            navigateToURL(new URLRequest(gift0.codeUrl),"blank");
         }
      }
      
      private function click(e:MouseEvent) : void
      {
         var gift0:GiftAddDefineGroup = Gaming.defineGroup.gift.getOne(this.gift_id);
         var bb0:Boolean = GiftAddit.addAndAutoBagSpacePan(gift0);
         if(bb0)
         {
            this.pageFlesh(true);
         }
      }
   }
}

