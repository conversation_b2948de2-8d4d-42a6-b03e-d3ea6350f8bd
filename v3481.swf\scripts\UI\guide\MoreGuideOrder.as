package UI.guide
{
   import UI.base.button.NormalBtn;
   
   public class MoreGuideOrder extends NormalGuideOrder
   {
      public function MoreGuideOrder()
      {
         super();
         arr = [this.moreBtn,this.mainBtn];
      }
      
      private function moreBtn() : OneGuideData
      {
         var btn0:NormalBtn = Gaming.uiGroup.moreBox.box.gripArr[1];
         if(!btn0.actived)
         {
            btn0 = null;
         }
         return new OneGuideData(btn0,"你有个新队友\n点击切换到队友");
      }
      
      private function mainBtn() : OneGuideData
      {
         var btn0:NormalBtn = Gaming.uiGroup.mainUI.getBtn("wear");
         if(Gaming.uiGroup.wearUI.visible)
         {
            btn0 = null;
         }
         return new OneGuideData(btn0,"点击进入队友背包\n可为他更换武器装备");
      }
   }
}

