package UI.helper.book
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGripBox;
   import UI.base.NormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.label.MoreLabelBox;
   import UI.base.scroll.NormalScrollBar;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.book.BookFather;
   import dataAll._app.book.IO_BookDefine;
   import dataAll._player.PlayerData;
   import dataAll.ui.label.LabelAddData;
   import flash.display.Sprite;
   
   public class HelperBookBoard extends NormalUI
   {
      public var contentBox:HelperBookBox = null;
      
      private var nowType:String = "";
      
      private var nowChildType:String = "";
      
      private var leftTag:Sprite;
      
      private var boxTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var leftBox:MoreLabelBox = new MoreLabelBox();
      
      private var iconBoxObj:Object = {};
      
      private var nowIconBox:ItemsGripBox = null;
      
      private var scrollBar:NormalScrollBar;
      
      public function HelperBookBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["leftTag","boxTag","pageTag"];
         super.setImg(img0);
         img0.addChild(this.leftBox);
         NormalUICtrl.setTag(this.leftBox,this.leftTag);
         this.addLeftLabel();
         this.scrollBar = new NormalScrollBar(this.leftBox,img0["maskTargetSp"],img0["scrollBarSp"],img0["scrollLineSp"],1,false,true,true);
         this.scrollBar.speed = 30;
         this.scrollBar.refresh();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function addLeftLabel() : void
      {
         var da0:LabelAddData = BookFather.getLabelAddData();
         this.leftBox.inDataByLabelAddData(da0);
         this.leftBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.PG.da.fleshAllNameObj();
         this.showLabel(this.nowType,this.nowChildType);
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         var strArr0:Array = e.fatherUrl.split("/");
         var type0:String = strArr0[strArr0.length - 2];
         var childType0:String = e.label;
         if(e.father != e.target)
         {
            this.showLabel(type0,childType0);
         }
         this.scrollBar.refresh();
      }
      
      private function showLabel(type0:String, childType0:String) : void
      {
         if(this.nowType == "" || this.nowChildType == "")
         {
            type0 = "arms";
            childType0 = "darkgoldArms";
            this.leftBox.showChildVisibleByName(type0,childType0);
         }
         this.nowType = type0;
         this.nowChildType = childType0;
         this.showIconBox(type0,childType0);
      }
      
      private function getIconBox(name0:String) : ItemsGripBox
      {
         return this.iconBoxObj[name0];
      }
      
      private function showIconBox(type0:String, childType0:String) : void
      {
         var dataArr0:Array = null;
         if(Boolean(this.nowIconBox))
         {
            this.nowIconBox.visible = false;
         }
         var box0:ItemsGripBox = this.getIconBox(childType0);
         if(!box0)
         {
            box0 = new ItemsGripBox();
            if(type0 == "arms")
            {
               box0.imgType = "HelperUI/armsIcon";
               box0.arg.init(4,4,28,20);
            }
            addChild(box0);
            NormalUICtrl.setTag(box0,this.boxTag);
            box0.pageBox.setToSmall();
            box0.setPagePos(this.pageTag);
            box0.evt.setWant(true);
            box0.addEventListener(ClickEvent.ON_CLICK,this.boxClick);
            dataArr0 = Gaming.defineGroup.book.getArrByMoreLabel(type0,childType0);
            box0.inData_byArr(dataArr0,"inData_bookDefine");
            this.leftBox.getSunBtn(this.nowType,this.nowChildType).setNumText(this.getCountStr(dataArr0));
         }
         box0.visible = true;
         this.nowIconBox = box0;
      }
      
      private function getCountStr(dataArr0:Array) : String
      {
         var d0:IO_BookDefine = null;
         var pg0:PlayerData = Gaming.PG.da;
         var max0:int = int(dataArr0.length);
         var now0:int = 0;
         for each(d0 in dataArr0)
         {
            if(pg0.getNameNum(d0.getName()) > 0)
            {
               now0++;
            }
         }
         return ComMethod.dropColor(now0,max0);
      }
      
      private function boxClick(e:ClickEvent) : void
      {
         this.contentBox.showDefine(e.childData as IO_BookDefine);
      }
   }
}

