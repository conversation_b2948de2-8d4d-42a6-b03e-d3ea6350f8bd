package UI.ask
{
   import UI.UIOrder;
   import UI.bag.ItemsGripBox;
   import UI.base.AppBtnBox;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.ask.AskData;
   import dataAll._app.ask.AskPropsData;
   import dataAll._app.ask.AskTemp;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.MovieClip;
   import flash.display.Shape;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.events.TimerEvent;
   import flash.text.TextField;
   import flash.utils.Timer;
   
   public class AskUI extends AppBtnBox
   {
      private var tipBtn:SimpleButton;
      
      private var topBarTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var numTxt:TextField;
      
      private var titleTxt:TextField;
      
      private var answerTag:Sprite;
      
      private var timeTxt:TextField;
      
      private var propsTag:Sprite;
      
      private var scoreTxt:TextField;
      
      private var topTxt:TextField;
      
      private var todayScoreTxt:TextField;
      
      private var infoTxt:TextField;
      
      private var endSp:Sprite;
      
      private var endBoard:AskEndBoard = new AskEndBoard();
      
      private var startSp:Sprite;
      
      private var startBtnSp:MovieClip;
      
      private var startBtn:NormalBtn = new NormalBtn();
      
      private var answerBox:ItemsGripBox = new ItemsGripBox();
      
      private var propsBox:ItemsGripBox = new ItemsGripBox();
      
      private var timer:Timer = new Timer(1000);
      
      private var closeBtn:SimpleButton;
      
      private var shape:Shape = new Shape();
      
      private var testB:Boolean = false;
      
      public function AskUI()
      {
         super();
         UICn = "问答";
         this.shape.graphics.beginFill(0,0.9);
         this.shape.graphics.drawRect(-416.5,-256.5,1666,1026);
         this.shape.visible = false;
         addChild(this.shape);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["tipBtn","closeBtn","endSp","timeTxt","topTxt","topBarTag","pageTag","numTxt","titleTxt","answerTag","titleTxt","propsTag","scoreTxt","todayScoreTxt","infoTxt","startSp","startBtnSp"];
         super.setImg(img0);
         addChild(this.answerBox);
         this.answerBox.x = this.answerTag.x;
         this.answerBox.y = this.answerTag.y;
         this.answerBox.setIconPro("AskUI/chooseBtn");
         this.answerBox.arg.init(1,4,0,10);
         this.answerBox.addEventListener(ClickEvent.ON_CLICK,this.answerClick);
         addChild(this.propsBox);
         this.propsTag.addChild(this.propsBox);
         this.propsBox.setIconPro("AskUI/propsBtn");
         this.propsBox.evt.setWantEvent(true,false,false,true,true);
         this.propsBox.arg.init(2,1,6,0);
         this.propsBox.addEventListener(ClickEvent.ON_CLICK,this.propsClick);
         this.propsBox.addEventListener(ClickEvent.ON_OVER,this.propsOver);
         this.propsBox.addEventListener(ClickEvent.ON_OUT,this.propsOut);
         this.startBtnSp = this.startSp["startBtnSp"];
         this.startSp.addChild(this.startBtn);
         this.startBtn.setImg(this.startBtnSp);
         this.startBtn.addEventListener(MouseEvent.CLICK,this.startClick);
         this.startBtn.setName("开始答题");
         addChild(this.startSp);
         addChild(this.endBoard);
         this.endBoard.setImg(this.endSp);
         this.endBoard.giftBtn.addEventListener(MouseEvent.CLICK,this.getGiftClick);
         this.timer.addEventListener(TimerEvent.TIMER,this.timerEvent);
         this.timer.start();
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         addChildAt(this.shape,0);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.showTop();
         this.showByState();
      }
      
      private function showTop() : void
      {
         this.affter_uploadScore();
      }
      
      private function affter_uploadScore() : void
      {
         this.topTxt.text = "无";
      }
      
      public function outLoginEvent() : void
      {
      }
      
      private function showByState() : void
      {
         var state0:String = null;
         var da0:AskData = Gaming.PG.da.ask;
         var temp0:AskTemp = da0.nowAskTemp;
         state0 = da0.getNowState();
         this.startSp.visible = false;
         this.endBoard.visible = false;
         this.shape.visible = false;
         if(state0 == "start")
         {
            this.startSp.visible = true;
         }
         else if(state0 == "end")
         {
            this.endBoard.visible = true;
            this.showEnd();
         }
         else if(state0 == "chosen")
         {
            this.shape.visible = true;
         }
         else if(state0 == "ing")
         {
            this.showAsk(da0);
            this.showProps();
            this.shape.visible = true;
         }
         this.closeBtn.visible = !this.shape.visible;
         this.showBtn(state0);
         this.showInfo(da0);
      }
      
      private function showEnd() : void
      {
         var da0:AskData = Gaming.PG.da.ask;
         this.endBoard.showInfo(da0.getEndString(),da0.getGift(),da0.getHaveGetGiftB());
         if(da0.isAllRightB())
         {
            Gaming.PG.save.headCount.askAllRightNum.add(Gaming.api.save.getNowServerDate().getStr());
         }
      }
      
      private function showAsk(da0:AskData) : void
      {
         var str0:* = null;
         var btn0:NormalBtn = null;
         var temp0:AskTemp = da0.nowAskTemp;
         this.numTxt.text = da0.getNumString();
         this.fleshTime(false);
         this.titleTxt.htmlText = temp0.def.getTitleAndAuthor();
         this.answerBox.nowLabel = "";
         this.answerBox.inData_byArr(temp0.answerArr,"inData_string");
         this.answerBox.setAllPro("mouseIconEffectB",true);
         for each(str0 in temp0.answerArr)
         {
            btn0 = this.answerBox.getBtnByLabel(str0);
            if(temp0.noArr.indexOf(str0) >= 0)
            {
               btn0.visible = false;
            }
            else
            {
               btn0.visible = true;
            }
         }
      }
      
      private function showBtn(str0:String) : void
      {
         var da0:AskData = Gaming.PG.da.ask;
         var yesBtn0:NormalBtn = getBtn("yes");
         var nextBtn0:NormalBtn = getBtn("next");
         yesBtn0.visible = false;
         nextBtn0.visible = false;
         this.timeTxt.visible = false;
         this.propsBox.visible = false;
         if(str0 == "ing")
         {
            this.timeTxt.visible = true;
            yesBtn0.visible = true;
            yesBtn0.actived = this.answerBox.nowLabel != "";
            this.answerBox.mouseChildren = true;
            this.answerBox.mouseEnabled = true;
            this.propsBox.visible = true;
         }
         else if(str0 == "chosen")
         {
            nextBtn0.visible = this.testB;
            nextBtn0.setName(da0.haveNextAskB() ? "下一题" : "答题完毕");
            this.answerBox.mouseChildren = false;
            this.answerBox.mouseEnabled = false;
         }
      }
      
      private function showInfo(da0:AskData) : void
      {
         this.scoreTxt.text = da0.getScore() + "";
         this.todayScoreTxt.text = da0.getTodayScore() + "";
         var str0:String = "";
         str0 += "正确答题：" + da0.getCorrectNumString();
         this.infoTxt.htmlText = str0;
      }
      
      private function answerClick(e:ClickEvent) : void
      {
         this.answerBox.setChoose(e.label);
         this.showBtn("ing");
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var da0:AskData = Gaming.PG.da.ask;
         var temp0:AskTemp = da0.nowAskTemp;
         var btn0:NormalBtn = e.target as NormalBtn;
         var l0:String = btn0.label;
         if(l0 == "yes")
         {
            this.chooseAnswer(this.answerBox.nowLabel);
         }
         else if(l0 == "next")
         {
            this.nextAsk();
         }
      }
      
      private function chooseAnswer(label0:String) : void
      {
         var da0:AskData = Gaming.PG.da.ask;
         var temp0:AskTemp = da0.nowAskTemp;
         var score0:int = da0.chooseAnswer(label0);
         if(score0 == 0)
         {
            if(this.testB)
            {
               this.answerBox.getBtnByLabel(temp0.correct).setName(temp0.correct + ComMethod.color("（正确答案）","#00FF00"));
            }
            Gaming.uiGroup.alertBox.showError("回答错误！\n正确答案：" + ComMethod.green(temp0.correct),this.testB ? null : this.nextAsk);
         }
         else
         {
            Gaming.uiGroup.alertBox.showSuccess("回答正确！",this.testB ? null : this.nextAsk);
         }
         this.showByState();
      }
      
      private function nextAsk() : void
      {
         var da0:AskData = Gaming.PG.da.ask;
         var endB0:Boolean = da0.nextAsk();
         this.showByState();
         if(endB0)
         {
            this.showTop();
         }
      }
      
      private function startClick(e:MouseEvent) : void
      {
         var da0:AskData = Gaming.PG.da.ask;
         da0.nextAsk();
         this.showByState();
         Gaming.soundGroup.playSound("uiSound","getTask");
      }
      
      private function timerEvent(e:TimerEvent) : void
      {
         var da0:AskData = null;
         var bb0:Boolean = false;
         var label0:String = null;
         if(Boolean(Gaming.PG.da))
         {
            da0 = Gaming.PG.da.ask;
            if(da0.state == "ing")
            {
               this.fleshTime(true);
               bb0 = da0.timer();
               if(bb0)
               {
                  label0 = this.answerBox.nowLabel;
                  this.chooseAnswer(label0);
               }
            }
         }
      }
      
      private function fleshTime(soundB0:Boolean) : void
      {
         var da0:AskData = Gaming.PG.da.ask;
         var nowTime0:int = da0.nowTime;
         this.timeTxt.htmlText = "答题剩余时间：" + ComMethod.color("<b><font size=\'14\'>" + nowTime0 + "</font></b>",nowTime0 <= 5 ? "#FF9900" : "#00FF00");
         if(nowTime0 <= 5)
         {
            if(soundB0)
            {
               Gaming.soundGroup.playSound("uiSound","winTime");
            }
         }
      }
      
      private function showProps() : void
      {
         var btn0:NormalBtn = null;
         var propsDa0:AskPropsData = null;
         var label0:String = null;
         var da0:AskData = Gaming.PG.da.ask;
         var temp0:AskTemp = da0.nowAskTemp;
         var arr0:Array = da0.propsArr;
         this.propsBox.inData_byArr(arr0,"inData_askProps");
         for each(btn0 in this.propsBox.gripArr)
         {
            propsDa0 = btn0.itemsData as AskPropsData;
            label0 = propsDa0.def.name;
            if(label0 == "clearError")
            {
               if(temp0.getCanChooseArr().length < 2)
               {
                  btn0.actived = false;
               }
            }
            else if(label0 == "double")
            {
               if(da0.doubleScoreB)
               {
                  btn0.actived = false;
                  btn0.isChosen = true;
               }
               else
               {
                  btn0.isChosen = false;
                  btn0.actived = propsDa0.getCanUseNum() > 0;
               }
            }
         }
      }
      
      private function propsClick(e:ClickEvent) : void
      {
         var propsDa0:AskPropsData = e.childData as AskPropsData;
         var index0:int = e.index;
         var label0:String = propsDa0.def.name;
         var da0:AskData = Gaming.PG.da.ask;
         var temp0:AskTemp = da0.nowAskTemp;
         da0.useProps(index0);
         if(label0 == "clearError")
         {
            temp0.clearError();
            this.showByState();
         }
         else if(label0 == "double")
         {
            da0.doubleScoreB = true;
            this.showProps();
         }
         Gaming.soundGroup.playSound("uiSound","changeLabel");
      }
      
      private function propsOver(e:ClickEvent) : void
      {
         var da0:AskPropsData = e.childData as AskPropsData;
         Gaming.uiGroup.tipBox.textTip.showFollowText(da0.def.mouseTip);
      }
      
      private function propsOut(e:ClickEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function getGiftClick(e:MouseEvent) : void
      {
         var da0:AskData = Gaming.PG.da.ask;
         var gift0:GiftAddDefineGroup = da0.getGift();
         var successB0:Boolean = GiftAddit.addAndAutoBagSpacePan(gift0,"");
         if(successB0)
         {
            da0.setHaveGetGiftB(true);
            this.showEnd();
            UIOrder.saveCanStop();
         }
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         hide();
      }
      
      private function tipOver(e:MouseEvent) : void
      {
         var str0:String = "1、每次答题正确都将获得答题积分。";
         str0 += "\n2、每天玩家可以答题" + AskData.getDayAllNum() + "题，答题结束后，系统根据积分高低相应给予玩家奖励。";
         str0 += "\n3、答题中可以使用“照妖镜”、“双倍积分”等道具。VIP等级越高，“照妖镜”道具可使用次数相应越高。";
         str0 += "\n4、答题过程中不能操作其他系统界面。";
         str0 += "\n5、答题中若退出登录，则将失去今天的答题机会。";
         str0 += "\n6、积分排行榜无名次奖励，请玩家们注意。";
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      private function tipOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
   }
}

