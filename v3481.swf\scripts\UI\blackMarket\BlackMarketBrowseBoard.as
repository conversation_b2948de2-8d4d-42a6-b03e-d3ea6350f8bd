package UI.blackMarket
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.alert.AlertBox;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.label.LabelBox;
   import UI.shop.GoodsBox;
   import UI.shop.ShopBuyLimit;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._app.blackMarket.BlackMarketCtrl;
   import dataAll._app.blackMarket.BlackMarketData;
   import dataAll._app.goods.GoodsAddit;
   import dataAll._app.goods.GoodsData;
   import dataAll.arms.ArmsData;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsCompareData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class BlackMarketBrowseBoard extends NormalUI
   {
      public var blackMarketUI:BlackMarketUI;
      
      private var leftBox:LabelBox = new LabelBox();
      
      private var itemsBox:ItemsGripBox = new ItemsGripBox();
      
      private var fleshBtn:NormalBtn = new NormalBtn();
      
      private var otherTxt:TextField;
      
      private var thingsSp:Sprite;
      
      private var thingsBox:GoodsBox = new GoodsBox();
      
      private var leftTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var barTag:Sprite;
      
      private var colorTag:Sprite;
      
      private var iconSp:Sprite;
      
      private var fleshBtnSp:MovieClip;
      
      private var taxStampTxt:TextField;
      
      private var numTxt:TextField;
      
      private var tipBtn:SimpleButton;
      
      public var closeBtn:SimpleButton;
      
      public var nowType:String = "";
      
      private var nowClickData:IO_ItemsData = null;
      
      public function BlackMarketBrowseBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["thingsSp","tipBtn","iconSp","numTxt","taxStampTxt","fleshBtnSp","leftTag","barTag","pageTag","closeBtn","otherTxt"];
         super.setImg(img0);
         FontDeal.dealOne(this.numTxt);
         this.leftTag.addChild(this.leftBox);
         this.addLeftLabel();
         addChild(this.itemsBox);
         this.itemsBox.arg.init(1,6,0,0);
         this.itemsBox.setIconPro("BlackMarketUI/itemsBar",60,60);
         this.itemsBox.evt.setWantEvent(true,true,true,true,true,true);
         this.itemsBox.addEventListener(ClickEvent.ON_CLICK,this.barClick);
         NormalUICtrl.setTag(this.itemsBox,this.barTag);
         this.itemsBox.pageBox.setToNormalBtn();
         this.itemsBox.pageBox.setXY_bySp(this.pageTag,this.itemsBox);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.itemsBox);
         var box0:ItemsGripBox = this.thingsBox;
         box0.imgType = "BlackMarketUI/shopGrip";
         box0.arg.init(3,4,6,6);
         box0.evt.setWantEvent(true,false,false,true,true);
         box0.addEventListener(ClickEvent.ON_CLICK,this.thingsClick);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(box0);
         box0.pageBox.setToNormalBtn();
         box0.pageBox.setXY_bySp(this.thingsSp["pageTag"],this.thingsSp["barTag"]);
         this.thingsSp["barTag"].addChild(box0);
         addChild(this.thingsSp);
         addChild(this.fleshBtn);
         this.fleshBtn.setImg(this.fleshBtnSp);
         this.fleshBtn.activedAndEnabled = false;
         this.fleshBtn.addEventListener(MouseEvent.CLICK,this.fleshBtnClick);
         this.fleshBtn.addEventListener(MouseEvent.MOUSE_OVER,this.fleshBtnOver);
         this.fleshBtn.addEventListener(MouseEvent.MOUSE_OUT,this.fleshBtnOut);
         addChild(this.iconSp);
         this.iconSp.mouseChildren = false;
         this.iconSp.mouseEnabled = false;
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
      }
      
      private function addLeftLabel() : void
      {
         this.leftBox.arg.init(1,10,0,0);
         this.leftBox.inData("BlackMarketUI/bigLabel",["arms","equip","things"],["武器","装备","物品"]);
         this.leftBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function get DATA() : BlackMarketData
      {
         return Gaming.PG.da.blackMarket;
      }
      
      override public function show() : void
      {
         super.show();
         this.showNow();
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showLabel(e.label);
      }
      
      private function showNow() : void
      {
         this.showLabel(this.nowType);
      }
      
      private function showLabel(type0:String) : void
      {
         if(type0 == "")
         {
            type0 = "arms";
         }
         this.nowType = type0;
         this.leftBox.setChoose(type0);
         var fleshB0:Boolean = this.DATA.firstFleshWantSaveB();
         if(type0 == "things")
         {
            this.showThings();
         }
         else
         {
            this.showItemsData(type0);
         }
         this.taxStampTxt.text = Gaming.PG.da.getThingsNum("taxStamp") + "";
         this.numTxt.htmlText = "今日还可以购买" + TextMethod.redZeroOrGreen(this.DATA.getBuySurplus()) + "次商品";
         this.fleshFleshBtn();
         if(fleshB0)
         {
            UIOrder.save();
         }
      }
      
      private function showItemsData(type0:String) : void
      {
         this.setThingsVisible(false);
         var arr0:Array = this.DATA.getDataArr(type0);
         this.itemsBox.inData_byArr(arr0,"inData_blackMarket");
         this.itemsBox.setFatherData(this.DATA.getDataGroup(type0));
         this.otherTxt.htmlText = type0 == "arms" ? "战斗力" : "";
         this.itemsBox.pageBox.showPage(0);
      }
      
      private function showThings() : void
      {
         this.setThingsVisible(true);
         var nameArr0:Array = this.DATA.getGoodsNameArr();
         var dataArr0:Array = Gaming.PG.da.goods.getDataArrByNameArr(nameArr0);
         this.thingsBox.inData_byArr(dataArr0,"inData_goods");
         this.fleshPrice();
      }
      
      private function fleshPrice() : void
      {
         this.taxStampTxt.text = Gaming.PG.da.getThingsNum("taxStamp") + "";
         if(this.thingsSp.visible)
         {
            this.thingsBox.doChildFun("fleshGoodsPrice");
         }
      }
      
      private function setThingsVisible(bb0:Boolean) : void
      {
         this.thingsSp.visible = bb0;
      }
      
      private function numPan() : Boolean
      {
         var num0:int = this.DATA.getBuySurplus();
         if(num0 <= 0)
         {
            Gaming.uiGroup.alertBox.showError("你今日已经没有购买次数了。");
            return false;
         }
         return true;
      }
      
      private function barClick(e:ClickEvent) : void
      {
         var dg0:ItemsDataGroup = null;
         var da0:IO_ItemsData = null;
         var price0:int = 0;
         var have0:int = 0;
         var tip0:String = null;
         if(this.numPan())
         {
            dg0 = Gaming.PG.da[this.leftBox.nowLabel + "Bag"];
            if(dg0.getSpaceSiteNum() <= 0)
            {
               Gaming.uiGroup.alertBox.showError("你的背包空位不足，无法购买此商品。");
            }
            else
            {
               da0 = e.childData as IO_ItemsData;
               this.nowClickData = da0;
               price0 = BlackMarketCtrl.getPrice(da0);
               have0 = Gaming.PG.da.getThingsNum("taxStamp");
               if(have0 < price0)
               {
                  Gaming.uiGroup.alertBox.showError("你的商券不足，无法购买此商品。");
               }
               else
               {
                  tip0 = "你是否要花费" + TextMethod.color(price0 + "商券","#00FF00") + "购买当前商品？";
                  Gaming.uiGroup.alertBox.showNormal(tip0,"yesAndNo",this.yesBar);
               }
            }
         }
      }
      
      private function yesBar() : void
      {
         var da0:IO_ItemsData = this.nowClickData;
         var price0:int = BlackMarketCtrl.getPrice(da0);
         Gaming.PG.da.thingsBag.useThings("taxStamp",price0,false,false);
         var type0:String = this.leftBox.nowLabel;
         var dg0:ItemsDataGroup = Gaming.PG.da[type0 + "Bag"];
         this.DATA.delData(type0,da0);
         dg0.addData(da0);
         this.DATA.buyEvent();
         if(da0 is ArmsData)
         {
            (da0 as ArmsData).fleshData_byEquip(Gaming.PG.DATA.getMerge(),true);
         }
         Gaming.soundGroup.playSound("uiSound","buy");
         this.showNow();
         ++Gaming.PG.save.getCount().blackMarketMaxNum;
      }
      
      public function getNowUICompareData(dd0:ItemsCompareData) : ItemsCompareData
      {
         return Gaming.PG.getUICompareData(dd0);
      }
      
      private function tipOver(e:MouseEvent) : void
      {
         var str0:String = "";
         str0 += "1、每天玩家第一次上线会自动刷新商品列表。";
         str0 += "\n2、已被交易的物品将从商品列表中消失。";
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      private function tipOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function thingsClick(e:ClickEvent) : void
      {
         var da0:GoodsData = null;
         var grip0:ItemsGrid = null;
         var alertBox0:AlertBox = null;
         var limit_str0:String = null;
         if(this.numPan())
         {
            da0 = e.childData as GoodsData;
            grip0 = e.child as ItemsGrid;
            alertBox0 = Gaming.uiGroup.alertBox;
            limit_str0 = ShopBuyLimit.getLimit(da0);
            if(da0.def.price < 10)
            {
               limit_str0 = "商券不足。";
            }
            if(limit_str0 != "")
            {
               alertBox0.showError(limit_str0);
            }
            else
            {
               alertBox0.shop.showCheck(da0,this.yes_buy);
            }
         }
      }
      
      private function yes_buy() : void
      {
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         var price0:Number = da0.getPrice();
         Gaming.PG.da.useCrrency(price0,da0.def.priceType);
         this.DATA.delGoods(da0.def.name);
         GoodsAddit.addByGoodsData(da0,true);
         this.DATA.buyEvent();
         Gaming.soundGroup.playSound("uiSound","buy");
         this.showNow();
         ++Gaming.PG.save.getCount().blackMarketMaxNum;
      }
      
      private function fleshFleshBtn() : void
      {
         this.fleshBtn.actived = this.DATA.getFleshSurplus() > 0;
      }
      
      private function fleshBtnClick(e:MouseEvent) : void
      {
         if(this.fleshBtn.actived == false)
         {
            return;
         }
         var must_d0:MustDefine = Gaming.defineGroup.must.fleshBlackMarket;
         PlayerMustCtrl.simpleBuy(must_d0,"刷新所有商品列表，需要：",this.yes_fleshBtn);
      }
      
      private function yes_fleshBtn() : void
      {
         Gaming.soundGroup.playSound("uiSound","getItems");
         this.DATA.clearData();
         this.DATA.fleshHand();
         this.showNow();
      }
      
      private function fleshBtnOver(e:MouseEvent) : void
      {
         var must_d0:MustDefine = Gaming.defineGroup.must.fleshBlackMarket;
         var str0:String = "需要：\n" + must_d0.getText();
         str0 += "\n\n" + ComMethod.color("今天还可以刷新" + ComMethod.colorEnoughNum(this.DATA.getFleshSurplus()) + "次","#00FF00");
         Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
      }
      
      private function fleshBtnOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
   }
}

