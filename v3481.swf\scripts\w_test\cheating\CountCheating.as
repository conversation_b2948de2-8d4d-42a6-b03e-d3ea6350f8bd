package w_test.cheating
{
   import UI.count.CountCtrl;
   import UI.test.SaveTestBox;
   import dataAll._player.count.props.testDiy.ArmsDpsTestDiy;
   
   public class CountCheating extends OneCheating
   {
      public function CountCheating()
      {
         super();
      }
      
      public function showLevelCount(str0:String, v0:int) : String
      {
         str0 = "\n" + Gaming.PG.save.worldMap.getAllCount();
         str0 += "\n\n" + Gaming.PG.da.worldMap.countNum();
         SaveTestBox.addText(str0);
         return "请在M框中查看";
      }
      
      public function showPayTest(str0:String, v0:int) : String
      {
         str0 = "\n-------------------------------\n";
         str0 += Gaming.PG.da.zuobiPaner.getTextByCrrency();
         str0 += "-------------------------------";
         SaveTestBox.addText(str0);
         return "请在M框中查看";
      }
      
      public function sendCount(str0:String, v0:int) : String
      {
         str0 = CountCtrl.sendCount4399();
         SaveTestBox.addText(str0);
         return "发送统计事件";
      }
      
      public function setArmsTestDiyData(str0:String, v0:int) : String
      {
         var sarr0:Array = str0.split(",");
         if(sarr0.length > 1)
         {
            ArmsDpsTestDiy.setData(Number(sarr0[0]),Number(sarr0[1]));
         }
         else
         {
            ArmsDpsTestDiy.setData(Number(sarr0[0]));
         }
         return "设置参数：" + str0;
      }
      
      public function startDemonMapTest(str0:String, v0:int) : String
      {
         var sarr0:Array = str0.split(",");
         if(sarr0.length > 1)
         {
            ArmsDpsTestDiy.startMapTest(Number(sarr0[0]),Number(sarr0[1]));
         }
         else
         {
            ArmsDpsTestDiy.startMapTest(Number(sarr0[0]));
         }
         return "";
      }
   }
}

