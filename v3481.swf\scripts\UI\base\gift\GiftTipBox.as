package UI.base.gift
{
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class GiftTipBox extends AutoNormalUI
   {
      private var tempPoint:Point = new Point();
      
      private var giftTag:Sprite;
      
      private var giftBox:ItemsGripBox = new ItemsGripBox();
      
      protected var backMc:MovieClip;
      
      public function GiftTipBox()
      {
         super();
         hide();
         mcTypeArr = ["tag"];
      }
      
      public function setToNormalImg() : void
      {
         this.setImg(Gaming.swfLoaderManager.getResourceFull("GiftUI/giftTipBox"));
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["backMc"];
         super.setImg(img0);
         this.giftBox.x = this.giftTag.x;
         this.giftBox.y = this.giftTag.y;
         addChild(this.giftBox);
         this.giftBox.arg.init(5,3,3,3);
         this.giftBox.setIconPro("GiftUI/itemsGrip");
         this.giftBox.evt.setWantEvent(true,false,false,true,true);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.giftBox);
         this.backMc.stop();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      public function showTip(g0:GiftAddDefineGroup, grip0:DisplayObject) : void
      {
         if(!img)
         {
            this.setToNormalImg();
         }
         this.giftBox.inData_byArr(g0.arr,"inData_gift");
         if(g0.arr.length > 10)
         {
            this.giftBox.y = this.giftTag.y - 55;
            this.backMc.gotoAndStop(2);
         }
         else
         {
            this.giftBox.y = this.giftTag.y;
            this.backMc.gotoAndStop(1);
         }
         var r0:Rectangle = grip0.getRect(grip0);
         var p0:Point = grip0.localToGlobal(this.tempPoint);
         var x0:Number = r0.x + r0.width / 2 + p0.x;
         var y0:Number = p0.y;
         if(y0 - height < 0)
         {
            y0 = height;
         }
         this.x = x0;
         this.y = y0;
         show();
      }
   }
}

