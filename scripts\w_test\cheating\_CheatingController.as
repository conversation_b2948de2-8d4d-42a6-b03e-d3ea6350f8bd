package w_test.cheating
{
   import dataAll.test.CheatingDefine;
   import flash.events.KeyboardEvent;
   
   public class _CheatingController
   {
      public var fun_arr:Array = [];
      
      private var cheatingStr:String = "";
      
      public var enabled:Boolean = false;
      
      public var clickTopGetSaveDataB:Boolean = false;
      
      public var endlessGrade:int = 0;
      
      public var closeZuobiPanB:Boolean = false;
      
      public var haveUnionDataB:Boolean = true;
      
      public var tempContribution:Number = 0;
      
      private var achieve:AchieveCheating = new AchieveCheating();
      
      public var ai:AICheating = new AICheating();
      
      private var arena:ArenaCheating = new ArenaCheating();
      
      private var arms:ArmsCheating = new ArmsCheating();
      
      private var bag:BagCheating = new BagCheating();
      
      private var count:CountCheating = new CountCheating();
      
      private var drop:DropCheating = new DropCheating();
      
      private var equip:EquipCheating = new EquipCheating();
      
      private var head:HeadCheating = new HeadCheating();
      
      private var level:LevelCheating = new LevelCheating();
      
      private var more:MoreCheating = new MoreCheating();
      
      private var other:OtherCheating = new OtherCheating();
      
      private var parts:PartsCheating = new PartsCheating();
      
      private var pay:PayCheating = new PayCheating();
      
      private var pet:PetCheating = new PetCheating();
      
      private var player:PlayerCheating = new PlayerCheating();
      
      private var save:SaveCheating = new SaveCheating();
      
      private var skill:SkillCheating = new SkillCheating();
      
      private var system:SystemCheating = new SystemCheating();
      
      private var task:TaskCheating = new TaskCheating();
      
      private var things:ThingsCheating = new ThingsCheating();
      
      private var ui:UICheating = new UICheating();
      
      private var union:UnionCheating = new UnionCheating();
      
      private var worldMap:WorldMapCheating = new WorldMapCheating();
      
      private var wilder:WilderCheating = new WilderCheating();
      
      private var post:PostCheating = new PostCheating();
      
      private var peak:PeakCheating = new PeakCheating();
      
      public function _CheatingController()
      {
         super();
         // 默认启用作弊功能
         this.enabled = true;
      }
      
      public function cheating(event:KeyboardEvent) : *
      {
         if(Gaming.uiGroup.testUI.haveDataB)
         {
            if(event.keyCode == 32)
            {
               this.cheatingByStr(this.cheatingStr);
               this.cheatingStr = "";
            }
            else
            {
               this.cheatingStr += String.fromCharCode(event.charCode);
            }
         }
      }
      
      private function cheatingByStr(c_str0:String) : *
      {
         var fun_name0:String = null;
         var value0:int = 0;
         var r_str0:String = null;

         // 移除商业版本限制，允许任何情况下开启作弊
         if(c_str0.indexOf("zuobikaiqi") >= 0 || c_str0.indexOf("zb") >= 0)
         {
            this.enabled = true;
            Gaming.uiGroup.showStat(true);
            return;
         }

         if(!this.enabled)
         {
            return;
         }
         var arr_len0:int = int(this.fun_arr.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            fun_name0 = this.fun_arr[i];
            if(!(fun_name0 == "SAVE" && c_str0 != "SAVE"))
            {
               if(c_str0.indexOf(fun_name0) >= 0)
               {
                  value0 = int(c_str0.replace(fun_name0,""));
                  r_str0 = this[fun_name0](c_str0,value0);
                  if(r_str0 != "")
                  {
                     Gaming.uiGroup.alertBox.showNormal(r_str0,"yes");
                     return;
                  }
               }
            }
         }
      }
      
      private function findCheatingDefine(c_str0:String) : void
      {
         var sarr0:Array = c_str0.split("=");
         var name0:String = sarr0[0];
         var num0:Number = Number(sarr0[1]);
         var d0:CheatingDefine = Gaming.defineGroup.cheating.getDefineByUpper(name0);
         if(Boolean(d0))
         {
            this.doOrder(d0.father,d0.name,"",num0);
         }
      }
      
      public function doOrder(f0:String, name0:String, str0:String, v0:Number) : void
      {
         var s0:String = this[f0][name0](str0,v0);
         if(s0 != "")
         {
            Gaming.uiGroup.alertBox.showSuccess(s0);
         }
      }
      
      private function closezuobipan(str0:String, v0:int) : String
      {
         this.closeZuobiPanB = !this.closeZuobiPanB;
         return (this.closeZuobiPanB ? "关闭" : "开启") + "作弊判断";
      }
   }
}

