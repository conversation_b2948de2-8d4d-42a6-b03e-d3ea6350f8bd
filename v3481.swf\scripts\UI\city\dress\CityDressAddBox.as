package UI.city.dress
{
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.must.NormalMustBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.city.dress.CityDressDataGroup;
   import dataAll._app.city.dress.CityDressType;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class CityDressAddBox extends NormalUI
   {
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var mustBoxSp:Sprite;
      
      private var closeBtn:SimpleButton;
      
      private var btnSp:MovieClip;
      
      private var txt:TextField;
      
      private var nowType:String = "";
      
      public function CityDressAddBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["mustBoxSp","closeBtn","btnSp","txt"];
         super.setImg(img0);
         FontDeal.dealOne(this.txt);
         addChild(this.mustBox);
         this.mustBox.setImg(this.mustBoxSp);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("增加装扮位");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function get dressData() : CityDressDataGroup
      {
         return Gaming.PG.da.city.dress;
      }
      
      public function showType(type0:String) : void
      {
         var mustD0:MustDefine = null;
         var bb0:Boolean = false;
         this.nowType = type0;
         var limit0:int = this.dressData.getLimitSpace(type0);
         var max0:int = this.dressData.getMaxSpace(type0);
         var typeStr0:String = CityDressType.getCn(type0) + "装扮位";
         if(max0 >= limit0)
         {
            this.txt.htmlText = ComMethod.color(typeStr0 + "已到达最大上限。","#00FF00");
            this.mustBox.visible = false;
            this.btn.actived = false;
         }
         else
         {
            this.txt.htmlText = ComMethod.color("添加" + typeStr0 + "所需","#00FF00");
            this.mustBox.visible = true;
            mustD0 = this.dressData.getAddSpaceMust(type0);
            bb0 = this.mustBox.inData(mustD0);
            this.btn.actived = bb0;
         }
         show();
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var mustD0:MustDefine = this.dressData.getAddSpaceMust(this.nowType);
         PlayerMustCtrl.deductMust(mustD0,this.affter_btnClick);
      }
      
      private function affter_btnClick() : void
      {
         this.dressData.addMaxSpace(this.nowType);
         Gaming.uiGroup.alertBox.showSuccess("添加成功！");
         this.showType(this.nowType);
         var e0:ClickEvent = new ClickEvent(ClickEvent.ON_FLESH);
         dispatchEvent(e0);
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         hide();
      }
   }
}

