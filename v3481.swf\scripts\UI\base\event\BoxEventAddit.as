package UI.base.event
{
   import flash.display.InteractiveObject;
   import flash.events.EventDispatcher;
   import flash.events.MouseEvent;
   
   public class BoxEventAddit
   {
      private var father:EventDispatcher = null;
      
      private var setB:Boolean = false;
      
      private var clickB:Boolean = true;
      
      private var downB:Boolean = false;
      
      private var upB:Boolean = false;
      
      private var overB:Boolean = false;
      
      private var outB:Boolean = false;
      
      private var moveB:Boolean = false;
      
      private var doubleClickB:Boolean = false;
      
      private var currentTargetB:Boolean = false;
      
      public var clickMustAcitved:Boolean = true;
      
      public function BoxEventAddit(father0:EventDispatcher)
      {
         super();
         this.father = father0;
      }
      
      public function setWantEvent(click0:<PERSON><PERSON>an, down0:<PERSON><PERSON><PERSON>, up0:<PERSON><PERSON><PERSON>, over0:<PERSON><PERSON>an, out0:<PERSON>olean, doubleClick0:Boolean = false) : void
      {
         if(this.setB)
         {
            INIT.showError("不能设置第二次侦听开关");
         }
         this.setB = true;
         this.clickB = click0;
         this.downB = down0;
         this.upB = up0;
         this.overB = over0;
         this.outB = out0;
         this.doubleClickB = doubleClick0;
      }
      
      public function setWant(click0:<PERSON><PERSON><PERSON>, over0:<PERSON>olean = false, down0:Boolean = false, doubleClick0:Boolean = false, move0:Boolean = false) : void
      {
         if(this.setB)
         {
            INIT.showError("不能设置第二次侦听开关");
         }
         this.setB = true;
         this.clickB = click0;
         this.downB = down0;
         this.upB = down0;
         this.overB = over0;
         this.outB = over0;
         this.moveB = move0;
         this.doubleClickB = doubleClick0;
      }
      
      public function setToCurrentTarget(bb0:Boolean) : void
      {
         this.currentTargetB = bb0;
      }
      
      public function addEL(lb0:InteractiveObject) : *
      {
         if(this.clickB)
         {
            lb0.addEventListener(MouseEvent.CLICK,this.buttonClick);
         }
         if(this.downB)
         {
            lb0.addEventListener(MouseEvent.MOUSE_DOWN,this.buttonDown);
         }
         if(this.upB)
         {
            lb0.addEventListener(MouseEvent.MOUSE_UP,this.buttonUp);
         }
         if(this.overB)
         {
            lb0.addEventListener(MouseEvent.MOUSE_OVER,this.buttonOver);
         }
         if(this.outB)
         {
            lb0.addEventListener(MouseEvent.MOUSE_OUT,this.buttonOut);
         }
         if(this.moveB)
         {
            lb0.addEventListener(MouseEvent.MOUSE_MOVE,this.buttonMove);
         }
         if(this.doubleClickB)
         {
            lb0.doubleClickEnabled = true;
            lb0.addEventListener(MouseEvent.DOUBLE_CLICK,this.doubleClick);
         }
      }
      
      public function removeEL(lb0:InteractiveObject) : *
      {
         if(this.clickB)
         {
            lb0.removeEventListener(MouseEvent.CLICK,this.buttonClick);
         }
         if(this.downB)
         {
            lb0.removeEventListener(MouseEvent.MOUSE_DOWN,this.buttonDown);
         }
         if(this.upB)
         {
            lb0.removeEventListener(MouseEvent.MOUSE_UP,this.buttonUp);
         }
         if(this.overB)
         {
            lb0.removeEventListener(MouseEvent.MOUSE_OVER,this.buttonOver);
         }
         if(this.outB)
         {
            lb0.removeEventListener(MouseEvent.MOUSE_OUT,this.buttonOut);
         }
         if(this.moveB)
         {
            lb0.removeEventListener(MouseEvent.MOUSE_MOVE,this.buttonMove);
         }
         if(this.doubleClickB)
         {
            lb0.removeEventListener(MouseEvent.DOUBLE_CLICK,this.doubleClick);
         }
      }
      
      protected function setGoul(clickEvent:ClickEvent, event:MouseEvent) : *
      {
         var t0:Object = this.currentTargetB ? event.currentTarget : event.target;
         clickEvent.child = t0;
         clickEvent.father = this.father;
         if(t0.hasOwnProperty("index"))
         {
            clickEvent.index = t0.index;
         }
         if(t0.hasOwnProperty("itemsData"))
         {
            clickEvent.childData = t0.itemsData;
         }
         if(t0.hasOwnProperty("label"))
         {
            clickEvent.label = t0.label;
         }
         if(t0.hasOwnProperty("fatherUrl"))
         {
            clickEvent.fatherUrl = t0.fatherUrl;
         }
      }
      
      protected function buttonClick(event:MouseEvent) : *
      {
         var t0:Object = event.target;
         if(this.clickMustAcitved)
         {
            if(t0.hasOwnProperty("actived"))
            {
               if(t0["actived"] == false)
               {
                  return;
               }
            }
         }
         var clickEvent:ClickEvent = new ClickEvent();
         this.setGoul(clickEvent,event);
         this.father.dispatchEvent(clickEvent);
      }
      
      protected function buttonDown(event:MouseEvent) : *
      {
         var t0:Object = event.target;
         if(this.clickMustAcitved)
         {
            if(t0.hasOwnProperty("actived"))
            {
               if(t0["actived"] == false)
               {
                  return;
               }
            }
         }
         var downEvent:ClickEvent = new ClickEvent(ClickEvent.ON_DOWN);
         this.setGoul(downEvent,event);
         this.father.dispatchEvent(downEvent);
      }
      
      protected function buttonUp(event:MouseEvent) : *
      {
         var t0:Object = event.target;
         if(this.clickMustAcitved)
         {
            if(t0.hasOwnProperty("actived"))
            {
               if(t0["actived"] == false)
               {
                  return;
               }
            }
         }
         var upEvent:ClickEvent = new ClickEvent(ClickEvent.ON_UP);
         this.setGoul(upEvent,event);
         this.father.dispatchEvent(upEvent);
      }
      
      protected function buttonOver(event:MouseEvent) : *
      {
         var downEvent:ClickEvent = new ClickEvent(ClickEvent.ON_OVER);
         this.setGoul(downEvent,event);
         this.father.dispatchEvent(downEvent);
      }
      
      protected function buttonOut(event:MouseEvent) : *
      {
         var upEvent:ClickEvent = new ClickEvent(ClickEvent.ON_OUT);
         this.setGoul(upEvent,event);
         this.father.dispatchEvent(upEvent);
      }
      
      protected function buttonMove(event:MouseEvent) : *
      {
         var upEvent:ClickEvent = new ClickEvent(ClickEvent.ON_MOVE);
         this.setGoul(upEvent,event);
         this.father.dispatchEvent(upEvent);
      }
      
      protected function doubleClick(event:MouseEvent) : *
      {
         var t0:Object = event.target;
         if(this.clickMustAcitved)
         {
            if(t0.hasOwnProperty("actived"))
            {
               if(t0["actived"] == false)
               {
                  return;
               }
            }
         }
         var clickEvent:ClickEvent = new ClickEvent(ClickEvent.ON_DOUBLE_CLICK);
         this.setGoul(clickEvent,event);
         this.father.dispatchEvent(clickEvent);
      }
   }
}

