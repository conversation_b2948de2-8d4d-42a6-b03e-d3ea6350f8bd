package UI.forging.blackChipConver
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.btnList.BtnBox;
   import UI.base.button.NormalBtn;
   import UI.base.must.NormalMustBox;
   import UI.base.numChoose.NumChooseBox;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.things.ThingsData;
   import dataAll.things.creator.BlackChipConverCtrl;
   import dataAll.things.define.ThingsDefine;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class BlackChipConverBoard extends BtnBox
   {
      private var beforeTag:Sprite;
      
      private var afterTag:Sprite;
      
      private var beforeGrip:ItemsGrid = new ItemsGrid();
      
      private var afterGrip:ItemsGrid = new ItemsGrid();
      
      private var numSp:Sprite;
      
      private var mustBoxSp:Sprite;
      
      private var converBtn:NormalBtn;
      
      private var moneyBtn:NormalBtn;
      
      private var upBtn:NormalBtn;
      
      private var downBtn:NormalBtn;
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var numBox:NumChooseBox = new NumChooseBox();
      
      public var nowData:ThingsData = null;
      
      private var afterDefine:ThingsDefine = null;
      
      private var showSuccessB:Boolean = false;
      
      public function BlackChipConverBoard()
      {
         super();
         setBtnB = true;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["beforeTag","afterTag","numSp","mustBoxSp"];
         super.setImg(img0);
         addChild(this.mustBox);
         this.mustBox.setImg(this.mustBoxSp);
         addChild(this.numBox);
         this.numBox.setImg(this.numSp);
         this.numBox.init(9999,1);
         this.numBox.addEventListener(Event.CHANGE,this.fleshData);
         this.beforeTag.addChild(this.beforeGrip);
         this.beforeGrip.setImgToEquipGrip();
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.beforeGrip);
         this.afterTag.addChild(this.afterGrip);
         this.afterGrip.setImgToEquipGrip();
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.afterGrip);
         this.moneyBtn.visible = false;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,"blackChipBag");
         this.fleshData();
      }
      
      private function fleshData(e:* = null) : void
      {
         this.showOneThingsDataAndPan(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      public function thingsGripClick(da0:ThingsData) : void
      {
         if(visible)
         {
            this.showOneThingsDataAndPan(da0);
         }
      }
      
      private function showOneThingsDataAndPan(da0:ThingsData) : void
      {
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择要转化的碎片。");
         this.showNone();
         if(Gaming.PG.da.thingsBag.dataArr.indexOf(da0) == -1)
         {
            da0 = null;
         }
         if(Boolean(da0))
         {
            this.showOneThingsData(da0);
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      private function showOneThingsData(da0:ThingsData) : void
      {
         this.nowData = da0;
         var num0:int = this.numBox.nowNum;
         var beforeD0:ThingsDefine = da0.save.getDefine();
         var afterArr0:Array = BlackChipConverCtrl.getAfterThingsDefineArr(beforeD0);
         if(afterArr0.indexOf(this.afterDefine) == -1)
         {
            this.afterDefine = afterArr0[0];
         }
         var mustNum0:int = BlackChipConverCtrl.getMustNum(beforeD0,num0,false);
         this.beforeGrip.inData_thingsDefine(beforeD0);
         this.beforeGrip.setNumText(mustNum0 + "");
         this.afterGrip.inData_thingsDefine(this.afterDefine);
         this.afterGrip.setNumText(num0 + "");
         this.upBtn.visible = afterArr0.length > 1;
         this.downBtn.visible = this.upBtn.visible;
         var must_d0:MustDefine = this.getMustDefine();
         var mustB0:Boolean = this.mustBox.inData(must_d0);
         this.converBtn.actived = mustB0;
         this.moneyBtn.isChosen = false;
      }
      
      private function showNone() : void
      {
         this.nowData = null;
         this.beforeGrip.clearData();
         this.afterGrip.clearData();
         this.mustBox.setShowState(false);
         this.converBtn.actived = false;
      }
      
      private function getMustDefine() : MustDefine
      {
         var beforeD0:ThingsDefine = this.nowData.save.getDefine();
         var num0:int = this.numBox.nowNum;
         return BlackChipConverCtrl.getMustDefine(beforeD0,num0,false);
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         this[btn0.label + "Click"]();
      }
      
      private function upClick() : void
      {
         this.chooseAfter(-1);
      }
      
      private function downClick() : void
      {
         this.chooseAfter(1);
      }
      
      private function chooseAfter(indexAdd0:int) : void
      {
         var beforeD0:ThingsDefine = this.nowData.save.getDefine();
         var afterArr0:Array = BlackChipConverCtrl.getAfterThingsDefineArr(beforeD0);
         var len0:int = int(afterArr0.length);
         var index0:int = int(afterArr0.indexOf(this.afterDefine));
         index0 = -1;
         if(true)
         {
            index0 = 0;
         }
         else
         {
            index0 = (index0 + indexAdd0 + len0) % len0;
         }
         this.afterDefine = afterArr0[index0];
      }
      
      private function moneyClick() : void
      {
         this.fleshData();
      }
      
      private function converClick() : void
      {
         var must_d0:MustDefine = null;
         if(Boolean(this.nowData))
         {
            must_d0 = this.getMustDefine();
            this.showSuccessB = must_d0.money == 0;
            PlayerMustCtrl.deductMust(must_d0,this.afterConver);
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("数据不存在！");
         }
      }
      
      private function afterConver() : void
      {
         var num0:int = this.numBox.nowNum;
         Gaming.PG.da.thingsBag.addDataByName(this.afterDefine.name,num0);
         this.fleshData();
         Gaming.uiGroup.allBagUI.fleshAllBox();
         if(this.showSuccessB)
         {
            Gaming.uiGroup.alertBox.showSuccess("转化成功！");
         }
      }
   }
}

