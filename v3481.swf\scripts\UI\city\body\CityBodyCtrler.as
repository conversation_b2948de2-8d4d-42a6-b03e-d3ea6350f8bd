package UI.city.body
{
   import UI.base.button.NormalBtn;
   import UI.city.CityEvent;
   import dataAll._app.city.dress.CityDressData;
   import dataAll._app.city.dress.CityDressDataGroup;
   import dataAll._app.city.dress.CityDressMould;
   import flash.display.DisplayObjectContainer;
   import flash.display.Sprite;
   import flash.events.EventDispatcher;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class CityBodyCtrler extends EventDispatcher
   {
      private var BG:CityBodyGroup = null;
      
      private var ctrlBody:CityBody = null;
      
      private var closeBtn:NormalBtn = new NormalBtn();
      
      private var dragB:Boolean = false;
      
      private var dragPoint:Point = new Point();
      
      public function CityBodyCtrler()
      {
         super();
      }
      
      public function init(BG0:CityBodyGroup) : void
      {
         this.BG = BG0;
         this.closeBtn.setImgUrl("CityUI/closeBtn");
         this.BG.getCon().ui.addChild(this.closeBtn);
         this.closeBtn.visible = false;
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeBtnClick,false,1);
      }
      
      private function get dressData() : CityDressDataGroup
      {
         return Gaming.PG.da.city.dress;
      }
      
      public function getDragB() : Boolean
      {
         return this.dragB;
      }
      
      public function bodyDown(b0:CityBody) : void
      {
         var con0:DisplayObjectContainer = null;
         if(this.ctrlBody == b0)
         {
            this.dragB = true;
            con0 = b0.getSprite().parent;
            this.dragPoint.x = con0.mouseX - b0.x;
            this.dragPoint.y = con0.mouseY - b0.y;
         }
      }
      
      public function clearDrag() : void
      {
         this.dragB = false;
         this.dragPoint.x = 0;
         this.dragPoint.y = 0;
      }
      
      public function addDressBody(mould0:CityDressMould) : void
      {
         var bodyCon0:Sprite = this.BG.getCon().things;
         var da0:CityDressData = this.dressData.addDataBy(mould0,bodyCon0.mouseX,bodyCon0.mouseY);
         var b0:CityBody = this.BG.addDressBody(da0);
         var e0:CityEvent = new CityEvent(CityEvent.ADD_CITY_BODY);
         dispatchEvent(e0);
      }
      
      private function removeBody(b0:CityBody) : void
      {
         this.BG.removeBody(b0);
         var da0:CityDressData = b0.getDressData();
         this.dressData.removeData(da0);
         var e0:CityEvent = new CityEvent(CityEvent.REMOVE_CITY_BODY);
         dispatchEvent(e0);
      }
      
      public function setCtrlBody(b0:CityBody) : void
      {
         if(Boolean(b0))
         {
            if(!b0.getDressData())
            {
               b0 = null;
            }
         }
         if(this.ctrlBody != b0)
         {
            this.clearCtrlBody();
            this.ctrlBody = b0;
            if(Boolean(b0))
            {
               this.ctrlBody.toSetGlow();
               this.closeBtn.visible = true;
               this.closeBtn.parent.addChild(this.closeBtn);
               this.closeBtnFollowBody();
            }
            this.dragB = false;
            this.BG.setCtrlBodyEvent(b0);
         }
      }
      
      private function clearCtrlBody() : void
      {
         if(Boolean(this.ctrlBody))
         {
            this.ctrlBody.clearSetGlow();
         }
         this.ctrlBody = null;
         this.closeBtn.visible = false;
         this.dragB = false;
      }
      
      private function closeBtnFollowBody() : void
      {
         var rect0:Rectangle = null;
         var scale0:Number = NaN;
         if(Boolean(this.ctrlBody))
         {
            rect0 = this.ctrlBody.getImgRect();
            scale0 = 0.9;
            this.closeBtn.x = this.ctrlBody.x + rect0.x + rect0.width * scale0;
            this.closeBtn.y = this.ctrlBody.y + rect0.y + rect0.height * (1 - scale0);
         }
      }
      
      private function closeBtnClick(e:MouseEvent) : void
      {
         if(Boolean(this.ctrlBody))
         {
            this.removeBody(this.ctrlBody);
            this.clearCtrlBody();
         }
      }
      
      public function FTimer() : void
      {
         if(Boolean(this.ctrlBody))
         {
            if(this.dragB)
            {
               this.ctrlBody.moveByMouse(this.dragPoint);
               this.BG.limitCoor(this.ctrlBody);
               this.closeBtnFollowBody();
            }
         }
      }
   }
}

