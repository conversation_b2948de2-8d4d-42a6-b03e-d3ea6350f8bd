package UI.gameWorld.agent
{
   import UI.gameWorld.vehicle.GWVehicleAgent;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.skill.SkillDataGroup;
   
   public class GWAgent
   {
      public var headBody:IO_NormalBody;
      
      public var arms:GWArmsAgent = new GWArmsAgent();
      
      public var skill:SkillDataGroup = null;
      
      public var vehicle:GWVehicleAgent = null;
      
      public var device:GWVehicleAgent = null;
      
      public var weapon:GWVehicleAgent = null;
      
      public function GWAgent()
      {
         super();
      }
   }
}

