package
{
   import flash.display.DisplayObject;
   import mx.core.SpriteAsset;
   
   [ExcludeClass]
   [Embed(source="/_assets/assets.swf", symbol="symbol39")]
   public class Gaming_noticeUIClass extends SpriteAsset
   {
      public var maskTargetSp:DisplayObject;
      
      public var twoBtnSp:DisplayObject;
      
      public var linkTxt:DisplayObject;
      
      public var oneBtnSp:DisplayObject;
      
      public var scrollLineSp:DisplayObject;
      
      public var nameTxt:DisplayObject;
      
      public var scrollBarSp:DisplayObject;
      
      public var titleTxt:DisplayObject;
      
      public var closeBtn:DisplayObject;
      
      public var contextSp:DisplayObject;
      
      public var giftBtnSp:DisplayObject;
      
      public function Gaming_noticeUIClass()
      {
         super();
      }
   }
}

