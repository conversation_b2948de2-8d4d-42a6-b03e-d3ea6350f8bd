package UI.api.union
{
   import flash.display.Stage;
   import unit4399.events.UnionEvent;
   
   public class Union_API
   {
      public var serviceHold:*;
      
      public var no_fun:Function = null;
      
      public var grow:UnionGrow_API = new UnionGrow_API();
      
      public var master:UnionMaster_API = new UnionMaster_API();
      
      public var member:UnionMember_API = new UnionMember_API();
      
      public var variables:UnionVariables_API = new UnionVariables_API();
      
      public var visitor:UnionVisitor_API = new UnionVisitor_API();
      
      private var apiArr:Array = [this.grow,this.master,this.member,this.variables,this.visitor];
      
      public function Union_API()
      {
         super();
      }
      
      public function addEvent(stage0:Stage, serviceHold0:*) : void
      {
         var api0:UnionBase_API = null;
         stage0.addEventListener(UnionEvent.UNION_VISITOR_SUCCESS,this.visitor.onVisitorSuccess);
         stage0.addEventListener(UnionEvent.UNION_MEMBER_SUCCESS,this.member.onMemberSuccess);
         stage0.addEventListener(UnionEvent.UNION_GROW_SUCCESS,this.grow.onGrowSuccess);
         stage0.addEventListener(UnionEvent.UNION_MASTER_SUCCESS,this.master.onMasterSuccess);
         stage0.addEventListener(UnionEvent.UNION_VARIABLES_SUCCESS,this.variables.onVariablesSuccess);
         stage0.addEventListener(UnionEvent.UNION_ERROR,this.unionCreateError);
         this.serviceHold = serviceHold0;
         for each(api0 in this.apiArr)
         {
            api0.father = this;
         }
      }
      
      public function outLoginEvent() : void
      {
         var api0:UnionBase_API = null;
         for each(api0 in this.apiArr)
         {
            api0.outLoginEvent();
         }
         this.no_fun = null;
      }
      
      public function doError(eId0:String) : void
      {
         var e0:UnionEvent = new UnionEvent(UnionEvent.UNION_ERROR,{"eId":eId0});
         this.unionCreateError(e0);
      }
      
      private function unionCreateError(e:UnionEvent) : *
      {
         var msg0:String = "未知错误";
         var eId0:String = e.data.eId;
         if(eId0 == "10003")
         {
            msg0 = "游戏未开通军队API";
         }
         if(eId0 == "10004")
         {
            msg0 = "只有帮主有权限";
         }
         if(eId0 == "10005")
         {
            msg0 = "用户未登陆";
         }
         if(eId0 == "20001")
         {
            msg0 = "用户黄金不足";
         }
         if(eId0 == "20002")
         {
            msg0 = "余额不足";
         }
         if(eId0 == "20003")
         {
            msg0 = "扣款失败";
         }
         if(eId0 == "20004")
         {
            msg0 = "军队名称已存在";
         }
         if(eId0 == "20005")
         {
            msg0 = "一个用户的一个存档，只能建一个军队";
         }
         if(eId0 == "20006")
         {
            msg0 = "超过申请数量上限";
         }
         if(eId0 == "20007")
         {
            msg0 = "该军队的申请列表已满";
         }
         if(eId0 == "20008")
         {
            msg0 = "用户已经有军队了";
         }
         if(eId0 == "20009")
         {
            msg0 = "已经申请过了";
         }
         if(eId0 == "20010")
         {
            msg0 = "用户还没有加入任何军队";
         }
         if(eId0 == "20011")
         {
            msg0 = "不存在该军队";
         }
         if(eId0 == "20012")
         {
            msg0 = "移除成员失败，用户不属于该军队";
         }
         if(eId0 == "20013")
         {
            msg0 = "移除成员失败，帮主不能被移除";
         }
         if(eId0 == "20014")
         {
            msg0 = "审核失败，军队成员已满";
         }
         if(eId0 == "20015")
         {
            msg0 = "编辑extra失败，只有帮主有该权限";
         }
         if(eId0 == "20016")
         {
            msg0 = "超过最大贡献值";
         }
         if(eId0 == "20017")
         {
            msg0 = "不存在该公共变量";
         }
         if(eId0 == "20018")
         {
            msg0 = "超过最大数量";
         }
         if(eId0 == "20019")
         {
            msg0 = "extra的字符数超过最大个数限制（1500）";
         }
         if(eId0 == "20020")
         {
            msg0 = "退出军队后，24小时内不能申请加军队";
         }
         if(eId0 == "20021")
         {
            msg0 = "没有兑换配置";
         }
         if(eId0 == "20022")
         {
            msg0 = "用户的申请信息已经过期";
         }
         if(eId0 == "20023")
         {
            msg0 = "军队id错误";
         }
         if(eId0 == "20024")
         {
            msg0 = "已经申请过解散军队了";
         }
         if(eId0 == "20025")
         {
            msg0 = "没有该任务";
         }
         if(eId0 == "20026")
         {
            msg0 = "用户不在审核列表中";
         }
         if(eId0 == "20027")
         {
            msg0 = "只有在加入军队的24小时后才能进行贡献";
         }
         if(eId0 == "20028")
         {
            msg0 = "没有解散过军队，不能进行取消解散";
         }
         if(eId0 == "20029")
         {
            msg0 = "公共变量未到生效时间";
         }
         if(eId0 == "20030")
         {
            msg0 = "账号不能变换存档加入同一个军队。";
         }
         if(eId0 == "20031")
         {
            msg0 = "贡献点不足";
         }
         if(eId0 == "20032")
         {
            msg0 = "用户不在该军队中";
         }
         if(eId0 == "20033")
         {
            msg0 = "不能转让给自己";
         }
         if(eId0 == "20034")
         {
            msg0 = "不存在的转让";
         }
         if(eId0 == "20035")
         {
            msg0 = "转让已存在";
         }
         if(eId0 == "20036")
         {
            msg0 = "解散状态不能转让";
         }
         if(eId0 == "20037")
         {
            msg0 = "转让状态不能解散";
         }
         if(eId0 == "20038")
         {
            msg0 = "未过24小时时间限制";
         }
         if(eId0 == "30001")
         {
            msg0 = "数据库添加失败";
         }
         if(eId0 == "30002")
         {
            msg0 = "数据库删除失败";
         }
         if(eId0 == "40001")
         {
            msg0 = "特殊用户的type填写错误";
         }
         if(eId0 == "40002")
         {
            msg0 = "没有这个用户";
         }
         if(eId0 == "60001")
         {
            msg0 = "该功能已损坏";
         }
         this.doNoFun(msg0);
      }
      
      public function doNoFun(str0:String) : void
      {
         if(this.no_fun is Function)
         {
            this.no_fun(str0);
         }
         else
         {
            Gaming.uiGroup.alertBox.showError(str0);
         }
      }
   }
}

