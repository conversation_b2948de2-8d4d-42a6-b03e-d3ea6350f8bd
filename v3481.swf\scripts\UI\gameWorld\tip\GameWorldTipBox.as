package UI.gameWorld.tip
{
   import UI.base.NormalUI;
   import UI.base.font.FontDeal;
   import com.sounto.utils.StringMethod;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class GameWorldTipBox extends NormalUI
   {
      private var txt:TextField;
      
      protected var firstX:int = 0;
      
      private var t:Number = -1;
      
      private var max_t:Number = 2;
      
      public var con:Sprite = null;
      
      private var strArr:Array = [];
      
      public function GameWorldTipBox()
      {
         super();
         this.mouseChildren = false;
         this.mouseEnabled = false;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["txt"];
         super.setImg(img0);
         FontDeal.dealOne(this.txt);
         this.firstX = this.x;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function setTextIndex(index0:int, str0:String) : void
      {
         if(this.strArr.length < index0 + 1)
         {
            this.strArr.length = index0 + 1;
         }
         this.strArr[index0] = str0;
      }
      
      public function showTextInsideArr(maxTime0:Number = 2) : void
      {
         var str0:String = StringMethod.concatStringArr(this.strArr,1);
         this.showText(str0,false,maxTime0);
      }
      
      public function showText(str0:String, soundB0:Boolean = true, maxTime0:Number = 2) : void
      {
         var beforeStr0:String = this.txt.text;
         this.setText(str0);
         this.max_t = maxTime0;
         this.t = 0;
         this.show();
         if(soundB0 && beforeStr0 != str0)
         {
            Gaming.soundGroup.playSound("uiSound","errorClick");
         }
      }
      
      protected function setText(str0:String) : void
      {
         this.txt.htmlText = FontDeal.getDealLeadingStr(this.txt,str0);
      }
      
      override public function show() : void
      {
         x = this.firstX;
         visible = true;
         if(Boolean(this.con))
         {
            this.con.addChild(this);
         }
      }
      
      override public function hide() : void
      {
         visible = false;
         x = this.firstX + 10000;
         if(Boolean(this.con) && Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
      
      public function FTimer() : void
      {
         if(this.t >= 0)
         {
            this.t += 0.03333333333333333;
            if(this.t > this.max_t)
            {
               this.t = -1;
               this.hide();
            }
         }
      }
   }
}

