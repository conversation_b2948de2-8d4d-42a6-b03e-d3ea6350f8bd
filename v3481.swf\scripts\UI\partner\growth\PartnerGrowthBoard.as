package UI.partner.growth
{
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import UI.base.heroImg.HeroEquipImgBox;
   import com.sounto.utils.DisplayMethod;
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._app.partner.PartnerData;
   import dataAll._app.partner.ability.PartnerAbilityData;
   import dataAll._app.partner.ability.PartnerAbilityDefine;
   import dataAll._player.more.NormalPlayerData;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class PartnerGrowthBoard extends AutoNormalUI
   {
      private var tipBtn:SimpleButton;
      
      private var resetBtn:NormalBtn;
      
      private var barTag:Sprite;
      
      private var imgTag:Sprite;
      
      private var infoTxt:TextField;
      
      private var pointTxt:TextField;
      
      private var heroImgBox:HeroEquipImgBox = new HeroEquipImgBox();
      
      private var heroName:String = "";
      
      private var barArr:Array = [];
      
      private var coverSp:Sprite;
      
      public function PartnerGrowthBoard()
      {
         super();
         btnSetB = true;
         mcTypeArr = ["btnSp","txt","tag"];
      }
      
      public function outLoginEvent() : void
      {
         this.removeHeroImg();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["tipBtn"];
         super.setImg(img0);
         FontDeal.dealOne(this.infoTxt);
         this.resetBtn.setName("重置");
         this.imgTag.scaleX = -1;
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function get PD() : NormalPlayerData
      {
         return Gaming.PG.DATA;
      }
      
      private function get partnerData() : PartnerData
      {
         return this.PD.partner;
      }
      
      override public function show() : void
      {
         super.show();
         this.imgTag.addChild(this.heroImgBox);
         this.fleshData();
      }
      
      override public function hide() : void
      {
         this.removeHeroImg();
         super.hide();
      }
      
      public function removeHeroImg() : void
      {
         if(Boolean(this.heroImgBox.parent))
         {
            this.heroImgBox.parent.removeChild(this.heroImgBox);
         }
      }
      
      private function fleshData() : void
      {
         if(Boolean(this.PD) && this.PD.isMainPlayerB() == false)
         {
            if(Gaming.LG.isGaming())
            {
               Gaming.uiGroup.wearUI.setCover("关卡中无法设置能力培养。");
            }
            else
            {
               Gaming.uiGroup.wearUI.setCover("");
               this.fleshLeft();
               this.fleshBar();
            }
         }
         else
         {
            Gaming.uiGroup.wearUI.setCover("P1没有能力培养。");
         }
      }
      
      private function fleshLeft() : void
      {
         var heroName0:String = null;
         var s0:String = null;
         var color2:String = null;
         var all0:int = 0;
         var surplus0:int = 0;
         heroName0 = this.PD.getImgHeroDefine().name;
         if(this.heroName != heroName0)
         {
            this.heroImgBox.setEquip_byObj(this.PD.getImageMcObj(),heroName0);
            this.heroName = heroName0;
         }
         s0 = "今天获得功勋值：" + TextMethod.colorSurplusNum(this.partnerData.save.dayExploit,PartnerData.maxDayExploit,"#FFFF00","#00FF00");
         s0 += "\n累计获得功勋值：";
         color2 = this.partnerData.save.exploit < PartnerData.maxExploit ? "#FFFF00" : "#00FF00";
         s0 += TextMethod.color(NumberMethod.toWan(this.partnerData.save.exploit) + "/" + NumberMethod.toWan(PartnerData.maxExploit,"",99999),color2);
         s0 += "\n1000点功勋值可获得1点能力点数";
         this.infoTxt.htmlText = FontDeal.getDealLeadingStr(this.infoTxt,s0);
         all0 = this.partnerData.getPointAll();
         surplus0 = all0 - this.partnerData.getPointAllUse();
         this.pointTxt.htmlText = surplus0 + "/" + all0;
         this.resetBtn.actived = surplus0 < all0;
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0 == this.resetBtn)
         {
            this.partnerData.resetPointAll();
            this.fleshData();
            Gaming.uiGroup.alertBox.showSuccess("重置成功！");
         }
      }
      
      private function fleshBar() : void
      {
         var darr0:Array = null;
         var d0:PartnerAbilityDefine = null;
         var bar0:PartnerAbilityBar = null;
         var url0:String = null;
         var da0:PartnerAbilityData = null;
         if(this.barArr.length == 0)
         {
            darr0 = Gaming.defineGroup.partner.getAbilityArr();
            for each(d0 in darr0)
            {
               bar0 = new PartnerAbilityBar();
               url0 = "PartnerUI/abilityBar";
               bar0.setImgUrl(url0);
               bar0.define = d0;
               bar0.fleshFun = this.fleshData;
               this.barTag.addChild(bar0);
               this.barArr.push(bar0);
            }
            DisplayMethod.arrange(this.barArr,8,8,1,6);
         }
         for each(bar0 in this.barArr)
         {
            da0 = this.partnerData.getAbilityData(bar0.define.name);
            bar0.fleshData(da0);
         }
      }
      
      protected function tipOver(e:MouseEvent) : void
      {
         var str0:String = "获得功勋值方法：";
         str0 += "\n1、角色消灭敌人！消灭精英怪或首领将获得更多功勋值；消灭等级过低的怪物将无法获得功勋值。";
         str0 += "\n2、角色受到敌人的伤害。受到伤害越高，获得功勋值就越多。";
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      protected function tipOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
   }
}

