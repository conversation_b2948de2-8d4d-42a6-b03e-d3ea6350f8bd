package UI.loading
{
   import UI.base._hide.HideNormalUI;
   import UI.base.loadBar.LoadBar;
   import com.sounto.net.SWFLoaderManager;
   import com.sounto.net.TextLoaderManager;
   import dataAll._data.ConstantDefine;
   import flash.display.Loader;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.ProgressEvent;
   import flash.net.URLRequest;
   import flash.text.TextField;
   import flash.text.TextFormat;
   import gameAll.process.PretreatmentGroup;
   
   public class LoadingUI extends HideNormalUI
   {
      private var timerMc:MovieClip = new MovieClip();
      
      public var swfM:SWFLoaderManager = null;
      
      public var textM:TextLoaderManager = null;
      
      public var preM:PretreatmentGroup = null;
      
      public var loadBar:LoadBar = new LoadBar();
      
      public var inVerTxt:TextField;
      
      public var loadBarSp:Sprite = null;
      
      private var backMc:MovieClip = null;
      
      private var loader:Loader = new Loader();
      
      private var first_loaderTxt:TextField = new TextField();
      
      private var completeFun:Function = null;
      
      public function LoadingUI()
      {
         super();
         var tf:TextFormat = new TextFormat("SimSun",14,10066329);
         this.first_loaderTxt.defaultTextFormat = tf;
         addChild(this.first_loaderTxt);
         this.first_loaderTxt.width = Gaming.WIDTH;
         this.first_loaderTxt.x = Gaming.WIDTH / 2;
         this.first_loaderTxt.y = 300;
      }
      
      public function loadResouce(completeFun0:Function) : void
      {
         inCon();
         this.swfM = Gaming.swfLoaderManager;
         this.textM = Gaming.textLoaderManager;
         this.preM = Gaming.pretreatGroup;
         this.completeFun = completeFun0;
         if(!img)
         {
            this.loader.load(new URLRequest(ConstantDefine.loadingSwfUrl));
            this.loader.contentLoaderInfo.addEventListener(ProgressEvent.PROGRESS,this.loadProcessFun);
            this.loader.contentLoaderInfo.addEventListener(Event.COMPLETE,this.loadCompleteFun);
         }
      }
      
      private function loadProcessFun(e:ProgressEvent) : void
      {
         this.first_loaderTxt.text = "正在加载：" + int(e.bytesLoaded / e.bytesTotal * 100) + "%";
      }
      
      private function loadCompleteFun(e:Event) : void
      {
         this.loader.contentLoaderInfo.removeEventListener(ProgressEvent.PROGRESS,this.loadProcessFun);
         this.loader.contentLoaderInfo.removeEventListener(Event.COMPLETE,this.loadCompleteFun);
         removeChild(this.first_loaderTxt);
         var newClass:Class = this.loader.contentLoaderInfo.applicationDomain.getDefinition("LoadingUI") as Class;
         var sp0:Sprite = new newClass();
         this.setImg(sp0);
         this.completeFun();
      }
      
      public function getBack() : MovieClip
      {
         var newClass:Class = this.loader.contentLoaderInfo.applicationDomain.getDefinition("backSp") as Class;
         return new newClass();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["loadBarSp","inVerTxt","backMc"];
         super.setImg(img0);
         this.loadBar.setImg(this.loadBarSp);
         addChild(this.loadBar);
         this.timerMc.addEventListener(Event.ENTER_FRAME,this.FTimer);
         this.inVerTxt.htmlText = "V" + ConstantDefine.inVersion;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         var f0:int = 0;
         super.show();
         if(Boolean(this.backMc))
         {
            f0 = 1;
            if(Gaming.LG.isSpaceMapB())
            {
               f0 = int(Math.random() * 2) + 8;
            }
            else
            {
               f0 = int(Math.random() * 5) + 1;
            }
            this.backMc.gotoAndStop(f0);
         }
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      private function FTimer(e:Event) : void
      {
         var per0:Number = NaN;
         var text0:String = null;
         if(visible)
         {
            per0 = 0;
            text0 = "";
            if(this.textM.loadState == "ing")
            {
               per0 = this.textM.getLoadingPer();
               text0 = this.textM.getLoadingText();
            }
            else if(this.swfM.loadState == "ing")
            {
               per0 = this.swfM.getOneLoadingPer();
               text0 = this.swfM.getLoadingText();
            }
            else if(this.preM.loadState == "pre")
            {
               per0 = this.preM.getLoadingPer();
               text0 = this.preM.getLoadingText();
            }
            else
            {
               this.hide();
            }
            this.loadBar.setPer(per0);
            this.loadBar.setText(int(per0 * 100) + "%");
            this.loadBar.setTopText(text0);
         }
      }
   }
}

