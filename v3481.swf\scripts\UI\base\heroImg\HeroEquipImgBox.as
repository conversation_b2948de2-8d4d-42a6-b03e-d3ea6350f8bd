package UI.base.heroImg
{
   import com.sounto.image.BmpMovieClip;
   import dataAll._player.more.NormalPlayerData;
   import dataAll._player.role.RoleName;
   import dataAll.body.define.HeroDefine;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipDefine;
   import flash.display.MovieClip;
   import flash.display.Shape;
   import flash.display.Sprite;
   import flash.geom.ColorTransform;
   import flash.geom.Rectangle;
   import gameAll.hero.image.HeroPartImage;
   
   public class HeroEquipImgBox extends Sprite
   {
      private static var allObj:Object = {};
      
      public var heroImg:HeroPartImage;
      
      private var backSp:Shape = new Shape();
      
      private var backRect:Rectangle = new Rectangle(-43,-133,86,147);
      
      private var lightB:Boolean = false;
      
      private var colorT:ColorTransform = new ColorTransform(1.5,1.5,1.5);
      
      private var colorTZ:ColorTransform = new ColorTransform();
      
      private var backLightBmp:BmpMovieClip;
      
      public var equipDataGroup:EquipDataGroup = null;
      
      public function HeroEquipImgBox()
      {
         super();
         this.mouseChildren = false;
         this.backSp.graphics.beginFill(16777215,0);
         this.backSp.graphics.drawRect(this.backRect.x,this.backRect.y,this.backRect.width,this.backRect.height);
         addChild(this.backSp);
      }
      
      public function imgInit() : void
      {
         var name0:String = RoleName.MAIN;
         this.setBaseImg(name0);
      }
      
      private function setBaseImg(heroName0:String) : void
      {
         var n:* = undefined;
         var baseDefine0:HeroDefine = Gaming.defineGroup.body.getHeroDefine(heroName0);
         var imgDefine0:HeroDefine = baseDefine0.getBonesHeroDefine();
         if(Boolean(this.heroImg))
         {
            this.heroImg.clear();
            removeChild(this.heroImg);
            this.heroImg = null;
         }
         this.heroImg = new HeroPartImage();
         this.heroImg.headPlayB = true;
         this.heroImg.changeArr = baseDefine0.upperImgArr;
         this.heroImg.mc.addPartSprite(baseDefine0.lowerImgArr);
         var allMc0:MovieClip = allObj[imgDefine0.name];
         if(!allMc0)
         {
            allMc0 = Gaming.swfLoaderManager.getResource(imgDefine0.name,"all");
            allObj[imgDefine0.name] = allMc0;
         }
         this.heroImg.init_Sprite(allMc0);
         this.heroImg.addChildAt(this.heroImg.hand_left,0);
         var movieDefineArr:Array = baseDefine0.movieDefineArr;
         for(n in movieDefineArr)
         {
            this.heroImg.addMovieDefine(movieDefineArr[n]);
         }
         addChild(this.heroImg);
         this.heroImg.play("equipStand");
      }
      
      public function setEquip_byPlayerData(pd0:NormalPlayerData, hdB0:Boolean = false) : void
      {
         this.setEquip_byObj(pd0.getImageMcObj(),pd0.getImgHeroDefine().name);
         this.equipDataGroup = pd0.equip;
         var ed0:EquipDefine = pd0.equip.getWearShowFashionDef();
         if(hdB0 && ed0 && ed0.hd)
         {
            this.scaleX = 2;
            this.scaleY = 2;
            this.heroImg.star.setSmoothing(true);
         }
         else
         {
            this.scaleX = 1;
            this.scaleY = 1;
         }
         this.heroImg.star.inLv(this.equipDataGroup.getStrengthenLightLvObj(),true);
      }
      
      public function setEquip_byObj(partObj0:Object, heroName0:String = "") : void
      {
         if(heroName0 != "")
         {
            this.setBaseImg(heroName0);
         }
         if(Boolean(this.heroImg))
         {
            this.heroImg.setAllEquip_byObj(partObj0);
            this.heroImg.hand_left.showLabel("fist");
            this.heroImg.FTimer();
            this.heroImg.fleshPart_byNowMc();
         }
      }
      
      public function showBackLight(color0:String) : void
      {
         if(Boolean(this.backLightBmp))
         {
            removeChild(this.backLightBmp);
            this.backLightBmp = null;
         }
         if(color0 != "")
         {
            this.backLightBmp = Gaming.EG.getAllBmpMovieCopy("lightEffect",color0 + "Drop");
            this.backLightBmp.mouseChildren = false;
            this.backLightBmp.mouseEnabled = false;
            addChildAt(this.backLightBmp,0);
            this.backLightBmp.y = -50;
            this.backLightBmp.speed = 2;
            this.backLightBmp.setTrueRotation(0);
            if(color0 == EquipColor.PURGOLD)
            {
               this.backLightBmp.speed = 1;
               this.backLightBmp.setTrueRotation(90);
            }
         }
      }
      
      public function playLabel(str0:String) : void
      {
         this.heroImg.play(str0);
         this.heroImg.fleshPart_byNowMc();
         this.heroImg.FTimer();
         if(str0 == "equipStand")
         {
            this.heroImg.hand_left.showLabel("fist");
         }
         else
         {
            this.heroImg.hand_left.showLabel("hold");
         }
      }
      
      public function stop() : void
      {
         if(Boolean(this.heroImg))
         {
            this.heroImg.enabled = false;
            this.heroImg.head.stop();
         }
      }
      
      public function play() : void
      {
         if(Boolean(this.heroImg))
         {
            this.heroImg.enabled = true;
            this.heroImg.head.play();
         }
      }
      
      public function Ftimer() : *
      {
         var x0:int = 0;
         var y0:int = 0;
         if(this.heroImg && this.heroImg.enabled && this.visible)
         {
            if(this.backLightBmp is BmpMovieClip)
            {
               this.backLightBmp.FTimer();
            }
            this.heroImg.FTimer();
            this.heroImg.fleshPart_byNowMc();
            x0 = this.mouseX;
            y0 = this.mouseY;
            if(x0 > this.backRect.x && x0 < this.backRect.x + this.backRect.width && y0 > this.backRect.y && y0 < this.backRect.y + this.backRect.height)
            {
               if(!this.lightB)
               {
                  this.transform.colorTransform = this.colorT;
                  this.lightB = true;
               }
            }
            else if(this.lightB)
            {
               this.transform.colorTransform = this.colorTZ;
               this.lightB = false;
            }
         }
      }
   }
}

