package UI.base.tip
{
   import UI.base.heroImg.HeroEquipImgBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.goods.GoodsData;
   import dataAll._player.role.RoleName;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.define.EquipDefine;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class HeroImgTipBox extends Sprite
   {
      public var tipBox:TipBox = null;
      
      protected var titleTxt:TextField = new TextField();
      
      private var imgBox:HeroEquipImgBox = new HeroEquipImgBox();
      
      protected var w:int = 0;
      
      protected var h:int = 0;
      
      public function HeroImgTipBox()
      {
         super();
         this.mouseChildren = false;
         this.mouseEnabled = false;
         addChild(this.imgBox);
         this.addChild(this.titleTxt);
         OneTextGather.setNormalFormat(this.titleTxt,14,true);
         this.titleTxt.y = 10;
      }
      
      public function showByFashionGoods(da0:GoodsData) : void
      {
         var f0:EquipDefine = Gaming.defineGroup.equip.getDefine(da0.def.defineLabel);
         this.showByFashionDefine(f0);
      }
      
      public function showByFashionDefine(f0:EquipDefine) : void
      {
         var sexRole0:String = null;
         this.setTitleText("<yellow 试穿效果/>");
         var fashionObj0:Object = EquipDataGroup.getImageMcObjByImgNameArr([f0.name],true);
         var obj0:Object = null;
         if(Gaming.PG.da.heroData.def.sex != f0.sex)
         {
            sexRole0 = RoleName.getOneBySex(f0.sex);
            obj0 = Gaming.BG.getPartImageMcObj(Gaming.defineGroup.body.getHeroDefine(sexRole0));
         }
         else
         {
            obj0 = Gaming.PG.da.getImageMcObj(null,false);
         }
         ComMethod.coverObj(obj0,fashionObj0);
         this.imgBox.setEquip_byObj(obj0,f0.getImgBodyName());
         this.imgBox.scaleX = f0.hd ? 2 : 1;
         this.imgBox.scaleY = this.imgBox.scaleX;
         this.fleshSize();
         this.show();
      }
      
      private function setTitleText(title0:String) : void
      {
         this.titleTxt.width = 10;
         this.titleTxt.htmlText = TextGatherAnalyze.swapText(title0);
      }
      
      protected function fleshSize() : void
      {
         var w0:int = 0;
         w0 = this.imgBox.width + 10;
         this.imgBox.x = w0 / 2;
         var h0:int = this.imgBox.height + 30;
         this.imgBox.y = h0 - this.imgBox.scaleY * 15;
         if(w0 < this.titleTxt.width + 10)
         {
            w0 = this.titleTxt.width + 10;
         }
         this.w = w0;
         this.h = h0;
         this.titleTxt.x = -this.titleTxt.width / 2 + w0 / 2;
      }
      
      public function show() : void
      {
         var w0:int = this.w;
         var h0:int = this.h;
         this.tipBox.hideAllshowBack("heroImg",w0,h0);
         this.visible = true;
         this.tipBox.show();
      }
   }
}

