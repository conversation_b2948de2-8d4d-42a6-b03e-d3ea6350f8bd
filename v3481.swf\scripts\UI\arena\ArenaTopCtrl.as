package UI.arena
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.StringDate;
   import dataAll._app.arena.ArenaData;
   import dataAll._app.arena.ArenaSave;
   import dataAll._app.top.define.TopBarDefineGroup;
   import dataAll._app.top.player.PlayerTopReturnData;
   import dataAll._app.top.player.PlayerTopUploadData;
   
   public class ArenaTopCtrl
   {
      private static var yesFun:Function;
      
      private static var noFun:Function;
      
      private static var topOpenB:Boolean = false;
      
      public static var topOpenTipB:Boolean = false;
      
      private static var startTimeStr:String = TextWay.toCode32("2015-7-18");
      
      public function ArenaTopCtrl()
      {
         super();
      }
      
      private static function get startTime() : StringDate
      {
         return new StringDate(TextWay.getText32(startTimeStr));
      }
      
      public static function get arenaSave() : ArenaSave
      {
         return Gaming.PG.da.arena.save;
      }
      
      public static function get arenaData() : ArenaData
      {
         return Gaming.PG.da.arena;
      }
      
      public static function getLinkStr() : String
      {
         if(isOverTimeB())
         {
            return getNowSeasonCn() + "竞技场赛季已经结束！";
         }
         return getNowSeasonCn() + "竞技场赛季正在进行中";
      }
      
      public static function getNowSeasonCn() : String
      {
         return "第" + arenaSave.nowPhase + "期";
      }
      
      public static function giftCloseB() : Boolean
      {
         return arenaSave.nowPhase >= 291;
      }
      
      public static function isCloseB() : Boolean
      {
         return arenaSave.nowPhase >= ArenaData.CLOSE_PHASE;
      }
      
      public static function countTopOpenB(str0:String) : Boolean
      {
         var da0:StringDate = new StringDate(str0);
         var date0:Date = da0.getDateClass();
         var day0:int = date0.day;
         var hour0:int = date0.hours;
         if(day0 > 0)
         {
            if(day0 == 6)
            {
               if(hour0 >= 9 && hour0 < 16)
               {
                  return true;
               }
            }
            else if(hour0 >= 9 && hour0 < 18)
            {
               return true;
            }
         }
         return false;
      }
      
      public static function getTopOpenB() : Boolean
      {
         return topOpenB;
      }
      
      public static function getTopOpenTip() : String
      {
         return "正常开放时间：周一至周五9点至18点、周六9点至16点。";
      }
      
      public static function openUI(yesFun0:Function, noFun0:Function) : void
      {
         yesFun = yesFun0;
         noFun = noFun0;
         Gaming.uiGroup.connectUI.show("获取数据时间……");
         Gaming.api.save.getServerTime(yes_getServerTime2,no_getServerTime2,true);
      }
      
      private static function yes_getServerTime2(str0:String) : void
      {
         arenaData.noConnectB = false;
         topOpenB = countTopOpenB(str0);
         var phase0:int = getNowPhase();
         arenaData.inNowPhase(phase0);
         Gaming.uiGroup.connectUI.hide();
         if(!giftCloseB())
         {
            if(isOverTimeB() && !arenaSave.phaseOverTipB)
            {
               arenaSave.phaseOverTipB = true;
               Gaming.uiGroup.alertBox.showNormal(getNowSeasonCn() + "当前赛季已结束！请在下午1点后到赛季奖界面领取奖励。\n你可以继续挑战竞技场获得优胜券，但不会获得积分。\n下个赛季将从本周六开始！","yes");
            }
         }
         if(yesFun is Function)
         {
            yesFun(str0);
         }
      }
      
      private static function no_getServerTime2(str0:String) : void
      {
         topOpenB = false;
         arenaData.noConnectB = true;
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError("获取服务器时间失败！无法进入竞技场。");
         if(noFun is Function)
         {
            noFun(str0);
         }
      }
      
      public static function uploadScoreBy(yesFun0:Function, noFun0:Function) : void
      {
         yesFun = yesFun0;
         noFun = noFun0;
         Gaming.uiGroup.connectUI.show("获取数据时间……");
         Gaming.api.save.getServerTime(yes_getServerTime,no_getServerTime,true);
      }
      
      private static function yes_getServerTime(str0:String) : void
      {
         arenaData.noConnectB = false;
         var d0:TopBarDefineGroup = getNowTopBarDefineGroup();
         var phase0:int = getNowPhase();
         arenaData.inNowPhase(phase0);
         Gaming.uiGroup.arenaUI.infoBox.fleshTop();
         Gaming.uiGroup.arenaUI.fleshByServerTime();
         getOwnRankScoreFun();
      }
      
      private static function no_getServerTime(str0:String) : void
      {
         arenaData.noConnectB = true;
         Gaming.uiGroup.alertBox.showError("获取数据失败！\n系统将无法获得你的排行榜数据！");
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.arenaUI.hide();
      }
      
      private static function getOwnRankScoreFun() : void
      {
         var d0:TopBarDefineGroup = getNowTopBarDefineGroup();
         Gaming.uiGroup.connectUI.show("获得成绩……");
         Gaming.api.top.getOneRankInfo(d0.id,Gaming.PG.loginData.name,Gaming.PG.getSaveIndex(),yes_getOwnRankScore,no_getOwnRankScore);
      }
      
      private static function yes_getOwnRankScore(returnDataObj:Object) : void
      {
         if(noAddScoreB() || returnDataObj.score > arenaSave.score)
         {
            over_unloadScore(returnDataObj);
         }
         else
         {
            uloadScoreFun();
         }
      }
      
      private static function no_getOwnRankScore(str0:String = "") : void
      {
         uloadScoreFun();
      }
      
      private static function uloadScoreFun() : void
      {
         var d0:TopBarDefineGroup = getNowTopBarDefineGroup();
         var uploadDa0:PlayerTopUploadData = Gaming.PG.da.getTopUploadData(d0);
         Gaming.uiGroup.connectUI.show("上传成绩……");
         Gaming.api.top.submitScore(uploadDa0,over_unloadScore,no_unloadScore);
      }
      
      private static function no_unloadScore(str0:String = "") : void
      {
         arenaData.inMyRank(-1);
         Gaming.uiGroup.arenaUI.infoBox.inDataByPlayerReturnData(null);
         Gaming.uiGroup.connectUI.hide();
         if(noFun is Function)
         {
            noFun(str0);
         }
      }
      
      private static function over_unloadScore(returnDataObj:Object) : void
      {
         var da0:PlayerTopReturnData = new PlayerTopReturnData();
         if(returnDataObj.hasOwnProperty("rank"))
         {
            da0.inData_byTopBarData(returnDataObj);
         }
         else
         {
            da0.inData_byObj(returnDataObj);
         }
         arenaData.inMyRank(da0.curRank);
         Gaming.uiGroup.arenaUI.infoBox.inDataByPlayerReturnData(da0);
         Gaming.uiGroup.connectUI.hide();
         if(yesFun is Function)
         {
            yesFun(returnDataObj);
         }
         Gaming.PG.save.headCount.inTop(getNowTopBarDefineGroup().name,da0.curRank);
      }
      
      public static function getNowTopBarDefineGroup() : TopBarDefineGroup
      {
         return arenaData.getTopBarDefineGroup();
      }
      
      public static function getNowPhase() : int
      {
         var nowDate0:StringDate = Gaming.api.save.getNowServerDate();
         return getPhaseByTime(nowDate0.getStr());
      }
      
      public static function getPhaseByTime(timeStr0:String) : int
      {
         var start0:StringDate = startTime;
         var now0:StringDate = new StringDate(timeStr0);
         var c0:int = now0.reductionOne(start0);
         return int(c0 / 7) + 1;
      }
      
      public static function getPhaseDayByTime(timeStr0:String) : int
      {
         var start0:StringDate = startTime;
         var now0:StringDate = new StringDate(timeStr0);
         var c0:int = now0.reductionOne(start0);
         return c0 % 7;
      }
      
      public static function noAddScoreB() : Boolean
      {
         return isCloseB() || isOverTimeB() || !arenaSave.newDayIsTruePhaseB();
      }
      
      public static function isOverTimeB(canGetGiftB0:Boolean = false) : Boolean
      {
         var nowDate0:StringDate = Gaming.api.save.getNowServerDate();
         var bb0:Boolean = nowDate0.getDateClass().day == 5;
         if(canGetGiftB0)
         {
            return bb0 && nowDate0.hours >= 13;
         }
         return bb0;
      }
      
      public static function isGiftTimeB() : Boolean
      {
         return isOverTimeB();
      }
   }
}

