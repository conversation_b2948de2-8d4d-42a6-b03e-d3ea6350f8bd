package UI.bag.parts
{
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.numChoose.NumChooseBox;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.NumberMethod;
   import dataAll._app.parts.PartsComposeCtrl;
   import dataAll._app.parts.PartsDataGroup;
   import dataAll._app.parts.define.PartsConst;
   import dataAll._player.PlayerData;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class PartsComposeBox extends AutoNormalUI
   {
      private var spaceTxt:TextField;
      
      private var coinTxt:TextField;
      
      private var spaceHookSp:MovieClip;
      
      private var coinHookSp:MovieClip;
      
      private var numBox:NumChooseBox;
      
      private var composeBtn:NormalBtn;
      
      private var closeBtn:NormalBtn;
      
      public function PartsComposeBox()
      {
         super();
         mcTypeArr = ["btnSp","txt","numBoxSp"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         this.numBox.addEventListener(Event.CHANGE,this.textChange);
         this.numBox.init(PartsConst.getMaxPartsLevel(),0);
         this.numBox.gapNum = PartsConst.cLv;
         this.composeBtn.setName("一键合成");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      private function fleshData() : void
      {
         var pd0:PlayerData = Gaming.PG.da;
         var partsBag0:PartsDataGroup = pd0.partsBag;
         var partsBagClone0:PartsDataGroup = partsBag0.clone();
         partsBagClone0.saveGroup.gripMaxNum = 9999;
         partsBagClone0.saveGroup.unlockTo(9998);
         var maxLv0:int = pd0.getSave().setting.partsComposeMaxLv;
         var beforeSpace0:int = int(partsBagClone0.dataArr.length);
         var coin0:Number = PartsComposeCtrl.compose(partsBagClone0,0,this.numDealFun(maxLv0 - PartsConst.cLv));
         var afterSpace0:int = int(partsBagClone0.dataArr.length);
         var mustSpace0:int = afterSpace0 - beforeSpace0;
         if(mustSpace0 < 1)
         {
            mustSpace0 = 1;
         }
         var nowSpace0:int = partsBag0.getSpaceSiteNum();
         var spaceB0:Boolean = nowSpace0 >= mustSpace0;
         this.spaceTxt.htmlText = ComMethod.colorMustNum(nowSpace0,mustSpace0);
         this.spaceHookSp.gotoAndStop(spaceB0 ? 1 : 2);
         this.numBox.nowNum = maxLv0;
         this.numBox.fleshPriceAndNum(0,false,false);
         var coinB0:Boolean = pd0.main.save.coin >= coin0;
         this.coinTxt.htmlText = NumberMethod.toWan(coin0,"");
         this.coinHookSp.gotoAndStop(coinB0 ? 1 : 2);
         this.composeBtn.actived = spaceB0 && coinB0 && coin0 > 0;
      }
      
      private function textChange(e:Event) : void
      {
         var pd0:PlayerData = Gaming.PG.da;
         var partsBag0:PartsDataGroup = pd0.partsBag;
         pd0.getSave().setting.partsComposeMaxLv = this.numBox.nowNum;
         this.fleshData();
      }
      
      private function numDealFun(num0:int) : int
      {
         var clv0:int = PartsConst.cLv;
         return Math.round(int(num0 / clv0) * clv0);
      }
      
      public function composeBtnClick(e:MouseEvent) : void
      {
         var pd0:PlayerData = Gaming.PG.da;
         var partsBag0:PartsDataGroup = pd0.partsBag;
         var maxLv0:int = pd0.getSave().setting.partsComposeMaxLv;
         var coin0:int = PartsComposeCtrl.compose(partsBag0,0,this.numDealFun(maxLv0 - PartsConst.cLv));
         pd0.main.useCoin(coin0);
         Gaming.uiGroup.alertBox.showSuccess("一键合成成功！");
         Gaming.uiGroup.partsUI.itemsDataRemove();
         Gaming.uiGroup.bagUI.fleshNowBox();
         Gaming.uiGroup.mainUI.fleshCoin();
         this.fleshData();
      }
      
      public function closeBtnClick(e:MouseEvent) : void
      {
         this.hide();
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var funName0:String = btn0.label + "BtnClick";
         if(this.hasOwnProperty(funName0))
         {
            this[funName0](e);
         }
      }
   }
}

