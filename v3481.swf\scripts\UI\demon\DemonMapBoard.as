package UI.demon
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.grid.NormalGrid;
   import UI.main.WorldMapBox;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._app.task.TaskData;
   import dataAll._app.worldMap.WorldMapData;
   import dataAll._app.worldMap.WorldMapDataGroup;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._app.worldMap.save.WorldMapSave;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.level.DemonDataCtrl;
   import dataAll.level.modeDiy.ModeDiyDefine;
   import dataAll.things.define.ThingsDefine;
   import dataAll.ui.GatherColor;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class DemonMapBoard extends AutoNormalUI
   {
      private var barTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var box:ItemsGripBox = new ItemsGripBox();
      
      private var giftBtn:NormalBtn;
      
      private var chestBtn:ItemsGrid = new ItemsGrid();
      
      private var chestSp:MovieClip;
      
      private var tipIndex:int = -1;
      
      public function DemonMapBoard()
      {
         super();
         mcTypeArr = ["tag","btnSp"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = elementNameArr.concat(["chestSp"]);
         super.setImg(img0);
         this.box.setIconPro("DemonUI/mapBar");
         this.box.arg.init(2,10,10,3,false);
         this.box.evt.setWant(true,true,false,false,true);
         addChild(this.box);
         NormalUICtrl.setTag(this.box,this.barTag);
         this.box.addEventListener(ClickEvent.ON_CLICK,this.barClick);
         this.box.addEventListener(ClickEvent.ON_OVER,this.barOver);
         this.box.addEventListener(ClickEvent.ON_OUT,this.barOut);
         this.box.addEventListener(ClickEvent.ON_MOVE,this.barMove);
         this.box.pageBox.setToNormalBtn();
         this.box.setPagePos(this.pageTag);
         this.giftBtn.setName("领宝箱");
         ItemsGripTipCtrl.addNormalBtnTip(this.giftBtn);
         addChild(this.chestBtn);
         this.chestBtn.setImg(this.chestSp);
         this.chestBtn.itemsData = Gaming.defineGroup.things.getDefine(DemonDataCtrl.demonChest);
         this.chestBtn.inData_thingsDefine(this.chestBtn.itemsData as ThingsDefine);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.chestBtn);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function get dataG() : WorldMapDataGroup
      {
         return Gaming.PG.da.worldMap;
      }
      
      public function outLoginEvent() : void
      {
         this.box.clearData();
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      private function fleshData() : void
      {
         var arr0:Array = this.dataG.getDemonSaveArr();
         this.box.inData_byArr(arr0,this.barInDataFun,false);
         this.fleshGift();
      }
      
      private function fleshGift() : void
      {
         var max0:int = this.dataG.getDemChestMax();
         var now0:int = this.dataG.saveGroup.ch;
         var cn0:String = "领修罗宝箱 " + ComMethod.dropColor(now0,max0);
         this.giftBtn.setName(cn0);
         this.giftBtn.tipString = "已通关地图：<blue " + this.dataG.getDemonWinNum() + "张/>";
         this.giftBtn.tipString += "\n已通关修罗九：<blue " + this.dataG.getDemonWinNum9() + "张/>";
         this.giftBtn.tipString += "\n领取修罗宝箱：" + ComMethod.dropColor(now0,max0) + "个";
         this.giftBtn.tipString += "\n\n<purple 1、每通关5张地图，可获得1个修罗宝箱，最多可获得4个。/>";
         this.giftBtn.tipString += "\n<purple 2、通关1张地图的修罗九难度，还可额外获得1个修罗宝箱，最多获得4个。/>";
         this.giftBtn.tipString += "\n<purple 3、5月5日开始，每周开放10张修罗九地图。/>";
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var g0:GiftAddDefineGroup = null;
         var bb0:Boolean = false;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0 == this.giftBtn)
         {
            g0 = this.dataG.getDemChestGift();
            if(Boolean(g0))
            {
               bb0 = GiftAddit.addAndTip(g0);
               if(bb0)
               {
                  this.dataG.demChestEvent();
                  this.fleshGift();
               }
            }
         }
      }
      
      private function barInDataFun(grip0:NormalGrid, s0:WorldMapSave) : void
      {
         var d0:WorldMapDefine = null;
         var timeStr0:String = null;
         var timePer0:Number = NaN;
         d0 = s0.getDefine();
         var diyD0:ModeDiyDefine = s0.getDemonDiyDef();
         grip0.itemsData = s0;
         grip0.label = d0.name;
         grip0.setIndexText(diyD0.cnName);
         grip0.setNewMc(diyD0.color);
         grip0.setName(d0.cnName);
         grip0.numTxt.visible = true;
         grip0.numTxt.htmlText = this.numColor(s0.demStone,s0.getStoneMaxAll());
         grip0.setOtherText(this.numColor(s0.demBall,s0.getBallMaxAll()));
         grip0.setIconName(this.getBossIcon(s0.getOneBossCn()));
         var dem9BossArr0:Array = d0.getDemon9BossCnArr();
         if(Boolean(dem9BossArr0) && dem9BossArr0.length > 0)
         {
            grip0.setIconName2(this.getBossIcon(dem9BossArr0[0]));
         }
         else
         {
            grip0.setIconName2("");
         }
         grip0.starMc.visible = true;
         var bestDiff0:int = s0.getBestDiffWin();
         var time0:Number = s0.getDemonWinTime(bestDiff0);
         if(bestDiff0 >= 0 && time0 > 0)
         {
            timeStr0 = ComMethod.getTimeStrTwo(time0);
            if(time0 >= 3600)
            {
               timeStr0 = ">1h";
            }
            timePer0 = time0 / 600;
            if(timePer0 > 1)
            {
               timePer0 = 1;
            }
            grip0.setLevelText(timeStr0);
            grip0.numBackSp.visible = true;
            grip0.numBackSp.scaleX = timePer0;
            grip0.starMc.gotoAndStop(bestDiff0 + 2);
         }
         else
         {
            grip0.starMc.gotoAndStop(1);
            grip0.setLevelText(ComMethod.color("0",GatherColor.graydarkColor));
            grip0.numBackSp.visible = false;
         }
         var winMaxB0:Boolean = s0.demWin >= s0.getDemWinMax();
         var taskDa0:TaskData = Gaming.PG.da.task.getIngTaskDataByWorldMap(d0.name);
         if(Boolean(taskDa0))
         {
            grip0.setShopBtnBackMc(3);
            grip0.levelTxt.visible = false;
            grip0.numBackSp.visible = false;
         }
         else if(winMaxB0)
         {
            grip0.setShopBtnBackMc("");
         }
         else if(s0.demStone >= s0.getStoneMaxAll())
         {
            grip0.setShopBtnBackMc(2);
         }
         else
         {
            grip0.setShopBtnBackMc(1);
         }
         grip0.activedAndEnabled = false;
         grip0.actived = !taskDa0 && !winMaxB0;
      }
      
      private function getBossIcon(cn0:String) : String
      {
         var d0:NormalBodyDefine = null;
         if(cn0 == "")
         {
            return "IconGather/random";
         }
         d0 = Gaming.defineGroup.body.getCnDefine(cn0);
         if(Boolean(d0))
         {
            return d0.headIconUrl;
         }
         return "";
      }
      
      private function numColor(v0:Number, max0:Number) : String
      {
         var color0:String = GatherColor.grayColor;
         if(v0 <= 0)
         {
            color0 = GatherColor.graydarkColor;
         }
         else if(v0 >= max0)
         {
            color0 = GatherColor.greenColor;
         }
         return TextMethod.color(String(v0),color0);
      }
      
      private function barOver(e:ClickEvent) : void
      {
         this.tipIndex = -1;
         var s0:WorldMapSave = e.childData as WorldMapSave;
         var str0:String = s0.getDemModeTip(this.dataG);
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      private function barMove(e:ClickEvent) : void
      {
         var save0:WorldMapSave = null;
         var s0:String = null;
         var grip0:NormalGrid = e.child as NormalGrid;
         var x0:int = grip0.mouseX;
         var i0:int = 0;
         if(x0 < 105)
         {
            i0 = 1;
         }
         if(x0 > 206 && x0 < 295)
         {
            i0 = 2;
         }
         if(i0 != this.tipIndex)
         {
            this.tipIndex = i0;
            save0 = e.childData as WorldMapSave;
            s0 = "";
            if(this.tipIndex == 0)
            {
               s0 = save0.getDemModeTip(this.dataG,true);
            }
            else if(this.tipIndex == 1)
            {
               s0 = save0.getDemonDiyDef().getGatherTip();
            }
            else if(this.tipIndex == 2)
            {
               s0 = save0.getDemSkillGatherTip();
            }
            if(s0 != "")
            {
               Gaming.uiGroup.tipBox.textTip.showFollowText(s0);
            }
            else
            {
               Gaming.uiGroup.tipBox.hide();
            }
         }
      }
      
      private function barOut(e:ClickEvent) : void
      {
         this.tipIndex = -1;
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function barClick(e:ClickEvent) : void
      {
         var da0:WorldMapData = this.dataG.getDataByName(e.label);
         if(da0.canDiffB())
         {
            Gaming.uiGroup.alertBox.diff.showDiff(da0,WorldMapBox.gotoLevel);
            Gaming.uiGroup.alertBox.diff.gotoMode(MapMode.DEMON,false);
         }
      }
   }
}

