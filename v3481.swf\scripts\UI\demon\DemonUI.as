package UI.demon
{
   import UI.base.AppNewUI;
   import dataAll.level.DemonDataCtrl;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class DemonUI extends AppNewUI
   {
      private var titleTxt:TextField;
      
      private var mapBoard:DemonMapBoard = new DemonMapBoard();
      
      public function DemonUI()
      {
         super();
         UICn = "修罗地图";
         swfLabel = "DemonUI";
         labelArr = ["map"];
         labelCnArr = ["地图"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = elementNameArr.concat(["titleTxt"]);
         super.setImg(img0);
         init_addLabel();
         init_addBox();
         init_other();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         var error0:String = DemonDataCtrl.openPan(Gaming.PG.da);
         if(error0 != "")
         {
            Gaming.uiGroup.alertBox.showInfo(error0);
            return;
         }
         super.show();
      }
      
      public function outLoginEvent() : void
      {
         this.mapBoard.outLoginEvent();
      }
      
      override protected function getTipText() : String
      {
         var str0:String = "";
         if(this.mapBoard.visible)
         {
            str0 += "1、每周系统会给每张地图指定一种玩法模式（包括团战、辅助、独战、冷门、决斗），鼠标放在模式文字上可查看模式说明。";
            str0 += "\n\n2、每张地图每周会掉落固定数量的无双材料，选择的难度越高，材料的掉率和掉落上限就越高。";
            str0 += "\n\n3、每张地图都拥有小怪和首领的专属技能。";
            str0 += "\n\n4、修罗模式下首领的攻击不会丢失。";
            str0 += "\n\n5、修罗模式以下道具无效：电磁铁、骷髅卡、月饼、汤圆、饺子、神宠卡片、神车卡片、摩卡令牌。";
            str0 += "\n\n6、修罗模式以下技能会被削弱：";
            str0 += "\n<blue 人物技能/>：馈赠、毒雾、魅惑、妖魅、金刚钻、派生导弹。";
            str0 += "\n<blue 装置技能/>：无疆之章、镭射穿梭器。/>";
            str0 += "\n<blue 武器技能/>：爆胆、击中回复、振奋、音爆(弦音)、包裹(卯兔)、龙卷(申猴)。/>";
            str0 += "\n<blue 时装技能/>：飞雷神、恶魔风脚、疾风斩、小炎戒。/>";
            str0 += "\n<blue 好感度、巅峰技能/>：分子虹吸、核爆。/>";
         }
         return str0;
      }
   }
}

