package UI.gameWorld.lottery
{
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._player.base.PlayerMainData;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class GameLotteryBox extends AutoNormalUI
   {
      private var giftTag:Sprite;
      
      private var giftBox:ItemsGripBox = new ItemsGripBox();
      
      private var errorTxt:TextField;
      
      private var numTxt:TextField;
      
      private var effectMc:MovieClip;
      
      private var saveNum:int = 0;
      
      public function GameLotteryBox()
      {
         super();
         mcTypeArr = ["btnSp","txt","tag"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = elementNameArr.concat(["effectMc"]);
         super.setImg(img0);
         this.giftTag.addChild(this.giftBox);
         this.giftBox.arg.init(5,1,8,5);
         this.giftBox.setIconPro("GameWorldUI/lotteryThingsGrip");
         this.giftBox.evt.setWant(false,true);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.giftBox);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
         this.effectMc.gotoAndPlay(2);
      }
      
      override public function hide() : void
      {
         super.hide();
         this.effectMc.stop();
      }
      
      public function lottery() : Boolean
      {
         var loNum0:int = 0;
         var g0:GiftAddDefineGroup = null;
         var error0:String = null;
         var bb0:Boolean = false;
         var mainDa0:PlayerMainData = Gaming.PG.da.main;
         var day0:int = mainDa0.save.dayLottery;
         var max0:int = PlayerMainData.getDayLotteryShowMax();
         if(day0 < max0)
         {
            loNum0 = Gaming.PG.da.getLotteryNum();
            g0 = Gaming.LG.getLotteryGiftNull(loNum0);
            if(Boolean(g0))
            {
               bb0 = true;
               error0 = GiftAddit.bagSpacePan(g0);
               if(error0 != "")
               {
                  this.errorTxt.htmlText = error0;
                  this.giftBox.visible = false;
               }
               else
               {
                  ++mainDa0.save.dayLottery;
                  Gaming.PG.save.getCount().lotteryOne(g0);
                  GiftAddit.add(g0);
                  this.errorTxt.htmlText = "";
                  this.giftBox.visible = true;
                  this.giftBox.inData_byGift(g0);
               }
               this.numTxt.htmlText = "通关抽奖获得\n" + ComMethod.color("每日上限 ") + ComMethod.dropColor(mainDa0.save.dayLottery,max0);
            }
         }
         if(bb0)
         {
            this.show();
            ++this.saveNum;
            if(this.saveNum >= 5)
            {
               this.saveNum = 0;
            }
         }
         return bb0;
      }
   }
}

