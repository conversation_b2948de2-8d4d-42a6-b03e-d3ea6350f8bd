package UI.test
{
   import UI.NormalUICtrl;
   import UI.base.NormalUI;
   import UI.base.box.NormalBox;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.test.CheatingDefine;
   import flash.display.Sprite;
   import flash.events.TextEvent;
   import flash.text.TextField;
   
   public class CheatingBox extends NormalUI
   {
      private var boxObj:Object = {};
      
      private var haveDataB:Boolean = false;
      
      private var boxTag:Sprite = null;
      
      private var menuTxt:TextField;
      
      private var nowLabel:String = "";
      
      private var nowBox:NormalBox;
      
      private var nowDefine:CheatingDefine;
      
      public function CheatingBox()
      {
         super();
      }
      
      public function initImg() : void
      {
         var img0:Sprite = Gaming.swfLoaderManager.getResource("TestUI","cheatingBox");
         this.setImg(img0);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["boxTag","menuTxt"];
         super.setImg(img0);
         this.menuTxt.addEventListener(TextEvent.LINK,this.menuClick);
         this.menuTxt.styleSheet = ComMethod.getLinkCss("#CCCCCC","#00FFFF");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.addBox();
         this.chooseBox(this.nowLabel);
      }
      
      private function addBox() : void
      {
         var localB0:Boolean = false;
         var nameArr0:Array = null;
         var cnArr0:Array = null;
         var n:* = undefined;
         var father0:String = null;
         var defineArr0:Array = null;
         var box0:NormalBox = null;
         var i:* = undefined;
         var btn0:NormalBtn = null;
         var d0:CheatingDefine = null;
         if(!this.haveDataB)
         {
            this.haveDataB = true;
            localB0 = Gaming.api.save.isLocal();
            nameArr0 = Gaming.defineGroup.cheating.fatherNameArr;
            cnArr0 = Gaming.defineGroup.cheating.fatherCnArr;
            for(n in nameArr0)
            {
               father0 = nameArr0[n];
               defineArr0 = Gaming.defineGroup.cheating.fatherArrObj[father0];
               box0 = new NormalBox();
               box0.setIconPro("TestUI/cheatingBoxBtn");
               box0.arg.init(6,8,21,15,false);
               box0.evt.setWantEvent(true,false,false,true,true);
               addChild(box0);
               NormalUICtrl.setTag(box0,this.boxTag);
               box0.setNowGripNum(defineArr0.length);
               this.boxObj[father0] = box0;
               box0.visible = false;
               box0.addEventListener(ClickEvent.ON_CLICK,this.btnClick);
               box0.addEventListener(ClickEvent.ON_OVER,this.btnOver);
               box0.addEventListener(ClickEvent.ON_OUT,this.btnOut);
               for(i in box0.gripArr)
               {
                  btn0 = box0.gripArr[i];
                  d0 = defineArr0[i];
                  btn0.setName(d0.cnName);
                  btn0.itemsData = d0;
                  btn0.actived = !d0.localB || localB0 || Gaming.PG.loginData.adminPan();
                  btn0.tipString = d0.tip;
               }
            }
            this.nowLabel = nameArr0[0];
         }
      }
      
      private function chooseFather(f0:String) : void
      {
         var n:* = undefined;
         var father0:String = null;
         var fatherCn0:String = null;
         var nameArr0:Array = Gaming.defineGroup.cheating.fatherNameArr;
         var cnArr0:Array = Gaming.defineGroup.cheating.fatherCnArr;
         var linkStr0:String = "";
         for(n in nameArr0)
         {
            father0 = nameArr0[n];
            fatherCn0 = cnArr0[n];
            if(father0 != f0)
            {
               linkStr0 += ComMethod.link(fatherCn0,father0);
            }
            else
            {
               linkStr0 += ComMethod.color(fatherCn0,"#00FF00");
            }
            linkStr0 += "  ";
         }
         this.menuTxt.htmlText = linkStr0;
      }
      
      private function chooseBox(f0:String) : void
      {
         this.nowLabel = f0;
         if(Boolean(this.nowBox))
         {
            this.nowBox.visible = false;
         }
         this.nowBox = this.boxObj[f0];
         if(Boolean(this.nowBox))
         {
            this.nowBox.visible = true;
         }
         this.chooseFather(f0);
      }
      
      private function menuClick(e:TextEvent) : void
      {
         var f0:String = e.text;
         this.chooseBox(f0);
      }
      
      private function btnOver(e:ClickEvent) : void
      {
         var d0:CheatingDefine = e.childData as CheatingDefine;
         if(d0.tip != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(d0.tip);
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      private function btnOut(e:ClickEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function btnClick(e:ClickEvent) : void
      {
         var d0:CheatingDefine = e.childData as CheatingDefine;
         if(d0.input != "")
         {
            this.nowDefine = d0;
            Gaming.uiGroup.alertBox.textInput.showTextInput(d0.getInputTitle(),d0.string,this.yesInput,"yesAndNo",9999);
         }
         else
         {
            Gaming.testCtrl.cheating.doOrder(d0.father,d0.name,"",0);
         }
      }
      
      private function yesInput(str0:String) : void
      {
         Gaming.testCtrl.cheating.doOrder(this.nowDefine.father,this.nowDefine.name,str0,Number(str0));
      }
   }
}

