package UI.edit
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.label.LabelBox;
   import UI.base.page.PageBox;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.StringMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._app.edit.boss.BossEditData;
   import dataAll._app.edit.boss.BossEditDataGroup;
   import dataAll._app.edit.boss.BossEditMethod;
   import dataAll._app.edit.boss.CodeBossEditDataGroup;
   import dataAll._app.top.TopBarData;
   import dataAll._app.top.TopBarDataGroup;
   import dataAll._app.top.define.TopBarDefineGroup;
   import dataAll._app.top.extra.BossEditTopExtra;
   import dataAll._app.top.player.PlayerTopUploadData;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.geom.Rectangle;
   import flash.text.TextField;
   
   public class BosseditTopBoard extends AutoNormalUI
   {
      protected static const MATCH:String = "match";
      
      protected static const COLLECT:String = "collect";
      
      protected static const HISTORY:String = "history";
      
      protected static const DEL:String = "del";
      
      protected static const CODE:String = "code";
      
      protected static const CTRL_CN:Object = {};
      
      private var titleTxt:TextField;
      
      private var gripBtn:NormalBtn;
      
      private var uploadBtn:NormalBtn;
      
      private var codeBtn:NormalBtn;
      
      private var labelTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var barTag:Sprite;
      
      private var ctrlTag:Sprite;
      
      private var labelBox:LabelBox = new LabelBox();
      
      private var box:ItemsGripBox = new ItemsGripBox();
      
      private var pageBox:PageBox = new PageBox();
      
      private var nowTopG:TopBarDefineGroup;
      
      private var nowUploadD:TopBarDefineGroup;
      
      private var nowPage:int = 0;
      
      private var mouseGrip:NormalBtn = null;
      
      private var tempG:CodeBossEditDataGroup = null;
      
      private var tempData:BossEditData = null;
      
      private var tempTopDataArr:Array = null;
      
      public function BosseditTopBoard()
      {
         super();
         mcTypeArr = ["tag","txt","btnSp"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var ctrlTxt0:TextField = null;
         CTRL_CN[COLLECT] = "收藏";
         CTRL_CN[DEL] = "删除";
         CTRL_CN[CODE] = "分享";
         super.setImg(img0);
         FontDeal.dealOne(this.titleTxt);
         var nameArr0:Array = [HISTORY,COLLECT].concat(Gaming.defineGroup.top.getNameArr("bossEdit").reverse());
         var cnNameArr0:Array = ["历史","收藏"].concat(Gaming.defineGroup.top.getCnNameArr("bossEdit").reverse());
         this.labelBox.arg.init(10,1,-15,0);
         this.labelBox.inData("BosseditUI/labelBtn",nameArr0,cnNameArr0);
         this.labelBox.setChoose_byIndex(0);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.labelBox.x = this.labelTag.x;
         this.labelBox.y = this.labelTag.y;
         addChild(this.labelBox);
         this.box.setIconPro("BosseditUI/topBar");
         this.box.arg.init(1,10,0,0,false);
         this.box.evt.setWant(true,true);
         addChild(this.box);
         NormalUICtrl.setTag(this.box,this.barTag);
         this.box.addEventListener(ClickEvent.ON_CLICK,this.barClick);
         this.box.addEventListener(ClickEvent.ON_OVER,this.barOver);
         this.box.addEventListener(ClickEvent.ON_OUT,this.barOut);
         this.box.pageBox.setToNormalBtn();
         this.box.setPagePos(this.pageTag);
         ctrlTxt0 = this.ctrlTag["txt"];
         addChild(this.ctrlTag);
         this.ctrlTag.visible = false;
         this.ctrlTag.mouseEnabled = false;
         this.ctrlTag["backSp"].mouseEnabled = false;
         ctrlTxt0.styleSheet = TextMethod.getLinkCss("","#FFFF00",false);
         ctrlTxt0.addEventListener(TextEvent.LINK,this.ctrlLink);
         TextMethod.setAutoFormat(ctrlTxt0,"right");
         FontDeal.dealOne(ctrlTxt0);
         this.ctrlTag.addEventListener(MouseEvent.MOUSE_OUT,this.barOutPan);
         this.pageBox.setToNormalBtn();
         addChild(this.pageBox);
         NormalUICtrl.setTag(this.pageBox,this.pageTag);
         this.pageBox.setMaxPageShow(10);
         this.pageBox.setPageNumOut(10);
         this.pageBox.addEventListener(ClickEvent.ON_SHOW_PAGE,this.pageClick);
         this.gripBtn.mouseEnabled = false;
         this.uploadBtn.setName("上传至大厅");
         ItemsGripTipCtrl.addNormalBtnTip(this.uploadBtn);
         this.uploadBtn.activedAndEnabled = false;
         this.codeBtn.setName("输入首领代码");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function get dataG() : BossEditDataGroup
      {
         return Gaming.PG.da.bossEdit;
      }
      
      public function outLoginEvent() : void
      {
         this.mouseGrip = null;
         this.nowTopG = null;
         this.nowUploadD = null;
         this.tempG = null;
         this.tempData = null;
         this.tempTopDataArr = null;
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshNowBox();
         this.fleshLeft();
         this.hideCtrlBtn();
      }
      
      override public function hide() : void
      {
         super.hide();
         this.mouseGrip = null;
         this.hideCtrlBtn();
      }
      
      private function fleshNowBox() : void
      {
         this.showBox(this.labelBox.nowLabel);
      }
      
      private function fleshLeft() : void
      {
         var da0:BossEditData = this.dataG.getMain();
         if(Boolean(da0))
         {
            this.gripBtn.visible = true;
            this.gripBtn.setName(da0.getTopLeftText(),true,true);
            this.gripBtn.setIconName(da0.getIconUrl());
            this.uploadBtn.visible = true;
            this.dataG.dealUploadBtn(this.uploadBtn);
         }
         else
         {
            this.gripBtn.visible = false;
            this.uploadBtn.visible = false;
         }
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      private function showBox(label0:String) : void
      {
         if(label0 == "")
         {
            label0 = this.labelBox.getFirstLabel();
         }
         this.labelBox.setChoose(label0);
         if(label0 == COLLECT)
         {
            this.showTopByBossDataG(this.dataG.getCollect());
         }
         else if(label0 == HISTORY)
         {
            this.showTopByBossDataG(this.dataG.getHistory());
         }
         else if(label0 == MATCH)
         {
            this.showTopByBossDataG(this.dataG.getMatchG());
         }
         else
         {
            this.showTop(label0);
         }
      }
      
      private function showTop(label0:String) : void
      {
         this.nowTopG = Gaming.defineGroup.top.getDefine(label0);
         this.pageBox.visible = true;
         this.box.pageBox.visible = false;
         this.showPage(0);
      }
      
      private function showPage(num0:int) : void
      {
         this.nowPage = num0;
         this.pageBox.showPage(num0);
      }
      
      private function pageClick(e:ClickEvent) : void
      {
         this.getRankList(e.index);
      }
      
      private function getRankList(page0:int) : void
      {
         this.nowPage = page0;
         Gaming.uiGroup.connectUI.show("获取排行榜数据……");
         Gaming.api.top.getRankListsData(this.nowTopG.id,this.box.arg.getOneNum(),this.nowPage + 1,this.yes_getRankList,this.no_getRankList);
      }
      
      private function yes_getRankList(dataArr0:Array) : void
      {
         this.tempTopDataArr = dataArr0;
         Gaming.uiGroup.connectUI.show("获取服务器数据……");
         Gaming.api.save.getServerTime(this.timeGetRank,this.no_getRankList);
      }
      
      private function timeGetRank(str0:String) : void
      {
         var dg0:TopBarDataGroup = null;
         Gaming.uiGroup.connectUI.hide();
         if(Boolean(this.tempTopDataArr))
         {
            dg0 = new TopBarDataGroup();
            try
            {
               dg0.inData_byArr(this.tempTopDataArr,this.nowTopG.name);
               this.box.setChoose_byIndex(-1);
               this.box.inData_byArr(dg0.arr,this.inTopBarFun);
            }
            catch(e:Error)
            {
               box.clearAllData();
               no_getRankList();
            }
         }
         else
         {
            this.box.clearAllData();
         }
         this.hideCtrlBtn();
      }
      
      private function inTopBarFun(grip0:ItemsGrid, da0:TopBarData) : void
      {
         var extra0:BossEditTopExtra = null;
         var code0:String = null;
         var obj0:Object = null;
         var bossDa0:BossEditData = null;
         var btnError0:String = "";
         var nowTime0:Number = Gaming.api.save.getNowServerDate().getDateClass().getTime() / 10000;
         if(da0.score > nowTime0 + 1)
         {
            btnError0 = "*****";
         }
         else
         {
            extra0 = da0.extraObj as BossEditTopExtra;
            code0 = extra0.o;
            obj0 = BossEditMethod.codeToObjAndPan(code0);
            if(obj0 is String == false)
            {
               bossDa0 = new BossEditData();
               bossDa0.tempInitTopData(da0,code0,obj0);
               this.inTopBarByBossData(grip0,bossDa0);
            }
            else
            {
               btnError0 = "--";
            }
         }
         if(btnError0 != "")
         {
            grip0.clearData();
            grip0.setName(btnError0);
            grip0.setPriceText(btnError0);
            grip0.setOtherText(btnError0);
            grip0.setNumText("");
            grip0.setShopBtnBackMc(1);
         }
         grip0.setLevel(this.nowPage * 10 + grip0.index + 1);
      }
      
      private function no_getRankList(str0:String = "") : void
      {
         UIOrder.alertError("获取排行榜数据失败！\n" + str0);
         Gaming.uiGroup.connectUI.hide();
      }
      
      private function uploadScore() : void
      {
         UIOrder.showTip("");
         var bb0:Boolean = UIOrder.zuobiPan(true,false);
         if(bb0)
         {
            UIOrder.alertError("你的账号存在异常，无法上传数据。");
         }
         else
         {
            Gaming.uiGroup.connectUI.show("获取服务器数据……");
            Gaming.api.save.getServerTime(this.timeUploadScore,this.no_unloadScore);
         }
      }
      
      private function timeUploadScore(str0:String) : void
      {
         var da0:BossEditData = this.dataG.getMain();
         if(!da0)
         {
            return;
         }
         var timeDa0:StringDate = new StringDate(str0);
         var topName0:String = da0.getUploadTopName();
         var topD0:TopBarDefineGroup = Gaming.defineGroup.top.getDefine(topName0);
         this.nowUploadD = topD0;
         var score0:Number = Math.floor(timeDa0.getDateClass().getTime() / 10000);
         var uploadDa0:PlayerTopUploadData = Gaming.PG.da.getTopUploadData(topD0,score0);
         Gaming.uiGroup.connectUI.show("上传首领……");
         Gaming.api.top.submitScore(uploadDa0,this.yes_unloadScore,this.no_unloadScore);
      }
      
      private function yes_unloadScore(returnDataObj:Object) : void
      {
         Gaming.uiGroup.connectUI.hide();
         var da0:BossEditData = this.dataG.getMain();
         if(!da0)
         {
            return;
         }
         if(Boolean(da0))
         {
            da0.uploadScoreEvent(returnDataObj);
            this.dataG.uploadScoreEvent();
         }
         this.fleshLeft();
         if(Boolean(this.nowUploadD))
         {
            this.showBox(this.nowUploadD.name);
         }
      }
      
      private function no_unloadScore(str0:String = "") : void
      {
         Gaming.uiGroup.connectUI.hide();
         UIOrder.alertError("上传数据失败！\n" + str0);
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0.actived)
         {
            if(btn0 == this.uploadBtn)
            {
               this.uploadScore();
            }
            else if(btn0 == this.codeBtn)
            {
               this.codeClick(e);
            }
         }
      }
      
      private function showTopByBossDataG(g0:CodeBossEditDataGroup) : void
      {
         this.pageBox.visible = false;
         this.box.pageBox.visible = true;
         this.box.setChoose_byIndex(-1);
         this.box.inData_byArr(g0.getUIDataArr(),this.inTopBarByBossData);
         this.hideCtrlBtn();
      }
      
      private function inTopBarByBossData(grip0:ItemsGrid, da0:BossEditData, starB0:Boolean = true) : void
      {
         grip0.itemsData = da0;
         grip0.activedAndIgnoreChosen = true;
         grip0.setLevel(grip0.index + 1);
         grip0.setName(da0.getCnName());
         grip0.setPriceText(da0.getPlayerName());
         grip0.setOtherText(da0.getLifeUI());
         grip0.setNumText(da0.getScoreUI(starB0));
         grip0.setIconScale(0.5);
         grip0.setIconName(da0.getIconUrl());
         grip0.setShopBtnBackMc(da0.getWinB() ? 2 : 1);
      }
      
      private function codeClick(e:MouseEvent) : void
      {
         Gaming.uiGroup.alertBox.textInput.showTextInput("请输入首领代码","",this.yesCode,"yesAndNo",1000);
      }
      
      private function yesCode(code0:String) : void
      {
         var obj0:Object = null;
         var grip0:NormalBtn = null;
         var dg0:CodeBossEditDataGroup = this.dataG.getHistory();
         var findDa0:BossEditData = dg0.getDataByTempCode(code0);
         if(!findDa0)
         {
            obj0 = BossEditMethod.codeToObjAndPan(code0);
            if(obj0 is String)
            {
               UIOrder.alertError(obj0 as String);
            }
            else
            {
               findDa0 = dg0.newDataByCodeAndObj(code0,obj0);
               dg0.afterInHistroy();
            }
         }
         if(Boolean(findDa0))
         {
            this.showBox(HISTORY);
            if(Boolean(findDa0))
            {
               grip0 = this.box.getBtnByItemsData(findDa0);
               if(Boolean(grip0))
               {
                  this.box.setChoose_byIndex(grip0.index);
                  this.box.gotoChoosePage();
                  grip0.getChooseMc().gotoAndPlay(2);
               }
            }
         }
      }
      
      private function collectData(da0:BossEditData) : void
      {
         var g0:CodeBossEditDataGroup = this.dataG.getCollect();
         var error0:String = g0.collectPan(da0);
         if(error0 == "")
         {
            g0.collectData(da0);
            Gaming.uiGroup.alertBox.showSuccess("收藏成功！");
         }
         else
         {
            UIOrder.alertError(error0);
         }
      }
      
      private function delData(g0:CodeBossEditDataGroup, da0:BossEditData) : void
      {
         this.tempData = da0;
         this.tempG = g0;
         Gaming.uiGroup.alertBox.showChoose("是否删除这条记录？",this.yesDel);
      }
      
      private function yesDel() : void
      {
         if(Boolean(this.tempData) && Boolean(this.tempG))
         {
            this.tempG.removeData(this.tempData);
            this.fleshNowBox();
            this.tempData = null;
            this.tempG = null;
         }
      }
      
      private function showCtrlBtn(grip0:NormalBtn, typeArr0:Array) : void
      {
         var type0:* = null;
         var txt0:TextField = null;
         var cn0:String = null;
         var text0:String = null;
         this.ctrlTag.visible = true;
         this.ctrlTag.x = grip0.x + this.box.x;
         this.ctrlTag.y = grip0.y + this.box.y;
         var textArr0:Array = [];
         for each(type0 in typeArr0)
         {
            cn0 = CTRL_CN[type0];
            text0 = TextMethod.link(cn0,type0);
            textArr0.push(text0);
         }
         txt0 = this.ctrlTag["txt"];
         txt0.htmlText = StringMethod.concatStringArr(textArr0,99,"  ");
      }
      
      private function ctrlLink(e:TextEvent) : void
      {
         var da0:BossEditData = null;
         var nowLabel0:String = null;
         if(Boolean(this.mouseGrip))
         {
            da0 = this.mouseGrip.itemsData as BossEditData;
            if(Boolean(da0) && da0.haveDataB())
            {
               nowLabel0 = this.labelBox.nowLabel;
               if(nowLabel0 == COLLECT)
               {
                  if(e.text == DEL)
                  {
                     this.delData(this.dataG.getCollect(),da0);
                  }
               }
               else if(nowLabel0 == HISTORY)
               {
                  if(e.text == COLLECT)
                  {
                     this.collectData(da0);
                  }
                  else if(e.text == DEL)
                  {
                     this.delData(this.dataG.getHistory(),da0);
                  }
               }
               else if(e.text == COLLECT)
               {
                  this.collectData(da0);
               }
               if(e.text == CODE)
               {
                  BossEditMethod.shareCode(da0,false);
               }
            }
         }
      }
      
      private function hideCtrlBtn() : void
      {
         this.ctrlTag.visible = false;
      }
      
      private function barClick(e:ClickEvent) : void
      {
         this.hideCtrlBtn();
         var da0:BossEditData = e.childData as BossEditData;
         if(Boolean(da0))
         {
            BossEditMethod.gotoMap(da0);
         }
      }
      
      private function barOver(e:ClickEvent) : void
      {
         var da0:BossEditData = null;
         var grip0:NormalBtn = e.child as NormalBtn;
         var nowLabel0:String = this.labelBox.nowLabel;
         if(nowLabel0 == COLLECT)
         {
            this.showCtrlBtn(grip0,[CODE,DEL]);
         }
         else if(nowLabel0 == HISTORY)
         {
            this.showCtrlBtn(grip0,[DEL,COLLECT]);
         }
         else
         {
            this.showCtrlBtn(grip0,[CODE,COLLECT]);
         }
         this.mouseGrip = grip0;
         var tip0:String = "";
         if(grip0.mouseX <= this.ctrlTag["shadowSp"].x)
         {
            da0 = grip0.itemsData as BossEditData;
            if(Boolean(da0))
            {
               tip0 = da0.getTopBarTip();
            }
         }
         UIOrder.showTip(tip0);
      }
      
      private function barOut(e:ClickEvent) : void
      {
         this.barOutPan();
         UIOrder.showTip("");
      }
      
      private function barOutPan(e:* = null) : void
      {
         var btn0:NormalBtn = null;
         var rect0:Rectangle = null;
         var inB0:Boolean = false;
         for each(btn0 in this.box.gripArr)
         {
            rect0 = btn0.getRect(btn0);
            if(rect0.contains(btn0.mouseX,btn0.mouseY))
            {
               inB0 = true;
               break;
            }
         }
         if(inB0 == false)
         {
            this.hideCtrlBtn();
         }
      }
   }
}

