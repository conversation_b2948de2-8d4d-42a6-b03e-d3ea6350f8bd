package UI.shop
{
   import dataAll._app.goods.GoodsData;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._app.love.LoveData;
   import dataAll._player.PlayerData;
   import dataAll._player.role.RoleName;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.gift.define.GiftType;
   import dataAll.items.IO_ItemsData;
   
   public class ShopBuyLimit
   {
      public function ShopBuyLimit()
      {
         super();
      }
      
      public static function getLimit(da0:GoodsData) : String
      {
         var itemsData0:IO_ItemsData = null;
         var d0:GoodsDefine = da0.def;
         var pd0:PlayerData = Gaming.PG.da;
         var specialNum0:int = pd0.goods.save.getSpecialNum(d0.name);
         if(specialNum0 > 0)
         {
            return "该商品已无法购买。";
         }
         if(d0.autoUseB && Gaming.LG.isGaming())
         {
            return "该商品在关卡内无法购买，请退出关卡。";
         }
         if(d0.dataType == GiftType.armsSkin)
         {
            if(pd0.main.haveArmsSkinB(da0.def.defineLabel))
            {
               return "你已经拥有了该武器皮肤，无需购买。";
            }
         }
         if(d0.moreLimitB)
         {
            if(pd0.getMoreNum() == 0)
            {
               return "你还没有队友，暂时无法购买此商品！";
            }
         }
         if(Boolean(d0.levelLimit))
         {
            if(pd0.level < d0.levelLimit)
            {
               return "人物等级到达" + d0.levelLimit + "级，才可购买此商品。";
            }
         }
         if(d0.limitOneB)
         {
            itemsData0 = pd0.findArenaGiftItemsData(d0.defineLabel);
            if(Boolean(itemsData0))
            {
               return "你的背包、仓库或者人物上已经存在有该物品\n请将它卖出（武器可拆解），才能继续购买该物品。";
            }
         }
         if(da0.isOverAllBuyB())
         {
            return "该商品的购买次数已经用完";
         }
         var fun0:Function = ShopBuyLimit[d0.name];
         if(fun0 is Function)
         {
            return fun0(da0);
         }
         return "";
      }
      
      private static function shuttleDevicer_1(da0:GoodsData) : String
      {
         return loveMust(RoleName.Girl,15000);
      }
      
      private static function shuttleSword(da0:GoodsData) : String
      {
         return shuttleDevicer_1(da0);
      }
      
      private static function blackHoleSword(da0:GoodsData) : String
      {
         return loveMust(RoleName.ZangShi,24000);
      }
      
      private static function loveMust(roleName0:String, must0:Number) : String
      {
         var loveDa0:LoveData = Gaming.PG.da.getLoveData(roleName0);
         if(Boolean(loveDa0))
         {
            if(loveDa0.getValue() >= must0)
            {
               return "";
            }
         }
         var d0:NormalBodyDefine = Gaming.defineGroup.body.getDefine(roleName0);
         return d0.getRoleCn() + d0.getLoveCn() + "到达 " + must0 + " 才能购买此商品。";
      }
   }
}

