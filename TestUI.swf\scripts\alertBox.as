package
{
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol575")]
   public dynamic class alertBox extends MovieClip
   {
      public var itemsColorSp:MovieClip;
      
      public var diffSp:MovieClip;
      
      public var downSp:MovieClip;
      
      public var numSp:MovieClip;
      
      public var yesBtn:MovieClip;
      
      public var infoSp:MovieClip;
      
      public var arenaSp:MovieClip;
      
      public var noBtn:MovieClip;
      
      public var shopSp:MovieClip;
      
      public var textInputSp:MovieClip;
      
      public var closeBtn:SimpleButton;
      
      public var doubleSp:MovieClip;
      
      public function alertBox()
      {
         super();
      }
   }
}

