package
{
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol423")]
   public dynamic class equipGrip extends MovieClip
   {
      public var numTxt:TextField;
      
      public var smallIconMc:MovieClip;
      
      public var annotationTxt:TextField;
      
      public var lockSp:MovieClip;
      
      public var levelTxt:TextField;
      
      public var backMc:MovieClip;
      
      public var numBackSp:MovieClip;
      
      public var iconCon:MovieClip;
      
      public var starMc:MovieClip;
      
      public var levelTxtBack:MovieClip;
      
      public var chooseMc:MovieClip;
      
      public var shopBtnBackMc:MovieClip;
      
      public var btnMc:MovieClip;
      
      public function equipGrip()
      {
         super();
      }
   }
}

