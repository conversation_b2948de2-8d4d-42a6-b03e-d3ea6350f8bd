package UI.wilder
{
   import UI.UIShow;
   import UI.base.AppNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.scroll.NormalScrollBar;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import dataAll._app.wilder.WilderData;
   import dataAll._app.wilder.WilderDataGroup;
   import dataAll._app.wilder.define.WilderFatherDefine;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import flash.text.TextField;
   
   public class WilderUI extends AppNormalUI
   {
      private var barTag:Sprite;
      
      private var scrollBar:NormalScrollBar;
      
      private var fightBtnSp:MovieClip;
      
      private var fightBtn:NormalBtn = new NormalBtn();
      
      private var linkTxt:TextField;
      
      private var closeBtn:SimpleButton;
      
      private var bossBoardSp:Sprite;
      
      private var bossBoard:WilderBossBoard = new WilderBossBoard();
      
      private var lootBoardSp:Sprite;
      
      private var keyTxt:TextField;
      
      public var lootBoard:WilderLootBoard = new WilderLootBoard();
      
      private var barArr:Array = [];
      
      public function WilderUI()
      {
         super();
         UICn = "秘境";
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["linkTxt","keyTxt","bossBoardSp","barTag","lootBoardSp","fightBtnSp","closeBtn"];
         super.setImg(img0);
         FontDeal.dealOne(this.keyTxt);
         FontDeal.dealOne(this.linkTxt);
         this.linkTxt.visible = false;
         this.scrollBar = new NormalScrollBar(this.barTag,img0["maskTargetSp"],img0["scrollBarSp"],img0["scrollLineSp"],5,false,true,true);
         this.scrollBar.speed = 18;
         this.scrollBar.refresh();
         this.fightBtnSp.visible = false;
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         this.keyTxt.addEventListener(MouseEvent.MOUSE_OVER,this.keyTxtOver);
         this.keyTxt.addEventListener(MouseEvent.MOUSE_OUT,this.keyTxtOut);
         addChild(this.bossBoard);
         this.bossBoard.setImg(this.bossBoardSp);
         this.bossBoard.hide();
         this.lootBoardSp.parent.removeChild(this.lootBoardSp);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function gotoBossUI(bossName0:String) : void
      {
         UIShow.showApp("wilder");
         this.lootBoard.hide();
         var da0:WilderData = Gaming.PG.da.wilder.getData(bossName0);
         if(Boolean(da0))
         {
            this.bossBoard.showWilderData(da0);
         }
      }
      
      protected function get wilderData() : WilderDataGroup
      {
         return Gaming.PG.da.wilder;
      }
      
      override public function show() : void
      {
         super.show();
         this.bossBoard.hide();
         this.lootBoard.hide();
         var tip0:String = this.wilderData.openUI();
         if(tip0 != "")
         {
            Gaming.uiGroup.alertBox.showSuccess(tip0);
         }
         this.initBar();
         this.fleshBar();
         this.fleshTxt();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      public function outLoginEvent() : void
      {
         this.bossBoard.outLoginEvent();
         this.clearBarData();
      }
      
      private function initBar() : void
      {
         var fatherArr0:Array = null;
         var beforeY:int = 0;
         var f0:WilderFatherDefine = null;
         var sp0:Sprite = null;
         var bar0:WilderFatherBar = null;
         if(this.barArr.length == 0)
         {
            fatherArr0 = Gaming.defineGroup.wilder.fatherArr;
            beforeY = 0;
            for each(f0 in fatherArr0)
            {
               bar0 = new WilderFatherBar();
               bar0.setImg(Gaming.swfLoaderManager.getResource("WilderUI","fatherBar"));
               bar0.x = 0;
               bar0.y = beforeY;
               beforeY += f0.getHeight();
               bar0.setTipVisible(f0.info != "");
               this.barTag.addChild(bar0);
               bar0.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
               bar0.addEventListener(ClickEvent.ON_OVER,this.gripOver);
               bar0.addEventListener(ClickEvent.ON_OUT,this.gripOut);
               bar0.addEventListener(ClickEvent.ON_CTRL_OVER,this.gripTipOver);
               bar0.addEventListener(ClickEvent.ON_CTRL_OUT,this.gripTipOut);
               bar0.addEventListener(ClickEvent.ON_CTRL_CLICK,this.gripTipClick);
               this.barArr.push(bar0);
            }
            sp0 = new Sprite();
            sp0.y = this.barTag.height + 100;
            sp0.graphics.drawRect(0,0,1,1);
            this.barTag.addChild(sp0);
            this.scrollBar.refresh();
         }
      }
      
      private function fleshBar() : void
      {
         var f0:WilderFatherDefine = null;
         var beforeY:int = 0;
         var bar0:WilderFatherBar = null;
         var dataArr0:Array = null;
         var fatherArr0:Array = Gaming.defineGroup.wilder.fatherArr;
         var index0:int = 0;
         var sortArr0:Array = [];
         for each(f0 in fatherArr0)
         {
            bar0 = this.barArr[index0];
            if(Boolean(bar0))
            {
               dataArr0 = Gaming.PG.da.wilder.getDataArrByFather(f0.name,false);
               bar0.inDataByWilderFatherDefine(f0,dataArr0);
               sortArr0.push(bar0);
            }
            index0++;
         }
         sortArr0.sort(this.sortBarFun);
         beforeY = 0;
         for each(bar0 in sortArr0)
         {
            bar0.x = 0;
            bar0.y = beforeY;
            beforeY += bar0.getHeight();
         }
      }
      
      private function sortBarFun(bar1:WilderFatherBar, bar2:WilderFatherBar) : int
      {
         return ArrayMethod.sortNumberFun(bar2.getLastOpenTimeValue(),bar1.getLastOpenTimeValue());
      }
      
      private function clearBarData() : void
      {
         var bar0:WilderFatherBar = null;
         for each(bar0 in this.barArr)
         {
            bar0.clearAllData();
         }
      }
      
      public function fleshTxt() : void
      {
         this.keyTxt.htmlText = ComMethod.color(Gaming.PG.da.wilder.saveGroup.keyNum + "","#00FF00",14) + " 可用于兑换大部分秘境的次数";
      }
      
      public function fleshGrip(bossName0:String) : void
      {
         var grip0:WilderGrip = this.getGrip(bossName0);
         if(Boolean(grip0))
         {
            grip0.inData_wilder(grip0.itemsData as WilderData);
         }
      }
      
      private function getGrip(bossName0:String) : WilderGrip
      {
         var bar0:WilderFatherBar = null;
         var grip0:WilderGrip = null;
         for each(bar0 in this.barArr)
         {
            grip0 = bar0.getBtnByLabel(bossName0) as WilderGrip;
            if(Boolean(grip0))
            {
               return grip0;
            }
         }
         return null;
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var da0:WilderData = null;
         var btn0:NormalBtn = e.child as NormalBtn;
         if(btn0.actived)
         {
            da0 = e.childData as WilderData;
            if(da0.def.isGM3())
            {
               if(da0.getTimeState() == 0)
               {
                  UIShow.showApp("anniverGm");
               }
            }
            else
            {
               this.bossBoard.showWilderData(da0);
            }
         }
      }
      
      private function gripOver(e:ClickEvent) : void
      {
         var da0:WilderData = e.childData as WilderData;
         var timeStr0:String = Gaming.PG.da.time.getReadTimeDate().getStr();
         Gaming.uiGroup.tipBox.textTip.showFollowText(da0.getMouseTip(timeStr0));
      }
      
      private function gripOut(e:ClickEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function gripTipOver(e:ClickEvent) : void
      {
         var d0:WilderFatherDefine = e.fatherData as WilderFatherDefine;
         if(d0.info != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(d0.info);
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      private function gripTipOut(e:ClickEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function gripTipClick(e:ClickEvent) : void
      {
         var d0:WilderFatherDefine = e.fatherData as WilderFatherDefine;
         if(d0.name == "dryTown")
         {
            Gaming.uiGroup.forgingUI.gotoThingsCompose("rocketMammoth");
         }
      }
      
      public function getNowData() : WilderData
      {
         return this.bossBoard.getNowData();
      }
      
      private function keyTxtOver(e:MouseEvent) : void
      {
         var tip0:String = "秘境钥匙数量，秘境钥匙可兑换任意一个副本(包括未开放)的挑战次数。";
         tip0 += "\n1、每天登陆可获得一定数量的钥匙。";
         tip0 += "\n2、钥匙数量超过100把之后将不会获得钥匙。";
         Gaming.uiGroup.tipBox.textTip.showFollowText(tip0);
      }
      
      private function keyTxtOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function linkClick(e:TextEvent) : void
      {
         navigateToURL(new URLRequest("https://my.4399.com/forums/thread-56779156"),"blank");
      }
      
      private function fightBtnClick(e:MouseEvent) : void
      {
         this.wilderData.addKeyNum();
         this.fleshTxt();
         Gaming.uiGroup.alertBox.showSuccess("成功获得1把秘境钥匙！");
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.hide();
      }
      
      public function FTimerSecond() : void
      {
      }
   }
}

