package UI.count.beforeDps
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.base.AutoNormalUI;
   import dataAll._player.count.props.DpsCountCtrl;
   import dataAll._player.count.props.DpsImageData;
   import flash.display.Sprite;
   
   public class OneDpsCountBox extends AutoNormalUI
   {
      private var txtTag:Sprite;
      
      private var lineTag:Sprite;
      
      private var txtBox:ItemsGripBox = new ItemsGripBox();
      
      private var countImg:DpsCountShape = new DpsCountShape();
      
      private var dataArr:Array = [];
      
      public function OneDpsCountBox()
      {
         super();
         mcTypeArr = ["tag"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         addChild(this.countImg);
         this.countImg.x = this.lineTag.x;
         this.countImg.y = this.lineTag.y;
         addChild(this.txtBox);
         this.txtBox.x = this.txtTag.x;
         this.txtBox.y = this.txtTag.y;
         this.txtBox.arg.init(2,2,0,-3);
         this.txtBox.imgType = "dpsCountTxtBar";
         this.txtBox.evt.setWantEvent(false,false,false,false,false);
         this.txtBox.setNowGripNum(4);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function fleshTxt() : void
      {
         var grip0:ItemsGrid = null;
         var da0:DpsImageData = null;
         for(var i:int = 0; i < this.txtBox.gripArr.length; i++)
         {
            grip0 = this.txtBox.gripArr[i];
            da0 = this.dataArr[i];
            if(Boolean(da0))
            {
               grip0.setName(da0.getStr());
            }
            else
            {
               grip0.setName("");
            }
         }
      }
      
      public function FTimerSecond() : void
      {
         var imgArr0:Array = null;
         if(visible)
         {
            this.dataArr = DpsCountCtrl.getImageDataArray(UILabel);
            this.fleshTxt();
            imgArr0 = DpsCountCtrl.getImageDataArray(UILabel,4);
            this.countImg.flesh(imgArr0);
         }
      }
   }
}

