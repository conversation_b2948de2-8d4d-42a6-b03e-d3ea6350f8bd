package UI.city.food
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.tip.TipBox;
   import dataAll._app.food.FoodBookAgent;
   import dataAll._app.food.FoodData;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class FoodBookBoard extends AutoNormalUI
   {
      private var coverSp:Sprite;
      
      private var effectMc:MovieClip;
      
      private var gripTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var numTxt:TextField;
      
      private var deductBtn:NormalBtn = new NormalBtn();
      
      private var addBtn:NormalBtn = new NormalBtn();
      
      private var itemsBox:ItemsGripBox = new ItemsGripBox();
      
      private var nowAgent:FoodBookAgent = null;
      
      private var nowGrip:ItemsGrid = null;
      
      public function FoodBookBoard()
      {
         super();
         mcTypeArr = ["txt","tag"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["coverSp"];
         super.setImg(img0);
         this.coverSp.addChild(this.deductBtn);
         this.deductBtn.setImg(this.coverSp["deductBtn"]);
         this.deductBtn.addEventListener(MouseEvent.CLICK,this.eatClick);
         this.coverSp.addChild(this.addBtn);
         this.addBtn.setImg(this.coverSp["addBtn"]);
         this.addBtn.addEventListener(MouseEvent.CLICK,this.composeClick);
         this.coverSp.visible = false;
         this.coverSp.addEventListener(MouseEvent.ROLL_OUT,this.coverOut);
         this.effectMc = this.coverSp["effectMc"];
         this.effectMc.stop();
         this.effectMc.visible = false;
         this.effectMc.mouseChildren = false;
         this.effectMc.mouseEnabled = false;
         addChild(this.itemsBox);
         this.itemsBox.setIconPro("FoodUI/bookGrip");
         this.itemsBox.arg.init(2,5,9,9);
         this.itemsBox.evt.setWant(false,true);
         this.itemsBox.addEventListener(ClickEvent.ON_OVER,this.gripOver);
         this.itemsBox.addEventListener(ClickEvent.ON_OUT,this.gripOut);
         this.itemsBox.x = this.gripTag.x;
         this.itemsBox.y = this.gripTag.y;
         this.itemsBox.pageBox.setToSmall();
         this.itemsBox.setPagePos(this.pageTag);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      protected function get foodData() : FoodData
      {
         return Gaming.PG.da.food;
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshItemsBox();
      }
      
      public function outLoginEvent() : void
      {
         this.nowAgent = null;
         this.nowGrip = null;
      }
      
      private function fleshItemsBox() : void
      {
         var aarr0:Array = this.foodData.getBookArr();
         this.itemsBox.inData_byArr(aarr0,this.dealGrip,false);
         this.numTxt.text = this.foodData.getUnlockText();
      }
      
      private function dealGrip(grip0:NormalBtn, a0:FoodBookAgent) : void
      {
         var iconUrlArr0:Array = null;
         var x0:int = 0;
         var iconUrl0:* = null;
         var icon0:Sprite = null;
         var num0:int = a0.num;
         grip0.itemsData = a0;
         grip0.setName(a0.cnName);
         grip0.setIconName(a0.iconUrl);
         grip0.setNumText(num0 > 0 ? num0 + "" : "");
         grip0.setSmallIcon(a0.getSmallIconLabel());
         grip0.starMc.visible = true;
         if(grip0.starMc.numChildren <= 1)
         {
            iconUrlArr0 = a0.getRawIconArr();
            x0 = 0;
            for each(iconUrl0 in iconUrlArr0)
            {
               icon0 = Gaming.swfLoaderManager.getResourceFull(iconUrl0);
               icon0.x = x0;
               x0 += icon0.width;
               grip0.starMc.addChild(icon0);
            }
         }
      }
      
      private function gripOver(e:ClickEvent) : void
      {
         var grip0:ItemsGrid = null;
         var tipBox0:TipBox = null;
         grip0 = e.child as ItemsGrid;
         var a0:FoodBookAgent = e.childData as FoodBookAgent;
         if(Boolean(grip0) && Boolean(a0))
         {
            this.nowAgent = a0;
            this.nowGrip = grip0;
            tipBox0 = Gaming.uiGroup.tipBox;
            tipBox0.textTip.setText(a0.getGatherTip());
            tipBox0.textTip.show();
            tipBox0.setPositionBySp(grip0);
            if(a0.unlockB)
            {
               this.itemsBox.addChild(this.coverSp);
               this.coverSp.x = grip0.x;
               this.coverSp.y = grip0.y;
               this.coverSp.visible = true;
               this.fleshCoverBtn();
            }
            else
            {
               this.coverSp.visible = false;
            }
         }
      }
      
      private function gripOut(e:ClickEvent) : void
      {
         if(!this.coverSp.visible)
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      private function fleshCoverBtn() : void
      {
         this.addBtn.actived = this.nowAgent.composeEnoughB();
         if(this.nowAgent.dayEatPan())
         {
            this.deductBtn.actived = false;
            this.deductBtn.setName("已进食");
         }
         else
         {
            this.deductBtn.setName("食用");
            if(this.nowAgent.num > 0)
            {
               this.deductBtn.actived = true;
            }
            else
            {
               this.deductBtn.actived = false;
            }
         }
      }
      
      private function coverOut(e:MouseEvent) : void
      {
         this.coverSp.visible = false;
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function eatClick(e:MouseEvent) : void
      {
         var a0:FoodBookAgent = null;
         if(Boolean(this.nowAgent) && Boolean(this.nowGrip))
         {
            if(this.foodData.save.eatNum >= this.foodData.getEatMax())
            {
               Gaming.uiGroup.alertBox.showError("今日进食次数已用完，无法再进食了。");
            }
            else
            {
               a0 = this.foodData.getEatBookAgent();
               if(Boolean(a0))
               {
                  Gaming.uiGroup.alertBox.showChoose("当前有食物还没消耗完，是否要立即进食？\n进食后，原本食物的效果将被覆盖。",this.doEat,null,a0.iconUrl);
               }
               else
               {
                  this.doEat();
               }
            }
         }
      }
      
      private function doEat() : void
      {
         var bb0:Boolean = this.foodData.eatBook(this.nowAgent.name);
         if(bb0)
         {
            this.fleshItemsBox();
            this.fleshCoverBtn();
            Gaming.uiGroup.foodUI.outfleshData(true);
            Gaming.soundGroup.playSound("uiSound","swapHero");
         }
      }
      
      private function composeClick(e:MouseEvent) : void
      {
         if(Boolean(this.nowAgent) && Boolean(this.nowGrip))
         {
            if(this.nowAgent.composeEnoughB())
            {
               this.foodData.composeBook(this.nowAgent.name);
               this.fleshCoverBtn();
               this.fleshItemsBox();
               Gaming.uiGroup.foodUI.outfleshData();
               this.effectMc.visible = true;
               this.effectMc.gotoAndPlay(1);
               Gaming.soundGroup.playSound("uiSound","success");
            }
         }
      }
   }
}

