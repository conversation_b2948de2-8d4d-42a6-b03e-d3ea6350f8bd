package UI.vip
{
   import UI.UIOrder;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.event.ClickEvent;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.gift.save.GiftSave;
   import flash.display.Sprite;
   
   public class VipExcBox extends AutoNormalUI
   {
      private var giftBox:ItemsGripBox = new ItemsGripBox();
      
      public function VipExcBox()
      {
         super();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function get giftSave() : GiftSave
      {
         return Gaming.PG.save.gift;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         this.giftBox.x = 25;
         this.giftBox.y = 25;
         this.addChild(this.giftBox);
         this.giftBox.arg.init(2,3,25,25);
         this.giftBox.setIconPro("VipUI/excBtn");
         this.giftBox.evt.setWant(true,true);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.giftBox);
         this.giftBox.addEventListener(ClickEvent.ON_CLICK,this.boxClick);
         this.giftBox.setNowGripNum(3);
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      private function fleshData() : void
      {
         var garr0:Array = Gaming.defineGroup.gift.getArrByFather("vipExc");
         this.giftBox.inData_byArr(garr0,this.gripFun);
      }
      
      private function gripFun(grip0:ItemsGrid, gd0:GiftAddDefineGroup) : void
      {
         var d0:GiftAddDefine = gd0.arr[0];
         grip0.inData_gift(d0);
         grip0.secData = gd0;
         grip0.activedAndEnabled = false;
         var getNum0:int = this.giftSave.getGiftGetNum(gd0.name);
         var total0:int = Gaming.PG.da.main.totalRecharged;
         var mustB0:Boolean = total0 >= gd0.mustLevel;
         grip0.setLevelText("累计充值满足\n   " + gd0.mustLevel,true);
         if(getNum0 > 0)
         {
            grip0.setName("已领取");
            grip0.actived = false;
            grip0.setGrayFilter(false);
         }
         else if(mustB0)
         {
            grip0.setName("领取");
            grip0.setGrayFilter(false);
            grip0.actived = true;
         }
         else
         {
            grip0.setName("未达标");
            grip0.setGrayFilter(true);
            grip0.actived = false;
         }
      }
      
      private function boxClick(e0:ClickEvent) : void
      {
         var gd0:GiftAddDefineGroup = null;
         var getNum0:int = 0;
         var total0:int = 0;
         var mustB0:Boolean = false;
         var error0:String = null;
         var grip0:ItemsGrid = e0.child as ItemsGrid;
         if(grip0.actived)
         {
            gd0 = grip0.secData as GiftAddDefineGroup;
            getNum0 = this.giftSave.getGiftGetNum(gd0.name);
            total0 = Gaming.PG.da.main.totalRecharged;
            mustB0 = total0 >= gd0.mustLevel;
            if(getNum0 == 0 && mustB0)
            {
               error0 = GiftAddit.bagSpacePan(gd0);
               if(error0 == "")
               {
                  this.giftSave.addGiftGetNum(gd0.name);
                  GiftAddit.add(gd0,"领取成功！");
                  this.fleshData();
               }
               else
               {
                  UIOrder.alertError(error0);
               }
            }
         }
      }
   }
}

