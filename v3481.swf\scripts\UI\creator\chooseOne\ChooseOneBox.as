package UI.creator.chooseOne
{
   import UI.base.tip.OneTextGather;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.ui.choose.ChooseOneDefineGroup;
   import flash.display.Sprite;
   import flash.events.TextEvent;
   import flash.text.TextField;
   
   public class ChooseOneBox extends Sprite
   {
      private var titleTxt:TextField = new TextField();
      
      private var txt:TextField = new TextField();
      
      private var define:ChooseOneDefineGroup;
      
      public var selectedArr:Array = [];
      
      public var maxWidth:int = 490;
      
      public function ChooseOneBox()
      {
         super();
         addChild(this.titleTxt);
         OneTextGather.setNormalFormat(this.titleTxt);
         this.titleTxt.textColor = 65535;
         this.titleTxt.styleSheet = ComMethod.getLinkCss("#00FF00","#00FF00");
         addChild(this.txt);
         this.txt.x = 5;
         this.txt.y = 25;
         OneTextGather.setNormalFormat(this.txt);
         this.txt.styleSheet = ComMethod.getLinkCss("#CCCCCC","#FFFFFF");
         this.titleTxt.addEventListener(TextEvent.LINK,this.linkClick);
         this.txt.addEventListener(TextEvent.LINK,this.linkClick);
      }
      
      public function inData(dg0:ChooseOneDefineGroup) : void
      {
         this.clearAll();
         this.define = dg0;
         this.flesh();
      }
      
      public function isFillB() : Boolean
      {
         return this.selectedArr.length >= this.define.max;
      }
      
      private function flesh() : void
      {
         this.titleTxt.htmlText = this.define.getSelectedString(this.selectedArr);
         this.txt.htmlText = this.define.getChooseString(this.selectedArr);
         this.txt.wordWrap = true;
         this.txt.width = this.maxWidth;
         this.drawRect(this.txt.x,this.txt.y,this.maxWidth,this.txt.height);
      }
      
      private function drawRect(x0:int, y0:int, w0:int, h0:int) : void
      {
         this.graphics.clear();
         this.graphics.beginFill(16777215,0.2);
         this.graphics.beginFill(16777215,0.1);
         this.graphics.drawRect(x0 - 4,y0 - 4,w0 + 8,h0 + 8);
      }
      
      public function clearAll() : void
      {
         this.titleTxt.text = "";
         this.txt.text = "";
         this.define = null;
         this.selectedArr.length = 0;
      }
      
      private function linkClick(e:TextEvent) : void
      {
         var name0:String = e.text;
         var selectedIndex0:int = int(this.selectedArr.indexOf(name0));
         if(selectedIndex0 >= 0)
         {
            this.selectedArr.splice(selectedIndex0,1);
         }
         else
         {
            this.selectedArr.push(name0);
            if(this.selectedArr.length > this.define.max)
            {
               this.selectedArr.splice(0,this.selectedArr.length - this.define.max);
            }
         }
         this.flesh();
         var e0:TextEvent = e.clone() as TextEvent;
         dispatchEvent(e0);
      }
   }
}

