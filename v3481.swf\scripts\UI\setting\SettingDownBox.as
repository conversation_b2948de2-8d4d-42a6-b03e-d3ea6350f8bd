package UI.setting
{
   import UI.base.btnList.BtnBox;
   import UI.base.button.NormalBtn;
   import flash.display.Sprite;
   import flash.display.StageQuality;
   import flash.events.MouseEvent;
   
   public class SettingDownBox extends BtnBox
   {
      private var dataObj:Object = {};
      
      public function SettingDownBox()
      {
         super();
         this.dataObj["sound"] = [{
            "cn":"音效开",
            "icon":"BasicUI/soundOn",
            "value":true
         },{
            "cn":"音效关",
            "icon":"BasicUI/soundOff",
            "value":false
         }];
         this.dataObj["music"] = [{
            "cn":"音乐开",
            "icon":"BasicUI/musicOn",
            "value":true
         },{
            "cn":"音乐关",
            "icon":"BasicUI/musicOff",
            "value":false
         }];
         this.dataObj["quality"] = [{
            "cn":"画质高",
            "icon":"BasicUI/qualityHigh",
            "value":StageQuality.HIGH
         },{
            "cn":"画质中",
            "icon":"BasicUI/qualityMiddle",
            "value":StageQuality.MEDIUM
         },{
            "cn":"画质低",
            "icon":"BasicUI/qualityLow",
            "value":StageQuality.LOW
         }];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      public function fleshData() : void
      {
         var n:* = undefined;
         var value0:* = undefined;
         var i:* = undefined;
         var obj0:Object = null;
         for(n in this.dataObj)
         {
            value0 = this.getData(n);
            for(i in this.dataObj[n])
            {
               obj0 = this.dataObj[n][i];
               if(obj0.value == value0)
               {
                  getBtn(n).setName(obj0.cn);
                  getBtn(n).setIconName(obj0.icon);
                  break;
               }
            }
         }
      }
      
      private function getData(name0:String) : *
      {
         if(name0 == "sound")
         {
            return Gaming.soundGroup.soundOn;
         }
         if(name0 == "quality")
         {
            if(Boolean(Gaming.PG.save))
            {
               return Gaming.PG.save.setting.quality;
            }
            return Gaming.ME.stage.quality.toLocaleLowerCase();
         }
         if(name0 == "music")
         {
            return Gaming.soundGroup.musicOn;
         }
      }
      
      private function setData(name0:String, value0:*) : void
      {
         if(name0 == "sound")
         {
            Gaming.soundGroup.soundOn = value0;
         }
         else if(name0 == "quality")
         {
            if(Boolean(Gaming.PG.save))
            {
               Gaming.PG.save.setting.setQuality(value0);
            }
            Gaming.ME.stage.quality = value0;
            Gaming.uiGroup.settingUI.qualityBox.fleshData();
         }
         else if(name0 == "music")
         {
            Gaming.soundGroup.musicOn = value0;
         }
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var n:* = undefined;
         var obj2:Object = null;
         var obj0:Object = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         var arr0:Array = this.dataObj[btn0.label];
         var value0:* = this.getData(btn0.label);
         var index0:int = 0;
         for(n in arr0)
         {
            obj0 = arr0[n];
            if(obj0.value == value0)
            {
               index0 = n;
               break;
            }
         }
         obj2 = arr0[(index0 + 1) % arr0.length];
         this.setData(btn0.label,obj2.value);
         this.fleshData();
      }
   }
}

