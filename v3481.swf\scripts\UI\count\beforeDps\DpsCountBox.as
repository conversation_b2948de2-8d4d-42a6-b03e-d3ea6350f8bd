package UI.count.beforeDps
{
   import UI.base.AutoNormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import dataAll._player.count.props.DpsCountCtrl;
   import flash.display.Sprite;
   
   public class DpsCountBox extends AutoNormalUI
   {
      private var labelTag:Sprite;
      
      private var labelBox:LabelBox = new LabelBox();
      
      private var labelArr:Array = DpsCountCtrl.labelArr.concat([]);
      
      private var boxObj:Object = {};
      
      public function DpsCountBox()
      {
         super();
         mcTypeArr = ["tag"];
         this.mouseChildren = false;
         this.mouseEnabled = false;
      }
      
      override protected function firstLoad() : void
      {
         this.setImg(Gaming.uiGroup.getBasicMovieClip("dpsCountBox"));
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var label0:* = null;
         var box0:OneDpsCountBox = null;
         super.setImg(img0);
         var labelArr0:Array = this.labelArr;
         addChild(this.labelBox);
         this.labelBox.arg.init(4,1,1,0);
         this.labelBox.evt.setWantEvent(true,false,false,false,false);
         this.labelBox.inData("dpsCountLabelBar",labelArr0,[],true);
         this.labelBox.setChoose_byIndex(0);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.labelBox.x = this.labelTag.x;
         this.labelBox.y = this.labelTag.y;
         for each(label0 in labelArr0)
         {
            box0 = new OneDpsCountBox();
            box0.setImg(Gaming.uiGroup.getBasicMovieClip("oneDpsCountBox"));
            addChild(box0);
            this.boxObj[label0] = box0;
            box0.UILabel = label0;
            box0.hide();
         }
         this.labelBox.nowLabel = this.labelArr[0];
         hide();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      public function open() : void
      {
         DpsCountCtrl.enabled = true;
         this.show();
      }
      
      public function openOrHide() : void
      {
         DpsCountCtrl.openOrClose();
         showOrHide();
         Gaming.uiGroup.showStat(!visible);
      }
      
      override public function show() : void
      {
         super.show();
         this.showBox(this.labelBox.nowLabel);
      }
      
      public function close() : void
      {
         DpsCountCtrl.enabled = false;
         hide();
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         var label0:String = e.label;
         this.showBox(label0);
      }
      
      private function showBox(label0:String) : void
      {
         var box0:OneDpsCountBox = null;
         for each(box0 in this.boxObj)
         {
            if(box0.UILabel != label0)
            {
               box0.hide();
            }
            else
            {
               box0.show();
            }
         }
         this.labelBox.setChoose(label0);
      }
      
      public function FTimerSecond() : void
      {
         var box0:OneDpsCountBox = null;
         if(DpsCountCtrl.enabled)
         {
            if(visible)
            {
               for each(box0 in this.boxObj)
               {
                  box0.FTimerSecond();
               }
            }
         }
      }
   }
}

