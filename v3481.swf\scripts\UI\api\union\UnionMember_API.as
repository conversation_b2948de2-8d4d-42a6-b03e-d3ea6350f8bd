package UI.api.union
{
   import com.adobe.serialization.json.JSON2;
   import dataAll._app.union.info.MemberInfo;
   import dataAll._app.union.info.MemberListInfo;
   import unit4399.events.UnionEvent;
   
   public class UnionMember_API extends UnionBase_API
   {
      public function UnionMember_API()
      {
         super();
      }
      
      public function getUnionMembers(idx:int, unionId:int, yesFun0:Function = null, noFun0:Function = null) : void
      {
         setFun(yesFun0,noFun0,"getUnionMembers");
         if(Gaming.isLocal())
         {
            this.yes_getUnionMembers(MemberListInfo.getSimulatedJson(25));
         }
         else
         {
            serviceHold.getUnionMembers(idx,unionId);
         }
      }
      
      private function yes_getUnionMembers(jsonStr0:String) : void
      {
         doYesFun(jsonStr0,"getUnionMembers");
      }
      
      public function setMemberExtra(idx:int, type:int, extra:String, unionId:int = 0, userId:Number = 0, userIndex:int = 0, yesFun0:Function = null, noFun0:Function = null) : void
      {
         setFun(yesFun0,noFun0,"setMemberExtra");
         if(Gaming.isLocal())
         {
            MemberInfo.setSimulated(extra);
            this.yes_setMemberExtra(true);
         }
         else
         {
            serviceHold.setMemberExtra(idx,type,extra,unionId,String(userId),userIndex);
         }
      }
      
      private function yes_setMemberExtra(bb0:Boolean) : void
      {
         doYesFun(bb0,"setMemberExtra");
      }
      
      public function setUnionExtra(idx:int, type:int, extra:String, unionId:int, yesFun0:Function = null, noFun0:Function = null) : void
      {
         setFun(yesFun0,noFun0,"setUnionExtra");
         if(Gaming.isLocal())
         {
            this.yes_setUnionExtra(true);
         }
         else
         {
            serviceHold.setUnionExtra(idx,type,extra,unionId);
         }
      }
      
      private function yes_setUnionExtra(bb0:Boolean) : void
      {
         doYesFun(bb0,"setUnionExtra");
      }
      
      public function getUnionLog(idx:int, pageNum:int, pageSize:int, yesFun0:Function = null, noFun0:Function = null) : void
      {
         var obj0:Object = null;
         setFun(yesFun0,noFun0,"getUnionLog");
         if(Gaming.isLocal())
         {
            obj0 = {
               "logList":[{
                  "type":"createUnion",
                  "username":"yjtest",
                  "time":1387772690,
                  "title":"妖精尾巴"
               },{
                  "time":1387772690,
                  "username":"yjtest",
                  "type":"joinUnion"
               }],
               "rowCount":2
            };
            this.yes_getUnionLog(JSON2.encode(obj0));
         }
         else
         {
            serviceHold.getUnionLog(idx,pageNum,pageSize);
         }
      }
      
      private function yes_getUnionLog(jsonStr0:String) : void
      {
         doYesFun(jsonStr0,"getUnionLog");
      }
      
      public function quitUion(idx:int, yesFun0:Function = null, noFun0:Function = null) : void
      {
         setFun(yesFun0,noFun0);
         if(Gaming.isLocal())
         {
            this.yes_quitUion(true);
         }
         else
         {
            serviceHold.quitUion(idx);
         }
      }
      
      private function yes_quitUion(bb0:Boolean) : void
      {
         doYesFun(bb0);
      }
      
      public function usePersonalContribution(idx:int, contribution:int) : void
      {
         serviceHold.usePersonalContribution(idx,contribution);
      }
      
      private function yes_usePersonalContribution(num0:int) : void
      {
      }
      
      public function setRole(idx:int, uid:Number, index:int, roleId:Number, yesFun0:Function = null, noFun0:Function = null) : void
      {
         setFun(yesFun0,noFun0,"setRole");
         if(Gaming.isLocal())
         {
            this.yes_setRole(true);
         }
         else if(serviceHold.hasOwnProperty("setRole"))
         {
            serviceHold.setRole(idx,uid,index,roleId);
         }
         else
         {
            Gaming.api.union.doError("60001");
         }
      }
      
      private function yes_setRole(bb0:Boolean) : void
      {
         doYesFun(bb0,"setRole");
      }
      
      public function onMemberSuccess(e:UnionEvent) : void
      {
         var dataObj:Object = e.data;
         var data0:* = dataObj.data;
         switch(dataObj.apiName)
         {
            case UnionEvent.UNI_API_BHMX:
            case UnionEvent.UNI_API_BHCY:
               this.yes_getUnionMembers(data0);
            case UnionEvent.UNI_API_CYTZBG:
               this.yes_setMemberExtra(data0);
            case UnionEvent.UNI_API_BHTZBG:
               this.yes_setUnionExtra(data0);
            case UnionEvent.UNI_API_BHRZ:
               this.yes_getUnionLog(data0);
            case UnionEvent.UNI_API_TCBH:
               this.yes_quitUion(data0);
            case UnionEvent.UNI_API_XHGRGXD:
               this.yes_usePersonalContribution(data0);
            case UnionEvent.UNI_API_SZJS:
               this.yes_setRole(data0);
         }
      }
   }
}

