package UI.forging.deviceUpgrade
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.must.NormalMustBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.device.DeviceData;
   import dataAll.equip.device.DeviceDataCreator;
   import dataAll.equip.device.DeviceDefine;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.skill.define.HeroSkillDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class DeviceUpgradeBoard extends NormalUI
   {
      private var btn:NormalBtn = new NormalBtn();
      
      private var infoBtnSp:MovieClip;
      
      private var infoBtn:NormalBtn = new NormalBtn();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var skillGrip:ItemsGrid = new ItemsGrid();
      
      public var nowData:DeviceData = null;
      
      private var mustBoxSp:Sprite;
      
      private var btnSp:MovieClip;
      
      private var gripTag:Sprite;
      
      private var nameTxt:TextField;
      
      private var infoTxt:TextField;
      
      private var beforeTxt:TextField;
      
      private var nextTxt:TextField;
      
      public function DeviceUpgradeBoard()
      {
         super();
         addChild(this.btn);
         addChild(this.mustBox);
         addChild(this.skillGrip);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.skillGrip);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["mustBoxSp","btnSp","infoBtnSp","gripTag","nameTxt","infoTxt","beforeTxt","nextTxt"];
         super.setImg(img0);
         this.mustBox.setImg(this.mustBoxSp);
         this.btn.setImg(this.btnSp);
         this.btn.setName("进阶");
         addChild(this.infoBtn);
         this.infoBtn.setImg(this.infoBtnSp);
         this.infoBtn.setName("进阶所需");
         ItemsGripTipCtrl.addNormalBtnTip(this.infoBtn);
         this.skillGrip.setImgToEquipGrip();
         NormalUICtrl.setTag(this.skillGrip,this.gripTag);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,"device");
         this.showOneDeviceDataAndPan(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      public function equipGripClick(da0:DeviceData) : void
      {
         if(visible)
         {
            this.showOneDeviceDataAndPan(da0);
         }
      }
      
      private function showOneDeviceDataAndPan(da0:DeviceData) : void
      {
         var dg0:EquipDataGroup = null;
         this.showNone();
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择要进阶的装置。");
         if(Boolean(da0))
         {
            dg0 = Gaming.PG.da.findEquipData(da0);
            if(dg0 is EquipDataGroup)
            {
               this.showOneDeviceData(da0);
            }
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      private function showOneDeviceData(da0:DeviceData) : void
      {
         var must_d0:MustDefine = null;
         var bb0:Boolean = false;
         this.nowData = da0;
         var deviceD0:DeviceDefine = da0.deviceDefine;
         var d0:HeroSkillDefine = deviceD0.getSkillDefine();
         var next_d0:HeroSkillDefine = deviceD0.getNextSkillDefine();
         this.mustBox.setMeMoreDef(da0.getNowNum() > 1 ? deviceD0 : null);
         this.skillGrip.inData_equip(da0);
         this.skillGrip.setNumText("");
         this.nameTxt.text = d0.cnName;
         this.infoTxt.text = d0.getDescriptionNoActiveCd();
         this.beforeTxt.htmlText = ComMethod.color("当前第" + d0.lv + "级","#FF9900") + "\n" + d0.getNowChangeText();
         if(!next_d0)
         {
            this.mustBox.setShowState(false);
            this.btn.actived = false;
            this.nextTxt.htmlText = ComMethod.color("已进阶至最高等级了","#FF9900");
         }
         else
         {
            this.nextTxt.htmlText = ComMethod.color("进阶后第" + next_d0.lv + "级","#FF9900") + "\n" + next_d0.getNowChangeText();
            must_d0 = da0.getUpradeMust();
            bb0 = this.mustBox.inData(must_d0);
            this.btn.actived = bb0;
         }
         this.infoBtn.tipString = DeviceDataCreator.getMustTip(deviceD0);
      }
      
      private function showNone() : void
      {
         this.infoBtn.tipString = "";
         this.skillGrip.clearData();
         this.nameTxt.text = "";
         this.infoTxt.text = "";
         this.beforeTxt.text = "";
         this.nextTxt.text = "";
         this.mustBox.setShowState(false);
         this.btn.actived = false;
      }
      
      public function btnClick(e:MouseEvent) : void
      {
         var mustBagNum0:int = 0;
         var must_d0:MustDefine = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0.actived)
         {
            if(btn0 == this.btn)
            {
               if(Boolean(this.nowData))
               {
                  mustBagNum0 = 0;
                  if(this.nowData.placeType == "bag")
                  {
                     mustBagNum0 = 1;
                  }
                  if(Gaming.PG.da.equipBag.getSpaceSiteNum() < mustBagNum0)
                  {
                     Gaming.uiGroup.alertBox.showError("当前装备背包至少需要 " + ComMethod.color(mustBagNum0 + "个","#00FF00") + " 空位！");
                  }
                  else
                  {
                     must_d0 = this.nowData.getUpradeMust();
                     PlayerMustCtrl.deductMust(must_d0,this.affter_click);
                  }
               }
            }
         }
      }
      
      private function affter_click() : void
      {
         var upgradeData0:DeviceData = this.nowData.getUpradeData();
         if(this.nowData.getNowNum() <= 1)
         {
            this.nowData.changeToOneData(upgradeData0);
         }
         else
         {
            this.nowData.addNowNum(-1);
            this.nowData = Gaming.PG.da.equipBag.addHaveNumData(upgradeData0) as DeviceData;
         }
         this.show();
         Gaming.uiGroup.alertBox.showSuccess("进阶成功！");
      }
   }
}

