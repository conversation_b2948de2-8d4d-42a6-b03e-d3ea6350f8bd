package w_test.cheating
{
   public class PostCheating extends OneCheating
   {
      public function PostCheating()
      {
         super();
      }
      
      public function addPostExp(str0:String, v0:int) : String
      {
         Gaming.PG.da.post.save.addPostExp(v0);
         return "添加职务经验：" + v0;
      }
      
      public function setPostLv(str0:String, v0:int) : String
      {
         if(v0 == 0)
         {
            return "";
         }
         Gaming.PG.da.post.save.postLv = v0;
         return "设置职务等级：" + v0;
      }
   }
}

