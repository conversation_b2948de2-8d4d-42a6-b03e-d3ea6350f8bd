package UI.edit
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.base.AutoNormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.numChoose.NumChooseBox;
   import dataAll._app.edit.boss.BossEditData;
   import dataAll._app.edit.boss.BossEditDataGroup;
   import dataAll._app.edit.boss.BossEditMethod;
   import dataAll._app.edit.boss.BossTaskDefine;
   import dataAll._app.edit.boss.TaskBossEditDataGroup;
   import dataAll._app.task.TaskState;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.Sprite;
   import flash.text.TextField;
   import flash.text.TextFieldAutoSize;
   
   public class BosseditLevelBoard extends AutoNormalUI
   {
      private var titleTxt:TextField;
      
      private var infoTxt:TextField;
      
      private var pageTag:Sprite;
      
      private var barTag:Sprite;
      
      private var box:ItemsGripBox = new ItemsGripBox();
      
      private var nowData:BossEditData = null;
      
      public function BosseditLevelBoard()
      {
         super();
         mcTypeArr = ["tag","txt","btnSp"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         FontDeal.dealOne(this.titleTxt);
         this.infoTxt.autoSize = TextFieldAutoSize.LEFT;
         FontDeal.dealLine(this.infoTxt);
         this.box.setIconPro("BosseditUI/levelBar");
         this.box.arg.init(1,6,0,-1,false);
         this.box.evt.setWant(true,true);
         addChild(this.box);
         NormalUICtrl.setTag(this.box,this.barTag);
         this.box.addEventListener(ClickEvent.ON_CLICK,this.barClick);
         this.box.addEventListener(ClickEvent.ON_OVER,this.barOver);
         this.box.addEventListener(ClickEvent.ON_OUT,this.barOut);
         this.box.pageBox.setToNormalBtn();
         this.box.setPagePos(this.pageTag);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function get dataG() : BossEditDataGroup
      {
         return Gaming.PG.da.bossEdit;
      }
      
      private function get taskG() : TaskBossEditDataGroup
      {
         return Gaming.PG.da.bossEdit.getTask();
      }
      
      public function outLoginEvent() : void
      {
         this.nowData = null;
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      private function fleshData() : void
      {
         this.fleshBox();
         this.fleshLeft();
      }
      
      private function fleshLeft() : void
      {
         this.titleTxt.text = "战场数据";
         this.infoTxt.htmlText = FontDeal.getDealLeadingStr(this.infoTxt,this.taskG.getLeftText());
      }
      
      private function fleshBox() : void
      {
         var da0:BossEditData = null;
         var state0:String = null;
         var daArr0:Array = this.taskG.getArr();
         var newArr0:Array = [];
         for each(da0 in daArr0)
         {
            state0 = da0.getTaskState();
            if(state0 != TaskState.isEnd)
            {
               newArr0.push(da0);
            }
         }
         this.box.inData_byArr(newArr0,this.inBarFun);
      }
      
      private function inBarFun(grip0:ItemsGrid, da0:BossEditData) : void
      {
         var taskDef0:BossTaskDefine = da0.getTaskDefine();
         var state0:String = da0.getTaskState();
         var g0:GiftAddDefineGroup = da0.getTaskGift(Gaming.PG);
         grip0.itemsData = da0;
         grip0.activedAndEnabled = false;
         grip0.setLevelText((Boolean(taskDef0) ? taskDef0.name + " - " : "") + "LV." + da0.lv);
         grip0.setName(da0.getCnName());
         grip0.setPriceText(da0.getPlayerName());
         grip0.setOtherText(da0.getLifeUI());
         grip0.setNumText(da0.getScoreUI());
         grip0.setIndexText(g0.getDescription(99,false));
         grip0.setIconName(da0.getIconUrl());
         grip0.setShopBtnBackMc(state0);
         grip0.actived = state0 == TaskState.ing || state0 == TaskState.complete || state0 == TaskState.over;
      }
      
      private function barClick(e:ClickEvent) : void
      {
         var nowDiff0:int = 0;
         var state0:String = null;
         var d0:BossTaskDefine = null;
         var diffLen0:int = 0;
         var g0:GiftAddDefineGroup = null;
         var error0:String = null;
         if(this.taskG.getWinNum() >= 10)
         {
            UIOrder.alertError("本周挑战次数已用完！");
            return;
         }
         var grip0:ItemsGrid = e.child as ItemsGrid;
         var da0:BossEditData = e.childData as BossEditData;
         if(grip0.actived && Boolean(da0))
         {
            nowDiff0 = da0.getTaskDiff();
            state0 = da0.getTaskState();
            if(state0 == TaskState.ing)
            {
               this.nowData = da0;
               d0 = da0.getTaskDefine();
               diffLen0 = d0.getDiffLen();
               Gaming.uiGroup.alertBox.showNumChoose(this.getDiffAlert(),nowDiff0 + 1,diffLen0,1,1,this.diffYesFun,"yesAndNo",this.diffChangeFun,this.fleshBox);
               Gaming.uiGroup.alertBox.setBtnText("挑战","返回");
            }
            else if(state0 == TaskState.complete)
            {
               g0 = da0.getTaskGift(Gaming.PG);
               error0 = GiftAddit.bagSpacePan(g0);
               if(error0 == "")
               {
                  da0.taskGiftEvent();
                  GiftAddit.add(g0,"领取奖励成功：\n" + g0.getDescription(99));
                  this.fleshData();
               }
               else
               {
                  UIOrder.alertError(error0);
               }
            }
            else if(state0 == TaskState.over)
            {
               this.nowData = da0;
               Gaming.uiGroup.alertBox.showChoose("是否继续挑战难度" + (nowDiff0 + 1) + "？",this.overYesFun,null,da0.getIconUrl());
            }
         }
      }
      
      private function overYesFun() : void
      {
         BossEditMethod.gotoMap(this.nowData,false);
      }
      
      private function diffChangeFun(box0:NumChooseBox) : String
      {
         var diff0:int = 0;
         if(Boolean(this.nowData))
         {
            diff0 = box0.nowNum - 1;
            this.nowData.setTaskDiff(diff0);
            return this.getDiffAlert();
         }
         return "数据错误";
      }
      
      private function getDiffAlert() : String
      {
         var da0:BossEditData = this.nowData;
         var d0:BossTaskDefine = da0.getTaskDefine();
         var nowDiff0:int = da0.getTaskDiff();
         var diffLen0:int = d0.getDiffLen();
         var g0:GiftAddDefineGroup = da0.getTaskGift(Gaming.PG);
         var s0:String = "请选择难度级别：" + (nowDiff0 + 1);
         return s0 + ("\n奖励：" + g0.getDescription(99,true));
      }
      
      private function diffYesFun(num0:Number) : void
      {
         if(Boolean(this.nowData))
         {
            this.nowData.setTaskDiff(num0 - 1);
            BossEditMethod.gotoMap(this.nowData,false);
         }
      }
      
      private function barOver(e:ClickEvent) : void
      {
         var tip0:String = "";
         var da0:BossEditData = e.childData as BossEditData;
         if(Boolean(da0))
         {
            tip0 = da0.getTaskTip(Gaming.PG);
         }
         UIOrder.showTip(tip0);
      }
      
      private function barOut(e:ClickEvent) : void
      {
         UIOrder.showTip("");
      }
   }
}

