package UI.top
{
   import UI.bag.ItemsGripBox;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import dataAll._app.top.IO_TopDataGroup;
   import dataAll._app.top.TopBarDataGroup;
   
   public class TopBarBox extends ItemsGripBox
   {
      public var otherFleshFun:Function = null;
      
      private var titleBar:TopBar = new TopBar();
      
      private var data:IO_TopDataGroup = null;
      
      private var tempSortName:String = "";
      
      public function TopBarBox()
      {
         super();
      }
      
      public function initTitle(imgType0:String, yGap0:int, sortClickB0:Boolean = false) : void
      {
         if(imgType == "")
         {
            imgType = imgType0;
            this.titleBar.setImg(getImgTypeMc());
            addChild(this.titleBar);
            this.titleBar.y = -this.titleBar.height - yGap0;
            this.titleBar.setTextArr([]);
            this.titleBar.changeToTitle();
            if(sortClickB0)
            {
               this.titleBar.addClickEvent();
               this.titleBar.addEventListener(ClickEvent.TOP_TITLE_CLICK,this.titleClick);
            }
         }
      }
      
      public function inData(da0:IO_TopDataGroup, sortName0:String = "") : void
      {
         this.data = da0;
         inData_byArr(da0.getTopArr(sortName0),da0.getTopFunName());
         this.titleBar.setTextArr(da0.getTopTitleCnArr());
         this.titleBar.changeToTitle();
      }
      
      public function inData_byTop(dg0:TopBarDataGroup) : void
      {
         this.inData(dg0);
      }
      
      private function titleClick(e:ClickEvent) : void
      {
         var text0:String = null;
         if(Boolean(this.data))
         {
            text0 = e.childData as String;
            this.inData(this.data,text0);
            this.doOtherFlesh();
         }
      }
      
      private function doOtherFlesh() : void
      {
         if(this.otherFleshFun is Function)
         {
            this.otherFleshFun();
         }
      }
      
      override protected function getNewGrip() : NormalBtn
      {
         return new TopBar();
      }
   }
}

