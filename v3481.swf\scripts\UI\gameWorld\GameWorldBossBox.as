package UI.gameWorld
{
   import UI.base.loadBar.LoadBar;
   import com.common.text.TextWay;
   import com.greensock.TweenLite;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.NumberMethod;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.level.define.unit.UnitType;
   import fl.motion.easing.Cubic;
   import flash.display.Sprite;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.data.NormalBodyData;
   
   public class GameWorldBossBox extends Sprite
   {
      private static const baseY:int = -150;
      
      private static const purpleColor:String = "#00FF00";
      
      private static const yellowColor:String = "#FFFF00";
      
      private var bossLifeBar:LoadBar = new LoadBar();
      
      private var _bossLifeTarget:IO_NormalBody = null;
      
      public var lifeMustNumberB:Boolean = false;
      
      public function GameWorldBossBox()
      {
         super();
         addChild(this.bossLifeBar);
         this.mouseChildren = false;
         this.mouseEnabled = false;
      }
      
      public function setImg(img0:Sprite) : void
      {
         this.x = img0.x;
         this.y = img0.y;
         this.bossLifeBar.setImg(img0);
         this.bossLifeBar.x = 0;
         this.bossLifeBar.y = 0;
         this.clearAll();
      }
      
      public function clearAll() : void
      {
         this.bossLifeBar.visible = false;
         this.bossLifeBar.y = baseY;
         this._bossLifeTarget = null;
         TweenLite.killTweensOf(this.bossLifeBar);
      }
      
      public function fleshEach() : void
      {
         var data0:NormalBodyData = null;
         var def0:NormalBodyDefine = null;
         var maxLife0:Number = NaN;
         var nowLife0:Number = NaN;
         var numB0:Boolean = false;
         if(Boolean(this._bossLifeTarget))
         {
            data0 = this._bossLifeTarget.getData();
            def0 = this._bossLifeTarget.getDefine();
            maxLife0 = data0.maxLife;
            nowLife0 = data0.nowLife;
            numB0 = this.lifeMustNumberB;
            if(!numB0)
            {
               if(data0.bodyLevel >= 86 && data0.unitType == UnitType.BOSS && !data0.isSaveDataPlayerB())
               {
                  numB0 = false;
               }
               else
               {
                  numB0 = true;
               }
            }
            if(!numB0)
            {
               this.bossLifeBar.setBodyPer(nowLife0 / maxLife0);
            }
            else
            {
               this.bossLifeBar.setPer(nowLife0 / maxLife0);
               this.bossLifeBar.setText(NumberMethod.toBigWan(nowLife0,purpleColor) + " / " + NumberMethod.toBigWan(maxLife0,purpleColor));
               this.bossLifeBar.setBarBack(data0.unitType);
               this.bossLifeBar.setOtherText(TextWay.numberToPer(nowLife0 / maxLife0));
            }
            this.bossLifeBar.setRightStr(data0.getCutLifeStr());
            this.bossLifeBar.setTopText(data0.getUIName() + ComMethod.color(data0.bodyLevel + "级",yellowColor));
            this.bossLifeBar.setIconMc(def0.shell);
            if(this._bossLifeTarget.getDie() > 0)
            {
               this._bossLifeTarget = null;
               TweenLite.to(this.bossLifeBar,0.5,{
                  "y":baseY,
                  "ease":Cubic.easeIn
               });
            }
         }
      }
      
      public function clearIfIsOne(b0:IO_NormalBody) : void
      {
         if(b0 == this._bossLifeTarget)
         {
            this.bossLifeTarget = null;
         }
      }
      
      public function set bossLifeTarget(b0:IO_NormalBody) : void
      {
         if(b0 == null)
         {
            this._bossLifeTarget = null;
            TweenLite.to(this.bossLifeBar,0.5,{
               "y":baseY,
               "ease":Cubic.easeIn
            });
            return;
         }
         if(b0 != this._bossLifeTarget)
         {
            this._bossLifeTarget = b0;
            this.bossLifeBar.visible = true;
            TweenLite.to(this.bossLifeBar,0.5,{
               "y":0,
               "ease":Cubic.easeOut
            });
            this.fleshSkillShow();
         }
      }
      
      public function fleshSkillShow(panBody0:IO_NormalBody = null) : void
      {
         var data0:NormalBodyData = null;
         var def0:NormalBodyDefine = null;
         if(Boolean(panBody0) && this._bossLifeTarget != panBody0)
         {
            return;
         }
         if(this._bossLifeTarget is IO_NormalBody)
         {
            data0 = this._bossLifeTarget.getData();
            def0 = this._bossLifeTarget.getDefine();
            this.bossLifeBar.setDownText(this._bossLifeTarget.getSkill().getNowSkillCnNameString());
            this.bossLifeBar.setRightText(data0.getUIRightText());
         }
      }
   }
}

