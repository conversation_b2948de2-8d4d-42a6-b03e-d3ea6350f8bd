package
{
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol460")]
   public dynamic class allBagUI extends MovieClip
   {
      public var labelTag:MovieClip;
      
      public var infoTxt:TextField;
      
      public var sortBtnSp2:MovieClip;
      
      public var gripTag:MovieClip;
      
      public var armsPageTag:MovieClip;
      
      public var titleTxt:TextField;
      
      public var closeBtn:SimpleButton;
      
      public var sortBtnSp:MovieClip;
      
      public function allBagUI()
      {
         super();
      }
   }
}

