package UI.peak
{
   import UI.NormalUICtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import UI.base.grid.NormalGridIcon;
   import UI.base.tip.TipBox;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._app.peak.IO_PointData;
   import dataAll._app.peak.PeakProDefine;
   import dataAll.skill.define.HeroSkillDefine;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class PeakProBar extends AutoNormalUI
   {
      private var nameTxt:TextField;
      
      private var addTxt:TextField;
      
      private var lvTxt:TextField;
      
      private var addBtn:NormalBtn;
      
      private var delBtn:NormalBtn;
      
      private var iconTag:Sprite;
      
      private var icon:NormalGridIcon;
      
      private var da:IO_PointData = null;
      
      private var define:PeakProDefine = null;
      
      private var fleshFun:Function;
      
      public function PeakProBar()
      {
         super();
         btnSetB = true;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         FontDeal.dealOne(this.nameTxt);
         this.addBtn.addEventListener(MouseEvent.MOUSE_OVER,this.addBtnOver);
         this.addBtn.addEventListener(MouseEvent.MOUSE_OUT,this.mouseOut);
         this.addBtn.activedAndEnabled = false;
         if(Boolean(this.iconTag))
         {
            this.icon = new NormalGridIcon();
            addChild(this.icon);
            NormalUICtrl.setTag(this.icon,this.iconTag);
            this.icon.mouseChildren = false;
            this.icon.mouseEnabled = true;
            this.icon.addEventListener(MouseEvent.MOUSE_OVER,this.iconOver);
            this.icon.addEventListener(MouseEvent.MOUSE_OUT,this.mouseOut);
         }
         this.lvTxt.addEventListener(MouseEvent.CLICK,this.lvTxtClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function get tipBox() : TipBox
      {
         return Gaming.uiGroup.tipBox;
      }
      
      public function inDefine(d0:PeakProDefine, da0:IO_PointData, fleshFun0:Function) : void
      {
         this.define = d0;
         this.da = da0;
         this.fleshFun = fleshFun0;
      }
      
      public function fleshDataBy(da0:IO_PointData) : void
      {
         this.da = da0;
         this.fleshData(da0.getSurplusPoint());
      }
      
      public function fleshData(surplusPoint0:int = -999) : void
      {
         if(surplusPoint0 == -999)
         {
            surplusPoint0 = int(this.da.getSurplusPoint());
         }
         var d0:PeakProDefine = this.define;
         var lv0:int = int(this.da.getOneLv(d0.name));
         var mustPoint0:int = int(this.da.getMustPointOne(d0.name));
         var nameStr0:String = d0.cnName;
         this.lvTxt.htmlText = TextMethod.colorMustNum(lv0,d0.max,"#00FFFF","#00FF00","#858585",true);
         this.addBtn.actived = surplusPoint0 >= mustPoint0 && lv0 < d0.max;
         this.delBtn.visible = lv0 > 0;
         var addStr0:String = "";
         if(d0.skill == "" && d0.iconUrl == "")
         {
            addStr0 = d0.getProAddStr(lv0);
         }
         else
         {
            addStr0 = "Lv." + lv0;
            this.icon.setIconName(d0.getIconUrl());
            this.setIconGrayFilter(lv0 <= 0);
         }
         if(Boolean(this.addTxt))
         {
            if(lv0 > 0)
            {
               this.addTxt.textColor = 16776960;
            }
            else
            {
               this.addTxt.textColor = 8750469;
            }
            this.addTxt.text = addStr0;
         }
         else if(lv0 > 0)
         {
            nameStr0 += " " + ComMethod.color(addStr0,"#FFFF00");
         }
         this.nameTxt.htmlText = nameStr0;
      }
      
      public function setIconGrayFilter(bb0:Boolean) : void
      {
         this.icon.filters = bb0 ? [NormalBtn.no_filter] : [];
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var d0:PeakProDefine = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0.actived)
         {
            d0 = this.define;
            if(btn0 == this.addBtn)
            {
               this.da.usePoint(d0.name);
               this.fleshFun();
            }
            else if(btn0 == this.delBtn)
            {
               this.da.delPoint(d0.name);
               this.fleshFun();
            }
         }
      }
      
      private function lvTxtClick(e:MouseEvent) : void
      {
         var now0:int = int(this.da.getOneLv(this.define.name));
         var max0:int = this.define.getMaxPoint(this.da.getSurplusPoint(),now0);
         var tip0:String = "输入加点（0~" + max0 + "）";
         Gaming.uiGroup.alertBox.showNumChoose(tip0,now0,max0,0,1,this.yesSetPoint,"yesAndNo",null,null,false);
      }
      
      private function yesSetPoint(lv0:int) : void
      {
         var now0:int = int(this.da.getOneLv(this.define.name));
         var max0:int = this.define.getMaxPoint(this.da.getSurplusPoint(),now0);
         lv0 = NumberMethod.limitRange(lv0,0,max0);
         this.da.setPoint(this.define.name,lv0);
         this.fleshFun();
      }
      
      private function addBtnOver(e:MouseEvent) : void
      {
         var must0:int = 0;
         var tip0:String = null;
         var d0:PeakProDefine = this.define;
         if(Boolean(d0))
         {
            must0 = int(this.da.getMustPointOne(d0.name));
            if(must0 > 1)
            {
               tip0 = d0.getAddBtnTip();
               this.tipBox.textTip.showFollowText(tip0);
            }
         }
      }
      
      private function iconOver(e:MouseEvent) : void
      {
         var nowLv0:int = 0;
         var lvStr0:String = null;
         var skillD0:HeroSkillDefine = null;
         var d0:PeakProDefine = this.define;
         if(Boolean(d0))
         {
            nowLv0 = int(this.da.getOneLv(d0.name));
            lvStr0 = nowLv0 <= 0 ? "" : nowLv0 + "";
            if(d0.descrip != "")
            {
               this.tipBox.equipTip.setText(d0.descrip,d0.cnName,lvStr0);
               this.tipBox.equipTip.show();
               this.tipBox.setPositionBySp(this.icon);
            }
            else
            {
               skillD0 = d0.getSkillDefine();
               if(Boolean(skillD0))
               {
                  this.tipBox.equipTip.setText(skillD0.getLevelGatherTip(nowLv0),d0.cnName,lvStr0,"");
                  this.tipBox.equipTip.show();
                  this.tipBox.setPositionBySp(this.icon);
               }
            }
         }
      }
      
      private function mouseOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
   }
}

