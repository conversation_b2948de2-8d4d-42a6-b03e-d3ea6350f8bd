package UI.tower
{
   import UI.UIGroup;
   import UI.UIOrder;
   import UI.base.AppNewUI;
   import flash.text.TextField;
   
   public class TowerUI extends AppNewUI
   {
      private var titleTxt:TextField;
      
      private var listBoard:TowerListBoard = new TowerListBoard();
      
      private var unendBoard:TowerUnendBoard = new TowerUnendBoard();
      
      public function TowerUI()
      {
         super();
         UICn = "虚天塔";
         UILabel = "tower";
         swfLabel = "TowerUI";
         labelArr = ["unend","list"];
         labelCnArr = ["天塔","幻塔"];
      }
      
      override protected function firstLoad() : void
      {
         elementNameArr = elementNameArr.concat(["titleTxt"]);
         setImgUrl(swfLabel + "/" + UILabel + "UI");
         this.titleTxt.htmlText = UICn;
         init_addLabel();
         init_addBox();
         init_other();
         UIGroup.setUIMiddle(this,true);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         var bb0:Boolean = UIOrder.openPan("Hospital5");
         if(bb0)
         {
            super.show();
         }
      }
      
      public function FKey() : void
      {
         if(visible)
         {
            this.listBoard.FKey();
         }
      }
      
      override protected function getTipText() : String
      {
         var str0:String = "";
         if(this.listBoard.visible)
         {
            str0 += "霞光大陆上空突然出现一座上百层的虚幻塔，每一层都有若干位拥有特殊技能的首领，还有与其对应的虚拟场景。击败这些首领将获得稀有零件等特别奖励。";
            str0 += "\n1、玩家可以挑战任意一层的首领";
            str0 += "\n2、每层设置4个难度，每个难度都有奖励，击败高难度可获得所有低难度奖励。";
            str0 += "\n3、虚幻塔为关卡的困难设定，其中无法使用道具、魂卡、食物。";
         }
         return str0;
      }
   }
}

