package UI.pet.evo
{
   import UI.UIOrder;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.grid.NormalGridIcon;
   import UI.base.must.NormalMustBar;
   import UI.base.must.NormalMustBox;
   import UI.pet.PetUI;
   import com.greensock.TweenLite;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.pet.PetData;
   import dataAll.pet.creator.PetDataEvoCtrl;
   import dataAll.pet.gene.define.GeneDefine;
   import fl.transitions.easing.Back;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class PetEvoBoard extends NormalUI
   {
      private var beforeTag:Sprite;
      
      private var beforeIcon:NormalGridIcon = new NormalGridIcon();
      
      private var afterTag:Sprite;
      
      private var afterIcon:NormalGridIcon = new NormalGridIcon();
      
      private var noSp:MovieClip;
      
      private var dpsBarSp:Sprite;
      
      private var lifeBarSp:Sprite;
      
      private var growMustSp:Sprite;
      
      private var growMustBar:NormalMustBar = new NormalMustBar();
      
      private var mustSp:Sprite;
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var btnSp:MovieClip;
      
      private var btn:NormalBtn = new NormalBtn();
      
      public function PetEvoBoard()
      {
         super();
      }
      
      private static function no_afterEvo(v0:* = null) : void
      {
         Gaming.uiGroup.petUI.hide();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["dpsBarSp","lifeBarSp","beforeTag","afterTag","mustSp","btnSp","noSp","growMustSp"];
         super.setImg(img0);
         this.noSp.stop();
         this.beforeTag.addChild(this.beforeIcon);
         this.afterTag.addChild(this.afterIcon);
         addChild(this.mustBox);
         this.mustBox.setImg(this.mustSp);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("进化");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         addChild(this.growMustBar);
         this.growMustBar.setImg(this.growMustSp);
         this.growMustBar.setText("所需资质培养总百分比");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         if(Boolean(PetUI.nowGrip))
         {
            this.showData(PetUI.getNowData());
         }
      }
      
      private function clearAll() : void
      {
         this.mustBox.setShowState(false);
         this.growMustBar.clearAll();
         this.btn.actived = false;
         this.noSp.visible = true;
      }
      
      private function showData(da0:PetData) : void
      {
         var beforeDefine0:GeneDefine = null;
         var must_d0:MustDefine = null;
         var mustB0:Boolean = false;
         var nowGrowPer0:Number = NaN;
         var mustGrowPer0:Number = NaN;
         var growB0:Boolean = false;
         beforeDefine0 = da0.getGeneDefine();
         var afterDefine0:GeneDefine = beforeDefine0.getEvoGeneDefine();
         var isRedB0:Boolean = ["red","orange"].indexOf(da0.gene.getColor()) >= 0;
         this.beforeIcon.setIconName(beforeDefine0.getBodyImgUrl());
         this.showMul(beforeDefine0);
         if(Boolean(afterDefine0) && isRedB0)
         {
            this.noSp.visible = false;
            this.afterIcon.setIconName(afterDefine0.getBodyImgUrl());
            this.showMul(afterDefine0,"sec");
            this.afterTag.scaleX = 0.9;
            this.afterTag.scaleY = this.afterTag.scaleX;
            TweenLite.to(this.afterTag,0.5,{
               "scaleX":1,
               "scaleY":1,
               "ease":Back.easeOut
            });
            must_d0 = PetDataEvoCtrl.getMust(da0);
            mustB0 = this.mustBox.inData(must_d0,da0.base.save.level,"尸宠",false);
            nowGrowPer0 = da0.gene.save.getGrowAllPer();
            mustGrowPer0 = beforeDefine0.growMust;
            growB0 = this.growMustBar.inData(nowGrowPer0,mustGrowPer0);
            this.btn.actived = mustB0 && growB0;
         }
         else
         {
            this.clearAll();
            this.afterIcon.clear();
            this.noSp.gotoAndStop(isRedB0 ? "no" : "onlyRed");
         }
      }
      
      private function showMul(d0:GeneDefine, spName0:String = "bar") : void
      {
         var name0:* = null;
         var per0:Number = NaN;
         var nameArr0:Array = GeneDefine.mulNameArr;
         for each(name0 in nameArr0)
         {
            per0 = d0.getMulUIPer(name0);
            this.setBarPer(name0,per0,spName0);
         }
      }
      
      private function setBarPer(name0:String, per0:Number, spName0:String = "bar") : void
      {
         if(per0 > 1)
         {
            per0 = 1;
         }
         if(per0 < 0)
         {
            per0 = 0;
         }
         var sp0:Sprite = this[name0 + "BarSp"];
         var bar0:Sprite = sp0[spName0 + "Sp"];
         if(spName0 == "bar")
         {
            bar0.scaleX = per0;
         }
         else
         {
            bar0.scaleX = sp0["barSp"].scaleX;
            TweenLite.to(bar0,0.5,{"scaleX":per0});
         }
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var must_d0:MustDefine = null;
         var da0:PetData = PetUI.getNowData();
         if(Boolean(da0))
         {
            must_d0 = PetDataEvoCtrl.getMust(da0);
            PlayerMustCtrl.deductMust(must_d0,this.afterEvo);
         }
      }
      
      private function afterEvo() : void
      {
         var da0:PetData = PetUI.getNowData();
         PetDataEvoCtrl.evolution(da0);
         ++Gaming.PG.save.headCount.petEvoNum;
         UIOrder.save(true,true,true,this.yes_afterEvo,no_afterEvo,false,true);
      }
      
      private function yes_afterEvo(v0:* = null) : void
      {
         PetUI.loadPetSwf(this.affter_loadPetSwf,this.fail_loadPetSwf);
      }
      
      private function affter_loadPetSwf() : void
      {
         Gaming.uiGroup.petUI.refleshBar();
         this.showData(PetUI.getNowData());
         Gaming.uiGroup.alertBox.showSuccess("进化成功！");
      }
      
      private function fail_loadPetSwf() : void
      {
         Gaming.uiGroup.petUI.hide;
         Gaming.uiGroup.alertBox.showError("文件读取失败！");
      }
   }
}

