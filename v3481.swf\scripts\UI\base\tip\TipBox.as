package UI.base.tip
{
   import UI.base.HaveConSprite;
   import UI.base.backboard.AutoBackboard;
   import com.sounto.utils.StringMethod;
   import flash.display.DisplayObject;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class TipBox extends HaveConSprite
   {
      public var equipTip:EquipTipBox = new EquipTipBox();
      
      public var armsTip:ArmsTipBox = new ArmsTipBox();
      
      public var textTip:NormalTipBox = new NormalTipBox();
      
      public var heroImgTip:HeroImgTipBox = new HeroImgTipBox();
      
      private var armsBoard:AutoBackboard = new AutoBackboard();
      
      public var normalBoard:AutoBackboard = new AutoBackboard();
      
      private var textBoard:AutoBackboard = new AutoBackboard();
      
      private var heroImgBoard:AutoBackboard = new AutoBackboard();
      
      private var nowBoard:AutoBackboard = null;
      
      public var followMouseB:Boolean = false;
      
      public function TipBox()
      {
         super();
         this.mouseChildren = false;
         this.mouseEnabled = false;
      }
      
      public function init() : void
      {
         addChild(this.armsBoard);
         addChild(this.normalBoard);
         addChild(this.textBoard);
         addChild(this.heroImgBoard);
         this.armsBoard.setImg(Gaming.uiGroup.getBasicMovieClip("armsTipBoard"));
         this.normalBoard.setImg(Gaming.uiGroup.getBasicMovieClip("normalTipBoard"));
         this.heroImgBoard.setImg(Gaming.uiGroup.getBasicMovieClip("normalTipBoard"));
         this.textBoard.setImg(Gaming.uiGroup.getBasicMovieClip("textTipBoard"));
         addChild(this.equipTip);
         this.equipTip.init();
         this.equipTip.tipBox = this;
         addChild(this.armsTip);
         this.armsTip.init();
         this.armsTip.tipBox = this;
         addChild(this.textTip);
         this.textTip.init();
         this.textTip.tipBox = this;
         addChild(this.heroImgTip);
         this.heroImgTip.tipBox = this;
      }
      
      public function hideAllshowBack(type0:String, w0:int, h0:int) : void
      {
         this.equipTip.visible = false;
         this.armsTip.visible = false;
         this.textTip.visible = false;
         this.heroImgTip.visible = false;
         this.armsBoard.visible = false;
         this.normalBoard.visible = false;
         this.textBoard.visible = false;
         this.heroImgBoard.visible = false;
         var b0:AutoBackboard = this[type0 + "Board"];
         b0.visible = true;
         b0.setSize(w0,h0);
         this.nowBoard = b0;
      }
      
      public function setPositionBySp(sp0:DisplayObject, maxX0:int = 530) : void
      {
         var maxH0:int = 0;
         maxH0 = 600;
         this.followMouseB = false;
         var p0:Point = sp0.localToGlobal(new Point());
         var rect0:Rectangle = sp0.getRect(sp0);
         var compareTipBox:TipBox = Gaming.uiGroup.compareTipBox;
         if(p0.x > maxX0)
         {
            this.x = -this.width + p0.x + 5;
            compareTipBox.x = x - compareTipBox.width + 5;
         }
         else
         {
            this.x = rect0.x + rect0.width + p0.x;
            if(x > maxX0)
            {
               this.x = maxX0;
            }
            compareTipBox.x = x + this.width - 5;
         }
         this.y = p0.y;
         if(y + this.height > maxH0)
         {
            this.y = maxH0 - this.height;
         }
         compareTipBox.y = p0.y;
         if(compareTipBox.y + compareTipBox.height > maxH0)
         {
            compareTipBox.y = maxH0 - compareTipBox.height;
         }
      }
      
      public function setPositionByMouse() : void
      {
         this.x = Gaming.gameSprite.mouseX + 10;
         this.y = Gaming.gameSprite.mouseY + 10;
         if(x + this.width >= Gaming.WIDTH)
         {
            this.x = x - (this.width + 14);
         }
         if(y + this.height >= Gaming.HEIGHT)
         {
            this.y = Gaming.HEIGHT - this.height;
         }
         var compareTipBox:TipBox = Gaming.uiGroup.compareTipBox;
         if(compareTipBox.visible)
         {
            compareTipBox.x = x + this.width - 5;
            compareTipBox.y = y;
         }
      }
      
      public function setPosition(x0:int, y0:int) : void
      {
         this.x = x0;
         this.y = y0;
         this.followMouseB = false;
      }
      
      override public function get width() : Number
      {
         if(Boolean(this.nowBoard))
         {
            return this.nowBoard.width;
         }
         return super.width;
      }
      
      override public function get height() : Number
      {
         if(Boolean(this.nowBoard))
         {
            return this.nowBoard.height;
         }
         return super.height;
      }
      
      override public function set x(v0:Number) : void
      {
         super.x = int(v0);
      }
      
      override public function set y(v0:Number) : void
      {
         super.y = int(v0);
      }
      
      public function show() : void
      {
         visible = true;
         super.inCon();
      }
      
      public function hide(e0:* = null) : void
      {
         visible = false;
         super.outCon();
         this.followMouseB = false;
         var compareTipBox:TipBox = Gaming.uiGroup.compareTipBox;
         if(compareTipBox != this && compareTipBox.visible)
         {
            compareTipBox.hide();
         }
      }
      
      public function showText(str0:String, cutLine0:int = 0) : void
      {
         var find0:String = null;
         var f0:int = 0;
         var s2:String = "";
         if(cutLine0 > 0)
         {
            find0 = "\n";
            f0 = StringMethod.findStrIndex(str0,find0,cutLine0 - 1);
            if(f0 >= 0)
            {
               s2 = str0.substr(f0 + find0.length);
               str0 = str0.substr(0,f0);
            }
         }
         if(s2 != "")
         {
            Gaming.uiGroup.compareTipBox.textTip.showFollowText(s2,false);
         }
         this.textTip.showFollowText(str0);
      }
      
      public function FTimer() : void
      {
         if(this.followMouseB)
         {
            if(this.visible)
            {
               this.setPositionByMouse();
            }
         }
      }
   }
}

