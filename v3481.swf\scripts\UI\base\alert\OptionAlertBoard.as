package UI.base.alert
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGripBox;
   import UI.base.AutoNormalUI;
   import UI.base.button.BtnAgent;
   import UI.base.button.BtnAgentGroup;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.grid.NormalGrid;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class OptionAlertBoard extends AutoNormalUI
   {
      public var box:AlertBox;
      
      private var titleTxt:TextField;
      
      private var closeBtn:NormalBtn;
      
      private var backSp:Sprite;
      
      private var downSp:Sprite;
      
      private var gripTag:Sprite;
      
      private var gripBox:ItemsGripBox = new ItemsGripBox();
      
      private var agent:BtnAgentGroup = null;
      
      public function OptionAlertBoard()
      {
         super();
         mcTypeArr = ["btnSp","txt","tag"];
      }
      
      override protected function firstLoad() : void
      {
         setImgUrl("BasicUI/optionBox");
         this.gripBox.imgType = "diffBar";
         this.gripBox.arg.init(2,5,3,2);
         addChild(this.gripBox);
         NormalUICtrl.setTag(this.gripBox,this.gripTag);
         this.gripBox.evt.setWant(true,true);
         this.gripBox.setNowGripNum(10);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.click);
         this.gripBox.addEventListener(ClickEvent.ON_OVER,this.barOver);
         this.gripBox.addEventListener(ClickEvent.ON_OUT,Gaming.uiGroup.tipBox.hide);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
      }
      
      public function showAgent(ag0:BtnAgentGroup) : void
      {
         this.box.showCheck(" ","");
         this.show();
         this.agent = ag0;
         this.gripBox.inData_byArr(ag0.getArr(),this.gripFun);
         this.backSp.height = this.gripBox.height;
         this.downSp.y = this.backSp.y + this.backSp.height;
      }
      
      private function gripFun(grip0:NormalGrid, a0:BtnAgent) : void
      {
         grip0.itemsData = a0;
         grip0.activedAndEnabled = false;
         grip0.setName(a0.cn);
         grip0.setIconName(a0.iconUrl);
         grip0.setSmallIcon(a0.smallIcon);
         grip0.setAllActived(a0.actived);
         grip0.tipString = a0.tipString;
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var funName0:String = btn0.label + "BtnClick";
         this[funName0](e);
      }
      
      private function closeBtnClick(e:MouseEvent) : void
      {
         this.box.hide();
      }
      
      private function click(e:ClickEvent) : void
      {
         if(Boolean(this.agent))
         {
            if(this.agent.clickFun is Function)
            {
               this.agent.clickFun(e.childData as BtnAgent,this.agent);
            }
         }
         this.box.hide();
      }
      
      private function barOver(e:ClickEvent) : void
      {
         var a0:BtnAgent = e.childData as BtnAgent;
         if(Boolean(a0))
         {
            Gaming.uiGroup.tipBox.showText(a0.tipString);
         }
      }
   }
}

