package UI.api.badWord
{
   import UI.test.SaveTestBox;
   import com.adobe.serialization.json.JSON2;
   import dataAll._data.ConstantDefine;
   import dataAll._player.role.RoleName;
   import open4399Tools.Open4399ToolsApi;
   import open4399Tools.events.Open4399ToolsEvent;
   
   public class BadWorldApi
   {
      private var open4399ToolsApi:Open4399ToolsApi;
      
      protected var yesFun:Function = null;
      
      protected var noFun:Function = null;
      
      protected var nowWord:String = "";
      
      protected var useB:Boolean = false;
      
      protected var ingB:Boolean = false;
      
      protected var context:String = "";
      
      public function BadWorldApi()
      {
         super();
      }
      
      public function init() : void
      {
         this.open4399ToolsApi = Open4399ToolsApi.getInstance();
         this.open4399ToolsApi.addEventListener(Open4399ToolsEvent.SERVICE_INIT,this.onServiceInitComplete);
         this.open4399ToolsApi.addEventListener(Open4399ToolsEvent.CHECK_BAD_WORDS_ERROR,this.onCheckBadWordsError);
      }
      
      public function check(str0:String, _yesFun:Function, _noFun:Function) : void
      {
         this.yesFun = _yesFun;
         this.noFun = _noFun;
         if(ConstantDefine.noBadArr.indexOf(str0) >= 0)
         {
            this.doYesFun();
            SaveTestBox.addText("BadWorldApi非敏感词，yesFun跳出");
            return;
         }
         var role0:String = RoleName.haveRoleNamePan(str0);
         if(role0 != "")
         {
            this.doNoFun("不能包含字符：" + role0 + "");
            return;
         }
         if(this.ingB)
         {
            SaveTestBox.addText("BadWorldApi不能同时进行2次，跳出");
            return;
         }
         this.ingB = true;
         this.nowWord = str0;
         if(Gaming.isLocal())
         {
            this.yesAduit();
         }
         else
         {
            Gaming.api.aduit.checkText(str0,this.yesAduit,this.doNoFun);
         }
      }
      
      private function yesAduit(str0:String = "") : void
      {
         SaveTestBox.addText("yesAduit语法判断完毕……");
         if(Gaming.isLocal() || Gaming.testCtrl.isApiB)
         {
            this.doYesFun();
            SaveTestBox.addText("本地或者api，yesFun跳出");
            return;
         }
         if(this.useB)
         {
            this.affter_check();
         }
         else
         {
            this.open4399ToolsApi.init();
         }
      }
      
      private function affter_check() : void
      {
         SaveTestBox.addText("词语判断中……");
         this.open4399ToolsApi.checkBadWords(this.nowWord);
      }
      
      private function onServiceInitComplete(event:Open4399ToolsEvent) : void
      {
         this.useB = true;
         this.open4399ToolsApi.addEventListener(Open4399ToolsEvent.CHECK_BAD_WORDS,this.onCheckBadWords);
         this.affter_check();
      }
      
      private function onCheckBadWordsError(e:Open4399ToolsEvent) : void
      {
         if(this.noFun is Function)
         {
            this.useB = false;
            this.ingB = false;
            this.noFun("接口调用出问题了！");
         }
      }
      
      private function onCheckBadWords(e:Open4399ToolsEvent) : void
      {
         var obj0:Object = JSON2.decode(e.data);
         if(obj0.code == 10000)
         {
            this.doYesFun();
         }
         else
         {
            this.doNoFun();
         }
      }
      
      private function doYesFun() : void
      {
         var fun0:Function = this.yesFun;
         if(fun0 is Function)
         {
            this.clearData();
            fun0();
         }
      }
      
      private function doNoFun(str0:String = "字符中包含敏感词汇。") : void
      {
         var fun0:Function = this.noFun;
         if(fun0 is Function)
         {
            this.clearData();
            fun0(str0);
         }
      }
      
      private function clearData() : void
      {
         this.ingB = false;
         this.yesFun = null;
         this.noFun = null;
      }
   }
}

