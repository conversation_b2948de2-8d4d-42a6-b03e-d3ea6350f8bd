package UI.bag
{
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipType;
   import dataAll.items.ItemsDataGroup;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   
   public class WearEquipGripBox extends ItemsGripBox
   {
      private var gripObj:Object = {};
      
      public function WearEquipGripBox()
      {
         super();
      }
      
      override public function inData_byDataGroup(dg0:ItemsDataGroup, fleshDataB0:Boolean = true) : void
      {
         var n:* = undefined;
         var da0:EquipData = null;
         var type0:String = null;
         var grip0:ItemsGrid = null;
         clearData();
         (evt as GripBoxEventAddit).fatherData = dg0;
         var d_arr0:Array = dg0.dataArr;
         for(n in d_arr0)
         {
            da0 = d_arr0[n];
            type0 = da0.save.partType;
            grip0 = this.gripObj[type0];
            if(<PERSON><PERSON><PERSON>(grip0))
            {
               grip0.activedAndGray = true;
               if(!grip0)
               {
                  INIT.showError("找不到装备格子：type:" + type0);
               }
               grip0.inData_equip(da0);
            }
         }
      }
      
      public function setGrip_byOutside(sp0:Sprite) : void
      {
         var n:* = undefined;
         var type0:String = null;
         var name0:String = null;
         var grip_img0:MovieClip = null;
         var grip0:ItemsGrid = null;
         if(gripArr.length > 0)
         {
            INIT.showError("只能设置素材一次");
         }
         var typeArr0:Array = EquipType.WEAR_ARR;
         for(n in typeArr0)
         {
            type0 = typeArr0[n];
            name0 = type0 + "Grip";
            grip_img0 = sp0[name0];
            if(Boolean(grip_img0))
            {
               sp0.removeChild(grip_img0);
               grip0 = addGrip(grip_img0) as ItemsGrid;
               grip0.setAnnotation(EquipType.getCnName(type0));
               grip0.equipType = type0;
               this.gripObj[type0] = grip0;
            }
         }
      }
      
      public function getGripByEquipType(type0:String) : ItemsGrid
      {
         var grip0:ItemsGrid = null;
         for each(grip0 in gripArr)
         {
            if(grip0.equipType == type0)
            {
               return grip0;
            }
         }
         return null;
      }
      
      override public function clear() : void
      {
         INIT.showError("该ui不能被清除");
      }
   }
}

