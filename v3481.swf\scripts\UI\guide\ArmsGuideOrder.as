package UI.guide
{
   import UI.bag.ItemsGrid;
   import dataAll.arms.ArmsData;
   
   public class ArmsGuideOrder extends NormalGuideOrder
   {
      public function ArmsGuideOrder()
      {
         super();
         arr = [this.mainBtn,this.armsBtn,this.loadBtn];
      }
      
      private function mainBtn() : OneGuideData
      {
         if(Gaming.PG.da.arms.dataArr.length == 1)
         {
            return new OneGuideData(Gaming.uiGroup.mainUI.getBtn("wear"),"你有一把新武器");
         }
         return null;
      }
      
      private function armsBtn() : OneGuideData
      {
         var s0:int = 0;
         var grip0:ItemsGrid = null;
         var da0:ArmsData = Gaming.PG.da.armsBag.dataArr[0];
         if(<PERSON><PERSON><PERSON>(da0))
         {
            Gaming.PG.da.armsBag.swapByOther(Gaming.PG.da.armsBag,da0.save.site,0);
            Gaming.uiGroup.bagUI.showBox("arms");
            Gaming.uiGroup.bagUI.armsBox.pageBox.showPage(0);
            s0 = da0.save.site;
            grip0 = Gaming.uiGroup.bagUI.armsBox.gripArr[s0];
            return new OneGuideData(grip0,"点击新武器");
         }
         return null;
      }
      
      private function loadBtn() : OneGuideData
      {
         return new OneGuideData(Gaming.uiGroup.btnList.gripArr[0],"点击“装上”\n即可装备武器");
      }
   }
}

