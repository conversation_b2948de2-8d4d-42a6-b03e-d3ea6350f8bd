package UI.base.gift
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import com.greensock.TweenLite;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.things.define.ThingsDefine;
   import fl.motion.easing.Back;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class GiftShowBox extends NormalUI
   {
      private var titleTxt:TextField;
      
      private var closeBtn:SimpleButton;
      
      private var infoTxt:TextField;
      
      private var giftTag:Sprite;
      
      private var giftBox:ItemsGripBox = new ItemsGripBox();
      
      public function GiftShowBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["titleTxt","closeBtn","infoTxt","giftTag"];
         super.setImg(img0);
         addChild(this.giftBox);
         NormalUICtrl.setTag(this.giftBox,this.giftTag);
         this.giftBox.setIconPro("equipGrip",50,50);
         this.giftBox.evt.setWantEvent(true,false,false,true,true);
         this.giftBox.arg.init(5,4,22,22);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.giftBox);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         Gaming.soundGroup.playSound("sound","alertShow");
         super.show();
         this.mouseChildren = true;
         this.scaleX = 0.7;
         this.scaleY = this.scaleX;
         TweenLite.to(this,0.3,{
            "scaleX":1,
            "scaleY":1,
            "ease":Back.easeOut
         });
      }
      
      override public function hide() : void
      {
         this.mouseChildren = false;
         super.hide();
      }
      
      public function setThingsDefine(d0:ThingsDefine) : void
      {
         this.titleTxt.text = d0.cnName;
         this.infoTxt.text = "打开" + d0.cnName + "可获得以下" + (d0.giftRandomB ? "任意一种" : "所有") + "物品：";
         this.giftBox.inData_byArr(d0.giftD.arr,"inData_gift");
         this.show();
      }
      
      public function setGiftAddDefineGroup(dg0:GiftAddDefineGroup, title0:String = "") : void
      {
         if(title0 == "")
         {
            title0 = dg0.cnName;
         }
         this.titleTxt.text = title0;
         this.infoTxt.text = "";
         this.giftBox.inData_byArr(dg0.arr,"inData_gift");
         this.show();
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.hide();
      }
   }
}

