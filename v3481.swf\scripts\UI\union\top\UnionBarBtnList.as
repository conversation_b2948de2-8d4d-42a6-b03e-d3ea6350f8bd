package UI.union.top
{
   import UI.base.btnList.BtnBox;
   import UI.base.button.NormalBtn;
   import dataAll._app.union.info.UnionInfo;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class UnionBarBtnList extends BtnBox
   {
      public var itemsData:UnionInfo;
      
      public var index:int = 0;
      
      public var clickFun:Function;
      
      public function UnionBarBtnList()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         getBtn("join").setName("加入");
         getBtn("url").setName("官帖");
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         if(this.clickFun is Function)
         {
            this.clickFun(this.itemsData,btn0.label);
         }
      }
      
      public function setJoinVisible(bb0:Boolean) : void
      {
         getBtn("join").visible = bb0;
      }
      
      public function setUrlVisible(bb0:Boolean) : void
      {
         getBtn("url").visible = bb0;
      }
   }
}

