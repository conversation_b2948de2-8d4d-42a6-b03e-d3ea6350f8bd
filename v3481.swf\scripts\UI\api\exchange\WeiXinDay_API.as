package UI.api.exchange
{
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   
   public class WeiXinDay_API
   {
      public var yesFun:Function;
      
      public var noFun:Function;
      
      internal var loader:URLLoader = new URLLoader();
      
      internal var url:URLRequest = new URLRequest("https://huodong2.4399.com/comm/zmxy3/api.php");
      
      public function WeiXinDay_API()
      {
         super();
         this.loader.addEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this.loader.addEventListener(Event.COMPLETE,this.loaderCompleteHandler);
      }
      
      public function startExchange(code0:String, _yesFun:Function = null, _noFun:Function = null) : *
      {
         this.yesFun = _yesFun;
         this.noFun = _noFun;
         var data0:URLVariables = new URLVariables();
         data0.code = code0;
         data0.cid = 8;
         this.url.data = data0;
         this.url.method = URLRequestMethod.POST;
         this.loader.load(this.url);
      }
      
      private function loaderCompleteHandler(e:Event) : void
      {
         var msg0:String = null;
         var json0:String = this.loader.data;
         var arr0:Array = json0.split("|");
         var first0:String = arr0[0];
         var last0:String = arr0[1];
         if(first0 == "1")
         {
            if(this.yesFun is Function)
            {
               this.yesFun(last0);
            }
         }
         else if(this.noFun is Function)
         {
            msg0 = "未知错误";
            if(last0 == "01")
            {
               msg0 = "未登录";
            }
            if(last0 == "02")
            {
               msg0 = "参数有误（缺漏参数cid）";
            }
            if(last0 == "03")
            {
               msg0 = "同一帐号连续错误超过20次";
            }
            if(last0 == "04")
            {
               msg0 = "兑换码无效（没有这个兑换码）";
            }
            if(last0 == "05")
            {
               msg0 = "兑换码无效（兑换码未领过）";
            }
            if(last0 == "06")
            {
               msg0 = "兑换码无效（已过期）";
            }
            if(last0 == "07")
            {
               msg0 = "兑换码无效（已被其他人领取）";
            }
            if(last0 == "08")
            {
               msg0 = "兑换码无效（已过期）";
            }
            if(last0 == "99")
            {
               msg0 = "其它错误";
            }
            this.noFun(msg0);
         }
      }
      
      private function errorHandler(e:IOErrorEvent) : void
      {
         if(this.noFun is Function)
         {
            this.noFun("网络连接错误");
         }
      }
   }
}

