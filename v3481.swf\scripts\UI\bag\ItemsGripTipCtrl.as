package UI.bag
{
   import UI.UIShow;
   import UI.base.TipSprite;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.heroImg.HeroEquipImgBox;
   import UI.base.tip.EquipTipBox;
   import UI.base.tip.TipBox;
   import UI.helper.book.HelperBookBox;
   import com.common.net.SaveImage;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.achieve.AchieveData;
   import dataAll._app.edit.card.BossCardData;
   import dataAll._app.food.FoodBookAgent;
   import dataAll._app.food.FoodRawAgent;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.head.define.HeadDefine;
   import dataAll._app.parts.PartsCreator;
   import dataAll._app.union.building.cooking.UnionCookingData;
   import dataAll._player.more.creator.MoreAddData;
   import dataAll.arms.ArmsData;
   import dataAll.arms.ArmsDataGroup;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.equip.EquipData;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.device.DeviceDataCreator;
   import dataAll.equip.device.DeviceDefine;
   import dataAll.equip.jewelry.JewelryDataCreator;
   import dataAll.equip.jewelry.JewelryDefine;
   import dataAll.equip.shield.ShieldDataCreator;
   import dataAll.equip.shield.ShieldDefine;
   import dataAll.equip.suit.SuitEquipDataAll;
   import dataAll.equip.suit.SuitEquipDataObj;
   import dataAll.equip.vehicle.VehicleDataCreator;
   import dataAll.equip.vehicle.VehicleDefine;
   import dataAll.equip.weapon.WeaponDataCreator;
   import dataAll.equip.weapon.WeaponDefine;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsCompareData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.pet.gene.GeneData;
   import dataAll.pet.gene.GeneDataGroup;
   import dataAll.skill.HeroSkillData;
   import dataAll.skill.HeroSkillDataGroup;
   import dataAll.skill.define.HeroSkillDefine;
   import dataAll.things.ThingsData;
   import dataAll.things.ThingsDataGroup;
   import dataAll.things.define.ThingsDefine;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.KeyboardEvent;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   import flash.ui.Keyboard;
   
   public class ItemsGripTipCtrl
   {
      public static var nowOverGrip:TipSprite;
      
      private static var keyFData:IO_ItemsData;
      
      private static var keyFDg:ItemsDataGroup;
      
      private static var tipBox:TipBox = null;
      
      private static var compareTipBox:TipBox = null;
      
      private static var skillDecripB:Boolean = false;
      
      public function ItemsGripTipCtrl()
      {
         super();
      }
      
      public static function init() : void
      {
         tipBox = Gaming.uiGroup.tipBox;
         compareTipBox = Gaming.uiGroup.compareTipBox;
      }
      
      public static function addEvent_byItemsGripBox(box0:ItemsGripBox, giftListB0:Boolean = false) : void
      {
         box0.addEventListener(ClickEvent.ON_OVER,gripOver);
         box0.addEventListener(ClickEvent.ON_OUT,gripOut);
         if(giftListB0)
         {
            ItemsGripBtnListCtrl.addGiftListEvent(box0);
         }
      }
      
      public static function addEvent_byItemsGrip(grip0:NormalBtn) : void
      {
         grip0.addEventListener(MouseEvent.MOUSE_OVER,gripOver);
         grip0.addEventListener(MouseEvent.MOUSE_OUT,gripOut);
      }
      
      private static function gripOver(e:Event) : void
      {
         ItemsGripBtnListCtrl.mouseOverGripEvent(e);
         if(Gaming.uiGroup.dragCtrl.dragB)
         {
            return;
         }
         if(e is ClickEvent)
         {
            gripOverFun((e as ClickEvent).child as NormalBtn,e as ClickEvent);
         }
         else
         {
            gripOverFun(e.target as NormalBtn);
         }
         Gaming.fouseToStage();
      }
      
      private static function gripOverFun(grip0:NormalBtn, e:ClickEvent = null) : void
      {
         var maxX0:int = 530;
         nowOverGrip = grip0;
         var fatherData0:Object = null;
         if(Boolean(e))
         {
            fatherData0 = e.fatherData;
         }
         var da0:Object = grip0.itemsData;
         if(grip0.tipFollowMouseB)
         {
            Gaming.uiGroup.tipBox.followMouseB = true;
         }
         if(da0 is ArmsData)
         {
            armsTip(da0 as ArmsData,fatherData0 as ArmsDataGroup);
         }
         else if(da0 is EquipData)
         {
            equipTip(da0 as EquipData,fatherData0 as EquipDataGroup);
         }
         else if(da0 is VehicleDefine)
         {
            vehicleDefineTip(da0 as VehicleDefine);
         }
         else if(da0 is DeviceDefine)
         {
            deviceDefineTip(da0 as DeviceDefine);
         }
         else if(da0 is WeaponDefine)
         {
            weaponDefineTip(da0 as WeaponDefine);
         }
         else if(da0 is ThingsData)
         {
            thingsTip(da0 as ThingsData,fatherData0 as ThingsDataGroup);
         }
         else if(da0 is ThingsDefine)
         {
            thingsDefineTip(da0 as ThingsDefine);
         }
         else if(da0 is HeroSkillData)
         {
            skillTip(da0 as HeroSkillData,fatherData0 as HeroSkillDataGroup);
         }
         else if(da0 is HeroSkillDefine)
         {
            skillDefineTip(da0 as HeroSkillDefine);
         }
         else if(da0 is GeneData)
         {
            geneTip(da0 as GeneData,fatherData0 as GeneDataGroup);
         }
         else if(da0 is GoodsData)
         {
            goodsTip(da0 as GoodsData);
         }
         else if(da0 is GiftAddDefine)
         {
            giftAddDefineTip(da0 as GiftAddDefine);
         }
         else if(da0 is HeadDefine)
         {
            headTip(da0 as HeadDefine);
         }
         else if(da0 is FoodBookAgent)
         {
            foodBookTip(da0 as FoodBookAgent);
         }
         else if(da0 is FoodRawAgent)
         {
            foodRawTip(da0 as FoodRawAgent);
         }
         else if(da0 is AchieveData)
         {
            maxX0 = 300;
            achieveTip(da0 as AchieveData);
         }
         else if(da0 is MoreAddData)
         {
            moreAddDataTip(da0 as MoreAddData);
         }
         else if(da0 is UnionCookingData)
         {
            cookingTip(da0 as UnionCookingData);
         }
         else if(da0 is BossCardData)
         {
            bossCardTip(da0 as BossCardData);
         }
         tipBox.setPositionBySp(grip0,maxX0);
      }
      
      public static function gripOut(e:Event = null) : void
      {
         var grip0:ItemsGrid = null;
         if(e is ClickEvent)
         {
            grip0 = (e as ClickEvent).child as ItemsGrid;
            if(grip0 is ItemsGrid)
            {
               if(grip0.isChosen && grip0.itemsData is EquipData)
               {
               }
            }
         }
         tipBox.hide();
         compareTipBox.hide();
      }
      
      public static function armsTip(da0:ArmsData, dg0:ArmsDataGroup = null) : void
      {
         keyFData = da0;
         keyFDg = dg0;
         var dd0:ItemsCompareData = UIShow.getNowUICompareDataBy(da0,dg0);
         var compareDa0:ArmsData = dd0.itemsData as ArmsData;
         var compareDg0:ArmsDataGroup = dd0.dataGroup as ArmsDataGroup;
         var dps0:Number = da0.getUIShowDps();
         var compareDps0:Number = 0;
         if(Boolean(compareDa0))
         {
            compareDps0 = compareDa0.getUIShowDps();
         }
         var str0:String = da0.getGatherTip(compareDa0,false,skillDecripB);
         if(!skillDecripB)
         {
            str0 += "\n\n" + ComMethod.color("<b>按下F键查看武器详情</b>","#FF9900");
         }
         tipBox.armsTip.setDpsText(dps0,compareDps0);
         tipBox.armsTip.setPartsIconUrlArr(da0.getPartsUIIconUrlArr());
         tipBox.armsTip.setText(str0,da0.getColorCnName() + (Gaming.isLocal() ? "-" + da0.getEvoLv() : ""),da0.save.getTrueLevel() + "",da0.save.getSeverTimeTip(),dg0,da0.save.strengthenLv);
         tipBox.armsTip.show();
         if(Boolean(compareDa0))
         {
            compareTipBox.armsTip.setDpsText(compareDps0,dps0);
            compareTipBox.armsTip.setPartsIconUrlArr(compareDa0.getPartsUIIconUrlArr());
            compareTipBox.armsTip.setText(compareDa0.getGatherTip(da0,false,skillDecripB),compareDa0.save.getColorCnName(),compareDa0.save.getTrueLevel() + "",compareDa0.save.getSeverTimeTip(),compareDg0,compareDa0.save.strengthenLv);
            compareTipBox.armsTip.show();
         }
         skillDecripB = false;
      }
      
      private static function vehicleDefineTip(d0:VehicleDefine, dg0:EquipDataGroup = null) : void
      {
         var da0:EquipData = VehicleDataCreator.getTempData(d0,Gaming.PG.da);
         equipTip(da0);
      }
      
      private static function deviceDefineTip(d0:DeviceDefine, dg0:EquipDataGroup = null) : void
      {
         var da0:EquipData = DeviceDataCreator.getTempData(d0,Gaming.PG.da);
         equipTip(da0);
      }
      
      private static function weaponDefineTip(d0:WeaponDefine, dg0:EquipDataGroup = null) : void
      {
         var da0:EquipData = WeaponDataCreator.getTempData(d0,Gaming.PG.da);
         equipTip(da0);
      }
      
      private static function jewelryDefineTip(d0:JewelryDefine, dg0:EquipDataGroup = null) : void
      {
         var da0:EquipData = JewelryDataCreator.getTempData(d0,Gaming.PG.da);
         equipTip(da0);
      }
      
      private static function shieldDefineTip(d0:ShieldDefine, dg0:EquipDataGroup = null) : void
      {
         var da0:EquipData = ShieldDataCreator.getTempData(d0,Gaming.PG.da);
         equipTip(da0);
      }
      
      public static function equipTip(da0:EquipData, dg0:EquipDataGroup = null, mustSkillDecripB0:Boolean = false, compareB0:Boolean = true) : void
      {
         var dd0:ItemsCompareData = null;
         var compareDa0:EquipData = null;
         var compareDg0:EquipDataGroup = null;
         var equipD0:EquipDefine = null;
         keyFData = da0;
         keyFDg = dg0;
         if(mustSkillDecripB0)
         {
            skillDecripB = true;
         }
         else
         {
            skillDecripB = da0.getTipSkillDecripB();
         }
         if(compareB0)
         {
            dd0 = UIShow.getNowUICompareDataBy(da0,dg0);
            compareDa0 = dd0.itemsData as EquipData;
         }
         var str0:String = da0.getGatherTip(compareDa0,"",skillDecripB);
         if(!skillDecripB)
         {
            str0 += "\n\n" + ComMethod.color("<b>按下F键查看装备技能详情</b>","#FF9900");
         }
         tipBox.equipTip.setText(str0,da0.save.getColorCnName(),da0.save.getLevelTipString(),da0.save.getSeverTimeTip(),dg0,da0.getStrengthenLv());
         tipBox.equipTip.show();
         if(Boolean(compareDa0))
         {
            compareDg0 = dd0.dataGroup as EquipDataGroup;
            compareTipBox.equipTip.setText(compareDa0.getGatherTip(da0,"",skillDecripB),compareDa0.save.getColorCnName(),compareDa0.save.getLevelTipString(),compareDa0.save.getSeverTimeTip(),compareDg0,compareDa0.getStrengthenLv());
            compareTipBox.equipTip.show();
         }
         else
         {
            equipD0 = da0.save.getDefine();
            if(equipD0.type == "fashion")
            {
               compareTipBox.heroImgTip.showByFashionDefine(equipD0);
            }
         }
         skillDecripB = false;
      }
      
      private static function thingsTip(da0:ThingsData, dg0:ThingsDataGroup = null) : void
      {
         tipBox.equipTip.setText(da0.getGatherTip(),da0.getCnName(),da0.getGatherLvText(),"");
         tipBox.equipTip.show();
      }
      
      private static function thingsDefineTip(d0:ThingsDefine) : void
      {
         if(d0.father == "parts")
         {
            thingsTip(PartsCreator.getThingsDataByPartsDefine(d0,1));
         }
         else
         {
            tipBox.equipTip.setText(d0.getGatherTip(Gaming.PG.da),d0.cnName,"","");
            tipBox.equipTip.show();
         }
      }
      
      private static function skillTip(da0:HeroSkillData, dg0:HeroSkillDataGroup = null, mustSkillDescripB0:Boolean = false) : void
      {
         keyFData = da0;
         keyFDg = dg0;
         skillDecripB = mustSkillDescripB0;
         var str0:String = da0.getGatherTip(skillDecripB);
         if(!skillDecripB)
         {
            str0 += "\n\n" + ComMethod.color("<b>按下F键查看技能等级详情</b>","#FF9900");
         }
         tipBox.equipTip.setText(str0,da0.getCnName(),da0.save.lv + "","");
         tipBox.equipTip.show();
         skillDecripB = false;
      }
      
      private static function skillDefineTip(d0:HeroSkillDefine) : void
      {
         tipBox.equipTip.setText(d0.getGatherTip(),d0.cnName,"","");
         tipBox.equipTip.show();
      }
      
      private static function goodsTip(da0:GoodsData) : void
      {
         var armsData0:ArmsData = null;
         var geneData0:GeneData = null;
         if(da0.def.dataType == "arms")
         {
            armsData0 = da0.getTempArmsData();
            armsTip(armsData0);
         }
         else if(da0.def.dataType == "gene")
         {
            geneData0 = Gaming.defineGroup.geneCreator.getTempSuperData(da0.def.defineLabel);
            geneTip(geneData0,null);
         }
         else
         {
            tipBox.equipTip.setText(da0.getGatherTip(),da0.getTipCnName(),da0.getLevelString(),"");
            tipBox.equipTip.show();
            if(da0.def.type == "fashion")
            {
               compareTipBox.heroImgTip.showByFashionGoods(da0);
            }
         }
      }
      
      private static function geneTip(da0:GeneData, dg0:GeneDataGroup) : void
      {
         tipBox.equipTip.setText(da0.getGatherTip(),da0.save.getColorCnName(),da0.getSave().getTrueLevel() + "","");
         tipBox.equipTip.show();
      }
      
      private static function giftAddDefineTip(d0:GiftAddDefine) : void
      {
         tipBox.equipTip.setText(d0.getBaseGatherTip(),d0.getBaseCnName(),"","");
         tipBox.equipTip.show();
      }
      
      private static function achieveTip(da0:AchieveData) : void
      {
         var tip0:String = da0.getGatherTip();
         if(tip0 != "")
         {
            tipBox.equipTip.setText(tip0,da0.getCnName(),"","");
            tipBox.equipTip.show();
         }
      }
      
      private static function bodyTip(d0:NormalBodyDefine) : void
      {
         tipBox.equipTip.setText(d0.getGatherTip(),d0.cnName,"","");
         tipBox.equipTip.show();
      }
      
      private static function moreAddDataTip(da0:MoreAddData) : void
      {
         tipBox.equipTip.setText(da0.getGatherTip(),da0.getCnName(),da0.getLv() + "","");
         tipBox.equipTip.show();
      }
      
      private static function headTip(d0:HeadDefine) : void
      {
         tipBox.equipTip.setText(d0.getGatherTip(),d0.cnName + "称号","","");
         tipBox.equipTip.show();
      }
      
      private static function cookingTip(da0:UnionCookingData) : void
      {
         tipBox.equipTip.setText(da0.getGatherTip(),da0.def.cnName,"","");
         tipBox.equipTip.show();
      }
      
      private static function foodRawTip(a0:FoodRawAgent) : void
      {
         tipBox.textTip.setText(a0.getGatherTip());
         tipBox.textTip.show();
      }
      
      private static function foodBookTip(a0:FoodBookAgent) : void
      {
         tipBox.textTip.setText(a0.getGatherTip());
         tipBox.textTip.show();
      }
      
      private static function bossCardTip(da0:BossCardData, fB0:Boolean = false) : void
      {
         tipBox.equipTip.setText(fB0 ? da0.getFTip() : da0.getGatherTip(),da0.getTipTitle());
         tipBox.equipTip.show();
      }
      
      public static function addEvent_byHeroImage(sp0:Sprite) : void
      {
         sp0.addEventListener(MouseEvent.MOUSE_OVER,suitOver);
         sp0.addEventListener(MouseEvent.MOUSE_OUT,suitOut);
      }
      
      private static function suitOver(e:MouseEvent) : void
      {
         var img0:HeroEquipImgBox = e.target as HeroEquipImgBox;
         var obj0:SuitEquipDataObj = img0.equipDataGroup.suitObj;
         if(obj0 is SuitEquipDataObj && !Gaming.uiGroup.dragCtrl.dragB)
         {
            suitTip(obj0);
         }
         else
         {
            tipBox.hide();
         }
      }
      
      private static function suitOut(e:MouseEvent) : void
      {
         tipBox.hide();
      }
      
      private static function suitTip(obj0:SuitEquipDataObj) : void
      {
         tipBox.equipTip.setText(obj0.getGatherTip(true),obj0.getColorCnName(),obj0.lv + "","");
         tipBox.followMouseB = true;
         tipBox.equipTip.show();
      }
      
      private static function suitShow(da0:EquipData) : void
      {
         var all0:SuitEquipDataAll = null;
         var n:* = undefined;
         var arr0:Array = null;
         var i:* = undefined;
         var tda0:EquipData = null;
         var grip0:ItemsGrid = null;
         if(da0.placeType != ItemsDataGroup.PLACE_WEAR)
         {
            return;
         }
         var wear0:ItemsGripBox = Gaming.uiGroup.wearUI.equipBox;
         var bag0:ItemsGripBox = Gaming.uiGroup.bagUI.equipBox;
         wear0.setChoose_byIndex(-1);
         bag0.setChoose_byIndex(-1);
         if(Gaming.uiGroup.dragCtrl.dragB)
         {
            return;
         }
         if(da0.haveSuitB)
         {
            all0 = new SuitEquipDataAll();
            all0.inDataByOne(da0);
            if(all0.getCollectedNum() > 0)
            {
               for(n in all0.obj)
               {
                  arr0 = all0.obj[n];
                  for(i in arr0)
                  {
                     tda0 = arr0[i];
                     grip0 = null;
                     if(tda0.placeType == ItemsDataGroup.PLACE_BAG)
                     {
                        grip0 = bag0.getBtnBySite(tda0.save.site) as ItemsGrid;
                     }
                     else
                     {
                        grip0 = wear0.getBtnBySite(tda0.save.site) as ItemsGrid;
                     }
                     if(grip0 is ItemsGrid)
                     {
                        grip0.isChosen = true;
                     }
                  }
               }
            }
         }
      }
      
      public static function addNormalBtnTip(btn0:TipSprite) : void
      {
         btn0.addEventListener(MouseEvent.MOUSE_OVER,normalBtnOver);
         btn0.addEventListener(MouseEvent.MOUSE_OUT,normalBtnOut);
      }
      
      private static function normalBtnOver(e:MouseEvent) : void
      {
         var btn0:TipSprite = null;
         var str0:String = null;
         if(e.target is TipSprite)
         {
            btn0 = e.target as TipSprite;
            if(btn0.FTipB)
            {
               nowOverGrip = btn0;
            }
            str0 = btn0.tipString;
            if(str0 != "")
            {
               Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
            }
            else
            {
               Gaming.uiGroup.tipBox.hide();
            }
         }
      }
      
      private static function normalBtnOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      public static function FTip(e:KeyboardEvent) : void
      {
         var obj0:Object = null;
         var thingsD0:ThingsDefine = null;
         if(nowOverGrip is NormalBtn && e.keyCode == Keyboard.F)
         {
            if(tipBox.visible)
            {
               obj0 = (nowOverGrip as NormalBtn).itemsData;
               thingsD0 = null;
               if(obj0 is GoodsData)
               {
                  thingsD0 = (obj0 as GoodsData).getFThingsDefine();
               }
               else if(obj0 is ThingsData)
               {
                  thingsD0 = (obj0 as ThingsData).save.getDefine();
                  if(!thingsD0.getShowGiftB())
                  {
                     thingsD0 = null;
                  }
               }
               else if(obj0 is ThingsDefine)
               {
                  thingsD0 = obj0 as ThingsDefine;
                  if(!thingsD0.getShowGiftB())
                  {
                     thingsD0 = null;
                  }
               }
               else if(obj0 is ArmsData)
               {
                  skillDecripB = true;
                  armsTip(keyFData as ArmsData,keyFDg as ArmsDataGroup);
                  tipBox.setPositionBySp(nowOverGrip);
               }
               else if(obj0 is EquipData)
               {
                  skillDecripB = true;
                  equipTip(keyFData as EquipData,keyFDg as EquipDataGroup,true);
                  tipBox.setPositionBySp(nowOverGrip);
               }
               else if(obj0 is HeroSkillData)
               {
                  skillDecripB = true;
                  skillTip(keyFData as HeroSkillData,keyFDg as HeroSkillDataGroup,true);
                  tipBox.setPositionBySp(nowOverGrip);
               }
               else if(obj0 is GiftAddDefineGroup)
               {
                  Gaming.uiGroup.giftShowBox.setGiftAddDefineGroup(obj0 as GiftAddDefineGroup);
               }
               else if(obj0 is BossCardData)
               {
                  bossCardTip(obj0 as BossCardData,true);
               }
               if(Boolean(thingsD0))
               {
                  Gaming.uiGroup.giftShowBox.setThingsDefine(thingsD0);
               }
            }
         }
      }
      
      public static function saveNowOverPng() : void
      {
         var box0:HelperBookBox = null;
         var grip0:NormalBtn = nowOverGrip as NormalBtn;
         if(Boolean(grip0))
         {
            grip0.icon.saveToPng(tipBox.equipTip.getTitleTxt().text);
         }
         else
         {
            box0 = Gaming.uiGroup.helperUI.bookBoard.contentBox;
            if(Boolean(box0))
            {
               box0.iconClick();
            }
         }
      }
      
      public static function saveTipPng() : void
      {
         var ebox0:EquipTipBox = null;
         var box0:HelperBookBox = null;
         if(tipBox.visible)
         {
            ebox0 = tipBox.equipTip;
            SaveImage.SaveByMC(tipBox,new Rectangle(0,0,ebox0.getW(),ebox0.getH()),ebox0.getTitleTxt().text + "属性");
         }
         else
         {
            box0 = Gaming.uiGroup.helperUI.bookBoard.contentBox;
            if(Boolean(box0))
            {
               box0.saveToPng();
            }
         }
      }
   }
}

