package UI.forging
{
   import UI.UIShow;
   import UI.base.AppNormalUI;
   import UI.base.NormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import UI.forging.armsElement.ArmsEleBoard;
   import UI.forging.armsElement.EleMoveBoard;
   import UI.forging.armsEvo.ArmsEvoBoard;
   import UI.forging.armsRemake.ArmsRemakeBoard;
   import UI.forging.armsUpgrade.ArmsUpgradeBoard;
   import UI.forging.blackChipConver.BlackChipConverBoard;
   import UI.forging.deviceUpgrade.DeviceUpgradeBoard;
   import UI.forging.equipCompose.EquipComposeBoard;
   import UI.forging.equipEvo.EquipEvoBoard;
   import UI.forging.equipRemake.EquipRemakeBoard;
   import UI.forging.equipUpgrade.EquipFillBoard;
   import UI.forging.equipUpgrade.EquipUpgradeBoard;
   import UI.forging.jewelryUpgrade.JewelryUpgradeBoard;
   import UI.forging.shieldUpgrade.ShieldUpgradeBoard;
   import UI.forging.skillAddMove.SkillAddMoveBoard;
   import UI.forging.strengthen.StrengthenBoard;
   import UI.forging.strengthen.StrengthenMoveBoard;
   import UI.forging.thingsCompose.ThingsComposeBoard;
   import UI.forging.weaponUpgrade.WeaponUpgradeBoard;
   import dataAll.arms.ArmsData;
   import dataAll.equip.EquipData;
   import dataAll.equip.device.DeviceData;
   import dataAll.equip.shield.ShieldData;
   import dataAll.equip.weapon.WeaponData;
   import dataAll.items.IO_ItemsData;
   import dataAll.things.ThingsData;
   import dataAll.things.define.ThingsComposeDefine;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class ForgingUI extends AppNormalUI
   {
      public var labelBox:LabelBox;
      
      public var labelTag:Sprite = null;
      
      private var fatherLabelBox:LabelBox;
      
      public var fatherLabelTag:Sprite = null;
      
      private var tipBtn:SimpleButton;
      
      private var coverSp:Sprite;
      
      private var coverTxt:TextField = null;
      
      private var titleTxt:TextField = null;
      
      private var labelArr:Array;
      
      private var cnObj:*;
      
      private var fatherLabelArr:Array;
      
      private var fatherCnArr:Array;
      
      private var armsLabelArr:Array;
      
      private var equipLabelArr:Array;
      
      private var advancedLabelArr:Array;
      
      private var strengthenLabelArr:Array;
      
      private var thingsLabelArr:Array;
      
      private var noLoadArr:Array;
      
      public var armsUpgradeBoard:ArmsUpgradeBoard;
      
      public var armsRemakeBoard:ArmsRemakeBoard;
      
      public var equipUpgradeBoard:EquipUpgradeBoard;
      
      public var equipFillBoard:EquipFillBoard;
      
      public var equipRemakeBoard:EquipRemakeBoard;
      
      public var armsEvoBoard:ArmsEvoBoard;
      
      public var equipEvoBoard:EquipEvoBoard;
      
      public var deviceUpgradeBoard:DeviceUpgradeBoard;
      
      public var weaponUpgradeBoard:WeaponUpgradeBoard;
      
      public var shieldUpgradeBoard:ShieldUpgradeBoard;
      
      public var jewelryUpgradeBoard:JewelryUpgradeBoard;
      
      public var equipStrengthenBoard:StrengthenBoard;
      
      public var armsStrengthenBoard:StrengthenBoard;
      
      public var strengthenMoveBoard:StrengthenMoveBoard;
      
      public var thingsComposeBoard:ThingsComposeBoard;
      
      public var equipComposeBoard:EquipComposeBoard;
      
      public var blackChipConverBoard:BlackChipConverBoard;
      
      public var armsEleBoard:ArmsEleBoard;
      
      public var eleMoveBoard:EleMoveBoard;
      
      public var skillAddMoveBoard:SkillAddMoveBoard;
      
      public var boxArr:Array;
      
      private var nowLabel:String = "";
      
      public function ForgingUI()
      {
         var label0:* = null;
         var box0:NormalUI = null;
         this.labelBox = new LabelBox();
         this.fatherLabelBox = new LabelBox();
         this.labelArr = ["armsUpgrade","armsRemake","armsEvo","equipUpgrade","equipFill","equipRemake","equipEvo","deviceUpgrade","weaponUpgrade","shieldUpgrade","jewelryUpgrade","armsStrengthen","equipStrengthen","strengthenMove","thingsCompose","equipCompose","armsEle","eleMove","blackChipConver","skillAddMove"];
         this.cnObj = {
            "armsUpgrade":"升级",
            "armsRemake":"重造",
            "armsStrengthen":"强化",
            "armsEvo":"进阶",
            "armsEle":"元素",
            "eleMove":"元素转移",
            "equipUpgrade":"升级",
            "equipRemake":"重造",
            "equipStrengthen":"强化",
            "equipEvo":"进阶",
            "equipCompose":"合成狂人",
            "equipFill":"满战",
            "skillAddMove":"附加转移",
            "deviceUpgrade":"装置",
            "weaponUpgrade":"副手",
            "shieldUpgrade":"护盾",
            "jewelryUpgrade":"饰品",
            "strengthenMove":"强化转移",
            "thingsCompose":"物品合成",
            "blackChipConver":"碎片转化"
         };
         this.fatherLabelArr = ["arms","equip","advanced","strengthen","things"];
         this.fatherCnArr = ["武器","装备","其他进阶","强化转移","物品合成"];
         this.armsLabelArr = ["armsUpgrade","armsRemake","armsStrengthen","armsEvo","armsEle","eleMove"];
         this.equipLabelArr = ["equipUpgrade","equipRemake","equipStrengthen","equipEvo","equipCompose","equipFill","skillAddMove"];
         this.advancedLabelArr = ["deviceUpgrade","weaponUpgrade","shieldUpgrade","jewelryUpgrade"];
         this.strengthenLabelArr = ["strengthenMove"];
         this.thingsLabelArr = ["thingsCompose","blackChipConver"];
         this.noLoadArr = ["skillAddMove","equipFill"];
         this.armsUpgradeBoard = new ArmsUpgradeBoard();
         this.armsRemakeBoard = new ArmsRemakeBoard();
         this.equipUpgradeBoard = new EquipUpgradeBoard();
         this.equipFillBoard = new EquipFillBoard();
         this.equipRemakeBoard = new EquipRemakeBoard();
         this.armsEvoBoard = new ArmsEvoBoard();
         this.equipEvoBoard = new EquipEvoBoard();
         this.deviceUpgradeBoard = new DeviceUpgradeBoard();
         this.weaponUpgradeBoard = new WeaponUpgradeBoard();
         this.shieldUpgradeBoard = new ShieldUpgradeBoard();
         this.jewelryUpgradeBoard = new JewelryUpgradeBoard();
         this.equipStrengthenBoard = new StrengthenBoard();
         this.armsStrengthenBoard = new StrengthenBoard();
         this.strengthenMoveBoard = new StrengthenMoveBoard();
         this.thingsComposeBoard = new ThingsComposeBoard();
         this.equipComposeBoard = new EquipComposeBoard();
         this.blackChipConverBoard = new BlackChipConverBoard();
         this.armsEleBoard = new ArmsEleBoard();
         this.eleMoveBoard = new EleMoveBoard();
         this.skillAddMoveBoard = new SkillAddMoveBoard();
         this.boxArr = [];
         super();
         UICn = "锻造";
         for each(label0 in this.labelArr)
         {
            box0 = this[label0 + "Board"];
            this.boxArr.push(box0);
            box0.UILabel = label0;
         }
         this.equipStrengthenBoard.itemsType = "equip";
         this.armsRemakeBoard.armsUpgradeBoard = this.armsUpgradeBoard;
         this.equipRemakeBoard.equipUpgradeBoard = this.equipUpgradeBoard;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var label0:* = null;
         var box0:NormalUI = null;
         var imgName0:String = null;
         elementNameArr = ["fatherLabelTag","titleTxt","labelTag","coverSp","tipBtn"];
         super.setImg(img0);
         this.labelBox.arg.init(7,1,-1,0);
         addChild(this.labelBox);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.labelBox.x = this.labelTag.x - 3;
         this.labelBox.y = this.labelTag.y;
         this.fatherLabelBox.arg.init(7,1,-6,0);
         addChild(this.fatherLabelBox);
         this.fatherLabelBox.inData("midLabelBtn",this.fatherLabelArr,this.fatherCnArr);
         this.fatherLabelBox.setChoose(this.fatherLabelArr[0]);
         this.fatherLabelBox.addEventListener(ClickEvent.ON_CLICK,this.fatherLabelClick);
         this.fatherLabelBox.x = this.fatherLabelTag.x;
         this.fatherLabelBox.y = this.fatherLabelTag.y;
         for each(label0 in this.labelArr)
         {
            box0 = this[label0 + "Board"];
            addChild(box0);
            box0.visible = false;
            imgName0 = label0 + "Board";
            if(label0 == "equipStrengthen" || label0 == "armsStrengthen")
            {
               imgName0 = "strengthenBoard";
            }
            else if(label0 == "shieldUpgrade" || label0 == "jewelryUpgrade")
            {
               imgName0 = "deviceUpgradeBoard";
            }
            else if(label0 == "eleMove")
            {
               imgName0 = "strengthenMoveBoard";
            }
            if(this.noLoadArr.indexOf(label0) == -1)
            {
               box0.setImg(Gaming.swfLoaderManager.getResource("ForgingUI",imgName0));
            }
         }
         addChild(this.coverSp);
         this.coverTxt = this.coverSp["txt"];
         this.setCoverText("");
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.showBox(this.nowLabel);
      }
      
      override public function hide() : void
      {
         if(Gaming.uiGroup.allBagUI.visible)
         {
            Gaming.uiGroup.allBagUI.hide();
         }
         if(Gaming.uiGroup.bagUI.visible)
         {
            Gaming.uiGroup.bagUI.hide();
         }
         super.hide();
      }
      
      public function outLoginEvent() : void
      {
         this.armsUpgradeBoard.outLoginEvent();
         this.equipUpgradeBoard.outLoginEvent();
         this.equipFillBoard.outLoginEvent();
         this.deviceUpgradeBoard.outLoginEvent();
         this.weaponUpgradeBoard.outLoginEvent();
         this.shieldUpgradeBoard.outLoginEvent();
         this.blackChipConverBoard.outLoginEvent();
         this.armsEvoBoard.outLoginEvent();
         this.equipEvoBoard.outLoginEvent();
         this.strengthenMoveBoard.outLoginEvent();
         this.equipComposeBoard.outLoginEvent();
         this.skillAddMoveBoard.outLoginEvent();
         this.eleMoveBoard.outLoginEvent();
      }
      
      public function gotoByOneData(da0:IO_ItemsData, label0:String) : void
      {
         var nowData0:IO_ItemsData = null;
         var ui0:NormalUI = this[label0 + "Board"];
         if(ui0.hasOwnProperty("nowData"))
         {
            if(label0.indexOf("arms") >= 0)
            {
               nowData0 = da0 as ArmsData;
            }
            else if(label0.indexOf("equip") >= 0)
            {
               nowData0 = da0 as EquipData;
            }
            else if(label0.indexOf("device") >= 0)
            {
               nowData0 = da0 as DeviceData;
            }
            else if(label0.indexOf("shield") >= 0)
            {
               nowData0 = da0 as ShieldData;
            }
            else if(label0.indexOf("weapon") >= 0)
            {
               nowData0 = da0 as WeaponData;
            }
            else if(label0.indexOf("blackChip") >= 0)
            {
               nowData0 = da0 as ThingsData;
            }
            ui0["nowData"] = nowData0;
            this.nowLabel = label0;
            UIShow.showByLabel("forging");
         }
      }
      
      public function gotoThingsCompose(name0:String) : void
      {
         var d0:ThingsComposeDefine = Gaming.defineGroup.things.getThingsComposeDefine(name0);
         if(Boolean(d0))
         {
            this.nowLabel = "thingsCompose";
            UIShow.showByLabel("forging");
            this.thingsComposeBoard.chooseDefine(d0);
         }
      }
      
      public function itemsGripClick(da0:IO_ItemsData) : void
      {
         var name0:* = null;
         var ui0:NormalUI = null;
         if(visible)
         {
            for each(name0 in this.labelArr)
            {
               ui0 = this[name0 + "Board"];
               if(ui0.visible)
               {
                  if(da0 is ArmsData && Boolean(ui0.hasOwnProperty("armsGripClick")))
                  {
                     ui0["armsGripClick"](da0 as ArmsData);
                  }
                  else if(da0 is EquipData && Boolean(ui0.hasOwnProperty("equipGripClick")))
                  {
                     ui0["equipGripClick"](da0 as EquipData);
                  }
                  else if(da0 is ThingsData && Boolean(ui0.hasOwnProperty("thingsGripClick")))
                  {
                     ui0["thingsGripClick"](da0 as ThingsData);
                  }
               }
            }
         }
      }
      
      public function setCoverText(str0:String) : void
      {
         this.coverSp.visible = str0 != "";
         this.coverTxt.htmlText = str0;
      }
      
      public function setTitleText(str0:String) : void
      {
         this.coverTxt.htmlText = "锻造·" + str0;
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      public function showBox(label0:String) : void
      {
         var n:* = undefined;
         var box0:NormalUI = null;
         if(label0 == "")
         {
            label0 = this.labelArr[0];
         }
         this.fleshLabelBy(label0);
         this.setCoverText("");
         for(n in this.boxArr)
         {
            this.boxArr[n].hide();
         }
         box0 = this[label0 + "Board"];
         box0.show();
      }
      
      private function fatherLabelClick(e:ClickEvent) : void
      {
         var labelArr0:Array = this[e.label + "LabelArr"];
         this.showBox(labelArr0[0]);
      }
      
      private function fleshLabelBy(label0:String) : void
      {
         var f0:* = null;
         var labelArr0:Array = null;
         var fatherLabel0:String = this.fatherLabelArr[0];
         for each(f0 in this.fatherLabelArr)
         {
            labelArr0 = this[f0 + "LabelArr"];
            if(labelArr0.indexOf(label0) >= 0)
            {
               fatherLabel0 = f0;
               break;
            }
         }
         this.fatherShowLabel(fatherLabel0);
         this.labelBox.setChoose(label0);
         this.nowLabel = label0;
         this.setTitleText(this.labelBox.nowCnName + this.fatherLabelBox.nowCnName);
      }
      
      private function fatherShowLabel(fatherLabel0:String) : void
      {
         this.fatherLabelBox.setChoose(fatherLabel0);
         var labelArr0:Array = this[fatherLabel0 + "LabelArr"];
         var cnArr0:Array = this.getCnArr(labelArr0);
         this.labelBox.clear();
         this.labelBox.inData("ForgingUI/secLabelBtn",labelArr0,cnArr0);
      }
      
      private function getCnArr(labelArr0:Array) : Array
      {
         var name0:* = null;
         var i0:int = 0;
         var cn0:String = null;
         var arr0:Array = [];
         for each(name0 in labelArr0)
         {
            i0 = int(this.labelArr.indexOf(name0));
            cn0 = this.cnObj[name0];
            arr0.push(cn0);
         }
         return arr0;
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.hide();
      }
      
      private function tipOver(e:MouseEvent) : void
      {
         var str0:String = "";
         if(this.armsUpgradeBoard.visible)
         {
            str0 += "1、升级武器将提高武器的伤害，进而提升战斗力。最高可升级19级。";
            str0 += "\n2、武器基础等级越高、品质越好，所需的转化石就越多。";
            str0 += "\n3、武器当前等级与基础等级的差值越大，所需的转化石就越多。";
         }
         else if(this.armsRemakeBoard.visible)
         {
            str0 += "1、重造会在保证当前属性条数不变的情况下，随机特殊属性与技能。";
            str0 += "\n2、已锁定的属性不会随机。";
            str0 += "\n3、锁定的属性越多，所需的神能石就越多。";
            str0 += "\n4、武器等级越高、属性越多，所需的神能石就越多。";
            str0 += "\n5、重造之后你可以使用“撤销”将武器恢复到刚才重造前的属性。";
         }
         else if(this.equipUpgradeBoard.visible)
         {
            str0 += "1、升级装备将提升装备几乎所有的属性。最高可升级19级。";
            str0 += "\n2、装备基础等级越高、品质越好，所需的转化石就越多。";
            str0 += "\n3、装备当前等级与基础等级的差值越大，所需的转化石就越多。";
         }
         else if(this.equipFillBoard.visible)
         {
            str0 += "将未满战的装备升级至满战装备。";
         }
         else if(this.equipRemakeBoard.visible)
         {
            str0 += "1、当前只可以重造装备的技能。";
            str0 += "\n2、装备等级越高，所需的神能石就越多。";
            str0 += "\n3、重造之后你可以使用“撤销”将装备恢复到刚才重造前的属性。";
         }
         else if(this.deviceUpgradeBoard.visible)
         {
            str0 += "1、进阶背包中的装置时，背包必须剩余1个空位。";
            str0 += "\n2、进阶装置只需消耗指定数量的1级同类装置。";
         }
         else if(this.shieldUpgradeBoard.visible)
         {
            str0 += "1、进阶背包中的护盾时，背包必须剩余1个空位。";
            str0 += "\n2、进阶护盾需要消耗该护盾碎片。";
         }
         else if(this.strengthenMoveBoard.visible)
         {
            str0 += "\n转移需要消耗的物品：";
            str0 += "\n1、必须消耗强化石，数量与两个物品强化等级相关。";
            str0 += "\n2、当其中一个物品强化等级超过15级时，还需要消耗一定数量的保级卡。";
            str0 += "\n3、不同种类的物品转移时，消耗品将翻倍。";
         }
         else if(this.armsStrengthenBoard.visible || this.equipStrengthenBoard.visible)
         {
            str0 += "<b>普通强化：</b>";
            str0 += "\n1、强化有一定成功率。";
            str0 += "\n2、27级之前，强化成功有一定几率提升1~3个强化等级。";
            str0 += "\n3、27级或以上，强化成功提升1个强化等级。";
            str0 += "\n4、强化失败将降低1个强化等级。";
            str0 += "\n\n<b>百分百强化：</b>";
            str0 += "\n1、强化百分百成功。";
            str0 += "\n2、强化成功提升1个强化等级。";
         }
         else if(!(this.armsEvoBoard.visible || this.equipEvoBoard.visible))
         {
            if(this.blackChipConverBoard.visible)
            {
               str0 += "1、可在同部位的黑色碎片之间互相转化。";
               str0 += "\n2、转化需要消耗2个黑色原始碎片。如果你使用黄金，将只需要消耗1个黑色原始碎片。";
            }
            else if(this.equipComposeBoard.visible)
            {
               str0 += "1、强化等级根据2个装备强化所消耗的材料进行平均后计算所得，最高不超过26级。";
               str0 += "\n2、技能和人物附加技能属性取强化等级高的那个装备，如果强化等级一致则取第1个装备的属性。";
               str0 += "\n3、合成装备等级为固定等级，与2个材料装备等级无关。";
               str0 += "\n4、合成后无法再分解成2个材料装备。";
               str0 += "\n5、如果材料装备的进化等级高于闪耀，则合成的装备也将获得额外的进化等级。";
               str0 += "\n6、合成装备的神级战力取决了材料装备中神级战力较高的那个。";
            }
            else if(this.armsEleBoard.visible)
            {
               str0 += "1、对于没有元素伤害的武器，可消耗宝石生成对应的元素伤害。";
               str0 += "\n2、对于已有元素伤害的武器，你可以强化其元素伤害值或者转化为其他元素伤害。";
            }
         }
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      private function tipOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      public function getNowData() : IO_ItemsData
      {
         if(this.armsStrengthenBoard.visible)
         {
            return this.armsStrengthenBoard.nowData;
         }
         if(this.armsEvoBoard.visible)
         {
            return this.armsEvoBoard.nowData;
         }
         if(this.equipStrengthenBoard.visible)
         {
            return this.equipStrengthenBoard.nowData;
         }
         if(this.equipEvoBoard.visible)
         {
            return this.equipEvoBoard.nowData;
         }
         return null;
      }
   }
}

