package UI.base.box
{
   import UI.base.HaveConSprite;
   import UI.base.button.NormalBtn;
   import UI.base.event.BoxEventAddit;
   import UI.base.event.ClickEvent;
   import UI.base.page.PageBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   
   public class NormalBox extends HaveConSprite
   {
      public var label:String = "";
      
      public var fatherUrl:String = "";
      
      public var imgType:String = "";
      
      public var btnClass:Class = NormalBtn;
      
      public var gripArr:Array = [];
      
      public var ctrlPositionType:String = "rightTop";
      
      public var ctrlBtnImgType:String = "";
      
      public var ctrlBtnArr:Array = [];
      
      public var arg:BoxArrangeAddit = new BoxArrangeAddit();
      
      public var evt:BoxEventAddit;
      
      public var pageBox:PageBox = new PageBox();
      
      public var nowLabel:String = "";
      
      public var nowIndex:int = -1;
      
      public var moreChooseB:Boolean = false;
      
      private var iconMaxWidth:int = 0;
      
      private var iconMaxHeight:int = 0;
      
      private var tempChildInDataFunName:String = "";
      
      public function NormalBox()
      {
         super();
         this.evt = new BoxEventAddit(this);
         addChild(this.pageBox);
      }
      
      public function inData_byGift(gift0:GiftAddDefineGroup) : void
      {
         this.inData_byArr(gift0.arr,"inData_gift");
      }
      
      public function inData_byArr(d_arr0:Array, childInDataFunName:*, clearDataB0:Boolean = true, maxNum0:int = -1) : void
      {
         var n:* = undefined;
         var da0:Object = null;
         var grip0:NormalBtn = null;
         if(clearDataB0)
         {
            this.clearData();
         }
         if(maxNum0 == -1)
         {
            maxNum0 = int(d_arr0.length);
         }
         this.setGripNum(maxNum0,this.imgType,false);
         this.tempChildInDataFunName = childInDataFunName;
         for(n in d_arr0)
         {
            da0 = d_arr0[n];
            grip0 = this.gripArr[n];
            if(childInDataFunName is String)
            {
               grip0[childInDataFunName](da0);
            }
            else if(childInDataFunName is Function)
            {
               childInDataFunName(grip0,da0);
            }
         }
         this.arrange();
         this.fleshPageBox();
      }
      
      public function refleshnDataNow() : void
      {
         var grip0:NormalBtn = null;
         if(this.tempChildInDataFunName == "")
         {
            return;
         }
         for each(grip0 in this.gripArr)
         {
            if(grip0.itemsData is Object)
            {
               grip0[this.tempChildInDataFunName](grip0.itemsData);
            }
         }
      }
      
      public function setIconPro(imgType0:String, maxWidth0:int = 0, maxHeight0:int = 0) : void
      {
         this.imgType = imgType0;
         this.iconMaxWidth = maxWidth0;
         this.iconMaxHeight = maxHeight0;
      }
      
      public function arrange() : void
      {
         this.arg.inData_byArr(this.gripArr);
         this.fleshCtrlBtnPosition();
      }
      
      public function addNewGrip(num0:int) : void
      {
         this.setGripNum(this.gripArr.length + 1,this.imgType);
      }
      
      protected function setGripNum(num0:int, imgType0:String, arrangeB:Boolean = true) : void
      {
         var j:int = 0;
         var i:int = 0;
         var mc0:MovieClip = null;
         this.arg.clear();
         var endIndex0:int = num0 - 1;
         var startIndex0:int = int(this.gripArr.length);
         if(imgType0 != this.imgType)
         {
            startIndex0 = 0;
         }
         this.imgType = imgType0;
         var grip0:NormalBtn = null;
         if(startIndex0 > endIndex0 + 1)
         {
            for(j = endIndex0 + 1; j <= startIndex0 - 1; j++)
            {
               grip0 = this.gripArr[j];
               this.removeGrip(grip0);
            }
            this.gripArr.splice(endIndex0 + 1,startIndex0 - endIndex0);
         }
         else if(startIndex0 != endIndex0 + 1)
         {
            for(i = startIndex0; i <= endIndex0; i++)
            {
               mc0 = this.getImgTypeMc();
               grip0 = this.addGrip(mc0);
            }
         }
         this.fleshCtrlBtn();
         if(arrangeB)
         {
            this.arrange();
         }
      }
      
      protected function getImgTypeMc() : MovieClip
      {
         var mc0:MovieClip = null;
         if(this.imgType.indexOf("/") == -1)
         {
            mc0 = Gaming.uiGroup.getBasicMovieClip(this.imgType);
         }
         else
         {
            mc0 = Gaming.swfLoaderManager.getResourceFull(this.imgType);
         }
         return mc0;
      }
      
      protected function addGrip(img0:MovieClip) : NormalBtn
      {
         var grip0:NormalBtn = this.getNewGrip();
         grip0.fatherUrl = this.fatherUrl + this.label + "/";
         grip0.iconMaxWidth = this.iconMaxWidth;
         grip0.iconMaxHeight = this.iconMaxHeight;
         grip0.index = this.gripArr.length;
         grip0.setImg(img0);
         this.evt.addEL(grip0);
         this.addChild(grip0);
         this.gripArr.push(grip0);
         return grip0;
      }
      
      protected function getNewGrip() : NormalBtn
      {
         return new this.btnClass();
      }
      
      protected function removeGrip(grip0:NormalBtn) : void
      {
         grip0.clear();
         this.evt.removeEL(grip0);
         this.removeChild(grip0);
      }
      
      public function clear() : void
      {
         var n:* = undefined;
         var grip0:NormalBtn = null;
         this.arg.clear();
         for(n in this.gripArr)
         {
            grip0 = this.gripArr[n];
            this.removeGrip(grip0);
         }
         this.gripArr.length = 0;
         this.pageBox.clearAllPage();
         this.clearCtrlBtn();
      }
      
      public function clearData() : void
      {
         var n:* = undefined;
         var grip0:NormalBtn = null;
         for(n in this.gripArr)
         {
            grip0 = this.gripArr[n];
            grip0.clearData();
         }
      }
      
      public function clearAllData() : void
      {
         this.inData_byArr([],"");
      }
      
      public function getBtnByLabel(label0:String) : NormalBtn
      {
         var n:* = undefined;
         var grip0:NormalBtn = null;
         for(n in this.gripArr)
         {
            grip0 = this.gripArr[n];
            if(grip0.label == label0)
            {
               return grip0;
            }
         }
         return null;
      }
      
      public function stopAll() : void
      {
         var b0:NormalBtn = null;
         for each(b0 in this.gripArr)
         {
            b0.stopAll();
         }
      }
      
      public function playAll() : void
      {
         var b0:NormalBtn = null;
         for each(b0 in this.gripArr)
         {
            b0.playAll();
         }
      }
      
      public function setAllPro(pro0:String, value0:*) : void
      {
         var i:* = undefined;
         var grip2:Object = null;
         for(i in this.gripArr)
         {
            grip2 = this.gripArr[i];
            grip2[pro0] = value0;
         }
      }
      
      public function setAllFun(funName0:String, value0:*) : void
      {
         var i:* = undefined;
         var grip2:Object = null;
         for(i in this.gripArr)
         {
            grip2 = this.gripArr[i];
            grip2[funName0](value0);
         }
      }
      
      public function setNowGripNum(num0:int) : void
      {
         this.setGripNum(num0,this.imgType);
      }
      
      public function setChoose(label0:String) : void
      {
         var btn0:NormalBtn = this.getBtnByLabel(label0);
         if(btn0 is NormalBtn)
         {
            this.setChoose_byIndex(btn0.index);
         }
      }
      
      public function setChooseByItemsData(obj0:Object) : NormalBtn
      {
         var btn0:NormalBtn = this.getBtnByItemsData(obj0);
         if(btn0 is NormalBtn)
         {
            return this.setChoose_byIndex(btn0.index);
         }
         return this.setChoose_byIndex(-1);
      }
      
      public function setChooseBySecData(obj0:Object) : NormalBtn
      {
         var btn0:NormalBtn = this.getBtnBySecData(obj0);
         if(btn0 is NormalBtn)
         {
            return this.setChoose_byIndex(btn0.index);
         }
         return this.setChoose_byIndex(-1);
      }
      
      public function setChoose_byIndex(index0:int) : NormalBtn
      {
         var n:* = undefined;
         var btn0:NormalBtn = null;
         var btn2:NormalBtn = null;
         if(this.gripArr.length == 0)
         {
            return null;
         }
         var clearB0:Boolean = index0 < 0 || index0 > this.gripArr.length - 1;
         this.nowIndex = index0;
         for(n in this.gripArr)
         {
            btn0 = this.gripArr[n];
            btn0.isChosen = false;
         }
         if(!clearB0)
         {
            btn2 = this.gripArr[index0];
            btn2.isChosen = true;
            this.nowLabel = btn2.label;
            return btn2;
         }
         return null;
      }
      
      public function gotoChoosePage() : int
      {
         var page0:int = int(this.nowIndex / this.arg.getOneNum());
         this.pageBox.showPage(page0);
         return page0;
      }
      
      public function setPagePos(pos0:DisplayObject) : void
      {
         this.pageBox.setXY_bySp(pos0,this);
      }
      
      public function fleshPageBox() : void
      {
         this.pageBox.inData_byTable(this.arg,5,this.arg.nowPage);
      }
      
      public function getChooseBtn() : NormalBtn
      {
         return this.gripArr[this.nowIndex];
      }
      
      public function fleshMoreChoose() : void
      {
         var btn0:NormalBtn = null;
         var da0:Object = null;
         var isChosen0:Boolean = false;
         for each(btn0 in this.gripArr)
         {
            da0 = btn0.itemsData;
            isChosen0 = false;
            if(Boolean(da0))
            {
               if(da0.hasOwnProperty("isChosen"))
               {
                  isChosen0 = Boolean(da0["isChosen"]);
               }
            }
            btn0.activedAndIgnoreChosen = true;
            btn0.isChosen = isChosen0;
         }
      }
      
      public function setCtrlBtnImgType(str0:String, positionType0:String = "rightTop") : void
      {
         if(this.gripArr.length == 0)
         {
            this.ctrlPositionType = positionType0;
            this.ctrlBtnImgType = str0;
         }
         else
         {
            INIT.showError("不能在图标生成后再setCtrlBtnImgType");
         }
      }
      
      private function fleshCtrlBtn() : void
      {
         var n:* = undefined;
         var grip0:NormalBtn = null;
         var btn0:NormalBtn = null;
         this.clearCtrlBtn();
         if(this.ctrlBtnImgType != "")
         {
            for(n in this.gripArr)
            {
               grip0 = this.gripArr[n];
               btn0 = new NormalBtn();
               btn0.index = n;
               btn0.label = grip0.label;
               btn0.setImg(Gaming.swfLoaderManager.getResourceFull(this.ctrlBtnImgType));
               btn0.addEventListener(MouseEvent.CLICK,this.ctrlBtnClick);
               btn0.addEventListener(MouseEvent.MOUSE_OVER,this.ctrlBtnOver);
               btn0.addEventListener(MouseEvent.MOUSE_OUT,this.ctrlBtnOut);
               this.ctrlBtnArr.push(btn0);
               grip0.extraBtn = btn0;
            }
         }
         this.fleshCtrlBtnPosition();
      }
      
      private function fleshCtrlBtnPosition() : void
      {
         var n:* = undefined;
         var btn0:NormalBtn = null;
         var grip0:NormalBtn = null;
         var p0:Point = null;
         if(this.ctrlBtnImgType != "")
         {
            for(n in this.gripArr)
            {
               btn0 = this.ctrlBtnArr[n];
               grip0 = this.gripArr[n];
               if(this.ctrlPositionType == "rightTop")
               {
                  btn0.x = grip0.x + grip0.width - 2;
                  btn0.y = grip0.y + 2;
               }
               else if(this.ctrlPositionType.indexOf(",") > 0)
               {
                  p0 = ComMethod.getPoint(this.ctrlPositionType);
                  btn0.x = grip0.x + p0.x;
                  btn0.y = grip0.y + p0.y;
               }
               else
               {
                  btn0.x = grip0.x;
                  btn0.y = grip0.y;
               }
               btn0.visible = grip0.visible;
               addChild(btn0);
            }
         }
      }
      
      public function setCtrlVisible(bb0:Boolean) : void
      {
         var btn0:NormalBtn = null;
         for each(btn0 in this.ctrlBtnArr)
         {
            btn0.visible = bb0;
         }
      }
      
      private function clearCtrlBtn() : void
      {
         var btn0:NormalBtn = null;
         for each(btn0 in this.ctrlBtnArr)
         {
            btn0.clear();
            this.removeChild(btn0);
            btn0.removeEventListener(MouseEvent.CLICK,this.ctrlBtnClick);
            btn0.removeEventListener(MouseEvent.MOUSE_OVER,this.ctrlBtnOver);
            btn0.removeEventListener(MouseEvent.MOUSE_OUT,this.ctrlBtnOut);
         }
         this.ctrlBtnArr.length = 0;
      }
      
      private function ctrlBtnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var evt0:ClickEvent = new ClickEvent(ClickEvent.ON_CTRL_CLICK);
         this.setCtrlBtnMouseEvent(btn0,evt0);
      }
      
      private function ctrlBtnOver(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var evt0:ClickEvent = new ClickEvent(ClickEvent.ON_CTRL_OVER);
         this.setCtrlBtnMouseEvent(btn0,evt0);
      }
      
      private function ctrlBtnOut(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var evt0:ClickEvent = new ClickEvent(ClickEvent.ON_CTRL_OUT);
         this.setCtrlBtnMouseEvent(btn0,evt0);
      }
      
      private function setCtrlBtnMouseEvent(btn0:NormalBtn, evt0:ClickEvent) : void
      {
         var grip0:NormalBtn = this.gripArr[btn0.index];
         evt0.index = btn0.index;
         evt0.label = btn0.label;
         evt0.child = grip0;
         evt0.childData = grip0.itemsData;
         evt0.father = this;
         dispatchEvent(evt0);
      }
      
      public function getBtnBySite(site0:int) : NormalBtn
      {
         return this.gripArr[site0];
      }
      
      public function getBtnByItemsData(da0:Object) : NormalBtn
      {
         var btn0:NormalBtn = null;
         for each(btn0 in this.gripArr)
         {
            if(btn0.itemsData == da0)
            {
               return btn0;
            }
         }
         return null;
      }
      
      public function getBtnBySecData(da0:Object) : NormalBtn
      {
         var btn0:NormalBtn = null;
         for each(btn0 in this.gripArr)
         {
            if(btn0.secData == da0)
            {
               return btn0;
            }
         }
         return null;
      }
   }
}

