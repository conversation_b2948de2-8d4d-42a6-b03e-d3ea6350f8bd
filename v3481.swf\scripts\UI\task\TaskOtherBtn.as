package UI.task
{
   import UI.UIOrder;
   import UI.base.button.NormalBtn;
   import dataAll._app.task.TaskData;
   import dataAll._app.task.define.TaskDefine;
   import dataAll._player.PlayerData;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class TaskOtherBtn
   {
      public function TaskOtherBtn()
      {
         super();
      }
      
      private static function get PD() : PlayerData
      {
         return Gaming.PG.da;
      }
      
      public static function click(da0:TaskData, btn0:NormalBtn) : void
      {
         var d0:TaskDefine = da0.def;
         var fun0:Function = TaskGotoPan[d0.name + "Click"];
         if(fun0 is Function)
         {
            fun0(da0,btn0);
         }
         else if(d0.isMemoryB())
         {
            memoryClick(da0,btn0);
         }
      }
      
      public static function flesh(da0:TaskData, btn0:NormalBtn) : void
      {
         var d0:TaskDefine = da0.def;
         var fun0:Function = TaskGotoPan[d0.name + "Flesh"];
         btn0.visible = true;
         if(fun0 is Function)
         {
            fun0(da0,btn0);
         }
         else if(d0.isMemoryB())
         {
            memoryFlesh(da0,btn0);
         }
         else
         {
            btn0.visible = false;
         }
      }
      
      public static function memoryClick(da0:TaskData, btn0:NormalBtn) : void
      {
         var g0:GiftAddDefineGroup = null;
         var bb0:Boolean = false;
         var bagSpace0:int = PD.armsBag.getSpaceSiteNum();
         if(bagSpace0 < 1)
         {
            UIOrder.alertError("武器背包空位不足。");
         }
         else
         {
            g0 = new GiftAddDefineGroup();
            g0.addGiftByStr("arms;gift;1;white;1;;;;diff_0");
            bb0 = GiftAddit.addAndAutoBagSpacePan(g0,"背包获得1把武器！");
            if(bb0)
            {
               ++PD.main.save.memArms;
               memoryFlesh(da0,btn0);
            }
         }
      }
      
      public static function memoryFlesh(da0:TaskData, btn0:NormalBtn) : void
      {
         var getNum0:int = 0;
         if(da0.getLv() <= 20)
         {
            getNum0 = PD.main.save.memArms;
            if(getNum0 >= 20)
            {
               btn0.setName("已领完");
               btn0.actived = false;
            }
            else
            {
               btn0.setName("领取1级武器");
               btn0.actived = true;
            }
            btn0.tipString = "已领取" + getNum0 + "把武器，最多领取" + 20 + "把。";
            btn0.activedAndEnabled = false;
         }
         else
         {
            btn0.visible = false;
         }
      }
   }
}

