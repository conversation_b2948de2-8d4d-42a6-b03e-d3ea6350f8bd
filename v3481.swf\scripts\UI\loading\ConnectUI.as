package UI.loading
{
   import UI.base._hide.HideSpirte;
   import UI.base.button.NormalBtn;
   import com.greensock.TweenLite;
   import com.sounto.cf.NiuBiCF;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class ConnectUI extends HideSpirte
   {
      private var txt:TextField;
      
      private var mc:MovieClip;
      
      protected var img:Sprite = null;
      
      protected var backSp:Sprite;
      
      private var stopBtnSp:MovieClip;
      
      private var stopBtn:NormalBtn = new NormalBtn();
      
      private var saveTxt:TextField;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      private var haveStopB:Boolean = false;
      
      public function ConnectUI()
      {
         super();
         this.mouseChildren = true;
         this.stopDelay = 0;
      }
      
      private function get stopDelay() : Number
      {
         return this.CF.getAttribute("stopDelay");
      }
      
      private function set stopDelay(v0:Number) : void
      {
         this.CF.setAttribute("stopDelay",v0);
      }
      
      private function get stopDelay_t() : Number
      {
         return this.CF.getAttribute("stopDelay_t");
      }
      
      private function set stopDelay_t(v0:Number) : void
      {
         this.CF.setAttribute("stopDelay_t",v0);
      }
      
      public function imgInit() : void
      {
         this.setImg(Gaming.uiGroup.getBasicMovieClip("connectUI"));
         visible = false;
         alpha = 0;
      }
      
      private function setImg(img0:Sprite) : void
      {
         var n:* = undefined;
         var name0:String = null;
         this.img = img0;
         var elementNameArr:Array = ["mc","txt","backSp","stopBtnSp","saveTxt"];
         for(n in elementNameArr)
         {
            name0 = elementNameArr[n];
            this.SET(name0,this.img[name0]);
            if(this.img[name0] is MovieClip)
            {
               this.img[name0].stop();
            }
         }
         addChildAt(this.img,0);
         this.x = this.img.x;
         this.y = this.img.y;
         this.img.x = 0;
         this.img.y = 0;
         addChild(this.stopBtn);
         this.stopBtn.setImg(this.stopBtnSp);
         this.stopBtn.setName("终止存档");
         this.stopBtn.actived = false;
         this.stopBtn.addEventListener(MouseEvent.CLICK,this.stopClick);
      }
      
      protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function show(str0:String = "", backAlpha:* = 0.8, stopSaveDelay0:Number = 0) : void
      {
         if(str0 == "")
         {
            str0 = "数据加载中";
         }
         this.backSp.alpha = backAlpha;
         this.txt.text = str0;
         this.mc.play();
         visible = true;
         this.mouseEnabled = true;
         if(stopSaveDelay0 > 0)
         {
            this.haveStopB = true;
            this.stopDelay = stopSaveDelay0;
            this.stopDelay_t = 0;
            this.stopBtn.visible = true;
            this.saveTxt.visible = true;
            this.fleshSaveText();
         }
         else
         {
            this.haveStopB = false;
            this.stopBtn.visible = false;
            this.saveTxt.visible = false;
         }
         TweenLite.to(this,0.3,{"alpha":1});
      }
      
      public function hide() : void
      {
         this.mouseEnabled = false;
         visible = false;
      }
      
      private function hideVisible() : void
      {
         visible = false;
      }
      
      private function fleshSaveText() : void
      {
         var s0:String = "";
         var c0:Number = this.stopDelay - this.stopDelay_t;
         if(c0 <= 0)
         {
            s0 = "存档时间过长，你可以尝试终止存档。";
         }
         else
         {
            s0 = "如果存档时间超过" + Math.ceil(c0) + "秒，则可以终止存档。";
         }
         this.saveTxt.text = s0;
      }
      
      private function stopClick(e:MouseEvent) : void
      {
         if(this.stopDelay_t > this.stopDelay)
         {
            this.stopDelay_t = 0;
            this.stopDelay = 0;
            this.haveStopB = false;
            this.stopBtn.visible = false;
            this.saveTxt.visible = false;
            this.hide();
         }
      }
      
      public function FTimerAll() : void
      {
         if(this.haveStopB)
         {
            if(visible)
            {
               if(this.stopDelay_t > this.stopDelay)
               {
                  if(!this.stopBtn.actived)
                  {
                     this.stopBtn.actived = true;
                     this.fleshSaveText();
                  }
               }
               else
               {
                  this.stopDelay_t += 0.03333333333333333;
                  this.fleshSaveText();
                  if(this.stopBtn.actived)
                  {
                     this.stopBtn.actived = false;
                  }
               }
            }
         }
      }
   }
}

