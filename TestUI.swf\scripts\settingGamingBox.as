package
{
   import flash.display.MovieClip;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol222")]
   public dynamic class settingGamingBox extends MovieClip
   {
      public var aboutBtn:MovieClip;
      
      public var resumeBtn:MovieClip;
      
      public var mainBtn:MovieClip;
      
      public var restartBtn:MovieClip;
      
      public var saveBtn:MovieClip;
      
      public var volumeSp:MovieClip;
      
      public function settingGamingBox()
      {
         super();
      }
   }
}

