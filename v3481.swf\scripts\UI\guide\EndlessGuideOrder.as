package UI.guide
{
   import UI.base.button.NormalBtn;
   import dataAll._app.worldMap.define.WorldMapDefine;
   
   public class EndlessGuideOrder extends NormalGuideOrder
   {
      public function EndlessGuideOrder()
      {
         super();
         arr = [this.btn];
      }
      
      private function btn() : OneGuideData
      {
         var btn0:NormalBtn = null;
         var name0:String = this.getName();
         if(name0 != "")
         {
            btn0 = Gaming.uiGroup.worldMapBox.getBtnByName(name0);
         }
         return new OneGuideData(btn0,"点击此地图\n可进入无尽模式");
      }
      
      private function getName() : String
      {
         var d0:WorldMapDefine = null;
         var d_arr0:Array = Gaming.defineGroup.worldMap.arr;
         for(var n:int = d_arr0.length - 1; n >= 0; n--)
         {
            d0 = d_arr0[n];
            if(d0.lv >= 35)
            {
               if(Gaming.PG.da.worldMap.saveGroup.getWinB(d0.name))
               {
                  return d0.name;
               }
            }
         }
         return "";
      }
   }
}

