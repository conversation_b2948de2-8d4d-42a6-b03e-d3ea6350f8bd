package UI
{
   import UI.base.AppNormalUI;
   import UI.pay.PayCtrl;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsCompareData;
   import dataAll.items.ItemsDataGroup;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   
   public class UIShow
   {
      public static var UIG:UIGroup = null;
      
      public static var nowApp:AppNormalUI = null;
      
      private static var beforeLevelApp:AppNormalUI = null;
      
      public function UIShow()
      {
         super();
      }
      
      public static function chooseLevel() : void
      {
         beforeLevelApp = nowApp;
      }
      
      public static function getBeforeLevelApp() : AppNormalUI
      {
         return beforeLevelApp;
      }
      
      public static function getBeforeAppLabel() : String
      {
         if(Boolean(beforeLevelApp))
         {
            return beforeLevelApp.UILabel;
         }
         return "";
      }
      
      public static function clearBeforeLevelApp() : void
      {
         beforeLevelApp = null;
      }
      
      public static function hideNowApp(e:* = null) : void
      {
         if(<PERSON><PERSON>an(nowApp))
         {
            nowApp.hide();
         }
      }
      
      public static function loading(mB:Boolean = false, wB:Boolean = false) : Boolean
      {
         UIG.hideAllAppUI();
         UIG.hideAllBackUI();
         UIG.loadingUI.show();
         return true;
      }
      
      public static function main(hideAllB0:Boolean = true, showB0:Boolean = true) : void
      {
         if(hideAllB0)
         {
            UIG.hideAllAppUI();
            UIG.hideAllBackUI();
         }
         if(showB0)
         {
            UIG.mainUI.show();
            UIG.moreBox.show();
         }
      }
      
      public static function gameWorld() : void
      {
         UIG.hideAllAppUI();
         UIG.hideAllBackUI();
         UIG.moreBox.hide();
         UIG.gameWorldUI.show();
      }
      
      public static function login() : void
      {
         UIG.hideAllAppUI();
         UIG.hideAllBackUI();
         UIG.loginUI.show();
      }
      
      public static function showByLabel(str0:String) : void
      {
         var fun0:Function = UIShow["show_" + str0];
         if(fun0 is Function)
         {
            fun0();
         }
         else
         {
            showApp(str0);
         }
      }
      
      private static function show_bossCard() : void
      {
         showApp("bossedit");
         Gaming.uiGroup.bosseditUI.showAndChooseBox("card");
      }
      
      private static function show_notice() : void
      {
         if(UIG.noticeUI.visible)
         {
            UIG.noticeUI.hide();
         }
         else
         {
            if(!UIG.loginUI.visible)
            {
               UIG.hideAllAppUI();
            }
            UIG.noticeUI.show();
         }
      }
      
      private static function show_bbs() : void
      {
         navigateToURL(new URLRequest("https://my.4399.com/forums-mtag-tagid-81949.html"),"blank");
      }
      
      private static function show_qa() : void
      {
         navigateToURL(new URLRequest("https://www.4399api.com/feedback/game/2"),"blank");
      }
      
      private static function show_arenaSeason() : void
      {
         navigateToURL(new URLRequest("https://my.4399.com/forums/thread-48405043"),"blank");
      }
      
      private static function show_pay() : void
      {
         PayCtrl.gotoPay();
      }
      
      public static function showApp(name0:String, mustB:Boolean = false) : Boolean
      {
         var ui0:AppNormalUI = UIG.getAppUI(name0);
         if(Boolean(ui0))
         {
            if(ui0.visible && !mustB)
            {
               ui0.hide();
            }
            else
            {
               UIG.hideAllAppUI();
               ui0.show();
            }
            return ui0.visible;
         }
         return false;
      }
      
      public static function reShowNowApp() : void
      {
         var ui0:AppNormalUI = null;
         if(Boolean(nowApp))
         {
            if(UIGroup.mustShowMoreNameArr.indexOf(nowApp.UILabel) >= 0)
            {
               ui0 = nowApp;
               ui0.hide();
               ui0.show();
            }
         }
      }
      
      public static function appUIShowEvent(ui0:AppNormalUI) : void
      {
         nowApp = ui0;
         Gaming.LG.pauseLevel();
         Gaming.uiGroup.btnList.hide();
         if(!UIG.mainUI.visible)
         {
            if(UIGroup.mustShowMoreNameArr.indexOf(nowApp.UILabel) >= 0)
            {
               UIG.moreBox.show();
               UIG.gameWorldUI.moreHeadBox.visible = false;
            }
         }
         else
         {
            UIG.moreBox.setShow(UIG.getMoreBoxVisible());
         }
      }
      
      public static function appUIHideEvent(ui0:AppNormalUI = null) : void
      {
         if(nowApp == ui0)
         {
            nowApp = null;
         }
         Gaming.uiGroup.btnList.hide();
         Gaming.uiGroup.mustBox.hide();
         if(UIG.mainUI.visible)
         {
            UIG.moreBox.showNoFlesh();
         }
         if(Gaming.LG.state == "pause")
         {
            if(UIG.canResumeB())
            {
               Gaming.LG.resumeLevel();
               UIG.moreBox.hide();
               UIG.gameWorldUI.moreHeadBox.visible = true;
            }
         }
      }
      
      public static function getNowUICompareDataBy(da0:IO_ItemsData, dg0:ItemsDataGroup) : ItemsCompareData
      {
         return getNowUICompareData(new ItemsCompareData(dg0,da0));
      }
      
      public static function getNowUICompareData(dd0:ItemsCompareData) : ItemsCompareData
      {
         if(nowApp is AppNormalUI)
         {
            return nowApp.getNowUICompareData(dd0);
         }
         return ItemsCompareData.ZERO;
      }
      
      public static function flesh_coinChange() : void
      {
         UIG.wearUI.fleshInfo();
         UIG.mainUI.fleshCoin();
         UIG.shopUI.fleshCoin();
      }
   }
}

