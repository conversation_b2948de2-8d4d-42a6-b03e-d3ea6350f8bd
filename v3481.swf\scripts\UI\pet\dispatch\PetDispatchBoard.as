package UI.pet.dispatch
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.base.btnList.BtnBox;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.NumberMethod;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.pet.PetData;
   import dataAll.pet.PetDataGroup;
   import dataAll.pet.dispatch.PetDispatchData;
   import dataAll.pet.dispatch.PetDispatchSave;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class PetDispatchBoard extends BtnBox
   {
      private var closeBtn:SimpleButton;
      
      private var gripBox:ItemsGripBox = new ItemsGripBox();
      
      private var gripTag:Sprite;
      
      private var infoTxt:TextField;
      
      private var startBtn:NormalBtn;
      
      private var giftBtn:NormalBtn;
      
      private var cancelBtn:NormalBtn;
      
      private var tipBtn:SimpleButton;
      
      private var chooseBox:PetDispatchChooseBox = new PetDispatchChooseBox();
      
      public function PetDispatchBoard()
      {
         super();
         setBtnB = true;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["closeBtn","gripTag","infoTxt","tipBtn"];
         super.setImg(img0);
         this.gripBox.setIconPro("PetUI/dispatchBtn");
         this.gripBox.arg.init(2,2,5,5);
         this.gripBox.evt.setWant(true,true);
         this.gripBox.setCtrlBtnImgType("BasicUI/gripDelBtn","205,15");
         addChild(this.gripBox);
         NormalUICtrl.setTag(this.gripBox,this.gripTag);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.gripBox.addEventListener(ClickEvent.ON_CTRL_CLICK,this.ctrlClick);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         this.startBtn.setName("开始派遣");
         this.cancelBtn.setName("取消派遣");
         this.giftBtn.setName("领取奖励");
         this.chooseBox.setImg(Gaming.swfLoaderManager.getResource("PetUI","dispatchChooseBox"));
         addChild(this.chooseBox);
         this.chooseBox.hide();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         hide();
      }
      
      private function get petData() : PetDataGroup
      {
         return Gaming.PG.da.pet;
      }
      
      private function get dispatchData() : PetDispatchData
      {
         return Gaming.PG.da.pet.dispatch;
      }
      
      override public function show() : void
      {
         this.fleshGetTime();
      }
      
      private function fleshGetTime() : void
      {
         Gaming.uiGroup.connectUI.show();
         Gaming.api.save.getServerTime(this.afterGetTime);
      }
      
      private function afterGetTime(timeStr0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         super.show();
         this.fleshData(timeStr0);
      }
      
      private function fleshNoTime() : void
      {
         this.fleshData(Gaming.api.save.nowSeverTime);
      }
      
      private function fleshData(nowTime0:String) : void
      {
         var newGrip0:ItemsGrid = null;
         var dataArr0:Array = this.dispatchData.getDataArr();
         this.gripBox.inData_byArr(dataArr0,"inData_dispatchPet");
         if(dataArr0.length < PetDispatchSave.getDispatchMax())
         {
            this.gripBox.addNewGrip(1);
            newGrip0 = this.gripBox.gripArr[this.gripBox.gripArr.length - 1];
            newGrip0.inData_dispatchPet(null);
         }
         var surplusMin0:Number = this.dispatchData.getSurplusMin(nowTime0);
         this.infoTxt.htmlText = this.getDispatchInfo(dataArr0,surplusMin0);
         this.fleshBtn(dataArr0,surplusMin0);
      }
      
      private function getDispatchInfo(dataArr0:Array, surplusMin0:Number) : String
      {
         var allHour0:int = 0;
         var s0:String = "";
         if(this.dispatchData.isOver())
         {
            s0 = ComMethod.color("今日派遣次数已用完","#FF3300");
         }
         else
         {
            allHour0 = this.dispatchData.getAllHour();
            if(this.dispatchData.isIng())
            {
               s0 = ComMethod.color(surplusMin0 > 0 ? "正在派遣中……" : "派遣已结束","#FFFF00");
               s0 += "\n总派遣时间 " + NumberMethod.to2(allHour0) + ":00";
               s0 += "\n剩余派遣时间 " + ComMethod.getTimeStrTwo(surplusMin0);
            }
            else if(dataArr0.length == 0)
            {
               s0 = ComMethod.color("请在上方添加派遣宠物","#00FFFF");
            }
            else
            {
               s0 += "总派遣时间 " + NumberMethod.to2(allHour0) + ":00";
            }
         }
         return s0;
      }
      
      private function fleshBtn(dataArr0:Array, surplusMin0:Number) : void
      {
         if(this.dispatchData.isIng())
         {
            this.startBtn.visible = false;
            this.giftBtn.visible = true;
            this.giftBtn.actived = this.dispatchData.getAllHour() * 60 - surplusMin0 >= 60;
            this.cancelBtn.visible = surplusMin0 > 0;
            this.gripBox.setCtrlVisible(false);
         }
         else
         {
            this.startBtn.visible = true;
            this.startBtn.actived = this.dispatchData.isNo() && dataArr0.length > 0;
            this.giftBtn.visible = false;
            this.cancelBtn.visible = false;
         }
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var grip0:ItemsGrid = e.child as ItemsGrid;
         var da0:PetData = grip0.itemsData as PetData;
         if(!da0)
         {
            this.chooseBox.showChoose(this.afterChoose,this.dispatchData.getCanAddNum());
         }
      }
      
      private function afterChoose(dataArr0:Array) : void
      {
         if(dataArr0.length > 0)
         {
            this.dispatchData.addDataArr(dataArr0);
            this.fleshNoTime();
         }
      }
      
      private function ctrlClick(e:ClickEvent) : void
      {
         var grip0:ItemsGrid = e.child as ItemsGrid;
         var da0:PetData = grip0.itemsData as PetData;
         this.dispatchData.removeData(da0);
         this.fleshNoTime();
      }
      
      private function tipOver(e:MouseEvent) : void
      {
         var str0:String = "";
         str0 += "1、宠物派遣时间由宠物生命值决定，派遣时间越长，则获得奖励数量就越多。";
         str0 += "\n2、获得高级物品奖励的概率由宠物战斗力和培养等级共同决定。";
         str0 += "\n3、每种宠物的派遣奖励都不同。";
         str0 += "\n4、每日只有一次派遣机会。";
         str0 += "\n5、同时你可以在派遣时间没有完成之前领取派遣奖励，但是奖励数量相应会减少。";
         str0 += "\n6、派遣期间可取消派遣，之后可再次选择宠物进行派遣。";
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.setText(str0);
            Gaming.uiGroup.tipBox.textTip.show();
            Gaming.uiGroup.tipBox.followMouseB = true;
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      private function tipOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var funName0:String = btn0.label + "BtnClick";
         this[funName0](e);
      }
      
      private function startBtnClick(e:MouseEvent) : void
      {
         if(this.dispatchData.isNo())
         {
            Gaming.uiGroup.connectUI.show();
            Gaming.api.save.getServerTime(this.startAfterGetTime);
         }
      }
      
      private function startAfterGetTime(timeStr0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         this.dispatchData.start(timeStr0);
         this.fleshNoTime();
      }
      
      private function cancelBtnClick(e:MouseEvent) : void
      {
         this.dispatchData.cancel();
         this.fleshNoTime();
      }
      
      private function giftBtnClick(e:MouseEvent) : void
      {
         var surplusMin0:Number = this.dispatchData.getSurplusMin(Gaming.api.save.nowSeverTime);
         if(surplusMin0 > 0)
         {
            Gaming.uiGroup.alertBox.showNormal("派遣时间还未结束，是否提早领取奖励？","yesAndNo",this.gotoGift);
         }
         else
         {
            this.gotoGift();
         }
      }
      
      private function gotoGift() : void
      {
         UIOrder.getStoreState(this.saveGift);
      }
      
      private function saveGift(v0:int) : void
      {
         var gift0:GiftAddDefineGroup = null;
         if(v0 == 1 || v0 == -2)
         {
            gift0 = this.dispatchData.getGiftEvent(Gaming.api.save.nowSeverTime);
            Gaming.PG.da.thingsBag.addGiftGroup(gift0);
            UIOrder.save(true,false,false,this.yesSaveGift,this.noSaveGift);
         }
      }
      
      private function yesSaveGift(v:* = null) : void
      {
         var gift0:GiftAddDefineGroup = this.dispatchData.saveSuccessEvent();
         this.fleshNoTime();
         Gaming.uiGroup.giftShowBox.setGiftAddDefineGroup(gift0,"获得以下奖励");
      }
      
      private function noSaveGift(v:* = null) : void
      {
         var gift0:GiftAddDefineGroup = this.dispatchData.saveFailEvent();
         Gaming.PG.da.thingsBag.removeGiftGroup(gift0);
      }
   }
}

