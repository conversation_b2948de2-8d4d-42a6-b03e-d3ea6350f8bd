package UI.gameWorld.vehicle
{
   import UI.base.AutoNormalUI;
   import UI.base.grid.NormalGridIcon;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class VehicleGrip extends AutoNormalUI
   {
      private var iconTag:Sprite;
      
      private var icon:NormalGridIcon = new NormalGridIcon();
      
      protected var numTxt:TextField;
      
      protected var keyTxt:TextField;
      
      protected var timeTxt:TextField;
      
      protected var cdSp:MovieClip = null;
      
      protected var lifeSp:MovieClip = null;
      
      protected var timeSp:MovieClip = null;
      
      public function VehicleGrip()
      {
         super();
         mcTypeArr = ["txt","tag"];
         this.mouseChildren = false;
         this.mouseEnabled = false;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         this.iconTag.addChild(this.icon);
         this.cdSp.stop();
         this.lifeSp.stop();
         this.lifeSp.stop();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function inAgent(a0:GWVehicleAgent) : void
      {
         if(Boolean(a0))
         {
            visible = a0.visible;
            if(a0.visible)
            {
               this.icon.setIconName(a0.iconLabel);
               this.setLifePer(a0.lifePer);
               this.setTimePer(a0.timePer);
               this.setCdPer(a0.cdPer);
               this.setKeyText(a0.keyName);
               this.setNumText(a0.numStr);
               this.setTimeText(a0.timeStr);
            }
         }
         else
         {
            visible = false;
         }
      }
      
      public function setKeyText(str0:String) : void
      {
         if(this.keyTxt is TextField)
         {
            this.keyTxt.text = str0;
         }
      }
      
      public function setNumText(str0:String) : void
      {
         if(this.numTxt is TextField)
         {
            this.numTxt.text = str0;
            this.numTxt.visible = str0 != "";
         }
      }
      
      public function setTimeText(str0:String) : void
      {
         if(this.timeTxt is TextField)
         {
            this.timeTxt.text = str0;
            this.timeTxt.visible = str0 != "";
         }
      }
      
      public function setLifePer(per0:Number) : void
      {
         if(Boolean(this.lifeSp))
         {
            if(per0 <= 0)
            {
               this.lifeSp.visible = false;
            }
            else
            {
               if(per0 > 1)
               {
                  per0 = 1;
               }
               this.lifeSp.visible = true;
               this.lifeSp.gotoAndStop(int((1 - per0) * this.lifeSp.totalFrames) + 1);
            }
         }
      }
      
      public function setTimePer(per0:Number) : void
      {
         if(Boolean(this.timeSp))
         {
            if(per0 <= 0)
            {
               this.timeSp.visible = false;
            }
            else
            {
               if(per0 > 1)
               {
                  per0 = 1;
               }
               this.timeSp.visible = true;
               this.timeSp.gotoAndStop(int((1 - per0) * this.timeSp.totalFrames) + 1);
            }
         }
      }
      
      public function setCdPer(per0:Number) : void
      {
         if(Boolean(this.cdSp))
         {
            if(per0 >= 1)
            {
               this.cdSp.visible = false;
            }
            else
            {
               if(per0 < 0)
               {
                  per0 = 0;
               }
               this.cdSp.visible = true;
               this.cdSp.gotoAndStop(int(per0 * this.cdSp.totalFrames));
            }
         }
      }
   }
}

