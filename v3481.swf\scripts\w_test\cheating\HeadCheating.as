package w_test.cheating
{
   public class HeadCheating extends OneCheating
   {
      public function HeadCheating()
      {
         super();
      }
      
      public function addNowHead(str0:String, v0:int) : String
      {
         var name0:String = Gaming.uiGroup.headUI.noBoard.nowChooseName;
         if(name0 != "")
         {
            Gaming.PG.da.head.addHead(name0,Gaming.api.save.getNowServerDate().getStr());
            return "添加称号：" + name0;
         }
         return "";
      }
   }
}

