package UI.edit.list
{
   import UI.UIGroup;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import com.sounto.utils.TextMethod;
   import dataAll._app.edit.list.EditListAgent;
   import dataAll._base.IO_Define;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.text.TextField;
   
   public class EditListBox extends AutoNormalUI
   {
      private var nowAgent:EditListAgent = null;
      
      private var nowTitle:String = "";
      
      private var chooseTag:Sprite;
      
      private var titleTxt:TextField;
      
      private var infoTxt:TextField;
      
      private var s1Txt:TextField;
      
      private var noBtn:NormalBtn;
      
      private var txtArr:Array = [];
      
      private var mouseTxt:TextField = null;
      
      private var mouseLine:int = -1;
      
      public function EditListBox()
      {
         super();
         mcTypeArr = ["txt","tag","btnSp"];
      }
      
      override protected function firstLoad() : void
      {
         setImgUrl("BosseditUI/listBox");
         this.noBtn.setName("取消");
         FontDeal.dealOne(this.infoTxt);
         FontDeal.dealLine(this.titleTxt);
         TextMethod.setAutoFormat(this.titleTxt,"center");
         this.titleTxt.styleSheet = TextMethod.getLinkCss("#C6784E","#FFFF00",false);
         this.titleTxt.addEventListener(TextEvent.LINK,this.titleClick);
         FontDeal.dealLine(this.s1Txt);
         TextMethod.setAutoFormat(this.s1Txt);
         this.s1Txt.styleSheet = TextMethod.getLinkCss("#66CC99","#FFFF00",false);
         this.addTxtArr(this.s1Txt,4,140);
         UIGroup.setUIMiddle(this,false,831,510);
      }
      
      private function addTxtArr(frist0:TextField, num0:int, xgap0:int) : void
      {
         var txt0:TextField = null;
         this.txtArr.push(frist0);
         for(var i:int = 0; i < num0; i++)
         {
            txt0 = TextMethod.copy(frist0);
            txt0.htmlText = frist0.htmlText;
            txt0.x = this.s1Txt.x + xgap0 * (i + 1);
            txt0.y = this.s1Txt.y;
            this.txtArr.push(txt0);
            addChild(txt0);
         }
         for each(txt0 in this.txtArr)
         {
            txt0.addEventListener(TextEvent.LINK,this.linkClick);
            txt0.addEventListener(MouseEvent.MOUSE_MOVE,this.textMove);
            txt0.addEventListener(MouseEvent.MOUSE_OUT,this.textOut);
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      public function outLoginEvent() : void
      {
         this.nowAgent = null;
         this.nowTitle = "";
      }
      
      override public function show() : void
      {
         super.show();
      }
      
      override public function hide() : void
      {
         this.mouseTxt = null;
         this.mouseLine = -1;
         super.hide();
      }
      
      private function clearShow() : void
      {
         this.titleTxt.htmlText = "";
         this.clearText();
      }
      
      private function clearText() : void
      {
         var txt0:TextField = null;
         for each(txt0 in this.txtArr)
         {
            txt0.htmlText = "";
         }
      }
      
      public function showAgent(a0:EditListAgent) : void
      {
         this.show();
         this.nowAgent = a0;
         this.nowTitle = a0.getFirstTitle(this.nowTitle);
         this.flesh();
      }
      
      private function flesh() : void
      {
         var title0:String = null;
         var titleIndex0:int = 0;
         var midY0:int = 0;
         var strArr0:Array = null;
         var i:int = 0;
         var txt0:TextField = null;
         if(Boolean(this.nowAgent))
         {
            title0 = this.nowTitle;
            titleIndex0 = this.nowAgent.getTitleIndex(title0);
            this.titleTxt.htmlText = FontDeal.getDealLeadingStr(this.titleTxt,this.nowAgent.getTitleHtml(title0));
            this.infoTxt.text = this.nowAgent.info;
            if(titleIndex0 >= 0)
            {
               midY0 = TextMethod.getLineMidY(this.titleTxt,titleIndex0 + 1);
               this.chooseTag.visible = true;
               this.chooseTag.y = midY0 - this.chooseTag.height / 2 + 3;
               strArr0 = this.nowAgent.getTextArr(title0);
               if(Boolean(strArr0))
               {
                  i = 0;
                  for each(txt0 in this.txtArr)
                  {
                     if(i <= strArr0.length - 1)
                     {
                        txt0.htmlText = FontDeal.getDealLeadingStr(txt0,strArr0[i]);
                     }
                     else
                     {
                        txt0.htmlText = "";
                     }
                     i++;
                  }
               }
               else
               {
                  this.clearText();
               }
            }
            else
            {
               this.chooseTag.visible = false;
               this.clearText();
            }
         }
         else
         {
            this.clearShow();
         }
      }
      
      private function titleClick(e:TextEvent) : void
      {
         this.nowTitle = e.text;
         this.flesh();
      }
      
      private function linkClick(e:TextEvent) : void
      {
         if(Boolean(this.nowAgent))
         {
            if(this.nowAgent.linkFun is Function)
            {
               this.nowAgent.linkFun(e.text);
            }
            if(this.nowAgent.linkAfterHideB)
            {
               this.hide();
            }
         }
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0 == this.noBtn)
         {
            this.hide();
         }
      }
      
      private function textMove(e:MouseEvent) : void
      {
         var txt0:TextField = null;
         var lineH0:Number = NaN;
         var line0:int = 0;
         var d0:IO_Define = null;
         if(Boolean(this.nowAgent) && this.nowAgent.tipFun is Function)
         {
            txt0 = e.target as TextField;
            lineH0 = TextMethod.getLineHeight(txt0);
            line0 = txt0.mouseY / lineH0;
            if(txt0 != this.mouseTxt || this.mouseLine != line0)
            {
               this.mouseTxt = txt0;
               this.mouseLine = line0;
               d0 = this.nowAgent.getData(this.nowTitle,this.txtArr.indexOf(txt0),line0);
               this.nowAgent.tipFun(d0);
            }
         }
      }
      
      private function textOut(e:MouseEvent) : void
      {
         this.mouseTxt = null;
         this.mouseLine = -1;
         Gaming.uiGroup.tipBox.hide();
      }
      
      override public function get width() : Number
      {
         return 831;
      }
      
      override public function get height() : Number
      {
         return 510;
      }
   }
}

