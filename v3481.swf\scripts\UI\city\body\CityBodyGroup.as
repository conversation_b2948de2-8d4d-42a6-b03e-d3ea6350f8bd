package UI.city.body
{
   import UI.city.body.house.CityHouseBody;
   import com.sounto.math.RectMethod;
   import com.sounto.utils.ArrayMethod;
   import dataAll._app.city.CityData;
   import dataAll._app.city.define.CityBodyDefine;
   import dataAll._app.city.define.CityBodyType;
   import dataAll._app.city.dress.CityDressData;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.geom.Rectangle;
   import flash.ui.Mouse;
   import flash.ui.MouseCursor;
   
   public class CityBodyGroup
   {
      private var initB:Boolean = false;
      
      private var con:CityCon = new CityCon();
      
      private var arr:Array = [];
      
      private var dressObj:Object = {};
      
      private var mouseBody:CityBody = null;
      
      private var ctrlBody:CityBody = null;
      
      private var limitRect:Rectangle = new Rectangle(34,276,883,300);
      
      public var cursor:String = "auto";
      
      public function CityBodyGroup()
      {
         super();
      }
      
      public function init(mapSp0:Sprite) : void
      {
         if(!this.initB)
         {
            this.initB = true;
            this.con.init(mapSp0);
            this.addHouseBySp(mapSp0["house"]);
         }
      }
      
      private function get cityData() : CityData
      {
         return Gaming.PG.da.city;
      }
      
      public function getCon() : CityCon
      {
         return this.con;
      }
      
      public function fleshBody(mapSp0:Sprite) : void
      {
         this.init(mapSp0);
         this.fleshDressBody();
         this.con.sortLayer();
      }
      
      private function addHouseBySp(sp0:Sprite) : void
      {
         var len0:int = 0;
         var i:int = 0;
         var mc0:DisplayObject = null;
         var name0:String = null;
         var b0:CityBody = null;
         if(Boolean(sp0))
         {
            len0 = sp0.numChildren;
            for(i = 0; i < len0; i++)
            {
               mc0 = sp0.getChildAt(i);
               name0 = mc0.name;
               b0 = this.addBody(name0);
               b0.x = mc0.x;
               b0.y = mc0.y;
            }
         }
      }
      
      public function fleshDressBody() : void
      {
         var b0:CityBody = null;
         var da0:CityDressData = null;
         var haveDa0:CityDressData = null;
         var dataArr0:Array = this.cityData.dress.getDataArr();
         var newArr0:Array = [];
         var newDressObj0:Object = {};
         for each(b0 in this.arr)
         {
            haveDa0 = b0.getDressData();
            if(Boolean(haveDa0))
            {
               if(dataArr0.indexOf(haveDa0) >= 0)
               {
                  newArr0.push(b0);
                  newDressObj0[haveDa0.getDataId()] = b0;
               }
               else
               {
                  this.removeBodyOnly(b0);
               }
            }
            else
            {
               newArr0.push(b0);
            }
         }
         this.dressObj = newDressObj0;
         this.arr = newArr0;
         for each(da0 in dataArr0)
         {
            if(!this.dressObj[da0.getDataId()])
            {
               this.addBodyByDressData(da0);
            }
         }
      }
      
      public function addDressBody(dressDa0:CityDressData) : CityBody
      {
         var b0:CityBody = this.addBodyByDressData(dressDa0);
         this.limitCoor(b0);
         this.con.sortLayer();
         return b0;
      }
      
      private function addBodyByDressData(dressDa0:CityDressData) : CityBody
      {
         return this.addBody(dressDa0.getDefine().name,dressDa0);
      }
      
      private function addBody(name0:String, dressDa0:CityDressData = null) : CityBody
      {
         var d0:CityBodyDefine = Gaming.defineGroup.cityBody.getDefine(name0);
         var b0:CityBody = this.getNewBodyByType(d0.type);
         b0.init(d0,dressDa0);
         this.con.things.addChild(b0.getSprite());
         if(Boolean(b0.getShadow()))
         {
            this.con.shadow.addChild(b0.getShadow());
         }
         this.arr.push(b0);
         if(Boolean(dressDa0))
         {
            this.dressObj[dressDa0.getDataId()] = b0;
         }
         return b0;
      }
      
      private function getNewBodyByType(type0:String) : CityBody
      {
         if(type0 == CityBodyType.house)
         {
            return new CityHouseBody();
         }
         return new CityBody();
      }
      
      public function removeBody(b0:CityBody) : void
      {
         var dataId0:String = null;
         this.removeBodyOnly(b0);
         ArrayMethod.remove(this.arr,b0);
         var da0:CityDressData = b0.getDressData();
         if(Boolean(da0))
         {
            dataId0 = da0.getDataId();
            if(Boolean(this.dressObj[dataId0]))
            {
               this.dressObj[dataId0] = null;
            }
         }
      }
      
      private function removeBodyOnly(b0:CityBody) : void
      {
         this.con.things.removeChild(b0.getSprite());
         if(Boolean(b0.getShadow()))
         {
            this.con.shadow.removeChild(b0.getShadow());
         }
         if(this.mouseBody == b0)
         {
            this.setMouse(null);
         }
      }
      
      public function setCtrlBodyEvent(b0:CityBody) : void
      {
         this.ctrlBody = b0;
         if(!Boolean(b0))
         {
            this.con.sortLayer();
         }
      }
      
      public function setMouse(b0:CityBody) : void
      {
         if(this.mouseBody != b0)
         {
            if(Boolean(this.mouseBody))
            {
               this.mouseBody.mouseOut();
               this.cursor = MouseCursor.AUTO;
            }
            if(Boolean(b0))
            {
               b0.mouseOver();
               this.cursor = MouseCursor.BUTTON;
            }
            this.mouseBody = b0;
         }
         if(Boolean(this.mouseBody) && this.mouseBody == this.ctrlBody)
         {
            this.cursor = MouseCursor.HAND;
         }
         Mouse.cursor = this.cursor;
      }
      
      public function getMouse() : CityBody
      {
         return this.mouseBody;
      }
      
      public function limitCoor(b0:Object, fixedB0:Boolean = false) : void
      {
         var x0:Number = Number(b0.x);
         var y0:Number = Number(b0.y);
         x0 = RectMethod.xLimitInRect(x0,this.limitRect);
         y0 = RectMethod.yLimitInRect(y0,this.limitRect);
         b0.x = x0;
         b0.y = y0;
      }
      
      public function FTimer(dressB0:Boolean, dragB0:Boolean) : void
      {
         var b0:CityBody = null;
         var mouseX0:Number = this.con.things.mouseX;
         var mouseY0:Number = this.con.things.mouseY;
         var maxY0:Number = -99999;
         var mouse0:CityBody = null;
         if(dragB0)
         {
            mouse0 = this.ctrlBody;
         }
         if(!dragB0 || !this.ctrlBody)
         {
            for each(b0 in this.arr)
            {
               if(b0.getMouseB(dressB0))
               {
                  if(b0.hitImg(mouseX0,mouseY0))
                  {
                     if(b0 == this.ctrlBody)
                     {
                        mouse0 = b0;
                        break;
                     }
                     if(maxY0 < b0.y)
                     {
                        maxY0 = b0.y;
                        mouse0 = b0;
                     }
                  }
               }
            }
         }
         this.setMouse(mouse0);
      }
   }
}

