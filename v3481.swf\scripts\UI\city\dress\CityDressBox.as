package UI.city.dress
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.label.LabelBox;
   import UI.city.CityEvent;
   import UI.city.body.CityBody;
   import UI.city.body.CityBodyCtrler;
   import UI.city.body.CityBodyGroup;
   import com.sounto.utils.TextMethod;
   import dataAll._app.city.CityData;
   import dataAll._app.city.dress.CityDressDataGroup;
   import dataAll._app.city.dress.CityDressMould;
   import dataAll._app.city.dress.CityDressType;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class CityDressBox extends AutoNormalUI
   {
      private var BG:CityBodyGroup;
      
      private var closeBtn:NormalBtn;
      
      private var addBtn:NormalBtn;
      
      private var clearBtn:NormalBtn;
      
      private var labelTag:Sprite;
      
      private var gripTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var cancelSp:Sprite;
      
      private var addBoxSp:Sprite;
      
      private var spaceTxt:TextField;
      
      private var labelBox:LabelBox = new LabelBox();
      
      private var gripBox:ItemsGripBox = new ItemsGripBox();
      
      private var addBox:CityDressAddBox = new CityDressAddBox();
      
      private var putMould:CityDressMould = null;
      
      private var putCon:Sprite = new Sprite();
      
      private var putImg:DisplayObject = null;
      
      private var ctrler:CityBodyCtrler = null;
      
      public function CityDressBox()
      {
         super();
         this.mouseEnabled = false;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["cancelSp","addBoxSp"];
         super.setImg(img0);
         FontDeal.dealOne(this.spaceTxt);
         this.cancelSp.visible = false;
         this.labelBox.arg.init(1,10,0,-9);
         this.labelBox.inData("CityUI/dressLabelBtn",CityDressType.uiArr,CityDressType.cnArr);
         NormalUICtrl.setTag(this.labelBox,this.labelTag);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         addChild(this.labelBox);
         this.gripBox.imgType = "CityUI/dressGrip";
         this.gripBox.arg.init(1,6,0,10);
         this.gripBox.evt.setWantEvent(true,false,false,true,true,true);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.gripBox.addEventListener(ClickEvent.ON_OVER,this.gripOver);
         this.gripBox.addEventListener(ClickEvent.ON_OUT,this.gripOut);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.gripBox);
         NormalUICtrl.setTag(this.gripBox,this.gripTag);
         this.gripBox.pageBox.setToSmall();
         this.gripBox.setPagePos(this.pageTag);
         addChild(this.gripBox);
         this.addBox.setImg(this.addBoxSp);
         this.addBox.addEventListener(ClickEvent.ON_FLESH,this.onlyFleshSpace);
         this.addBox.hide();
         addChild(this.addBox);
         this.addBtn.addEventListener(MouseEvent.CLICK,this.addBtnClick);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         this.clearBtn.addEventListener(MouseEvent.CLICK,this.clearClick);
         this.putCon.mouseChildren = false;
         this.putCon.mouseEnabled = false;
         this.putCon.visible = false;
         this.cancelSp.addEventListener(MouseEvent.CLICK,this.cancelClick);
         addChild(this.cancelSp);
         addChild(this.putCon);
         addChild(this.closeBtn);
      }
      
      public function init(ctrler0:CityBodyCtrler, BG0:CityBodyGroup) : void
      {
         this.BG = BG0;
         this.ctrler = ctrler0;
         this.ctrler.addEventListener(CityEvent.ADD_CITY_BODY,this.ctrlBodyEvent);
         this.ctrler.addEventListener(CityEvent.REMOVE_CITY_BODY,this.ctrlBodyEvent);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         if(!img)
         {
            setImgUrl("CityUI/dressBox");
         }
         super.show();
         this.showLabel(this.labelBox.nowLabel);
      }
      
      override public function hide() : void
      {
         super.hide();
         this.clearCtrlState();
      }
      
      private function get cityData() : CityData
      {
         return Gaming.PG.da.city;
      }
      
      private function get dressData() : CityDressDataGroup
      {
         return Gaming.PG.da.city.dress;
      }
      
      public function clearCtrlState() : void
      {
         this.clearPut();
         this.addBox.hide();
         this.ctrler.setCtrlBody(null);
         this.ctrler.clearDrag();
      }
      
      public function mouseUp(b0:CityBody) : Boolean
      {
         var chooseBody0:CityBody = null;
         if(visible)
         {
            chooseBody0 = null;
            if(Boolean(this.putMould))
            {
               this.putBody(this.putMould);
            }
            else if(Boolean(b0))
            {
               chooseBody0 = b0;
            }
            this.ctrler.setCtrlBody(chooseBody0);
            return false;
         }
         return true;
      }
      
      private function startPut(mould0:CityDressMould) : void
      {
         this.addBox.hide();
         if(this.putMould != mould0)
         {
            this.clearPut();
            this.putMould = mould0;
            this.putImg = CityBody.getNewImg(mould0.getDefine(),mould0);
            this.putCon.addChild(this.putImg);
         }
         this.cancelSp.visible = true;
         this.putCon.visible = true;
         this.putConFollowMouse();
         this.ctrler.setCtrlBody(null);
      }
      
      private function clearPut() : void
      {
         this.putMould = null;
         if(Boolean(this.putImg))
         {
            this.putCon.removeChild(this.putImg);
            this.putImg = null;
         }
         if(Boolean(this.cancelSp))
         {
            this.cancelSp.visible = false;
         }
         this.putCon.visible = false;
      }
      
      private function putConFollowMouse() : void
      {
         this.putCon.x = this.putCon.parent.mouseX;
         this.putCon.y = this.putCon.parent.mouseY;
         this.BG.limitCoor(this.putCon,true);
      }
      
      private function putBody(mould0:CityDressMould) : void
      {
         this.ctrler.addDressBody(mould0);
         Gaming.soundGroup.playSound("sound","changeLabel");
         this.clearPut();
      }
      
      private function ctrlBodyEvent(e0:CityEvent) : void
      {
         this.onlyFleshSpace();
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var grip0:ItemsGrid = e.child as ItemsGrid;
         if(grip0.actived)
         {
            this.startPut(e.childData as CityDressMould);
         }
      }
      
      private function gripOver(e:ClickEvent) : void
      {
         var mould0:CityDressMould = e.childData as CityDressMould;
         var grip0:ItemsGrid = e.child as ItemsGrid;
         var tip0:String = "<b>" + TextMethod.color(mould0.getCn(),"#FFFF00") + "</b>";
         tip0 += "\n占用" + mould0.getUseSpace() + "个装扮位";
         var other0:String = "";
         var shopStr0:String = grip0.getShopBtnStr();
         if(!grip0.actived)
         {
            if(shopStr0 == "used")
            {
               other0 = "该装扮物已装扮，无法再次装扮。";
            }
            else
            {
               other0 = "装扮位不足，无法装扮";
            }
            tip0 += "\n" + TextMethod.color(other0,"#FF6600");
         }
         Gaming.uiGroup.tipBox.textTip.showFollowText(tip0);
      }
      
      private function gripOut(e:ClickEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function cancelClick(e:MouseEvent) : void
      {
         this.clearPut();
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.addBox.hide();
         this.showLabel(e.label);
      }
      
      private function showLabel(label0:String) : void
      {
         if(label0 == "")
         {
            label0 = this.labelBox.getFirstLabel();
         }
         this.labelBox.setChoose(label0);
         var type0:String = label0;
         this.gripBox.inData_byArr(this.dressData.getMouldArr(type0),"inData_dressMould");
         this.onlyFleshSpace();
      }
      
      public function onlyFleshSpace(e:* = null) : void
      {
         var type0:String = null;
         var useSpace0:int = 0;
         var maxSpace0:int = 0;
         var grip0:ItemsGrid = null;
         var mould0:CityDressMould = null;
         var mustSpace0:int = 0;
         var spaceB0:Boolean = false;
         var useB0:Boolean = false;
         var shopStr0:String = null;
         if(visible)
         {
            type0 = this.labelBox.nowLabel;
            useSpace0 = this.dressData.getUseSpace(type0);
            maxSpace0 = this.dressData.getMaxSpace(type0);
            this.spaceTxt.htmlText = "装扮位 " + TextMethod.colorSurplusNum(useSpace0,maxSpace0);
            this.dressData.fleshAddNum();
            for each(grip0 in this.gripBox.gripArr)
            {
               grip0.activedAndEnabled = false;
               mould0 = grip0.itemsData as CityDressMould;
               mustSpace0 = mould0.getUseSpace();
               spaceB0 = true;
               if(maxSpace0 - useSpace0 < mustSpace0)
               {
                  spaceB0 = false;
               }
               useB0 = mould0.canUseB();
               grip0.actived = spaceB0 && useB0;
               shopStr0 = "";
               if(!useB0)
               {
                  shopStr0 = "used";
               }
               else if(!spaceB0)
               {
                  shopStr0 = "no";
               }
               grip0.setShopBtnBackMc(shopStr0);
            }
         }
      }
      
      private function addBtnClick(e:MouseEvent) : void
      {
         this.addBox.showType(this.labelBox.nowLabel);
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         this.clearPut();
         this.hide();
      }
      
      private function clearClick(e:MouseEvent) : void
      {
         Gaming.uiGroup.alertBox.showChoose("是否要清空所有装扮物？",this.afterClear);
      }
      
      private function afterClear() : void
      {
         this.dressData.clearAllData();
         this.onlyFleshSpace();
         this.BG.fleshDressBody();
      }
      
      public function FTimer() : void
      {
         if(Boolean(this.putMould))
         {
            this.putConFollowMouse();
         }
      }
   }
}

