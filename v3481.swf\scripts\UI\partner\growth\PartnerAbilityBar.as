package UI.partner.growth
{
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import UI.base.tip.TipBox;
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._app.partner.ability.PartnerAbilityData;
   import dataAll._app.partner.ability.PartnerAbilityDefine;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class PartnerAbilityBar extends AutoNormalUI
   {
      private var nameTxt:TextField;
      
      private var allTxt:TextField;
      
      private var addTxt:TextField;
      
      private var addBtn:NormalBtn;
      
      private var delBtn:NormalBtn;
      
      public var define:PartnerAbilityDefine = null;
      
      private var itemsData:PartnerAbilityData = null;
      
      public var fleshFun:Function;
      
      public function PartnerAbilityBar()
      {
         super();
         btnSetB = true;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         FontDeal.dealOne(this.nameTxt);
         this.addTxt.addEventListener(MouseEvent.CLICK,this.lvTxtClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function get tipBox() : TipBox
      {
         return Gaming.uiGroup.tipBox;
      }
      
      public function fleshData(da0:PartnerAbilityData) : void
      {
         this.itemsData = da0;
         var maxAdd0:Number = da0.getPointAddMax();
         var add0:Number = da0.getPointAdd();
         var surplus0:* = da0.partnerData.getPointSurplus();
         this.nameTxt.text = da0.def.cnName;
         this.addTxt.htmlText = TextMethod.colorMustNum(add0,maxAdd0,"#00FFFF","#00FF00","#858585",true);
         this.addBtn.actived = surplus0 >= 1 && add0 < maxAdd0;
         this.delBtn.visible = add0 > 0;
         if(add0 > 0)
         {
            this.allTxt.textColor = 16776960;
         }
         else
         {
            this.allTxt.textColor = 8750469;
         }
         this.allTxt.text = da0.getPoint() + "";
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var da0:PartnerAbilityData = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0.actived)
         {
            da0 = this.itemsData;
            if(btn0 == this.addBtn)
            {
               da0.addPoint();
               this.fleshFun();
            }
            else if(btn0 == this.delBtn)
            {
               da0.delPoint();
               this.fleshFun();
            }
         }
      }
      
      private function lvTxtClick(e:MouseEvent) : void
      {
         var now0:int = this.itemsData.getPointAdd();
         var max0:int = this.itemsData.getPointAddMaxCan();
         var tip0:String = "输入加点（0~" + max0 + "）";
         Gaming.uiGroup.alertBox.showNumChoose(tip0,now0,max0,0,1,this.yesSetPoint,"yesAndNo",null,null,false);
      }
      
      private function yesSetPoint(lv0:int) : void
      {
         var max0:int = this.itemsData.getPointAddMaxCan();
         lv0 = NumberMethod.limitRange(lv0,0,max0);
         this.itemsData.setPoint(lv0);
         this.fleshFun();
      }
   }
}

