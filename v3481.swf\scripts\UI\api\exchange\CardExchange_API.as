package UI.api.exchange
{
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   
   public class CardExchange_API
   {
      public var yesFun:Function;
      
      public var noFun:Function;
      
      internal var loader:URLLoader = new URLLoader();
      
      internal var url:URLRequest = new URLRequest("https://huodong2.4399.com/2016/xdkp/api.php");
      
      public function CardExchange_API()
      {
         super();
         this.loader.addEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this.loader.addEventListener(Event.COMPLETE,this.loaderCompleteHandler);
      }
      
      public function startExchange(uid0:String, type0:String, _yesFun:Function = null, _noFun:Function = null) : *
      {
         this.yesFun = _yesFun;
         this.noFun = _noFun;
         if(Gaming.api.save.isLocal())
         {
            uid0 = "1069144161";
         }
         var data0:URLVariables = new URLVariables();
         data0.uid = Number(uid0);
         data0.type = type0;
         this.url.data = data0;
         this.url.method = URLRequestMethod.POST;
         this.loader.load(this.url);
      }
      
      private function loaderCompleteHandler(e:Event) : void
      {
         if(this.yesFun is Function)
         {
            this.yesFun(this.loader.data);
         }
      }
      
      private function errorHandler(e:IOErrorEvent) : void
      {
         if(this.noFun is Function)
         {
            this.noFun();
         }
      }
   }
}

