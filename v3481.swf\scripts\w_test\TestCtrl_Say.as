package w_test
{
   import flash.events.KeyboardEvent;
   import flash.ui.Keyboard;
   
   public class TestCtrl_Say extends TestCtrl_Normal
   {
      public function TestCtrl_Say()
      {
         super();
      }
      
      override public function mouseClick() : void
      {
      }
      
      override public function FTimer() : void
      {
      }
      
      override public function keyDown(e:KeyboardEvent) : void
      {
         if(e.keyCode != Keyboard.NUMBER_2)
         {
            if(e.keyCode != Keyboard.NUMBER_3)
            {
               if(e.keyCode != Keyboard.NUMBER_4)
               {
                  if(e.keyCode == Keyboard.NUMBER_5)
                  {
                  }
               }
            }
         }
      }
   }
}

