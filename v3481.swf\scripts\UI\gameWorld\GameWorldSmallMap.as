package UI.gameWorld
{
   import UI.UIOrder;
   import UI.UIShow;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import com.sounto.math.Maths;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.scene.SceneDefine;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   import flash.text.TextField;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.data.NormalBodyData;
   import gameAll.body.motion.NormalGroundMotion;
   import gameAll.drop.body.DropBody;
   import gameAll.hero.HeroBody;
   import gameAll.scene.weather.WeatherData;
   
   public class GameWorldSmallMap extends NormalUI
   {
      public static var SX:Number = 0.04;
      
      private var pointData:BitmapData = null;
      
      private var mapBmp:Bitmap = new Bitmap();
      
      private var pointBmp:Bitmap = new Bitmap(this.pointData);
      
      private var bbsBtnSp:MovieClip;
      
      private var bbsBtn:NormalBtn = new NormalBtn();
      
      private var radarBmp:Bitmap = new Bitmap();
      
      private var radarData:BitmapData = null;
      
      private var mapCon:Sprite = new Sprite();
      
      private var mapRect:Rectangle = null;
      
      private var viewRect:Rectangle = new Rectangle();
      
      private var pointRect:Rectangle = new Rectangle();
      
      private var tempRect:Rectangle = new Rectangle(0,0,3,3);
      
      private var sceneDefine:SceneDefine = null;
      
      private var dropShowB:Boolean = false;
      
      private var heroX:int = 0;
      
      private var heroY:int = 0;
      
      public var maskSp:Sprite = null;
      
      public var timeTxt:TextField = null;
      
      private var model:String = "normal";
      
      private var scale:Number = 1;
      
      private var t:Number = 1;
      
      private var weatherMc:MovieClip = null;
      
      public function GameWorldSmallMap()
      {
         super();
         addChild(this.mapCon);
         this.mapCon.addChild(this.mapBmp);
         this.mapCon.addChild(this.pointBmp);
         this.mapCon.addChild(this.radarBmp);
         this.mapCon.mouseChildren = false;
         this.mapCon.mouseEnabled = false;
         this.mouseEnabled = false;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["maskSp","timeTxt","bbsBtnSp","weatherMc"];
         super.setImg(img0);
         img.mouseEnabled = false;
         img.mouseChildren = false;
         this.timeTxt.mouseEnabled = false;
         this.weatherMc.stop();
         addChild(this.bbsBtn);
         this.bbsBtn.setImg(this.bbsBtnSp);
         this.bbsBtn.addEventListener(MouseEvent.CLICK,this.bbsClick);
         addChild(this.weatherMc);
         this.weatherMc.gotoAndStop("no");
         this.weatherMc.addEventListener(MouseEvent.MOUSE_OVER,this.weatherOver);
         this.weatherMc.addEventListener(MouseEvent.MOUSE_OUT,Gaming.uiGroup.tipBox.hide);
         if(Boolean(this.maskSp))
         {
            this.maskSp.alpha = 0;
            this.mapCon.mask = this.maskSp;
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function startLevel() : void
      {
         var d0:SceneDefine = null;
         var r0:Rectangle = null;
         this.weatherMc.gotoAndStop("no");
         var data0:BitmapData = Gaming.sceneGroup.smallMapData;
         if(Boolean(data0))
         {
            this.dropShowB = Gaming.LG.nowLevel.define.info.dropSmallMapB;
            this.model = "normal";
            this.mapBmp.bitmapData = data0;
            this.mapRect = Gaming.sceneGroup.smallMapRect;
            d0 = Gaming.sceneGroup.define;
            this.sceneDefine = d0;
            r0 = d0.viewRangeRect;
            this.viewRect.x = r0.x * SX;
            this.viewRect.y = r0.y * SX;
            this.viewRect.width = r0.width * SX;
            this.viewRect.height = r0.height * SX;
            this.pointData = new BitmapData(this.viewRect.width,this.viewRect.height,true,0);
            this.pointBmp.bitmapData = this.pointData;
            this.pointRect.width = this.viewRect.width;
            this.pointRect.height = this.viewRect.height;
            this.radarData = new BitmapData(this.maskSp.width,this.maskSp.height,true,0);
            this.radarBmp.bitmapData = this.radarData;
            this.mapBmp.x = this.mapRect.x;
            this.mapBmp.y = this.mapRect.y;
            this.pointBmp.x = this.viewRect.x;
            this.pointBmp.y = this.viewRect.y;
            this.mapCon.y = this.maskSp.height - (this.mapRect.y + this.mapRect.height);
         }
      }
      
      public function clearAll() : void
      {
         this.mapRect = null;
         if(Boolean(this.pointData))
         {
            this.pointData.dispose();
         }
      }
      
      public function setModel(str0:String, scaleMul0:Number = 1) : void
      {
         if(this.model != str0)
         {
            this.pointData.fillRect(this.pointRect,0);
            this.radarData.fillRect(this.radarData.rect,0);
         }
         this.model = str0;
         this.scale = scaleMul0;
      }
      
      public function FTimerSecond() : void
      {
      }
      
      public function FTimer() : void
      {
         var wda0:WeatherData = null;
         var cdMc0:MovieClip = null;
         var f0:int = 0;
         if(this.t > 0.3)
         {
            this.t = 0;
            if(this.model == "radar")
            {
               this.radarDraw();
            }
            else
            {
               this.normalDraw();
            }
            wda0 = Gaming.sceneGroup.weather.getEffectNow();
            if(Boolean(wda0))
            {
               this.weatherMc.gotoAndStop(wda0.getFrameLabel());
               cdMc0 = this.weatherMc["cdMc"];
               if(Boolean(cdMc0))
               {
                  f0 = wda0.getLifePer() * cdMc0.totalFrames;
                  if(f0 < 1)
                  {
                     f0 = 1;
                  }
                  if(f0 > cdMc0.totalFrames)
                  {
                     f0 = cdMc0.totalFrames;
                  }
                  cdMc0.gotoAndStop(f0);
               }
            }
            else
            {
               this.weatherMc.gotoAndStop("no");
            }
         }
         else
         {
            this.t += 0.03333333333333333;
         }
         var timeFixedB0:Boolean = Gaming.LG.isUnionBattleB();
         var boB0:Boolean = timeFixedB0 || !Gaming.LG.isOnlyBoss() && (Gaming.LG.isNormalOnlyB() || Gaming.LG.isDemonB());
         var ts0:String = ComMethod.getTimeStrTwo(Gaming.LG.nowLevel.dat.tempLevelTime,false,timeFixedB0);
         if(boB0)
         {
            ts0 = Gaming.LG.nowLevel.eventG.getEnemyBoStr() + "  " + ts0;
         }
         this.timeTxt.htmlText = ts0;
      }
      
      private function normalDraw() : void
      {
         var x0:int = 0;
         var y0:int = 0;
         if(this.mapRect is Rectangle)
         {
            this.mapBmp.visible = true;
            this.pointData.lock();
            this.pointData.fillRect(this.pointRect,0);
            this.drawBodyArr(Gaming.BG.ENEMY_ARR);
            this.drawBodyArr(Gaming.BG.WE_ARR);
            if(this.dropShowB)
            {
               this.drawDropArr(Gaming.dropGroup.arr);
            }
            this.pointData.unlock();
            x0 = this.maskSp.x + this.maskSp.width / 2 - this.heroX;
            y0 = this.maskSp.y + this.maskSp.height / 2 - this.heroY;
            if(this.viewRect.width <= this.maskSp.width)
            {
               x0 = this.maskSp.x + this.maskSp.width / 2 - this.viewRect.width / 2;
            }
            else if(x0 + this.viewRect.width < this.maskSp.x + this.maskSp.width)
            {
               x0 = this.maskSp.x + this.maskSp.width - this.viewRect.width;
            }
            else if(x0 > this.maskSp.x)
            {
               x0 = this.maskSp.x;
            }
            if(this.viewRect.height <= this.maskSp.height)
            {
               y0 = this.maskSp.y + this.maskSp.height - (this.viewRect.y + this.viewRect.height);
            }
            else if(y0 + this.viewRect.y + this.viewRect.height < this.maskSp.y + this.maskSp.height)
            {
               y0 = this.maskSp.y + this.maskSp.height - this.viewRect.height - this.viewRect.y;
            }
            else if(y0 + this.viewRect.y > this.maskSp.y)
            {
               y0 = this.maskSp.y - this.viewRect.y;
            }
            this.mapCon.x = x0;
            this.mapCon.y = y0;
         }
      }
      
      private function drawBodyArr(arr0:Array) : void
      {
         var n:* = undefined;
         var b0:IO_NormalBody = null;
         var dat0:NormalBodyData = null;
         var mot0:NormalGroundMotion = null;
         var x0:int = 0;
         var y0:int = 0;
         var bcolor0:int = 0;
         var showB0:Boolean = false;
         var color0:* = 0;
         for(n in arr0)
         {
            b0 = arr0[n];
            dat0 = b0.getData();
            mot0 = b0.getMot();
            x0 = mot0.x * SX - this.viewRect.x;
            y0 = mot0.MY * SX - this.viewRect.y;
            this.tempRect.x = x0 - 1;
            this.tempRect.y = y0 - 1;
            bcolor0 = dat0.smallMapColor;
            showB0 = Boolean(b0.getAiFindB());
            color0 = 4294920778;
            if(bcolor0 == -1)
            {
               showB0 = false;
            }
            else if(bcolor0 == -2)
            {
               showB0 = true;
            }
            else
            {
               if(bcolor0 == 0)
               {
                  if(dat0.camp == "we")
                  {
                     if(dat0.playerCtrlB)
                     {
                        color0 = 4294967040;
                        this.heroX = x0;
                        this.heroY = y0;
                     }
                     else
                     {
                        color0 = 4278255360;
                     }
                  }
               }
               else
               {
                  color0 = uint(bcolor0);
               }
               if(dat0.camp == "we")
               {
                  showB0 = dat0.existB;
               }
               else
               {
                  showB0 = Boolean(b0.getAiFindB());
               }
            }
            if(showB0)
            {
               this.pointData.fillRect(this.tempRect,color0);
            }
         }
      }
      
      private function drawDropArr(arr0:Array) : void
      {
         var b0:DropBody = null;
         var color0:String = null;
         var c0:* = 0;
         for each(b0 in arr0)
         {
            color0 = b0.define.smallMapColor;
            if(color0 != "")
            {
               c0 = uint(color0);
               this.tempRect.x = b0.mot.x * SX - this.viewRect.x - 1;
               this.tempRect.y = b0.mot.y * SX - this.viewRect.y - 1;
               this.pointData.fillRect(this.tempRect,c0);
            }
         }
      }
      
      private function radarDraw() : void
      {
         var b0:IO_NormalBody = null;
         var dat0:NormalBodyData = null;
         var cx0:Number = NaN;
         var cy0:Number = NaN;
         var bcolor0:int = 0;
         var ra0:Number = NaN;
         var len0:Number = NaN;
         this.radarData.lock();
         this.radarData.fillRect(this.radarData.rect,0);
         this.mapBmp.visible = false;
         var arr0:Array = Gaming.BG.ENEMY_ARR;
         var hero0:HeroBody = Gaming.PG.ctrlHero;
         var w0:int = this.radarBmp.width;
         var h0:int = this.radarBmp.height;
         for each(b0 in arr0)
         {
            dat0 = b0.getData();
            cx0 = (b0.getMot().x - hero0.mot.x) * this.scale;
            cy0 = (b0.getMot().y - hero0.mot.y) * this.scale;
            bcolor0 = dat0.smallMapColor;
            if(bcolor0 != -1 && bcolor0 != 0 && Boolean(b0.getAiFindB()))
            {
               ra0 = Math.atan2(cy0,cx0);
               len0 = Maths.Long(cx0,cy0);
               if(len0 >= 30)
               {
                  bcolor0 = 1728014079;
                  cx0 = Math.cos(ra0) * 29;
                  cy0 = Math.sin(ra0) * 29;
               }
               else
               {
                  bcolor0 = -39169;
               }
               this.tempRect.x = cx0 + w0 / 2;
               this.tempRect.y = cy0 + h0 / 2;
               this.radarData.fillRect(this.tempRect,bcolor0);
            }
         }
         this.tempRect.x = w0 / 2 - 1;
         this.tempRect.y = h0 / 2 - 1;
         this.radarData.fillRect(this.tempRect,4294967040);
         this.radarData.unlock();
      }
      
      private function bbsClick(e:MouseEvent) : void
      {
         UIShow.showByLabel("bbs");
      }
      
      private function weatherOver(e:MouseEvent) : void
      {
         var da0:WeatherData = Gaming.sceneGroup.weather.getEffectNow();
         if(Boolean(da0))
         {
            UIOrder.showTip(da0.getGatherTip());
         }
      }
   }
}

