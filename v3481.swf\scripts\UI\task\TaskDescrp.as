package UI.task
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.StringMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._app.edit.boss.BossEditData;
   import dataAll._app.task.TaskData;
   import dataAll._app.task.define.TaskDefine;
   import dataAll._app.task.define.TaskType;
   import dataAll._player.PlayerData;
   import dataAll._player.role.RoleName;
   import dataAll.body.define.BodyFather;
   import dataAll.level.define.LevelDefine;
   import dataAll.skill.define.SkillDescrip;
   import dataAll.ui.GatherColor;
   import gameAll.level._diy.active.ArmsEdit23LevelDiy;
   import gameAll.level.event.LevelEventAction;
   
   public class TaskDescrp
   {
      public function TaskDescrp()
      {
         super();
      }
      
      private static function get PD() : PlayerData
      {
         return Gaming.PG.da;
      }
      
      public static function getText(da0:TaskData) : String
      {
         var levelD0:LevelDefine = null;
         var d0:TaskDefine = da0.def;
         var s0:String = d0.description;
         var fun0:Function = TaskDescrp[d0.name];
         if(fun0 is Function)
         {
            s0 = fun0(da0);
         }
         if(da0.def.isMemoryB())
         {
            s0 = memory(da0);
         }
         if(da0.playerData.isDoubleB())
         {
            levelD0 = da0.getLevelDefine();
            if(Boolean(levelD0))
            {
               if(levelD0.info.mustSingleB)
               {
                  s0 += "\n\n" + ComMethod.color("<b>当前任务只能在单人模式下进行，进入任务后系统自动转为单人模式。</b>","#FF80FF");
               }
            }
         }
         if(d0.openD.timeLimitB())
         {
            s0 += "\n活动时间：" + d0.openD.start + "~" + d0.openD.end;
         }
         return s0;
      }
      
      private static function memory(da0:TaskData) : String
      {
         var s0:String = da0.def.description;
         var lv0:int = da0.getLv();
         var skillUnlock0:int = TaskType.getSkillUnlockNum(lv0);
         var partsUnlockB0:Boolean = lv0 >= TaskType.getPartsUnlock();
         var equipUnlock0:String = TaskType.getEquipTypeUnlockText(lv0);
         s0 += "\n" + TextMethod.color("可上场角色：",GatherColor.orangeColor) + RoleName.getMemoryText(lv0);
         s0 += "\n" + TextMethod.color("限制：",GatherColor.orangeColor);
         s0 += "武器等级不超过" + TextMethod.color(lv0 + "级",GatherColor.yellowColor);
         if(partsUnlockB0 == false)
         {
            s0 += "（零件无效）";
         }
         s0 += "，技能位置解锁" + TextMethod.color("前" + skillUnlock0 + "个",GatherColor.yellowColor);
         if(equipUnlock0 != "")
         {
            s0 += "，装备解锁：" + TextMethod.color(equipUnlock0,GatherColor.yellowColor);
         }
         else
         {
            s0 += "，所有装备无效。";
         }
         if(lv0 >= TaskType.getPetOpen_M())
         {
            s0 += "尸宠解锁。";
         }
         return s0;
      }
      
      public static function weekMadboss(da0:TaskData) : String
      {
         var s0:String = da0.def.description;
         var skillArr0:Array = LevelEventAction.getWeekMadbossSkillArr();
         var cn0:String = SkillDescrip.getSkillArrGather(skillArr0,GatherColor.orangeColor,false,false);
         return s0 + ("本周技能如下：\n" + cn0);
      }
      
      public static function sumBossTask(da0:TaskData) : String
      {
         var main0:BossEditData = PD.bossEdit.getMain();
         var bossCn0:String = "";
         if(Boolean(main0))
         {
            bossCn0 = "你的主力首领为：<b>" + ComMethod.color(main0.getCnName(),GatherColor.yellowColor) + "</b>";
            if(main0.sumPan())
            {
               bossCn0 += "（可召唤）";
            }
            else
            {
               bossCn0 += ComMethod.color("（不可召唤）",GatherColor.redColor) + "，请到首领工厂重新添加首领。";
            }
            bossCn0 = ComMethod.color(bossCn0,GatherColor.greenColor);
         }
         else
         {
            bossCn0 = "你没有设置主力首领，请到 " + ComMethod.color("首领工厂>创造首领",GatherColor.greenColor) + " 中添加首领，战胜它，并设置为主力首领，之后才能更好的体验任务。";
            bossCn0 = ComMethod.color(bossCn0,GatherColor.orangeColor);
         }
         var s0:String = da0.def.description;
         s0 += ComMethod.color("注意：" + BodyFather.noSumCnStr + "无法进行召唤。",GatherColor.blueColor);
         return s0 + ("\n" + bossCn0);
      }
      
      public static function maxSpeedTask(da0:TaskData) : String
      {
         return weekArmsArr(da0,"本周不能在本任务中使用的武器：");
      }
      
      public static function anniverTask23(da0:TaskData) : String
      {
         return weekArmsArr(da0,"本周不再获得超级散射效果的武器：");
      }
      
      private static function weekArmsArr(da0:TaskData, title0:String) : String
      {
         var cnArr0:Array = null;
         var s0:String = da0.def.description;
         var armsArr0:Array = da0.playerData.main.save.weekArmsArr;
         if(armsArr0.length > 0)
         {
            cnArr0 = Gaming.defineGroup.bullet.getArmsCnArrByArr(armsArr0);
            s0 += TextMethod.color(title0,"#FF80FF");
            s0 += TextMethod.color(StringMethod.concatStringArr(cnArr0,999),"#FF6600");
         }
         return s0;
      }
      
      public static function armsEdit23(da0:TaskData) : String
      {
         var s0:String = da0.def.description;
         return s0 + ("今日限定武器类型：" + ComMethod.color(ArmsEdit23LevelDiy.getArmsTypeArrStr(),GatherColor.orangeColor) + "。");
      }
   }
}

