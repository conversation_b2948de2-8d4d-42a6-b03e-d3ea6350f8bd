package UI.pet.grow
{
   import UI.UIOrder;
   import UI.UIShow;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import UI.base.must.NormalMustBox;
   import UI.base.oneKey.OneKeyCtrl;
   import UI.pet.PetUI;
   import com.common.text.TextWay;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.pet.PetData;
   import dataAll.pet.gene.GeneData;
   import dataAll.pet.gene.creator.GeneDataGrowthCtrl;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class PetGrowBoard extends NormalUI
   {
      private var nameTxt:TextField;
      
      private var baseTxt:TextField;
      
      private var nowTxt:TextField;
      
      private var limitTxt:TextField;
      
      private var allTxt:TextField;
      
      private var successTxt:TextField;
      
      private var mustSp:Sprite;
      
      private var btnSp:MovieClip;
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var nowTipStr:String = "";
      
      private var successB:Boolean = false;
      
      private var numKey:OneKeyCtrl = new OneKeyCtrl();
      
      private var numKeyBtnSp:MovieClip;
      
      private var numKeyBtn:NormalBtn = new NormalBtn();
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public function PetGrowBoard()
      {
         super();
      }
      
      private function get keyNum() : Number
      {
         return this.CF.getAttribute("keyNum");
      }
      
      private function set keyNum(v0:Number) : void
      {
         this.CF.setAttribute("keyNum",v0);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["nameTxt","baseTxt","nowTxt","limitTxt","allTxt","successTxt","mustSp","btnSp","numKeyBtnSp"];
         super.setImg(img0);
         FontDeal.dealLine(this.nameTxt);
         FontDeal.dealLine(this.baseTxt);
         FontDeal.dealOne(this.nowTxt);
         FontDeal.dealLine(this.limitTxt);
         FontDeal.dealOne(this.successTxt);
         FontDeal.dealOne(this.allTxt);
         addChild(this.mustBox);
         this.mustBox.setImg(this.mustSp);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("培养");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         addChild(this.numKeyBtn);
         this.numKeyBtn.setImg(this.numKeyBtnSp);
         this.numKeyBtn.activedAndEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.numKeyBtn);
         this.numKeyBtn.addEventListener(MouseEvent.CLICK,this.numKeyBtnClick);
         this.numKeyBtn.tipString = "一键培养。\n以下情况自动停止：\n1、已达总百分比上限。\n2、所有属性已达上限。\n3、材料不足。";
         this.numKey.startFun = this.numKeyStart;
         this.numKey.endFun = this.numKeyEnd;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         if(Boolean(PetUI.nowGrip))
         {
            this.showData(PetUI.getNowData());
         }
      }
      
      private function get nowData() : PetData
      {
         if(Boolean(PetUI.nowGrip))
         {
            return PetUI.getNowData();
         }
         return null;
      }
      
      private function clearAll() : void
      {
         this.mustBox.setShowState(false);
         this.btn.actived = false;
         this.numKeyBtn.actived = this.btn.actived;
         this.nowTipStr = "";
         this.successB = false;
      }
      
      private function showData(da0:PetData) : void
      {
         this.clearAll();
         this.showPro(da0.gene);
         var overStr0:String = this.showAllPerAndSuccessRate(da0.gene);
         var overLimitB0:Boolean = overStr0 != "";
         var mustDefine0:MustDefine = GeneDataGrowthCtrl.getMust(da0);
         var bb0:Boolean = this.mustBox.inData(mustDefine0,Gaming.PG.da.level);
         this.btn.setName(overLimitB0 ? overStr0 : "培养");
         this.btn.actived = !overLimitB0 && bb0;
         this.numKeyBtn.actived = this.btn.actived;
      }
      
      private function showAllPerAndSuccessRate(da0:GeneData) : String
      {
         var growObj0:Object = da0.save.growObj;
         var nowPro0:Number = GeneDataGrowthCtrl.getAllProValue(growObj0);
         var allLimit0:Number = GeneDataGrowthCtrl.getAllLimit(da0.save.getDefine(),da0.getColor());
         var successRate0:Number = 0;
         var one0:Object = GeneDataGrowthCtrl.getGrowProOne(da0.save);
         if(one0 is Object)
         {
            successRate0 = Number(one0["successRate"]);
         }
         var overLimitB0:Boolean = nowPro0 >= allLimit0 || !(one0 is Object);
         this.allTxt.htmlText = ComMethod.getPerCompareStr(nowPro0,allLimit0,false);
         this.successTxt.htmlText = TextWay.numberToPer(successRate0,0);
         return overLimitB0 ? (nowPro0 >= allLimit0 ? "已达总百分比上限" : "所有属性已达上限") : "";
      }
      
      private function getNowPetPro() : Number
      {
         if(Boolean(this.nowData))
         {
            return GeneDataGrowthCtrl.getAllProValue(this.nowData.gene.save.growObj);
         }
         return 0;
      }
      
      private function showPro(da0:GeneData) : void
      {
         var n:* = undefined;
         var s0:String = null;
         var txt0:TextField = null;
         var txtArr0:Array = [this.nameTxt,this.baseTxt,this.nowTxt,this.limitTxt];
         var strArr0:Array = GeneDataGrowthCtrl.getProStrArray(da0);
         for(n in strArr0)
         {
            s0 = strArr0[n];
            txt0 = txtArr0[n];
            if(txt0 == this.nowTxt)
            {
               txt0.htmlText = FontDeal.getDealLeadingStr(txt0,s0);
            }
            else
            {
               txt0.text = s0;
            }
         }
      }
      
      private function btnClick(e:MouseEvent = null) : void
      {
         this.goGrow(1);
      }
      
      private function goGrow(v0:int) : void
      {
         var da0:PetData = null;
         var must_d0:MustDefine = null;
         if(v0 == 1 || v0 == -2)
         {
            da0 = PetUI.getNowData();
            if(Boolean(da0))
            {
               must_d0 = GeneDataGrowthCtrl.getMust(da0);
               PlayerMustCtrl.deductMust(must_d0,this.afterGrow,this.stopNumKey);
            }
            else
            {
               this.stopNumKey();
            }
         }
         else
         {
            this.stopNumKey();
         }
      }
      
      private function afterGrow() : void
      {
         this.numKey.useMust(GeneDataGrowthCtrl.getMust(this.nowData));
         var da0:PetData = PetUI.getNowData();
         var beforeHead0:String = da0.gene.getPetHead();
         var one0:Object = GeneDataGrowthCtrl.growSave(da0.gene.save);
         this.successB = one0["successB"];
         if(this.numKey.ingB)
         {
            this.nextNumKey(true);
         }
         else
         {
            this.nowTipStr = one0["tip"];
            if(da0.gene.getPetHead() != beforeHead0)
            {
               this.nowTipStr += "\n同时尸宠获得称号：" + da0.gene.getPetHead();
            }
            UIOrder.save(true,true,false,this.yes_afterGrow,UIShow.hideNowApp,false,true);
         }
      }
      
      private function yes_afterGrow(v0:* = null) : void
      {
         if(this.successB)
         {
            Gaming.uiGroup.alertBox.showSuccess("培养成功！\n" + this.nowTipStr);
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("培养失败！");
         }
         this.nowData.fleshProData();
         Gaming.uiGroup.petUI.fleshData();
      }
      
      private function numKeyBtnClick(e:MouseEvent) : void
      {
         if(this.numKeyBtn.actived)
         {
            Gaming.uiGroup.alertBox.showNumChoose("选择培养次数",1,20,1,1,this.yesNumKey);
         }
      }
      
      private function yesNumKey(num0:int) : void
      {
         this.keyNum = num0;
         this.afterNumKey(1);
      }
      
      private function afterNumKey(v0:int) : void
      {
         if(v0 == 1 || v0 == -2)
         {
            if(Boolean(this.nowData))
            {
               Gaming.uiGroup.connectUI.show("数据处理中……",1);
               this.numKey.start(this.keyNum,this.getNowPetPro());
            }
         }
      }
      
      private function numKeyStart() : void
      {
         if(this.btn.actived)
         {
            this.goGrow(1);
         }
         else
         {
            this.stopNumKey();
         }
      }
      
      private function numKeyEnd() : void
      {
         if(this.numKey.getSaveB())
         {
            UIOrder.save(true,true,false,this.yes_numKeyEnd,UIShow.hideNowApp,false,true);
         }
         else
         {
            this.yes_numKeyEnd();
         }
      }
      
      private function yes_numKeyEnd(v:* = null) : void
      {
         var tip0:String = null;
         Gaming.uiGroup.connectUI.hide();
         if(this.numKey.getNum() > 0)
         {
            this.nowData.fleshProData();
            Gaming.uiGroup.petUI.fleshData();
            tip0 = "培养总百分比变化：" + this.numKey.getPerChangeStr();
            tip0 += "\n培养次数：" + this.numKey.getNum() + "次";
            if(Boolean(this.numKey.getGift()))
            {
               tip0 += "\n消耗材料：" + this.numKey.getGift().getDescription();
            }
            Gaming.uiGroup.alertBox.showSuccess(tip0);
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("当前无法一键培养。");
         }
         this.numKey.clear();
      }
      
      private function nextNumKey(saveB0:Boolean) : void
      {
         Gaming.uiGroup.petUI.fleshData();
         this.numKey.next(this.getNowPetPro(),saveB0);
      }
      
      private function stopNumKey(v0:* = null) : void
      {
         this.numKey.doErrorIng("培养中止！");
      }
   }
}

