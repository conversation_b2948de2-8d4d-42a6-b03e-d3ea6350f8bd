package UI.guide
{
   import flash.display.DisplayObject;
   
   public class OneGuideData
   {
      public var btn:DisplayObject = null;
      
      public var secondBtn:DisplayObject = null;
      
      public var text:String = "";
      
      public var eventName:String = "";
      
      public var state:String = "wait";
      
      public var delay:Number = 0;
      
      public var t:Number = 0;
      
      public function OneGuideData(btn0:DisplayObject, str0:String, eventName0:String = "click", delay0:Number = 0, secondBtn0:DisplayObject = null)
      {
         super();
         this.btn = btn0;
         this.secondBtn = secondBtn0;
         this.text = str0;
         this.eventName = eventName0;
         this.delay = delay0;
      }
   }
}

