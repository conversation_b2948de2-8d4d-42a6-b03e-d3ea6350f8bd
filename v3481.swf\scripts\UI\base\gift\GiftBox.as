package UI.base.gift
{
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.font.FontDeal;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.gift.define.GiftBase;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class GiftBox extends NormalUI
   {
      private var expSp:Sprite;
      
      private var coinSp:Sprite;
      
      private var thingsTxt:TextField;
      
      public var gripBox:ItemsGripBox;
      
      public var arrangeType:int = 0;
      
      public function GiftBox()
      {
         super();
      }
      
      public function setToNormalImg() : void
      {
         this.setImg(Gaming.uiGroup.getBasicMovieClip("normalGift"));
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["expSp","coinSp","thingsTxt"];
         super.setImg(img0);
         if(Boolean(this.expSp))
         {
            FontDeal.dealOne(this.expSp["nameTxt"]);
            FontDeal.dealOne(this.expSp["valueTxt"]);
         }
         if(Boolean(this.coinSp))
         {
            FontDeal.dealOne(this.coinSp["nameTxt"]);
            FontDeal.dealOne(this.coinSp["valueTxt"]);
         }
         if(Boolean(this.thingsTxt))
         {
            FontDeal.dealOne(this.thingsTxt);
            this.thingsTxt.text = "物品";
            this.gripBox = new ItemsGripBox();
            this.gripBox.imgType = "equipGrip";
            this.gripBox.evt.setWantEvent(true,false,false,true,true);
            this.gripBox.arg.init(10,1,1,1);
            addChild(this.gripBox);
            this.gripBox.y = this.thingsTxt.y;
            this.gripBox.x = this.thingsTxt.x + 35;
            ItemsGripTipCtrl.addEvent_byItemsGripBox(this.gripBox);
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function hideText() : void
      {
         this.thingsTxt.visible = false;
      }
      
      public function inGiftAddDefineGroup(dg0:GiftAddDefineGroup) : void
      {
         var n:* = undefined;
         var d0:GiftAddDefine = null;
         this.thingsTxt.text = "物品";
         this.hideAll();
         var arr0:Array = dg0.arr;
         var arr2:Array = [];
         for(n in arr0)
         {
            d0 = arr0[n];
            if(d0.name == GiftBase.exp)
            {
               this.setExp(d0.num);
            }
            else if(d0.name == GiftBase.coin)
            {
               this.setCoin(d0.num);
            }
            else
            {
               arr2.push(d0);
               if(arr0.length == 1 && d0.type == "more")
               {
                  this.thingsTxt.text = "队友";
               }
            }
         }
         this.setThings(arr2);
         this.arrange();
      }
      
      public function hideAll() : void
      {
         this.setExp(0);
         this.setCoin(0);
      }
      
      private function arrange() : void
      {
         this["arrange_" + this.arrangeType]();
      }
      
      private function arrange_0() : void
      {
         if(this.expSp.visible)
         {
            this.expSp.x = 0;
            this.coinSp.x = this.expSp.x + this.expSp.width + 30;
         }
         else
         {
            this.coinSp.x = 0;
         }
         if(!this.expSp.visible && !this.coinSp.visible)
         {
            this.thingsTxt.y = 0;
         }
         else
         {
            this.thingsTxt.y = this.expSp.y + this.expSp.height + 5;
         }
         this.gripBox.y = this.thingsTxt.y;
      }
      
      private function arrange_1() : void
      {
         this.expSp.x = 0;
         this.coinSp.x = 0;
         if(this.expSp.visible)
         {
            this.expSp.y = 0;
            this.coinSp.y = this.expSp.y + this.expSp.height + 20;
         }
         else
         {
            this.coinSp.y = 0;
         }
         this.thingsTxt.y = 0;
         if(!this.expSp.visible && !this.coinSp.visible)
         {
            this.thingsTxt.x = 0;
         }
         else
         {
            this.thingsTxt.x = this.expSp.x + this.expSp.width + 10;
         }
         this.gripBox.y = this.thingsTxt.y;
         this.gripBox.x = this.thingsTxt.x + this.thingsTxt.width;
      }
      
      private function setThings(arr0:Array) : void
      {
         if(arr0.length == 0)
         {
            if(Boolean(this.thingsTxt))
            {
               this.thingsTxt.visible = false;
            }
            this.gripBox.visible = false;
         }
         else
         {
            if(Boolean(this.thingsTxt))
            {
               this.thingsTxt.visible = true;
            }
            this.gripBox.visible = true;
            this.gripBox.inData_byArr(arr0,"inData_gift");
         }
      }
      
      private function setExp(v0:Number) : void
      {
         if(Boolean(this.expSp))
         {
            if(v0 == 0)
            {
               this.expSp.visible = false;
            }
            else
            {
               this.expSp.visible = true;
               this.expSp["nameTxt"].text = "经验";
               this.expSp["valueTxt"].htmlText = ComMethod.color(v0 + "","#00FF00");
            }
         }
      }
      
      private function setCoin(v0:Number) : void
      {
         if(Boolean(this.expSp))
         {
            if(v0 == 0)
            {
               this.coinSp.visible = false;
            }
            else
            {
               this.coinSp.visible = true;
               this.coinSp["nameTxt"].text = "银币";
               this.coinSp["valueTxt"].htmlText = ComMethod.color(v0 + "","#FFFF00");
            }
         }
      }
   }
}

