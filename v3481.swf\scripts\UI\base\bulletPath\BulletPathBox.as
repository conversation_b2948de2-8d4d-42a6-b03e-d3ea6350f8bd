package UI.base.bulletPath
{
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import com.sounto.math.Maths;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.NumberMethod;
   import dataAll.arms.ArmsData;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.CapsStyle;
   import flash.display.Graphics;
   import flash.display.LineScaleMode;
   import flash.display.Shape;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   import flash.text.TextField;
   
   public class BulletPathBox extends AutoNormalUI
   {
      private var closeBtn:SimpleButton;
      
      private var yesBtn:NormalBtn;
      
      private var restartBtn:NormalBtn;
      
      private var coverSp:Sprite;
      
      private var tipTxt:TextField;
      
      private var pathCon:Sprite = new Sprite();
      
      private var shootTag:Sprite;
      
      private var bmp:BitmapData;
      
      private var bmpSp:Bitmap = new Bitmap();
      
      private var shape:Shape = new Shape();
      
      private var grah:Graphics = this.shape.graphics;
      
      private var drawingB:Boolean = false;
      
      private var bmpDrawRect:Rectangle = new Rectangle();
      
      private var path2:Array = [];
      
      private var beforeX:int = 0;
      
      private var beforeY:int = 0;
      
      private var changeB:Boolean = false;
      
      private var moveGap:int = 1;
      
      private var pointMax:int = 1;
      
      private var nowData:ArmsData = null;
      
      public function BulletPathBox()
      {
         super();
         mcTypeArr = ["btnSp","tag","txt"];
      }
      
      override protected function firstLoad() : void
      {
         super.hide();
         this.setImg(Gaming.swfLoaderManager.getResourceFull("OtherUI/bulletParthBox"));
         this.tipTxt.mouseEnabled = false;
         this.showNormalTip();
         this.restartBtn.setName("重绘");
         this.yesBtn.setName("保存");
         var index0:int = img.getChildIndex(this.coverSp);
         img.addChildAt(this.pathCon,index0);
         this.pathCon.mouseChildren = false;
         this.pathCon.mask = this.coverSp;
         this.pathCon.addChild(this.shootTag);
         this.bmp = new BitmapData(this.width,this.height,true,0);
         this.bmpSp.bitmapData = this.bmp;
         this.pathCon.addChild(this.shape);
         this.pathCon.addChild(this.bmpSp);
         this.shootTag.mouseChildren = false;
         this.shootTag.mouseEnabled = false;
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         this.pathCon.addEventListener(MouseEvent.MOUSE_DOWN,this.conDown);
         this.pathCon.addEventListener(MouseEvent.MOUSE_MOVE,this.conMove);
         x = 70;
         y = 15;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["closeBtn","coverSp"];
         super.setImg(img0);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function hide() : void
      {
         if(visible)
         {
            if(firstB)
            {
               this.stopdraw();
               this.nowData = null;
               this.clearDrawAndPath();
            }
         }
         super.hide();
      }
      
      public function outLoginEvent() : void
      {
         if(firstB)
         {
            this.stopdraw();
            this.nowData = null;
            this.path2.length = 0;
         }
      }
      
      public function editData(da0:ArmsData) : void
      {
         var ar2:Array = null;
         show();
         this.clearDrawAndPath();
         this.nowData = da0;
         this.moveGap = da0.bulletSpeed;
         this.pointMax = da0.getPathPointMax();
         this.setChangeB(false);
         this.path2.length = 0;
         var p1:Array = da0.save.l;
         for each(ar2 in p1)
         {
            this.path2.push([ar2[0] + this.shootTag.x,ar2[1] + this.shootTag.y]);
         }
         this.drawByPath2();
      }
      
      private function savePath() : void
      {
         var newArr0:Array = null;
         var ar2:Array = null;
         if(Boolean(this.nowData))
         {
            newArr0 = [];
            for each(ar2 in this.path2)
            {
               newArr0.push([ar2[0] - this.shootTag.x,ar2[1] - this.shootTag.y]);
            }
            this.nowData.save.l = newArr0;
         }
      }
      
      private function setChangeB(bb0:Boolean) : void
      {
         this.changeB = bb0;
         this.yesBtn.actived = bb0;
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0 == this.restartBtn)
         {
            if(this.changeB)
            {
               Gaming.uiGroup.alertBox.showChoose("确定要重绘吗？",this.clearDrawAndPath);
            }
            else
            {
               this.clearDrawAndPath();
               this.setChangeB(true);
            }
         }
         else if(btn0 == this.yesBtn)
         {
            this.savePath();
            this.hide();
         }
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         if(this.changeB)
         {
            Gaming.uiGroup.alertBox.showChoose("关闭窗口后，当前轨迹修改将丢失，是否关闭？",this.hide);
         }
         else
         {
            this.hide();
         }
      }
      
      private function showNormalTip() : void
      {
         this.showTip("枪口是子弹发射位置，请在空白处画出子弹轨迹。" + this.getSurplusBulletStr());
      }
      
      private function showMaxTip() : void
      {
         this.showTip(ComMethod.color("轨迹已达到长度上限，无法再继续绘制。","#FF3300") + this.getSurplusBulletStr());
      }
      
      private function getSurplusBulletStr() : String
      {
         if(this.pointMax > 1)
         {
            return "(已用子弹" + this.path2.length + "/" + this.pointMax + ")";
         }
         return "";
      }
      
      private function showTip(s0:String = "") : void
      {
         this.tipTxt.htmlText = s0;
         this.tipTxt.visible = s0 != "";
      }
      
      private function clearDrawAndPath() : void
      {
         this.path2 = [];
         this.showNormalTip();
         this.stopdraw();
         this.grah.clear();
      }
      
      private function startdraw() : void
      {
         this.drawingB = true;
      }
      
      private function stopdraw() : void
      {
         this.drawingB = false;
         if(visible)
         {
            this.bmp.fillRect(this.bmpDrawRect,0);
            this.bmpDrawRect.width = 0;
            this.bmpDrawRect.height = 0;
         }
      }
      
      private function conMove(e:MouseEvent) : void
      {
         var x0:int = 0;
         var y0:int = 0;
         if(this.drawingB)
         {
            x0 = this.pathCon.mouseX;
            y0 = this.pathCon.mouseY;
            x0 = NumberMethod.limitRange(x0,0,this.bmp.width);
            y0 = NumberMethod.limitRange(y0,0,this.bmp.height);
            this.bmpDraw(x0,y0);
            this.shapeDraw(x0,y0);
         }
      }
      
      private function bmpDraw(x0:int, y0:int) : void
      {
         var r0:Rectangle = this.bmpDrawRect;
         if(r0.width == 0)
         {
            r0.x = x0;
            r0.y = y0;
            r0.width = 1;
            r0.height = 1;
         }
         this.bmp.setPixel32(x0,y0,4278255615);
         this.bmp.setPixel32(x0 + 1,y0,4278255615);
         this.bmp.setPixel32(x0,y0 + 1,4278255615);
         this.bmp.setPixel32(x0 + 1,y0 + 1,4278255615);
         if(r0.left > x0)
         {
            r0.left = x0;
         }
         if(r0.top > y0)
         {
            r0.top = y0;
         }
         if(r0.right < x0 + 2)
         {
            r0.right = x0 + 2;
         }
         if(r0.bottom < y0 + 2)
         {
            r0.bottom = y0 + 2;
         }
      }
      
      private function shapeDraw(x0:int, y0:int) : void
      {
         var bx0:int = 0;
         var by0:int = 0;
         var barr0:Array = null;
         var ra0:Number = NaN;
         var num0:int = 0;
         var i:int = 0;
         var x1:Number = NaN;
         var y1:Number = NaN;
         if(this.path2.length > 0)
         {
            barr0 = this.path2[this.path2.length - 1];
            bx0 = int(barr0[0]);
            by0 = int(barr0[1]);
         }
         else
         {
            bx0 = this.shootTag.x;
            by0 = this.shootTag.y;
         }
         var gap0:Number = Maths.Long(bx0 - x0,by0 - y0);
         if(Boolean(gap0) && this.moveGap > 0)
         {
            if(gap0 >= this.moveGap)
            {
               ra0 = Math.atan2(y0 - by0,x0 - bx0);
               num0 = Math.floor(gap0 / this.moveGap);
               for(i = 1; i <= num0; i++)
               {
                  x1 = bx0 + Math.cos(ra0) * (this.moveGap * i);
                  y1 = by0 + Math.sin(ra0) * (this.moveGap * i);
                  this.addPathPoint(x1,y1);
               }
            }
         }
      }
      
      private function addPathPoint(x0:int, y0:int) : void
      {
         var bx0:int = 0;
         var by0:int = 0;
         var barr0:Array = null;
         if(this.path2.length >= this.pointMax)
         {
            this.showMaxTip();
         }
         else
         {
            this.setChangeB(true);
            this.showNormalTip();
            bx0 = this.shootTag.x;
            by0 = this.shootTag.y;
            if(this.path2.length > 0)
            {
               barr0 = this.path2[this.path2.length - 1];
               bx0 = int(barr0[0]);
               by0 = int(barr0[1]);
            }
            this.path2.push([x0,y0]);
            this.drawLine(bx0,by0,x0,y0);
         }
      }
      
      private function drawLine(bx0:int, by0:int, x0:int, y0:int) : void
      {
         this.grah.lineStyle(2,26367,1,true,LineScaleMode.NORMAL,CapsStyle.SQUARE);
         this.grah.moveTo(bx0,by0);
         this.grah.lineTo(x0,y0);
         this.grah.lineStyle();
         this.grah.beginFill(26367);
         this.grah.drawCircle(x0,y0,3);
      }
      
      private function drawByPath2() : void
      {
         var arr2:Array = null;
         var x0:int = 0;
         var y0:int = 0;
         var bx0:int = this.shootTag.x;
         var by0:int = this.shootTag.y;
         for each(arr2 in this.path2)
         {
            x0 = int(arr2[0]);
            y0 = int(arr2[1]);
            this.drawLine(bx0,by0,x0,y0);
            bx0 = x0;
            by0 = y0;
         }
      }
      
      private function conDown(e:MouseEvent) : void
      {
         this.startdraw();
      }
      
      private function conUp(e:MouseEvent) : void
      {
      }
      
      public function mouseUp(e:MouseEvent) : void
      {
         if(this.drawingB)
         {
            this.stopdraw();
         }
      }
      
      public function focusLeave(e:Event) : void
      {
         if(this.drawingB)
         {
            this.stopdraw();
         }
      }
   }
}

