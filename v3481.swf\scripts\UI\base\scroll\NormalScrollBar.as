package UI.base.scroll
{
   import flash.display.*;
   import flash.events.*;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.utils.Timer;
   
   public class NormalScrollBar
   {
      public static const H:* = "H";
      
      public static const L:* = "L";
      
      public const name:* = "滚动条窗口";
      
      private var _speed:Number = 15;
      
      private var _upBtn:Sprite;
      
      private var _downBtn:Sprite;
      
      private var _tween:Number;
      
      private var _elastic:Boolean;
      
      private var _lineAbleClick:Boolean;
      
      private var _mouseWheel:Boolean;
      
      private var _direction:String;
      
      private var _scale9Grid:Rectangle;
      
      private var _target:DisplayObject;
      
      private var _maskTarget:DisplayObject;
      
      private var _scrollBar:Sprite;
      
      private var _scrollLine:Sprite;
      
      private var _timer:Timer = null;
      
      private var _scrollBarOriginalPoint:Point;
      
      private var _parentMC:DisplayObjectContainer;
      
      private var _rectangle:Rectangle;
      
      private var _distanceX:Number;
      
      private var _distanceY:Number;
      
      private var _targetPoint:Number = NaN;
      
      private var _coor:String;
      
      private var _length:String;
      
      private var _mouse:String;
      
      private var _oldLength:Point;
      
      private var _abled:* = true;
      
      public var changeFun:Function = null;
      
      public function NormalScrollBar($target:DisplayObjectContainer, $maskTarget:*, $scrollBar:Sprite, $scrollLine:Sprite, $tween:Number = 5, $elastic:Boolean = false, $lineAbleClick:Boolean = false, $mouseWheel:Boolean = false, $direction:String = "L")
      {
         super();
         if(!($maskTarget is DisplayObject || $maskTarget is Rectangle))
         {
            INIT.showError("没有传入遮罩对象");
         }
         this._target = $target;
         this._maskTarget = $maskTarget is Rectangle ? this.drawMaskTarget($maskTarget) : $maskTarget;
         this._scrollBar = $scrollBar;
         this._scrollLine = $scrollLine;
         this._tween = $tween < 1 || $tween > 20 ? 1 : $tween;
         this._elastic = $elastic;
         this._lineAbleClick = $lineAbleClick;
         this._mouseWheel = $mouseWheel;
         this._direction = $direction;
         this._parentMC = this._scrollBar.parent;
         this._coor = $direction == "H" ? "x" : "y";
         this._length = this._coor == "x" ? "width" : "height";
         this._mouse = this._coor == "x" ? "mouseX" : "mouseY";
         this._oldLength = new Point(this._scrollBar.width,this._scrollBar.height);
         this._scale9Grid = new Rectangle(this._scrollBar.width / 3,this._scrollBar.height / 3,this._scrollBar.width / 3,this._scrollBar.height / 3);
         this.makeScrollPane();
      }
      
      public function set tween($value:Number) : void
      {
         this._tween = $value < 1 || $value > 20 ? 1 : $value;
      }
      
      public function set elastic($value:Boolean) : void
      {
         this._elastic = $value;
         if(this._abled)
         {
            this.makeScrollBar();
         }
      }
      
      public function set lineAbleClick($value:Boolean) : void
      {
         this._lineAbleClick = $value;
         if(this._lineAbleClick)
         {
            this._scrollLine.addEventListener(MouseEvent.MOUSE_DOWN,this.scrollLineMouseDownHandler,false,0,true);
         }
         else
         {
            this._scrollLine.removeEventListener(MouseEvent.MOUSE_DOWN,this.scrollLineMouseDownHandler);
         }
      }
      
      public function set stepNumber($value:Number) : void
      {
         this._speed = $value;
      }
      
      public function set mouseWheel($value:Boolean) : void
      {
         this._mouseWheel = $value;
         if(this._mouseWheel && this._abled)
         {
            this._parentMC.stage.addEventListener(MouseEvent.MOUSE_WHEEL,this.mouseWheelHandler,false,0,true);
         }
         else if(this._abled)
         {
            this._parentMC.stage.removeEventListener(MouseEvent.MOUSE_WHEEL,this.mouseWheelHandler);
         }
      }
      
      public function set direction($value:String) : void
      {
         this._direction = $value;
         this._coor = this._direction == "H" ? "x" : "y";
         this._length = this._coor == "x" ? "width" : "height";
         this._mouse = this._coor == "x" ? "mouseX" : "mouseY";
         if(this._abled)
         {
            this.makeScrollBar();
         }
      }
      
      public function set scale9Grid($value:Rectangle) : void
      {
         this._scale9Grid = $value;
         try
         {
            this._scrollBar.scale9Grid = this._scale9Grid;
         }
         catch(e:*)
         {
            _scrollBar.scale9Grid = null;
         }
      }
      
      public function set speed($value:Number) : void
      {
         this._speed = $value < 5 || $value > 35 ? 15 : $value;
      }
      
      public function set UP($target:Sprite) : void
      {
         this._upBtn = $target;
         if(!this._abled)
         {
            this._upBtn.visible = false;
            this._upBtn.removeEventListener(MouseEvent.MOUSE_DOWN,this.upDownBtnMouseDownHandler);
            return;
         }
         this._upBtn.addEventListener(MouseEvent.MOUSE_DOWN,this.upDownBtnMouseDownHandler,false,0,true);
      }
      
      public function set DOWN($target:Sprite) : void
      {
         this._downBtn = $target;
         if(!this._abled)
         {
            this._downBtn.visible = false;
            this._downBtn.removeEventListener(MouseEvent.MOUSE_DOWN,this.upDownBtnMouseDownHandler);
            return;
         }
         this._downBtn.addEventListener(MouseEvent.MOUSE_DOWN,this.upDownBtnMouseDownHandler,false,0,true);
      }
      
      public function refresh() : void
      {
         this.checkAbled();
      }
      
      private function makeScrollPane() : void
      {
         this.initAllThing();
         this._scrollBarOriginalPoint = new Point(this._scrollBar.x,this._scrollBar.y);
         this.makeMask();
         this.checkAbled();
      }
      
      private function checkAbled() : void
      {
         if(this._maskTarget[this._length] >= this._target[this._length])
         {
            if(Boolean(this._rectangle))
            {
               this.setPer(0);
            }
            this._scrollBar.visible = false;
            this._scrollLine.visible = false;
            if(Boolean(this._downBtn))
            {
               this._downBtn.visible = false;
            }
            if(Boolean(this._upBtn))
            {
               this._upBtn.visible = false;
            }
            this._abled = false;
            if(Boolean(this._upBtn) && Boolean(this._downBtn))
            {
               this._upBtn.removeEventListener(MouseEvent.MOUSE_DOWN,this.upDownBtnMouseDownHandler);
               this._downBtn.removeEventListener(MouseEvent.MOUSE_DOWN,this.upDownBtnMouseDownHandler);
            }
            if(this._mouseWheel)
            {
               this.makeMouseWheel("stop");
            }
         }
         else
         {
            this._scrollBar.visible = true;
            this._scrollLine.visible = true;
            if(Boolean(this._downBtn))
            {
               this._downBtn.visible = true;
            }
            if(Boolean(this._upBtn))
            {
               this._upBtn.visible = true;
            }
            this._abled = true;
            this.makeScrollBar();
            if(Boolean(this._upBtn) && Boolean(this._downBtn))
            {
               this._upBtn.addEventListener(MouseEvent.MOUSE_DOWN,this.upDownBtnMouseDownHandler,false,0,true);
               this._downBtn.addEventListener(MouseEvent.MOUSE_DOWN,this.upDownBtnMouseDownHandler,false,0,true);
            }
            if(this._lineAbleClick)
            {
               this.makeScrollLine();
            }
            if(this._mouseWheel)
            {
               this.makeMouseWheel();
            }
            this.timeListener();
         }
      }
      
      private function timeListener() : void
      {
         if(this._timer != null)
         {
            return;
         }
         this._timer = new Timer(33.333333333333336,0);
         this._timer.addEventListener(TimerEvent.TIMER,this.timeHandler,false,0,true);
         this._timer.start();
      }
      
      private function initAllThing() : void
      {
         this.setRegistration(this._maskTarget as DisplayObjectContainer);
         this.setRegistration(this._target as DisplayObjectContainer);
         this.setRegistration(this._scrollLine);
         this.setRegistration(this._scrollBar);
      }
      
      private function makeMask() : void
      {
         if(this._maskTarget.parent == null)
         {
            this._parentMC.addChild(this._maskTarget);
         }
         this._target.mask = this._maskTarget;
      }
      
      private function makeScrollLine() : void
      {
         this._scrollLine.buttonMode = false;
         this._scrollLine.addEventListener(MouseEvent.MOUSE_DOWN,this.scrollLineMouseDownHandler,false,0,true);
      }
      
      private function makeMouseWheel(state:String = "start") : void
      {
         if(state == "start")
         {
            Gaming.ME.stage.addEventListener(MouseEvent.MOUSE_WHEEL,this.mouseWheelHandler,false,0,true);
         }
         else if(state == "stop")
         {
            Gaming.ME.stage.removeEventListener(MouseEvent.MOUSE_WHEEL,this.mouseWheelHandler);
         }
      }
      
      private function makeScrollBar() : void
      {
         this._scrollBar.buttonMode = true;
         this._scrollBar.mouseChildren = false;
         this.scrollBarLength();
         if(this._coor == "y")
         {
            this._rectangle = new Rectangle(this._scrollBarOriginalPoint.x,this._scrollBarOriginalPoint.y,0,this._scrollLine.getRect(this._parentMC)[this._length] - this._scrollBar.getRect(this._parentMC)[this._length]);
         }
         else
         {
            this._rectangle = new Rectangle(this._scrollBarOriginalPoint.x,this._scrollBarOriginalPoint.y,this._scrollLine.getRect(this._parentMC)[this._length] - this._scrollBar.getRect(this._parentMC)[this._length],0);
         }
         this._scrollBar.addEventListener(MouseEvent.MOUSE_DOWN,this.scrollBarMouseDownHandler,false,0,true);
         this._scrollBar.addEventListener(MouseEvent.MOUSE_UP,this.scrollBarMouseUpHandler,false,0,true);
         Gaming.ME.stage.addEventListener(MouseEvent.MOUSE_UP,this.scrollBarMouseUpHandler,false,0,true);
         Gaming.ME.stage.addEventListener(Event.MOUSE_LEAVE,this.scrollBarMouseUpHandler,false,0,true);
      }
      
      private function scrollBarLength() : void
      {
         if(this._elastic)
         {
            try
            {
               this._scrollBar.scale9Grid = this._scale9Grid;
            }
            catch(e:*)
            {
               _scrollBar.scale9Grid = null;
            }
         }
         else
         {
            this._scrollBar.scale9Grid = null;
         }
         this._scrollBar.width = this._oldLength.x;
         this._scrollBar.height = this._oldLength.y;
         this._scrollBar[this._length] = this._elastic ? this._scrollLine[this._length] * this._maskTarget[this._length] / this._target[this._length] : this._oldLength[this._coor];
      }
      
      private function scrollBarMouseDownHandler(e:MouseEvent) : void
      {
         this._distanceX = this._scrollBar.x - this._parentMC.mouseX;
         this._distanceY = this._scrollBar.y - this._parentMC.mouseY;
         this._scrollBar.addEventListener(Event.ENTER_FRAME,this.scrolBarEnterFrameHandler,false,0,true);
      }
      
      private function scrollBarMouseUpHandler(e:*) : void
      {
         this._scrollBar.removeEventListener(Event.ENTER_FRAME,this.scrolBarEnterFrameHandler);
      }
      
      private function scrolBarEnterFrameHandler(e:Event) : void
      {
         this.makeDragBar();
      }
      
      private function timeHandler(e:TimerEvent) : void
      {
         this.scrollMachine();
      }
      
      private function scrollLineMouseDownHandler(e:MouseEvent) : void
      {
         if(this._parentMC[this._mouse] > this._scrollBar[this._coor])
         {
            this._scrollBar[this._coor] += 3 * this._speed;
         }
         else
         {
            this._scrollBar[this._coor] -= 3 * this._speed;
         }
         this.judgeBoundary();
      }
      
      private function upDownBtnMouseDownHandler(e:MouseEvent) : void
      {
         if(e.currentTarget == this._downBtn)
         {
            this._scrollBar[this._coor] += 3 * this._speed;
         }
         else
         {
            this._scrollBar[this._coor] -= 3 * this._speed;
         }
         this.judgeBoundary();
      }
      
      private function mouseWheelHandler(e:MouseEvent) : void
      {
         if(e.delta < 0)
         {
            this._scrollBar[this._coor] += this._speed;
         }
         else
         {
            this._scrollBar[this._coor] -= this._speed;
         }
         this.judgeBoundary();
      }
      
      private function makeDragBar() : void
      {
         this._scrollBar.x = this._parentMC.mouseX + this._distanceX;
         this._scrollBar.y = this._parentMC.mouseY + this._distanceY;
         this.judgeBoundary();
      }
      
      private function judgeBoundary() : void
      {
         if(this._scrollBar.x < this._rectangle.x)
         {
            this._scrollBar.x = this._rectangle.x;
         }
         if(this._scrollBar.x > this._rectangle.right)
         {
            this._scrollBar.x = this._rectangle.right;
         }
         if(this._scrollBar.y < this._rectangle.y)
         {
            this._scrollBar.y = this._rectangle.y;
         }
         if(this._scrollBar.y > this._rectangle.bottom)
         {
            this._scrollBar.y = this._rectangle.bottom;
         }
      }
      
      private function scrollMachine() : void
      {
         var affterB0:Boolean = true;
         var b_p0:Number = this._targetPoint;
         this._targetPoint = this._maskTarget[this._coor] - (this._scrollBar[this._coor] - this._scrollBarOriginalPoint[this._coor]) * (this._target[this._length] - this._maskTarget[this._length]) / (this._scrollLine[this._length] - this._scrollBar[this._length]);
         if(Math.abs(this._target[this._coor] - this._targetPoint) < 0.3)
         {
            if(this._target[this._coor] != this._targetPoint)
            {
               this._target[this._coor] = this._targetPoint;
            }
            affterB0 = false;
         }
         if(affterB0)
         {
            if(this._tween != 0)
            {
               this._target[this._coor] += (this._targetPoint - this._target[this._coor]) / this._tween;
            }
            else
            {
               this._target[this._coor] = this._targetPoint;
            }
         }
         if(b_p0 != this._targetPoint)
         {
            if(this.changeFun is Function)
            {
               this.changeFun(this.getPer());
            }
         }
      }
      
      private function drawMaskTarget($rect:Rectangle) : Sprite
      {
         var maskTarget:Sprite = new Sprite();
         maskTarget.graphics.beginFill(16777215);
         maskTarget.graphics.drawRect($rect.x,$rect.y,$rect.width,$rect.height);
         maskTarget.graphics.endFill();
         return maskTarget;
      }
      
      private function setRegistration($target:DisplayObjectContainer) : void
      {
         var target:DisplayObject = null;
         var rect:* = $target.getRect($target);
         var _x:* = rect.x;
         var _y:* = rect.y;
         var depth:Number = $target.numChildren;
         if(depth == 0)
         {
            return;
         }
         for(var i:Number = 0; i < depth; i++)
         {
            target = $target.getChildAt(i);
            target.x -= _x;
            target.y -= _y;
         }
         if($target.parent != null)
         {
            $target.x += _x;
            $target.y += _y;
         }
      }
      
      public function getPer() : Number
      {
         return -this._targetPoint;
      }
      
      public function setPer(v0:Number) : void
      {
         if(v0 < 0)
         {
            v0 = 0;
         }
         if(v0 > 1)
         {
            v0 = 1;
         }
         this._scrollBar[this._coor] = (this._scrollLine[this._length] - this._scrollBar[this._length]) * v0 + this._scrollBarOriginalPoint[this._coor];
         this.judgeBoundary();
         this.scrollMachine();
      }
   }
}

