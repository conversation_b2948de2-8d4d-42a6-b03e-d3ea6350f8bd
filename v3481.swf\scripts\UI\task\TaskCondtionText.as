package UI.task
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.task.TaskData;
   import dataAll._app.task.define.TaskConditionDefine;
   import dataAll._app.task.define.TaskConditionType;
   import gameAll.body.IO_NormalBody;
   
   public class TaskCondtionText
   {
      public function TaskCondtionText()
      {
         super();
      }
      
      public static function getText(da0:TaskData, uiB0:Boolean = true) : String
      {
         var conText0:String = da0.def.conditionText;
         var con0:TaskConditionDefine = da0.getCondition();
         if(uiB0)
         {
            conText0 = da0.def.uiConditionText;
         }
         var fun0:Function = TaskCondtionText[conText0];
         if(fun0 is Function)
         {
            return fun0();
         }
         if(con0.type == TaskConditionType.collect || con0.type == TaskConditionType.value)
         {
            conText0 = TaskCondtionText[con0.type](da0,conText0);
         }
         if(con0.time > 0)
         {
            conText0 = time(da0,conText0);
         }
         return conText0.replace("[n]","\n");
      }
      
      private static function collect(da0:TaskData, conText0:String) : String
      {
         var num0:int = 0;
         var nowNum0:int = 0;
         var c0:TaskConditionDefine = da0.getCondition();
         num0 = c0.value;
         nowNum0 = da0.save.c;
         conText0 = conText0.replace("[num]",ComMethod.color(num0 + "","#00FF00"));
         return conText0.replace("[nowNum]",ComMethod.color(nowNum0 + "","#00FFFF"));
      }
      
      private static function value(da0:TaskData, conText0:String) : String
      {
         var c0:TaskConditionDefine = da0.getCondition();
         var num0:int = c0.value;
         return conText0.replace("[num]",ComMethod.color(num0 + "","#00FF00"));
      }
      
      private static function time(da0:TaskData, conText0:String) : String
      {
         var c0:TaskConditionDefine = da0.getCondition();
         var overTime0:Number = c0.time - Gaming.LG.nowLevel.dat.getTaskTime();
         if(overTime0 < 0)
         {
            overTime0 = 0;
         }
         var timeText0:String = ComMethod.getTimeStrTwo(overTime0);
         timeText0 = ComMethod.color(timeText0,overTime0 <= 5 ? "#FF9900" : "#00FF00");
         return conText0.replace("[time]",timeText0);
      }
      
      private static function macth_kills_ZhangShi() : String
      {
         return macth_kills_one("藏师将军","藏师");
      }
      
      private static function macth_kills_Arthur() : String
      {
         return macth_kills_one("亚瑟","亚瑟");
      }
      
      private static function macth_kills_doctor() : String
      {
         return macth_kills_one("制毒师","制毒师");
      }
      
      private static function macth_kills_XinLing() : String
      {
         return macth_kills_one("心零","心零");
      }
      
      private static function macth_kills_Jungle() : String
      {
         return macth_kills_one("丛林特种兵","特种兵");
      }
      
      private static function macth_kills_one(cnName0:String, shortName0:String) : String
      {
         var b0:IO_NormalBody = null;
         var b1:IO_NormalBody = null;
         var num0:int = 0;
         var num1:int = 0;
         if(Gaming.LG.isGaming())
         {
            b0 = Gaming.PG.da.hero;
            b1 = Gaming.BG.filter.getBody_byId(cnName0);
            if(b0 is IO_NormalBody && b1 is IO_NormalBody)
            {
               num0 = int(b0.getData().countD.killEnemyNum);
               num1 = int(b1.getData().countD.killEnemyNum);
            }
         }
         var num_txt0:String = ComMethod.color(String(num0),"FF9900");
         if(num0 > num1)
         {
            num_txt0 = ComMethod.color(String(num0),"00FF00");
         }
         return "我 " + num_txt0 + ":" + num1 + " " + shortName0;
      }
   }
}

