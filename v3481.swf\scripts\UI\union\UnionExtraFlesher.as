package UI.union
{
   import com.sounto.process.YesAndNoFun;
   import dataAll._app.union.UnionData;
   import dataAll._app.union.info.MemberInfo;
   import dataAll._app.union.info.UnionInfo;
   import dataAll._player.IO_PlayerLevelGetter;
   
   public class UnionExtraFlesher extends YesAndNoFun
   {
      public function UnionExtraFlesher()
      {
         super();
      }
      
      private function get unionData() : UnionData
      {
         return Gaming.PG.da.union;
      }
      
      public function fleshMemberInfo(yesFun0:Function = null, noFun0:Function = null) : void
      {
         setFun(yesFun0,noFun0,"member");
         Gaming.uiGroup.connectUI.show("上传个人信息……");
         var extra0:String = this.unionData.extraGet.getMemberExtraString();
         var u0:UnionInfo = this.unionData.nowUnion;
         var m0:MemberInfo = this.unionData.nowMember;
         Gaming.api.union.member.setMemberExtra(Gaming.getSaveIndex(),1,extra0,u0.unionId,m0.uId,m0.index,this.yes_fleshMemberInfo,this.no_fleshMemberInfo);
      }
      
      private function yes_fleshMemberInfo(bb0:Boolean) : void
      {
         Gaming.uiGroup.connectUI.hide();
         if(bb0)
         {
            doYesFun(bb0,"member");
         }
         else
         {
            this.no_fleshMemberInfo("");
         }
      }
      
      private function no_fleshMemberInfo(str0:String) : void
      {
         doNoFun(str0,"member");
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError("上传个人数据失败！\n" + str0);
      }
      
      public function fleshUnionInfo(yesFun0:Function = null, noFun0:Function = null) : void
      {
         setFun(yesFun0,noFun0,"union");
         Gaming.uiGroup.connectUI.show("语法判断中……");
         var notice0:String = this.unionData.nowUnion.extraObj.getNotice();
         Gaming.api.badWorld.check(notice0,this.do_fleshUnionInfo,this.badwordError);
      }
      
      private function do_fleshUnionInfo() : void
      {
         Gaming.uiGroup.connectUI.show("上传军队信息……");
         var extra0:String = this.unionData.extraGet.getUnionExtraString();
         var u0:UnionInfo = this.unionData.nowUnion;
         var m0:UnionInfo = this.unionData.nowUnion;
         Gaming.api.union.member.setUnionExtra(Gaming.getSaveIndex(),1,extra0,u0.unionId,this.yes_fleshUnionInfo,this.no_fleshUnionInfo);
      }
      
      private function yes_fleshUnionInfo(bb0:Boolean) : void
      {
         Gaming.uiGroup.connectUI.hide();
         if(bb0)
         {
            doYesFun(bb0,"union");
         }
         else
         {
            this.no_fleshUnionInfo("");
         }
      }
      
      private function no_fleshUnionInfo(str0:String = "") : void
      {
         doNoFun(str0,"union");
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError("上传军队数据失败！\n" + str0);
      }
      
      private function badwordError(str0:String = "") : void
      {
         this.no_fleshUnionInfo("公告内容包含敏感词汇，请修改。");
      }
      
      public function killBossEvent(lg0:IO_PlayerLevelGetter) : void
      {
         var uploadB0:Boolean = this.unionData.killBossFlesher(lg0);
         if(uploadB0)
         {
            Gaming.LG.pauseLevel();
            this.fleshMemberInfo(this.yes_overLevel,this.no_overLevel);
         }
         else if(this.unionData.isTrainB() && Boolean(lg0.getLevelDataNull()))
         {
            Gaming.LG.pauseLevel();
            Gaming.uiGroup.alertBox.showSuccess("击毙首领，用时：" + lg0.getLevelDataNull().getFixedLevelTime() + "秒。");
         }
      }
      
      private function yes_overLevel(data0:* = null) : void
      {
         Gaming.uiGroup.alertBox.showSuccess("上传成绩成功！请返回争霸界面查看。",null,false);
      }
      
      private function no_overLevel(data0:* = null) : void
      {
         Gaming.uiGroup.alertBox.showError("上传成绩失败！你的争霸成绩将无效。",null,false);
      }
   }
}

