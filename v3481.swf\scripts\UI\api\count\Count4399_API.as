package UI.api.count
{
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLLoaderDataFormat;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   
   public class Count4399_API
   {
      public var yesFun:Function;
      
      public var noFun:Function;
      
      private var loader:URLLoader = new URLLoader();
      
      private var urlStr:String = "https://stat.api.4399.com/archive_statistics/log.js";
      
      private var game_id:int = 100027788;
      
      public function Count4399_API()
      {
         super();
         this.loader.dataFormat = URLLoaderDataFormat.TEXT;
         this.loader.addEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this.loader.addEventListener(Event.COMPLETE,this.loaderCompleteHandler);
      }
      
      public function start(uid0:String, saveIndex0:int, urlString0:String, _yesFun:Function = null, _noFun:Function = null) : *
      {
         if(Gaming.api.save.isLocal())
         {
            return;
         }
         this.yesFun = _yesFun;
         this.noFun = _noFun;
         if(Gaming.api.save.isLocal())
         {
            uid0 = "607876138";
         }
         var str0:String = this.urlStr + "?" + "game_id=" + this.game_id + "&uid=" + uid0 + "&index=" + saveIndex0 + urlString0;
         var url0:URLRequest = new URLRequest(str0);
         url0.method = URLRequestMethod.GET;
         this.loader.load(url0);
      }
      
      private function loaderCompleteHandler(e:Event) : void
      {
         if(this.yesFun is Function)
         {
            this.yesFun(this.loader.data);
         }
      }
      
      private function errorHandler(e:IOErrorEvent) : void
      {
         if(this.noFun is Function)
         {
            this.noFun();
         }
      }
   }
}

