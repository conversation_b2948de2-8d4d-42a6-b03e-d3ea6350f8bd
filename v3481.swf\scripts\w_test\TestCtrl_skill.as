package w_test
{
   import dataAll.bullet.BulletDefine;
   import flash.events.KeyboardEvent;
   import flash.geom.Point;
   import flash.ui.Keyboard;
   import gameAll.bullet.BulletLauncher;
   import gameAll.hero.HeroBody;
   
   public class TestCtrl_skill extends TestCtrl_Normal
   {
      public function TestCtrl_skill()
      {
         super();
      }
      
      override public function mouseClick() : void
      {
         var d0:BulletDefine = null;
         var p0:Point = null;
         var hero0:HeroBody = Gaming.PG.ctrlHero;
         if(<PERSON><PERSON><PERSON>(hero0))
         {
            d0 = Gaming.defineGroup.bullet.getDefine("moreMissile");
            p0 = Gaming.gameSprite.getGameMouse();
            BulletLauncher.shoot(hero0,d0,p0.x,p0.y);
         }
      }
      
      override public function FTimer() : void
      {
      }
      
      override public function keyDown(e:KeyboardEvent) : void
      {
         if(e.keyCode != Keyboard.NUMBER_2)
         {
            if(e.keyCode != Keyboard.NUMBER_3)
            {
               if(e.keyCode != Keyboard.NUMBER_4)
               {
                  if(e.keyCode == Keyboard.NUMBER_5)
                  {
                  }
               }
            }
         }
      }
   }
}

