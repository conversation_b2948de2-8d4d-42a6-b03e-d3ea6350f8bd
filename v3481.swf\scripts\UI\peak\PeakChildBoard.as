package UI.peak
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.label.LabelBox;
   import UI.base.loadBar.LoadBar;
   import UI.base.must.ExpendUIAgent;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.DisplayMethod;
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._app.peak.PeakData;
   import dataAll._app.peak.PeakProDefine;
   import dataAll._app.peak.PeakSave;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class PeakChildBoard extends AutoNormalUI
   {
      private var infoTxt:TextField;
      
      private var lvTxt:TextField;
      
      private var maxTxt:TextField;
      
      private var pointTxt:TextField;
      
      private var expTxt:TextField;
      
      private var loadBar:LoadBar;
      
      private var resetBtn:NormalBtn;
      
      private var barTag:Sprite;
      
      private var labelTag:Sprite;
      
      private var dpsTxt:TextField;
      
      private var dpsTitleTxt:TextField;
      
      private var maxDpsTxt:TextField;
      
      private var dpsBtn:NormalBtn;
      
      private var barArr:Array = [];
      
      private var labelBox:LabelBox = new LabelBox();
      
      public function PeakChildBoard()
      {
         super();
         btnSetB = true;
         mcTypeArr = ["btnSp","txt","tag","loadBarSp"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         FontDeal.dealOne(this.maxTxt);
         FontDeal.dealOne(this.expTxt);
         FontDeal.dealStaticLine(this.infoTxt);
         FontDeal.dealStaticLine(this.dpsTitleTxt);
         FontDeal.dealOne(this.maxDpsTxt);
         this.labelBox.arg.init(3,1,-6,0);
         addChild(this.labelBox);
         this.labelBox.inData("midLabelBtn",PeakSave.labelArr,["方案1","方案2","方案3"]);
         this.labelBox.setChoose_byIndex(0);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         NormalUICtrl.setTag(this.labelBox,this.labelTag);
         this.resetBtn.setName("重置");
         this.dpsTxt.addEventListener(MouseEvent.MOUSE_OVER,this.dpsOver);
         this.dpsTxt.addEventListener(MouseEvent.MOUSE_OUT,Gaming.uiGroup.tipBox.hide);
         this.dpsTitleTxt.addEventListener(MouseEvent.MOUSE_OVER,this.dpsOver);
         this.dpsTitleTxt.addEventListener(MouseEvent.MOUSE_OUT,Gaming.uiGroup.tipBox.hide);
         ItemsGripTipCtrl.addNormalBtnTip(this.dpsBtn);
         this.dpsBtn.activedAndEnabled = false;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function get peakData() : PeakData
      {
         return Gaming.PG.da.peak;
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         var bb0:Boolean = this.peakData.swapPlan(e.label);
         if(!bb0)
         {
            Gaming.uiGroup.alertBox.showError("不存在方案" + e.label);
         }
         else
         {
            this.labelBox.setChoose(e.label);
            this.fleshData();
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.labelBox.setChoose(this.peakData.save.now);
         this.fleshData();
         var tip0:String = this.peakData.getUITipAndShow();
         if(tip0 != "")
         {
            Gaming.uiGroup.alertBox.showSuccess(tip0);
         }
      }
      
      private function fleshData() : void
      {
         this.fleshLeft();
         this.fleshBar();
         this.fleshDps();
      }
      
      private function fleshLeft() : void
      {
         this.lvTxt.text = this.peakData.level + "";
         this.maxTxt.text = "最高" + PeakSave.MAX_LV + "级";
         this.loadBar.setLife(this.peakData.exp,this.peakData.getNowMaxExp(),"",true);
         var day0:Number = this.peakData.dayExp;
         var max0:Number = PeakSave.MAX_DAY_EXP;
         var color0:String = day0 >= max0 ? "#00FF00" : "#00FFFF";
         this.expTxt.htmlText = TextMethod.color("今日已转化" + TextMethod.color(NumberMethod.toWan(day0) + "/" + NumberMethod.toWan(max0),color0) + "点的巅峰经验。");
         var surplus0:int = this.peakData.getSurplusPoint();
         var all0:int = this.peakData.getAllPoint();
         this.pointTxt.htmlText = surplus0 + "/" + all0;
         this.resetBtn.actived = surplus0 < all0;
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0 == this.resetBtn)
         {
            this.peakData.resetPoint();
            this.fleshData();
            Gaming.uiGroup.alertBox.showSuccess("重置成功！");
         }
         else if(btn0 == this.dpsBtn)
         {
            this.dpsClick(e);
         }
      }
      
      private function fleshBar() : void
      {
         var darr0:Array = null;
         var d0:PeakProDefine = null;
         var bar0:PeakProBar = null;
         var url0:String = null;
         if(this.barArr.length == 0)
         {
            darr0 = Gaming.defineGroup.peakPro.getArrByFather("peak");
            for each(d0 in darr0)
            {
               bar0 = new PeakProBar();
               url0 = "PeakUI/proBar";
               if(!d0.isProB())
               {
                  url0 = "PeakUI/skillBar";
               }
               bar0.setImgUrl(url0);
               bar0.inDefine(d0,this.peakData,this.fleshData);
               this.barTag.addChild(bar0);
               this.barArr.push(bar0);
            }
            DisplayMethod.arrange(this.barArr,8,8,2,10);
         }
         for each(bar0 in this.barArr)
         {
            bar0.fleshDataBy(this.peakData);
         }
      }
      
      private function fleshDps() : void
      {
         this.dpsTxt.htmlText = "+" + NumberMethod.toPer(this.peakData.getWholeDpsAdd());
         this.maxDpsTxt.htmlText = "最大 " + NumberMethod.toPer(PeakSave.getWholeDpsMax());
         var tip0:String = "";
         if(this.peakData.canWholeDpsB() == false)
         {
            tip0 += "<orange 巅峰等级需要到达" + PeakSave.WHOLE_DPS_LV + "级，才能开启该功能。/>";
            this.dpsBtn.actived = false;
         }
         else
         {
            tip0 += "每天只能提升" + ComMethod.dropColor(this.peakData.save.dpDay,PeakSave.WHOLE_DPS_DAY) + "次。";
            tip0 += "\n每次提升<blue " + NumberMethod.toPer(PeakSave.WHOLE_DPS_ONE,3) + "/>。";
            this.dpsBtn.actived = true;
         }
         this.dpsBtn.tipString = tip0;
      }
      
      private function dpsClick(e:MouseEvent) : void
      {
         if(Gaming.uiGroup.mustBox.visible)
         {
            Gaming.uiGroup.mustBox.hide();
         }
         else if(this.dpsBtn.actived)
         {
            this.showWholeDpsMust();
         }
      }
      
      private function showWholeDpsMust() : void
      {
         var a0:ExpendUIAgent = this.peakData.getWholeDpsMustAgent(this.dpsYesFun);
         Gaming.uiGroup.mustBox.showData(a0,this.dpsBtn);
      }
      
      private function dpsYesFun() : void
      {
         UIOrder.playSuccessSound();
         this.peakData.wholeDpsEvent();
         this.fleshData();
         this.showWholeDpsMust();
         Gaming.PG.da.moreWay.fleshAllEquip();
      }
      
      private function dpsOver(e:MouseEvent) : void
      {
         UIOrder.showTip("全体战斗力，对主角战斗力、队友战斗力、宠物战斗力和载具伤害上限都有明显的加成效果。");
      }
   }
}

