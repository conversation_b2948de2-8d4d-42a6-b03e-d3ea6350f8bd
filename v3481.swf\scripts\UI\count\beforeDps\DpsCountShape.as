package UI.count.beforeDps
{
   import dataAll._player.count.props.DpsImageData;
   import flash.display.Shape;
   import flash.geom.Rectangle;
   
   public class DpsCountShape extends Shape
   {
      private var allRect:Rectangle = new Rectangle(0,0,170,59);
      
      public function DpsCountShape()
      {
         super();
      }
      
      public function flesh(dataArr0:Array) : void
      {
         var max0:int = 0;
         var da0:DpsImageData = null;
         graphics.clear();
         if(dataArr0.length > 0)
         {
            max0 = this.getMax(dataArr0);
            for each(da0 in dataArr0)
            {
               this.drawOne(da0,max0);
            }
         }
      }
      
      private function getMax(dataArr0:Array) : Number
      {
         var da0:DpsImageData = null;
         var v0:Number = NaN;
         var max0:Number = 100;
         for each(da0 in dataArr0)
         {
            v0 = da0.getMax();
            if(v0 > max0)
            {
               max0 = v0;
            }
         }
         return max0;
      }
      
      private function drawOne(da0:DpsImageData, max0:Number) : void
      {
         var v0:Number = NaN;
         var y0:int = 0;
         var x0:int = 0;
         var color0:uint = uint("0xFF" + da0.color);
         var w0:int = this.allRect.width;
         var h0:int = this.allRect.height;
         var valueArr0:Array = da0.valueArr;
         var len0:int = int(valueArr0.length);
         var first0:int = 0;
         if(len0 > w0)
         {
            first0 = len0 - w0;
         }
         graphics.lineStyle(null,color0,da0.alpha,false,"normal","none","miter",1);
         graphics.moveTo(0,h0);
         for(var i:int = first0; i < len0; i++)
         {
            v0 = da0.getValue(i);
            y0 = h0 - v0 / max0 * h0;
            x0 = i - first0 + 1;
            graphics.lineTo(x0,y0);
         }
      }
   }
}

