package UI.forging.skillAddMove
{
   import UI.bag.BagUI;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.grid.NormalGrid;
   import UI.base.must.NormalMustBox;
   import UI.forging.equipCompose.EquipComposeIcon;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.StringMethod;
   import dataAll.equip.EquipData;
   import dataAll.equip.creator.EquipSkillAddCtreator;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipFatherDefine;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.ui.GatherColor;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class SkillAddMoveBoard extends AutoNormalUI
   {
      private static const canFatherArr:Array = [""];
      
      private var btn:NormalBtn;
      
      private var mustBox:NormalMustBox;
      
      private var tipTxt:TextField;
      
      private var icon1:EquipComposeIcon = new EquipComposeIcon();
      
      private var icon2:EquipComposeIcon = new EquipComposeIcon();
      
      private var iconArr:Array = [this.icon1,this.icon2];
      
      public function SkillAddMoveBoard()
      {
         super();
         mcTypeArr = ["btnSp","mustBoxSp","txt","Bx"];
      }
      
      override protected function firstLoad() : void
      {
         var icon0:EquipComposeIcon = null;
         setImgUrl("ForgingUI/skillAddMoveBoard");
         this.btn.setName("转移");
         var tip0:String = ComMethod.color("拖入2件装备，交换它们的技能附加属性。",GatherColor.blueColor,14);
         tip0 += "\n只支持以下装备：" + StringMethod.concatStringArr(EquipFatherDefine.skillAddMoveCnArr,99);
         FontDeal.dealOne(this.tipTxt);
         this.tipTxt.htmlText = FontDeal.getDealLeadingStr(this.tipTxt,tip0);
         for each(icon0 in this.iconArr)
         {
            icon0.addDelBtn();
            icon0.addEventListener(ClickEvent.ON_UP,this.gripUp);
            icon0.addEventListener(ClickEvent.ON_CTRL_CLICK,this.delClick);
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function getMust() : MustDefine
      {
         var mustD0:MustDefine = new MustDefine();
         mustD0.inThingsDataByArr(["nuclearStone;20"]);
         return mustD0;
      }
      
      private function dataPan(da0:EquipData, index0:int) : String
      {
         var otherDa0:EquipData = null;
         var d0:EquipDefine = da0.save.getDefine();
         if(d0.canSkillAddMoveB())
         {
            if(da0.save.isHaveSkillAddB())
            {
               if(Gaming.PG.da.equipBag.dataArr.indexOf(da0) >= 0)
               {
                  otherDa0 = null;
                  if(index0 == 0)
                  {
                     otherDa0 = this.icon2.getItemsData() as EquipData;
                  }
                  else
                  {
                     if(index0 != 1)
                     {
                        return "放入位置不对！";
                     }
                     otherDa0 = this.icon1.getItemsData() as EquipData;
                  }
                  if(Boolean(otherDa0))
                  {
                     return "";
                  }
                  return "";
               }
               return "该装备不存在！";
            }
            return "装备必须有技能附加属性。";
         }
         return "该装备不符合条件。";
      }
      
      override public function show() : void
      {
         super.show();
         var bag0:BagUI = Gaming.uiGroup.bagUI;
         bag0.showAndLabel("equip",true);
         this.fleshData();
      }
      
      override public function hide() : void
      {
         if(Gaming.uiGroup.bagUI.visible)
         {
            Gaming.uiGroup.bagUI.hide();
         }
         super.hide();
      }
      
      private function fleshData() : void
      {
         var mustD0:MustDefine = this.getMust();
         var mustB0:Boolean = this.mustBox.inData(mustD0);
         var iconB0:Boolean = this.fleshPanIcon();
         this.btn.actived = mustB0 && iconB0;
      }
      
      public function outLoginEvent() : void
      {
         if(Boolean(img))
         {
            this.iconClearData();
         }
      }
      
      private function fleshPanIcon() : Boolean
      {
         var icon0:EquipComposeIcon = null;
         var haveB0:Boolean = false;
         var da0:EquipData = null;
         var index0:int = 0;
         var errorStr0:String = null;
         var txt0:String = null;
         var allB0:Boolean = true;
         for each(icon0 in this.iconArr)
         {
            haveB0 = false;
            da0 = icon0.getItemsData() as EquipData;
            if(Boolean(da0))
            {
               index0 = int(this.iconArr.indexOf(icon0));
               errorStr0 = this.dataPan(da0,index0);
               if(errorStr0 == "")
               {
                  icon0.inEquipData(da0);
                  txt0 = EquipSkillAddCtreator.getProTip(da0.save.heroSkillAddObj);
                  icon0.setTxt(txt0);
                  haveB0 = true;
               }
            }
            if(haveB0 == false)
            {
               icon0.clearEquipData();
               icon0.setTxt("");
               allB0 = false;
            }
         }
         return allB0;
      }
      
      private function iconClearData() : void
      {
         var icon0:EquipComposeIcon = null;
         for each(icon0 in this.iconArr)
         {
            icon0.clearData();
         }
      }
      
      private function findIcon(da0:EquipData) : EquipComposeIcon
      {
         var icon0:EquipComposeIcon = null;
         for each(icon0 in this.iconArr)
         {
            if(icon0.getItemsData() == da0)
            {
               return icon0;
            }
         }
         return null;
      }
      
      private function gripUp(e:ClickEvent) : void
      {
         var targetDa0:EquipData = null;
         var icon0:EquipComposeIcon = null;
         var index0:int = 0;
         var errorStr0:String = null;
         var tgrip0:NormalGrid = Gaming.uiGroup.dragCtrl.dragChild;
         if(Boolean(tgrip0))
         {
            targetDa0 = tgrip0.itemsData as EquipData;
            if(Boolean(targetDa0))
            {
               if(!this.findIcon(targetDa0))
               {
                  icon0 = e.target as EquipComposeIcon;
                  index0 = int(this.iconArr.indexOf(icon0));
                  errorStr0 = this.dataPan(targetDa0,index0);
                  if(errorStr0 == "")
                  {
                     icon0.setItmesData(targetDa0);
                     this.fleshData();
                  }
                  else
                  {
                     Gaming.uiGroup.alertBox.showError(errorStr0);
                  }
               }
            }
         }
      }
      
      private function delClick(e:ClickEvent) : void
      {
         var icon0:EquipComposeIcon = e.target as EquipComposeIcon;
         icon0.clearEquipData();
         this.fleshData();
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var must_d0:MustDefine = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0.actived)
         {
            if(btn0 == this.btn)
            {
               if(this.fleshPanIcon())
               {
                  must_d0 = this.getMust();
                  PlayerMustCtrl.deductMust(must_d0,this.afterDeduct);
               }
               else
               {
                  Gaming.uiGroup.alertBox.showError("数据出现变化，无法转移！");
               }
            }
         }
      }
      
      private function afterDeduct() : void
      {
         var da1:EquipData = this.icon1.getItemsData() as EquipData;
         var da2:EquipData = this.icon2.getItemsData() as EquipData;
         da1.moveSkillAdd(da2);
         this.show();
         Gaming.uiGroup.alertBox.showSuccess("转移成功！");
      }
   }
}

