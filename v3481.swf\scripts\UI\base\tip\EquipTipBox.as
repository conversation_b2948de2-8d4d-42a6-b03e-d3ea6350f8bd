package UI.base.tip
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll.items.ItemsDataGroup;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class EquipTipBox extends Sprite
   {
      public var tipBox:TipBox = null;
      
      protected var valueTxt:OneTextGather = new OneTextGather();
      
      protected var titleTxt:TextField = new TextField();
      
      protected var lvTxt:TextField = new TextField();
      
      protected var timeTxt:TextField = new TextField();
      
      protected var wearTipSp:Sprite;
      
      protected var w:int = 0;
      
      protected var h:int = 0;
      
      public var MIN_WIDTH:int = 150;
      
      public function EquipTipBox()
      {
         super();
         this.mouseChildren = false;
         this.mouseEnabled = false;
         this.addChild(this.valueTxt);
         this.valueTxt.x = 20;
         this.valueTxt.y = 45;
         this.valueTxt.TEXT2_MAX_X = 80;
         this.addChild(this.titleTxt);
         OneTextGather.setNormalFormat(this.titleTxt,14,true);
         this.titleTxt.y = 10;
         this.addChild(this.lvTxt);
         OneTextGather.setNormalFormat(this.lvTxt,12,true);
         this.lvTxt.y = 12;
         this.lvTxt.textColor = 16776960;
         this.addChild(this.timeTxt);
         OneTextGather.setNormalFormat(this.timeTxt,12);
         this.timeTxt.textColor = 6710886;
      }
      
      public function init() : void
      {
         this.valueTxt.init();
         this.wearTipSp = Gaming.uiGroup.getBasicMovieClip("wearTipSp");
         addChild(this.wearTipSp);
      }
      
      public function setText(str0:String, title0:String, lv0:String = "", time0:String = "", dg0:ItemsDataGroup = null, addLv0:int = 0) : void
      {
         this.titleTxt.width = 10;
         this.timeTxt.width = 10;
         this.valueTxt.setText(str0);
         this.titleTxt.htmlText = TextGatherAnalyze.swapText(title0);
         var lvStr0:String = lv0 == "" ? "" : lv0 + "级";
         if(addLv0 > 0)
         {
            lvStr0 += ComMethod.color("+" + addLv0,"#00FF00");
         }
         this.lvTxt.htmlText = lvStr0;
         this.timeTxt.text = time0;
         this.fleshSize();
         var w0:int = this.w;
         var h0:int = this.h;
         this.titleTxt.x = -this.titleTxt.width / 2 + w0 / 2;
         if(lv0 != "")
         {
            this.titleTxt.x -= 10;
         }
         this.lvTxt.x = this.titleTxt.x + this.titleTxt.width + 1;
         if(this.getTestB())
         {
            this.timeTxt.x = -this.timeTxt.width / 2 + w0 / 2;
            this.timeTxt.y = h0 - 25;
            this.timeTxt.visible = time0 != "";
         }
         this.wearTipSp.visible = false;
         if(Boolean(dg0))
         {
            if(dg0.placeType == ItemsDataGroup.PLACE_WEAR)
            {
               this.wearTipSp.visible = true;
            }
         }
      }
      
      public function getTitleTxt() : TextField
      {
         return this.titleTxt;
      }
      
      protected function getTestB() : Boolean
      {
         return Gaming.testCtrl.enabled;
      }
      
      protected function fleshSize() : void
      {
         var w0:int = this.valueTxt.x + this.valueTxt.width;
         var h0:int = this.valueTxt.y + this.valueTxt.height + 10;
         var max0:int = w0;
         if(max0 < this.titleTxt.width + this.lvTxt.width * 2 + 10)
         {
            max0 = this.titleTxt.width + this.lvTxt.width * 2 + 10;
         }
         if(this.getTestB())
         {
            if(max0 < this.timeTxt.width + 10)
            {
               max0 = this.timeTxt.width + 10;
            }
            h0 += 20;
         }
         if(max0 < this.MIN_WIDTH)
         {
            max0 = this.MIN_WIDTH;
         }
         w0 = max0;
         this.w = w0;
         this.h = h0;
      }
      
      public function show() : void
      {
         var w0:int = this.w;
         var h0:int = this.h;
         if(Boolean(this.tipBox))
         {
            this.tipBox.hideAllshowBack("normal",w0,h0);
            this.tipBox.show();
         }
         this.visible = true;
      }
      
      public function getW() : int
      {
         return this.w;
      }
      
      public function getH() : int
      {
         return this.h;
      }
   }
}

