package UI.arena.exchange
{
   import UI.UIOrder;
   import UI.api.shop.ShopBuyObject;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.alert.AlertBox;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.shop.GoodsBox;
   import UI.shop.ShopBuyLimit;
   import dataAll._app.goods.GoodsAddit;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.goods.define.PriceType;
   import dataAll.items.IO_ItemsData;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class ArenaExchangeBoard extends NormalUI
   {
      private var nowBox:GoodsBox = new GoodsBox();
      
      private var coinTxt:TextField = null;
      
      private var gripTag:Sprite;
      
      private var pageTag:Sprite;
      
      public function ArenaExchangeBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["coinTxt","gripTag","pageTag"];
         super.setImg(img0);
         FontDeal.dealOne(this.coinTxt);
         var box0:ItemsGripBox = this.nowBox;
         box0.imgType = "ArenaUI/shopGrip";
         box0.arg.init(2,4,4,4);
         box0.evt.setWantEvent(true,false,false,true,true);
         box0.x = this.gripTag.x;
         box0.y = this.gripTag.y;
         box0.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(box0);
         box0.pageBox.setToNormalBtn();
         box0.pageBox.setXY_bySp(this.pageTag,this.gripTag);
         addChild(box0);
         this.coinTxt.addEventListener(MouseEvent.MOUSE_OVER,this.textOver);
         this.coinTxt.addEventListener(MouseEvent.MOUSE_OUT,this.textOut);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         var bb0:Boolean = UIOrder.zuobiPan();
         if(bb0)
         {
            Gaming.uiGroup.arenaUI.hide();
            return;
         }
         this.fleshData();
      }
      
      private function fleshData() : void
      {
         var dataArr0:Array = null;
         if(this.visible)
         {
            dataArr0 = Gaming.PG.da.goods.getDataArr("arena","all");
            this.nowBox.inData_byArr(dataArr0,"inData_goods");
            this.fleshPrice();
         }
      }
      
      public function fleshPrice() : void
      {
         this.fleshCoin();
         this.nowBox.doChildFun("fleshGoodsPrice");
      }
      
      private function fleshCoin() : void
      {
         var num0:int = 0;
         if(visible)
         {
            num0 = Gaming.PG.da.getCrrency(PriceType.ARENA_STAMP);
            this.coinTxt.text = num0 + "";
         }
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var itemsData0:IO_ItemsData = null;
         var da0:GoodsData = e.childData as GoodsData;
         var grip0:ItemsGrid = e.child as ItemsGrid;
         var alertBox0:AlertBox = Gaming.uiGroup.alertBox;
         var limit_str0:String = ShopBuyLimit.getLimit(da0);
         if(da0.def.dataType != "things")
         {
            itemsData0 = Gaming.PG.da.findArenaGiftItemsData(da0.def.defineLabel);
            if(Boolean(itemsData0))
            {
               limit_str0 = "你的背包、仓库或者人物上已经存在有该物品\n请将它卖出（武器可拆解），才能继续兑换该物品。";
            }
         }
         if(limit_str0 != "")
         {
            alertBox0.showNormal(limit_str0,"yes",null,null,"no");
         }
         else
         {
            alertBox0.shop.showCheck(da0,this.yes_buy);
         }
      }
      
      private function textOver(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.textTip.showFollowText("<yellow <b>优胜券</b>/>\n获得竞技场分数即可增加相同数量的优胜券。如果你的排名足够高，每天你还可以领取额外的优胜券。");
      }
      
      private function textOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function yes_buy() : void
      {
         var shopObj0:ShopBuyObject = null;
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         var price0:Number = da0.getPrice();
         if(da0.def.priceType == PriceType.MONEY)
         {
            shopObj0 = da0.getShopObj();
            Gaming.uiGroup.connectUI.show();
            Gaming.api.shop.buyPropNd(shopObj0,this.do_buy);
         }
         else
         {
            this.do_buy();
         }
      }
      
      private function do_buy() : void
      {
         Gaming.uiGroup.connectUI.hide();
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         var price0:Number = da0.getPrice();
         Gaming.PG.da.goods.addBuyNum(da0.def.name,da0.nowNum);
         if(da0.def.priceType != PriceType.MONEY)
         {
            Gaming.PG.da.useCrrency(price0,da0.def.priceType);
         }
         GoodsAddit.addByGoodsData(da0,true);
         if(da0.def.inBagType == "things")
         {
            Gaming.uiGroup.gameWorldUI.propsNumChangeEvent();
         }
         Gaming.soundGroup.playSound("uiSound","buy");
         this.fleshPrice();
         Gaming.uiGroup.mainUI.fleshCoin();
      }
   }
}

