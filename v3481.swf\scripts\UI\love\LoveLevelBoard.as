package UI.love
{
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import dataAll._app.love.define.LoveLevelDefine;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class LoveLevelBoard extends LoveChildBoard
   {
      private var labelTag:Sprite;
      
      private var txt:TextField;
      
      private var nameTxt:TextField;
      
      private var btnSp:MovieClip;
      
      private var labelBox:LabelBox = new LabelBox();
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var nowDefine:LoveLevelDefine = null;
      
      public function LoveLevelBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["txt","nameTxt","labelTag","btnSp"];
         super.setImg(img0);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("领取");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.addLabel();
         this.fleshData();
      }
      
      private function addLabel() : void
      {
         var arr0:Array = null;
         if(this.labelBox.gripArr.length == 0)
         {
            arr0 = loveData.getLoveRoleAll().levelNameArr;
            this.labelBox.arg.init(1,8,0,-5);
            addChild(this.labelBox);
            this.labelBox.inData("LoveUI/levelLabelBtn",arr0,[],true);
            this.labelBox.setChoose_byIndex(0);
            this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
            this.labelBox.x = this.labelTag.x;
            this.labelBox.y = this.labelTag.y;
         }
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showLabel(e.label);
      }
      
      private function fleshData() : void
      {
         this.showLabel(this.labelBox.nowLabel);
         this.fleshLabel();
      }
      
      private function showLabel(label0:String) : void
      {
         if(label0 == "")
         {
            label0 = this.labelBox.getFirstLabel();
         }
         this.labelBox.setChoose(label0);
         var d0:LoveLevelDefine = loveData.getLoveRoleAll().getLevelDefineByName(label0);
         this.nowDefine = d0;
         var value0:int = loveData.save.value;
         var bb0:Boolean = value0 >= d0.must;
         var str0:String = "";
         str0 += d0.getMustRange();
         str0 += "\n\n\n";
         str0 += d0.getInfo(bb0);
         this.btn.actived = false;
         this.btn.setName("领取");
         this.btn.visible = false;
         if(d0.gift.arr.length > 0)
         {
            this.btn.visible = true;
            if(loveData.save.getGiftB(d0))
            {
               this.btn.setName("今日已领取");
            }
            else
            {
               this.btn.actived = bb0;
            }
         }
         this.txt.htmlText = str0;
         this.nameTxt.htmlText = loveData.getCn() + "：\n\n\n开放功能：\n\n";
      }
      
      private function fleshLabel() : void
      {
         var btn0:NormalBtn = null;
         var f0:int = 0;
         var label0:String = null;
         var d0:LoveLevelDefine = null;
         var value0:int = loveData.save.value;
         for each(btn0 in this.labelBox.gripArr)
         {
            f0 = btn0.index + 1 + (loveData.isMaleB() ? 10 : 0);
            label0 = btn0.label;
            d0 = loveData.getLoveRoleAll().getLevelDefineByName(label0);
            btn0.smallIconMc.alpha = value0 < d0.must ? 0.4 : 1;
            btn0.setSmallIconFrame(f0);
         }
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var gift_dg0:GiftAddDefineGroup = this.nowDefine.gift;
         var successB0:Boolean = GiftAddit.addAndAutoBagSpacePan(gift_dg0,"领取奖励成功！");
         if(successB0)
         {
            loveData.save.setGiftB(this.nowDefine,true);
            this.fleshData();
            Gaming.uiGroup.bagUI.fleshNowBox();
         }
      }
   }
}

