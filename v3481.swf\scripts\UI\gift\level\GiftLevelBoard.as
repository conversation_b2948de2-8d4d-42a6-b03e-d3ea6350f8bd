package UI.gift.level
{
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.scroll.NormalScrollBar;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.gift.level.LevelGiftData;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class GiftLevelBoard extends AutoNormalUI
   {
      private var scrollBar:NormalScrollBar;
      
      private var barTag:Sprite;
      
      private var maskTargetSp:Sprite;
      
      private var scrollBarSp:Sprite;
      
      private var scrollLineSp:Sprite;
      
      private var barArr:Array = [];
      
      private var onlyCompleteBtn:NormalBtn;
      
      public function GiftLevelBoard()
      {
         super();
         mcTypeArr = ["btnSp","txt","tag"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         this.scrollBar = new NormalScrollBar(this.barTag,this.maskTargetSp,this.scrollBarSp,this.scrollLineSp,1,false,true,true);
         this.scrollBar.speed = 18;
         this.scrollBar.refresh();
         this.onlyCompleteBtn.setName("只显示未领取礼包");
         this.onlyCompleteBtn.isChosen = true;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.createBox();
         this.fleshUnlock(true);
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0 == this.onlyCompleteBtn)
         {
            this.onlyCompleteBtn.isChosen = !this.onlyCompleteBtn.isChosen;
            this.fleshUnlock(true);
         }
      }
      
      private function createBox() : void
      {
         var da0:LevelGiftData = null;
         var bar0:GiftLevelBar = null;
         if(this.barArr.length > 0)
         {
            return;
         }
         var arr0:Array = Gaming.PG.da.gift.getLevelGiftArr();
         var index0:int = 0;
         for each(da0 in arr0)
         {
            bar0 = new GiftLevelBar();
            bar0.setImg(Gaming.swfLoaderManager.getResource("GiftUI","levelGiftBar"));
            bar0.y = (bar0.height + 3) * index0;
            bar0.inData(da0);
            bar0.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
            this.barTag.addChild(bar0);
            this.barArr.push(bar0);
            index0++;
         }
         this.scrollBar.refresh();
      }
      
      private function fleshUnlock(fleshShowB0:Boolean) : void
      {
         var n:* = undefined;
         var da0:LevelGiftData = null;
         var bar0:GiftLevelBar = null;
         var showIndex0:int = 0;
         var arr0:Array = Gaming.PG.da.gift.getLevelGiftArr();
         for(n in arr0)
         {
            da0 = arr0[n];
            bar0 = this.barArr[n];
            bar0.inData(da0);
            if(fleshShowB0)
            {
               if(this.onlyCompleteBtn.isChosen == false || da0.getShowB())
               {
                  this.barTag.addChild(bar0);
                  bar0.y = (bar0.height + 3) * showIndex0;
                  showIndex0++;
               }
               else
               {
                  bar0.y = 0;
                  if(Boolean(bar0.parent))
                  {
                     bar0.parent.removeChild(bar0);
                  }
               }
            }
         }
         if(fleshShowB0)
         {
            this.scrollBar.refresh();
         }
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var da0:LevelGiftData = e.childData as LevelGiftData;
         var gift0:GiftAddDefineGroup = da0.define;
         var bb0:Boolean = GiftAddit.addAndAutoBagSpacePan(gift0);
         if(bb0)
         {
            Gaming.PG.da.gift.setLevelGiftGetB(gift0.name,true);
            this.fleshUnlock(false);
         }
      }
   }
}

