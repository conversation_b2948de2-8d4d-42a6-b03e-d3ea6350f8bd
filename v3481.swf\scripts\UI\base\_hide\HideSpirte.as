package UI.base._hide
{
   import flash.display.Sprite;
   
   public class <PERSON><PERSON><PERSON><PERSON><PERSON> extends Sprite
   {
      protected var visibleX:int = 0;
      
      protected var showX:int = 0;
      
      public function HideSpirte()
      {
         super();
      }
      
      override public function get x() : Number
      {
         return this.showX;
      }
      
      override public function set x(v0:Number) : void
      {
         this.showX = v0;
         super.x = this.showX + this.visibleX;
      }
      
      override public function get visible() : <PERSON><PERSON>an
      {
         return this.visibleX == 0;
      }
      
      public function get trueVisible() : Bo<PERSON>an
      {
         return super.visible;
      }
      
      override public function set visible(bb0:<PERSON><PERSON>an) : void
      {
         super.visible = bb0;
         this.visibleX = bb0 ? 0 : 10000;
         super.x = this.showX + this.visibleX;
      }
   }
}

