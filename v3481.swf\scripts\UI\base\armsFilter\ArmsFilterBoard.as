package UI.base.armsFilter
{
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.setting.SettingSave;
   import dataAll.arms.creator.ArmsFilter;
   import dataAll.arms.save.ArmsFilterSave;
   import dataAll.items.ItemsDataGroup;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class ArmsFilterBoard extends AutoNormalUI
   {
      private var titleTxt:TextField;
      
      private var closeBtn:NormalBtn;
      
      private var hideBtn:NormalBtn;
      
      private var labelTag:Sprite;
      
      private var labelBox:LabelBox = new LabelBox();
      
      private var coverSp:Sprite;
      
      private var textAF:ArmsFilterText = new ArmsFilterText();
      
      private var infoTxt:TextField;
      
      private var openBtn:NormalBtn;
      
      private var openX:Number = 0;
      
      private var openY:Number = 0;
      
      private var nowSave:ArmsFilterSave = null;
      
      public function ArmsFilterBoard()
      {
         super();
         mcTypeArr = ["btnSp","txt","tag"];
      }
      
      private static function get SAVE() : SettingSave
      {
         return Gaming.PG.save.setting;
      }
      
      override protected function firstLoad() : void
      {
         elementNameArr = elementNameArr.concat(["coverSp"]);
         setImgUrl("OtherUI/armsFilterBoard");
         this.init_addLabel();
         this.x = 202;
         this.y = 10;
         this.openX = this.openBtn.x;
         this.openY = this.openBtn.y;
         addChild(this.coverSp);
         addChild(this.openBtn);
         this.textAF.init(this.infoTxt);
         this.textAF.addEventListener(Event.CHANGE,this.textAFChange);
      }
      
      protected function init_addLabel() : void
      {
         var labelArr0:Array = ArmsFilter.getLabelArr();
         this.labelBox.arg.init(ArmsFilter.FILTER_MAX,1,-6,0);
         addChild(this.labelBox);
         this.labelBox.inData("narrowLabelBtn",labelArr0,labelArr0);
         this.labelBox.setChoose_byIndex(0);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.labelBox.x = this.labelTag.x;
         this.labelBox.y = this.labelTag.y;
         this.hideBtn.setName("开启功能");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      public function outLoginEvent() : void
      {
         this.nowSave = null;
         this.textAF.outLoginEvent();
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      public function fleshData() : void
      {
         this.hideBtn.isChosen = SAVE.getRareDecomposeArms();
         this.fleshLabelBox();
         this.showBox(this.labelBox.nowLabel);
      }
      
      private function setOpen(bb0:Boolean) : void
      {
         if(bb0)
         {
            this.openBtn.setName("禁用");
            this.openBtn.x = this.openX;
            this.openBtn.y = this.openY;
            this.setCover("");
         }
         else
         {
            this.openBtn.setName("开启");
            this.openBtn.x = this.width / 2 - this.openBtn.width / 2;
            this.openBtn.y = 322;
            this.setCover("已关闭，点击开启。");
         }
      }
      
      private function setCover(str0:String) : void
      {
         if(str0 == "")
         {
            this.coverSp.visible = false;
         }
         else
         {
            this.coverSp.visible = true;
            this.coverSp["txt"].htmlText = str0;
         }
      }
      
      private function textAFChange(e:Event) : void
      {
         this.fleshLabelBox();
      }
      
      public function fleshLabelBox() : void
      {
         var btn0:NormalBtn = null;
         var title0:String = null;
         var s0:ArmsFilterSave = null;
         var num0:int = 0;
         for each(btn0 in this.labelBox.gripArr)
         {
            title0 = String(btn0.index + 1);
            s0 = SAVE.getArmsFilter(btn0.label);
            if(Boolean(s0) && s0.bb)
            {
               title0 = s0.getTitle();
               num0++;
            }
            btn0.setName(title0);
         }
         if(SAVE.getRareDecomposeArms() == false)
         {
            num0 = 0;
         }
         this.titleTxt.htmlText = "保留以下 " + ComMethod.orange(num0 + "种") + " 属性武器";
      }
      
      protected function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      private function showBox(label0:String) : void
      {
         if(label0 == "")
         {
            label0 = this.labelBox.getFirstLabel();
         }
         this.labelBox.setChoose(label0);
         var s0:ArmsFilterSave = SAVE.getArmsFilter(label0);
         this.showSave(s0);
      }
      
      private function showSave(s0:ArmsFilterSave) : void
      {
         if(SAVE.getRareDecomposeArms())
         {
            this.openBtn.visible = true;
            if(Boolean(s0))
            {
               if(s0.bb)
               {
                  this.setOpen(true);
               }
               else
               {
                  this.setOpen(false);
               }
               this.textAF.showSave(s0);
            }
            else
            {
               this.setOpen(false);
               this.textAF.clearSave();
            }
         }
         else
         {
            this.openBtn.visible = false;
            this.setCover("已全部关闭，要开启请点击左上角“开启功能”。");
         }
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var boxLabel0:String = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(Boolean(btn0))
         {
            boxLabel0 = this.labelBox.nowLabel;
            if(btn0.actived)
            {
               if(btn0 == this.closeBtn)
               {
                  hide();
                  Gaming.uiGroup.bagUI.gotoDecomposeMust(ItemsDataGroup.TYPE_ARMS);
               }
               else if(btn0 == this.hideBtn)
               {
                  SAVE.setRareDecomposeArms(!SAVE.getRareDecomposeArms());
                  this.fleshData();
               }
               if(btn0 == this.openBtn)
               {
                  SAVE.openArmsFilterAdd(boxLabel0,!SAVE.isArmsFilterOpen(boxLabel0));
                  this.fleshData();
               }
            }
         }
      }
   }
}

