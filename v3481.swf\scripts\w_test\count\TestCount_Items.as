package w_test.count
{
   import dataAll.items.save.ItemsSave;
   
   public class TestCount_Items
   {
      public var num:int = 0;
      
      public var arr:Array = new Array(50);
      
      public function TestCount_Items()
      {
         super();
         for(var i:int = 0; i < 50; i++)
         {
            this.arr[i] = 0;
         }
      }
      
      public function inData(s0:ItemsSave) : void
      {
         var _loc3_:* = s0.getTrueLevel() - 1;
         var _loc2_:* = this.arr;
         var _loc4_:* = _loc2_[s0.getTrueLevel() - 1] + 1;
         _loc2_[_loc3_] = _loc4_;
         ++this.num;
      }
      
      public function getLvString() : String
      {
         var n:* = undefined;
         var num0:int = 0;
         var str0:String = "";
         for(n in this.arr)
         {
            num0 = int(this.arr[n]);
            if(num0 > 0)
            {
               str0 += "【" + (n + 1) + "】---- " + num0 + "\n";
            }
         }
         return str0;
      }
   }
}

