package UI.main
{
   import UI.base.button.NormalBtn;
   import dataAll._app.task.TaskData;
   import dataAll._app.wilder.define.WilderDefine;
   import dataAll._app.worldMap.WorldMapData;
   import dataAll._app.worldMap.WorldMapDataGroup;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._app.worldMap.save.WorldMapSave;
   import dataAll.body.define.NormalBodyDefine;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.ui.Mouse;
   import gameAll.level.LevelGetting;
   
   public class WorldMapSprite extends Sprite
   {
      protected var img:Sprite;
      
      protected var mapSp:Sprite;
      
      public var bigDefine:WorldMapDefine = null;
      
      public var btnObj:Object = {};
      
      private var btnArr:Array = [];
      
      public var btnClickFun:Function = null;
      
      public var btnOverFun:Function = null;
      
      public var btnOutFun:Function = null;
      
      public var btnMoveFun:Function = null;
      
      public var mapClickFun:Function = null;
      
      public function WorldMapSprite()
      {
         super();
      }
      
      public function setImgB() : Boolean
      {
         return this.btnArr.length > 0;
      }
      
      public function setImg(sp0:Sprite) : void
      {
         var ds0:DisplayObject = null;
         var name0:String = null;
         var d0:WorldMapDefine = null;
         var btn0:WorldMapGrip = null;
         var btnSp0:MovieClip = null;
         this.img = sp0;
         if(this.btnArr.length > 0)
         {
            INIT.showError("地图层不能创建2次");
         }
         var num0:int = sp0.numChildren;
         for(var i:int = 0; i < num0; i++)
         {
            ds0 = sp0.getChildAt(i);
            if(ds0 is Sprite)
            {
               name0 = ds0.name;
               d0 = Gaming.defineGroup.worldMap.getDefine(name0);
               if(Boolean(d0))
               {
                  btn0 = new WorldMapGrip();
                  btnSp0 = Gaming.swfLoaderManager.getResourceFull(d0.getGripUrl());
                  if(!btnSp0)
                  {
                     INIT.showError("找不到素材：MainUI/worldMapBtn");
                  }
                  btn0.setImg(btnSp0);
                  btn0.label = d0.name;
                  btn0.setName(d0.cnName);
                  btn0.activedAndEnabled = false;
                  ds0.visible = false;
                  addChild(btn0);
                  btn0.x = ds0.x;
                  btn0.y = ds0.y;
                  if(d0.havePointerB())
                  {
                     btn0.lineTo(d0.pointer.x,d0.pointer.y);
                  }
                  this.btnObj[d0.name] = btn0;
                  this.btnArr.push(btn0);
                  btn0.addEventListener(MouseEvent.CLICK,this.btnClickFun);
                  btn0.addEventListener(MouseEvent.MOUSE_OVER,this.btnOverFun);
                  btn0.addEventListener(MouseEvent.MOUSE_OUT,this.btnOutFun);
                  btn0.addEventListener(MouseEvent.MOUSE_MOVE,this.btnMoveFun);
               }
            }
         }
         addChildAt(sp0,0);
      }
      
      public function getBtnByName(name0:String) : NormalBtn
      {
         return this.btnObj[name0];
      }
      
      public function inData(dg0:WorldMapDataGroup) : void
      {
         var n:* = undefined;
         var btn0:WorldMapGrip = null;
         var label0:String = null;
         var da0:WorldMapData = null;
         var s0:WorldMapSave = null;
         var d0:WorldMapDefine = null;
         var task_da0:TaskData = null;
         var winB0:Boolean = false;
         this.showByWorldMapData(dg0);
         for(n in this.btnObj)
         {
            btn0 = this.btnObj[n];
            btn0.visible = false;
            btn0.actived = true;
            btn0.setLevelText("");
            btn0.setSmallIcon("");
         }
         for(n in this.btnObj)
         {
            btn0 = this.btnObj[n];
            label0 = btn0.label;
            da0 = LevelGetting.getWorldMapData(label0);
            s0 = da0.getSaveNull();
            d0 = Gaming.defineGroup.worldMap.getDefine(label0);
            if(!s0)
            {
               if(d0.firstShowB)
               {
                  btn0.visible = true;
                  btn0.alpha = 0.5;
                  btn0.actived = false;
               }
            }
            else
            {
               task_da0 = da0.taskData;
               winB0 = s0.winB;
               if(!(d0.unlockTask == "no" || d0.mustWinShowB && !task_da0 && !winB0))
               {
                  btn0.visible = true;
                  btn0.actived = true;
                  btn0.alpha = 1;
                  btn0.setSmallIcon(s0.winB ? "" : "new");
                  if(task_da0 is TaskData)
                  {
                     btn0.setSmallIcon("task");
                  }
                  else if(winB0)
                  {
                     btn0.setLevelText(da0.getLvText(dg0.playerData.level));
                  }
                  btn0.save = s0;
                  btn0.taskData = task_da0;
                  if(s0.winB)
                  {
                     if(da0.isMustTaskActivedB() == false)
                     {
                        btn0.alpha = 0.5;
                        btn0.actived = false;
                     }
                  }
                  else
                  {
                     this.dealLinkMap(d0);
                  }
               }
            }
         }
         this.countBossNum();
      }
      
      private function countBossNum() : void
      {
         var btn0:WorldMapGrip = null;
         var wilder0:int = 0;
         var label0:String = null;
         var da0:WorldMapData = null;
         var bossD0:NormalBodyDefine = null;
         var id0:String = null;
         var obj0:Object = {};
         var num0:int = 0;
         for each(btn0 in this.btnObj)
         {
            label0 = btn0.label;
            da0 = LevelGetting.getWorldMapData(label0);
            bossD0 = da0.getOneBossDefine();
            if(Boolean(bossD0))
            {
               id0 = bossD0.index + "";
               if(obj0.hasOwnProperty(id0) == false)
               {
                  obj0[id0] = bossD0;
                  num0++;
               }
            }
         }
         wilder0 = this.countWilderNum(obj0);
         INIT.tempTrace(num0 + wilder0);
      }
      
      private function countWilderNum(obj0:Object) : int
      {
         var d0:WilderDefine = null;
         var bossD0:NormalBodyDefine = null;
         var id0:String = null;
         var num0:int = 0;
         var dobj0:Object = Gaming.defineGroup.wilder.obj;
         for each(d0 in dobj0)
         {
            bossD0 = d0.getBodyDefine();
            if(Boolean(bossD0))
            {
               id0 = bossD0.index + "";
               if(obj0.hasOwnProperty(id0) == false)
               {
                  obj0[id0] = bossD0;
                  num0++;
               }
            }
         }
         return num0;
      }
      
      private function showByWorldMapData(dg0:WorldMapDataGroup) : void
      {
      }
      
      private function dealLinkMap(d0:WorldMapDefine) : void
      {
         var n:* = undefined;
         var label0:String = null;
         var btn0:NormalBtn = null;
         var linkArr0:Array = d0.linkArr;
         if(Boolean(linkArr0))
         {
            for(n in linkArr0)
            {
               label0 = linkArr0[n];
               btn0 = this.btnObj[label0];
               if(Boolean(btn0))
               {
                  if(!btn0.visible)
                  {
                     btn0.visible = true;
                     btn0.alpha = 0.5;
                     btn0.actived = false;
                  }
               }
            }
         }
      }
      
      public function setDesertVisible(bb0:Boolean) : void
      {
         var mc0:DisplayObject = this.img.getChildByName("desertMc");
         if(Boolean(mc0))
         {
            mc0.visible = bb0;
         }
      }
      
      private function mapSpOver(e:MouseEvent) : void
      {
         if(!Gaming.LG.isGaming())
         {
            Mouse.cursor = "hand";
         }
      }
      
      private function mapSpOut(e:MouseEvent) : void
      {
         if(!Gaming.LG.isGaming())
         {
            Mouse.cursor = "auto";
         }
      }
   }
}

