package UI.gift.yuanXiao
{
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class Yuan<PERSON>iaoOverBoard extends AutoNormalUI
   {
      private var txt:TextField;
      
      private var backBtn:NormalBtn;
      
      private var giftBox:ItemsGripBox = new ItemsGripBox();
      
      public function YuanXiaoOverBoard()
      {
         super();
         btnSetB = true;
         mcTypeArr = ["txt","btnSp"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         this.backBtn.setName("返回");
         this.addChild(this.giftBox);
         this.giftBox.arg.init(5,2,10,10);
         this.giftBox.setIconPro("YuanXiaoUI/itemsGrip",50,50);
         this.giftBox.evt.setWantEvent(true,false,false,true,true);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.giftBox);
         this.giftBox.x = this.txt.x;
         this.giftBox.y = this.txt.y + this.txt.height + 20;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      public function showData(game0:YuanXiaoGame) : void
      {
         var s0:String = "你的元宵一共跳入了" + (game0.bowlNum + 1) + "个碗！";
         if(game0.allGift.arr.length > 0)
         {
            s0 += "\n获得了以下奖励：";
         }
         else
         {
            s0 += "\n没有获得奖励。";
         }
         this.txt.htmlText = s0;
         this.giftBox.inData_byGift(game0.allGift);
         Gaming.soundGroup.playAndAdd("YuanXiaoUI/fail");
         show();
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var e0:ClickEvent = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0 == this.backBtn)
         {
            e0 = new ClickEvent(ClickEvent.ON_CLICK);
            dispatchEvent(e0);
         }
      }
   }
}

