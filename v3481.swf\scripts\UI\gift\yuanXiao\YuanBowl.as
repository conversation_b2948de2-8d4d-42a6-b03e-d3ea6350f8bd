package UI.gift.yuanXiao
{
   import UI.bag.ItemsGrid;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefine;
   import flash.display.MovieClip;
   import flash.geom.Rectangle;
   import flash.text.TextField;
   
   public class YuanBowl extends YuanMotion
   {
      private static const HIT_RECT:Rectangle = new Rectangle(-58,-87,116,35);
      
      private var frontImg:MovieClip;
      
      private var img:MovieClip;
      
      private var icon:ItemsGrid = new ItemsGrid();
      
      private var gift:GiftAddDefine = null;
      
      public function YuanBowl()
      {
         super();
      }
      
      public function initImg() : void
      {
         if(Boolean(this.img))
         {
            INIT.showError("YuanBall已存在img");
         }
         this.img = Gaming.swfLoaderManager.getResourceFull("YuanXiaoUI/bowl");
         this.img.stop();
         this.frontImg = Gaming.swfLoaderManager.getResourceFull("YuanXiaoUI/bowlFront");
         this.frontImg.stop();
         this.effectMc.stop();
         this.icon.setImgUrl("YuanXiaoUI/itemsGrip");
         this.frontImg.addChild(this.icon);
         this.icon.y = -72;
         this.icon.x = -this.icon.width / 2;
      }
      
      public function init(con0:YuanXiaoCon, index0:int) : void
      {
         con0.back.addChild(this.img);
         con0.front.addChild(this.frontImg);
         this.x = 0;
         this.y = 0;
         this.icon.clearData();
         this.gift = YuanGift.getGift(index0);
         if(Boolean(this.gift))
         {
            this.icon.inData_gift(this.gift);
            this.setNumText(0);
         }
         else
         {
            this.setNumText(index0);
         }
      }
      
      public function removeMe() : void
      {
         this.img.stop();
         this.img.parent.removeChild(this.img);
         this.frontImg.stop();
         this.frontImg.parent.removeChild(this.frontImg);
      }
      
      public function setBowlNum(num0:int) : void
      {
      }
      
      private function setNumText(num0:int) : void
      {
         var txt0:TextField = this.frontImg["numTxt"];
         txt0.htmlText = num0 + "";
         txt0.visible = num0 > 0;
      }
      
      private function get effectMc() : MovieClip
      {
         return this.frontImg["effectMc"];
      }
      
      override public function set x(value:Number) : void
      {
         super.x = value;
         if(Boolean(this.img))
         {
            this.img.x = value;
         }
         if(Boolean(this.frontImg))
         {
            this.frontImg.x = value;
         }
      }
      
      override public function set y(value:Number) : void
      {
         super.y = value;
         if(Boolean(this.img))
         {
            this.img.y = value;
         }
         if(Boolean(this.frontImg))
         {
            this.frontImg.y = value;
         }
      }
      
      public function hit(x0:Number, y0:Number) : Boolean
      {
         x0 -= _x;
         y0 -= _y;
         var bb0:Boolean = HIT_RECT.contains(x0,y0);
         if(bb0)
         {
            return true;
         }
         return false;
      }
      
      public function getHitY() : int
      {
         return _y - 75;
      }
      
      public function hitEvent() : GiftAddDefine
      {
         this.img.gotoAndPlay("hit");
         var g0:GiftAddDefine = this.gift;
         if(Boolean(this.gift))
         {
            this.effectMc.gotoAndPlay(2);
            GiftAddit.addByDefine(this.gift,Gaming.PG.da);
            this.gift = null;
            this.icon.clearData();
         }
         return g0;
      }
      
      public function setMiddle(ball0:YuanMotion) : void
      {
         ball0.x = x;
         ball0.y = this.getHitY();
      }
   }
}

