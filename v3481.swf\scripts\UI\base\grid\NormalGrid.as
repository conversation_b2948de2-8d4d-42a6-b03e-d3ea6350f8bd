package UI.base.grid
{
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.items.IO_ItemsData;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class NormalGrid extends NormalBtn
   {
      public static var pro_arr:Array = [];
      
      public var state:String = "blank";
      
      public var annotationTxt:TextField;
      
      public var lockSp:Sprite = null;
      
      public var indexTxt:TextField;
      
      public var otherTxt:TextField;
      
      public var levelTxt:TextField;
      
      public var levelTxtBack:MovieClip;
      
      public var priceTxt:TextField;
      
      public var shopBtnBackMc:MovieClip;
      
      public var secMc:MovieClip;
      
      public var backMc:MovieClip;
      
      public function NormalGrid()
      {
         super();
      }
      
      public function getShopItemsName() : String
      {
         var id0:IO_ItemsData = itemsData as IO_ItemsData;
         var giftD0:GiftAddDefine = itemsData as GiftAddDefine;
         if(<PERSON><PERSON><PERSON>(id0))
         {
            return id0.getSave().getTrueName();
         }
         if(Boolean(giftD0))
         {
            return giftD0.name;
         }
         return "";
      }
      
      public function getShopItemsType() : String
      {
         var id0:IO_ItemsData = itemsData as IO_ItemsData;
         var giftD0:GiftAddDefine = itemsData as GiftAddDefine;
         if(Boolean(id0))
         {
            return id0.getSave().itemsType;
         }
         if(Boolean(giftD0))
         {
            return giftD0.type;
         }
         return "";
      }
      
      override public function setImg(img0:MovieClip) : void
      {
         var n:* = undefined;
         var pro0:String = null;
         super.setImg(img0);
         for(n in pro_arr)
         {
            pro0 = pro_arr[n];
            if(!this[pro0])
            {
               this[pro0] = img0[pro0];
            }
         }
         if(Boolean(this.backMc))
         {
            this.backMc.stop();
         }
         if(Boolean(smallIconMc))
         {
            smallIconMc.stop();
         }
         if(Boolean(this.levelTxtBack))
         {
            this.levelTxtBack.stop();
         }
         if(Boolean(this.shopBtnBackMc))
         {
            this.shopBtnBackMc.visible = false;
            this.shopBtnBackMc.stop();
         }
         if(Boolean(this.secMc))
         {
            this.secMc.stop();
            this.secMc.visible = false;
         }
         this.setState("blank");
         this.dealFontOther();
      }
      
      private function dealFontOther() : void
      {
         FontDeal.dealOne(this.annotationTxt);
         FontDeal.dealOne(this.indexTxt);
         FontDeal.dealOne(this.otherTxt);
         FontDeal.dealOne(this.levelTxt);
         FontDeal.dealOne(this.priceTxt);
      }
      
      override public function clearShow() : void
      {
         super.clearShow();
         this.setState("blank");
      }
      
      override public function clear() : void
      {
         clearData();
         super.clear();
      }
      
      public function canInItemsB() : Boolean
      {
         return itemsData == null && this.state == "blank";
      }
      
      public function canSwapItemsB() : Boolean
      {
         if(this.state == "blank")
         {
            return itemsData == null;
         }
         if(this.state == "fill")
         {
            return true;
         }
         return false;
      }
      
      public function canShowTipB() : Boolean
      {
         return this.state == "fill" && this.haveItemsData();
      }
      
      public function canDragB() : Boolean
      {
         return this.canShowTipB();
      }
      
      public function haveItemsData() : Boolean
      {
         return itemsData != null;
      }
      
      public function setState(str0:String) : void
      {
         this.state = str0;
         this.setLevelText("");
         setNumText("");
         setName("");
         this.setBackFrame(1);
         setNew(false);
         this.setAnnotationVisible(false);
         setStarIcon();
         this.setLockVisible(false);
         this.setShopBtnBackMc("");
         setSmallIcon("");
         setIconLight();
         this.setOtherText("");
         this.setSecMc("");
         setIconLight();
         if(this.state == "blank")
         {
            this.setAnnotationVisible(true);
         }
         else if(this.state != "fill")
         {
            if(this.state == "lock")
            {
               this.setLockVisible(true);
            }
            else
            {
               INIT.showError("格子没有这个状态state：" + str0);
            }
         }
      }
      
      public function setBackLabel(str0:String) : void
      {
         if(this.backMc is Sprite)
         {
            this.backMc.gotoAndStop(str0);
         }
      }
      
      public function setBackFrame(f0:int) : void
      {
         if(this.backMc is Sprite)
         {
            f0 = (f0 - 1) % this.backMc.totalFrames + 1;
            if(this.backMc.currentFrame != f0)
            {
               this.backMc.gotoAndStop(f0);
            }
         }
      }
      
      public function setLevel(lv0:int) : void
      {
         if(lv0 > 0)
         {
            this.setLevelText(String(lv0));
         }
         else
         {
            this.setLevelText("");
         }
      }
      
      public function setLevelText(str0:String, leadPanB0:Boolean = false) : void
      {
         if(this.levelTxt is TextField)
         {
            if(leadPanB0 && str0 != "")
            {
               str0 = FontDeal.getDealLeadingStr(this.levelTxt,str0);
            }
            this.levelTxt.htmlText = str0;
            this.levelTxt.visible = str0 != "";
         }
         if(Boolean(this.levelTxtBack))
         {
            this.levelTxtBack.visible = str0 != "";
         }
      }
      
      public function setOtherText(str0:String) : void
      {
         if(this.otherTxt is TextField)
         {
            this.otherTxt.htmlText = str0;
         }
      }
      
      public function setPriceText(str0:String) : void
      {
         if(this.priceTxt is TextField)
         {
            this.priceTxt.htmlText = str0;
         }
      }
      
      public function setIndexText(str0:String) : void
      {
         if(this.indexTxt is TextField)
         {
            this.indexTxt.htmlText = str0;
         }
      }
      
      public function setShopBtnBackMc(str0:*) : void
      {
         if(this.shopBtnBackMc is Sprite)
         {
            this.shopBtnBackMc.visible = true;
            if(str0 is String)
            {
               if(str0 == "")
               {
                  this.shopBtnBackMc.visible = false;
               }
            }
            if(this.shopBtnBackMc.visible)
            {
               this.shopBtnBackMc.gotoAndStop(str0);
            }
         }
      }
      
      public function getShopBtnBackMc() : MovieClip
      {
         return this.shopBtnBackMc;
      }
      
      public function getShopBtnStr() : String
      {
         if(this.shopBtnBackMc is Sprite)
         {
            return this.shopBtnBackMc.currentFrameLabel;
         }
         return "";
      }
      
      public function setSecMc(str0:*) : void
      {
         if(Boolean(this.secMc))
         {
            this.secMc.visible = true;
            if(str0 is String)
            {
               if(str0 == "")
               {
                  this.secMc.visible = false;
               }
            }
            if(this.secMc.visible)
            {
               this.secMc.gotoAndStop(str0);
            }
         }
      }
      
      public function setAnnotation(str0:String) : void
      {
         if(this.annotationTxt is TextField)
         {
            this.annotationTxt.text = str0;
         }
      }
      
      public function setAnnotationVisible(bb0:Boolean) : void
      {
         if(this.annotationTxt is TextField)
         {
            this.annotationTxt.visible = bb0;
         }
      }
      
      public function setLockVisible(bb0:Boolean) : void
      {
         if(this.lockSp is Sprite)
         {
            this.lockSp.visible = bb0;
         }
      }
   }
}

