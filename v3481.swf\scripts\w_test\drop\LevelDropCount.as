package w_test.drop
{
   import UI.test.SaveTestBox;
   import dataAll._app.worldMap.define.MapMode;
   import gameAll.body.IO_NormalBody;
   import gameAll.drop.body.DropBody;
   import gameAll.level.data.OverLevelShow;
   
   public class LevelDropCount
   {
      public static var testB:Boolean = false;
      
      public static var max:int = 0;
      
      public static var now:int = 0;
      
      private static var countAll:DropCountObjAll = new DropCountObjAll();
      
      public function LevelDropCount()
      {
         super();
      }
      
      public static function setTextB(bb0:Boolean, num0:int) : void
      {
         max = num0;
         testB = bb0;
         if(bb0)
         {
            Gaming.ME.stage.frameRate = 100;
         }
         else
         {
            Gaming.ME.stage.frameRate = 30;
         }
      }
      
      public static function restartLevel() : Boolean
      {
         var str0:String = null;
         if(now >= max)
         {
            now = 0;
            str0 = countAll.getString();
            SaveTestBox.addText(str0);
            trace(str0);
            countAll.init();
            return true;
         }
         if(Gaming.LG.mapMode == MapMode.ENDLESS)
         {
            Gaming.LG.overLevel(OverLevelShow.ORDER);
         }
         else
         {
            Gaming.LG.restartLevel();
         }
         ++now;
         return true;
      }
      
      public static function bodyAdd(b0:IO_NormalBody) : void
      {
         if(testB)
         {
            if(b0.getData().camp == "enemy")
            {
               Gaming.TG.hurt.toDie(b0,Gaming.PG.ctrlHero);
            }
         }
      }
      
      public static function allDrop(db0:DropBody) : void
      {
         if(testB)
         {
            countAll.allDrop(db0);
         }
      }
   }
}

