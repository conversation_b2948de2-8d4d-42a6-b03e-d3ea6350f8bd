package UI.top
{
   import UI.base.NormalUI;
   import UI.base.grid.NormalGridIcon;
   import UI.base.heroImg.HeroEquipImgBox;
   import dataAll._app.head.define.HeadDefine;
   import dataAll._player.role.RoleName;
   import dataAll.equip.vehicle.VehicleDefine;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.geom.Point;
   import flash.text.TextField;
   
   public class TopHeroImageBox extends NormalUI
   {
      private var firstTag:Sprite;
      
      private var secTag:Sprite;
      
      private var headTag:Sprite;
      
      private var pointerSp:Sprite;
      
      private var txt:TextField;
      
      private var postTxt:TextField;
      
      private var headIcon:NormalGridIcon = new NormalGridIcon();
      
      private var playerBox:HeroEquipImgBox = new HeroEquipImgBox();
      
      private var vehicleBox:NormalGridIcon = new NormalGridIcon();
      
      public function TopHeroImageBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["postTxt","headTag","firstTag","secTag","pointerSp","txt"];
         super.setImg(img0);
         addChild(this.vehicleBox);
         this.firstTag.addChild(this.vehicleBox);
         addChild(this.playerBox);
         this.playerBox.imgInit();
         this.playerBox.x = this.firstTag.x;
         this.playerBox.y = this.firstTag.y;
         addChild(this.headIcon);
         this.headIcon.x = this.headTag.x;
         this.headIcon.y = this.headTag.y;
         this.mouseChildren = false;
         this.mouseEnabled = false;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function showByBar(bar0:TopBar) : void
      {
         var headDefine0:HeadDefine = null;
         var obj0:Object = bar0.itemsData.extraObj;
         if(obj0.hasOwnProperty("equip"))
         {
            this.playerBox.visible = false;
            this.vehicleBox.visible = false;
            this.visible = true;
            this.setPosition(bar0);
            this.showEquip(obj0["equip"],obj0["es"],obj0["role"]);
            if(obj0.hasOwnProperty("veh"))
            {
               this.showVehicle(obj0["veh"]);
            }
            if(obj0.hasOwnProperty("player"))
            {
               this.txt.text = obj0["player"];
            }
            if(obj0.hasOwnProperty("post"))
            {
               this.postTxt.text = obj0["post"];
            }
            this.txt.x = this.firstTag.x - this.txt.width / 2;
            this.postTxt.x = this.firstTag.x - this.postTxt.width / 2;
            this.headIcon.clearData();
            if(obj0.hasOwnProperty("head"))
            {
               if(obj0["head"] != "")
               {
                  this.txt.x = this.secTag.x - this.txt.width / 2;
                  this.postTxt.x = this.secTag.x - this.postTxt.width / 2;
                  headDefine0 = Gaming.defineGroup.head.getDefine(obj0["head"]);
                  this.headIcon.setIconName(headDefine0.iconUrl);
               }
            }
         }
         else
         {
            this.visible = false;
         }
      }
      
      private function setPosition(bar0:DisplayObject) : void
      {
         var p0:Point = null;
         var p2:Point = null;
         p0 = bar0.localToGlobal(new Point());
         p2 = this.parent.localToGlobal(new Point());
         this.x = p0.x - p2.x;
         this.y = p0.y - this.pointerSp.y - p2.y + bar0.height / 2;
      }
      
      private function showEquip(str0:String, es0:String, roleName0:String) : void
      {
         if(str0 == "")
         {
            return;
         }
         if(roleName0 == null && roleName0 == "")
         {
            roleName0 = RoleName.Striker;
         }
         var equipNameArr0:Array = str0.split(",");
         this.playerBox.visible = true;
         var imgObj0:Object = Gaming.BG.getPartImageMcObjByEquipNameArr(equipNameArr0,Gaming.defineGroup.body.getHeroDefine(roleName0));
         this.playerBox.setEquip_byObj(imgObj0);
         this.playerBox.x = this.firstTag.x;
         this.playerBox.y = this.firstTag.y;
         if(Boolean(es0))
         {
            this.playerBox.heroImg.star.inTopLv(es0);
         }
      }
      
      private function showVehicle(str0:String) : void
      {
         if(str0 == "")
         {
            return;
         }
         var d0:VehicleDefine = Gaming.defineGroup.vehicle.getDefine(str0);
         if(Boolean(d0))
         {
            this.vehicleBox.visible = true;
            this.vehicleBox.setIconName(d0.getBodyDefine().bmpUrl,false);
            this.vehicleBox.x = 0;
            this.vehicleBox.y = -this.vehicleBox.height / 2 + 15;
         }
         else
         {
            this.vehicleBox.visible = false;
         }
         this.playerBox.x = this.secTag.x;
         this.playerBox.y = this.secTag.y;
      }
      
      public function FTimer() : void
      {
         if(visible)
         {
            this.playerBox.Ftimer();
         }
      }
   }
}

