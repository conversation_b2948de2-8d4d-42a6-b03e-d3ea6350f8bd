package UI.helper.pedia
{
   import UI.helper.HeplperLinkText;
   import com.sounto.utils.TextMethod;
   import flash.display.Sprite;
   
   public class HeplperSaveSp extends HeplperLinkText
   {
      public function HeplperSaveSp()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         var s0:String = "";
         s0 += TextMethod.http("存档被覆盖变为一级","https://www.4399api.com/feedback/game-feedback?id=39");
         s0 += "\n" + TextMethod.http("多开造成物品丢失","https://www.4399api.com/feedback/game/2/41");
         s0 += "\n" + TextMethod.http("购买商城物品，游戏币扣除，物品未收到","https://www.4399api.com/feedback/game-feedback?id=9");
         s0 += "\n" + TextMethod.http("举报其它玩家修改存档","https://www.4399api.com/feedback/game-feedback?id=46");
         s0 += "\n" + TextMethod.http("充值问题大全","https://www.4399api.com/feedback/game/3");
         s0 += "\n" + TextMethod.http("其他问题","https://www.4399api.com/feedback/game/2");
         linkTxt.htmlText = s0;
      }
   }
}

