package UI.arena
{
   import UI.arena.appoint.ArenaAppointBoard;
   import UI.arena.exchange.ArenaExchangeBoard;
   import UI.arena.gift.ArenaNewGiftBoard;
   import UI.arena.history.ArenaHistoryBoard;
   import UI.arena.info.ArenaInfoBox;
   import UI.arena.room.ArenaRoomBoard;
   import UI.arena.top.ArenaTopBoard;
   import UI.base.AppNormalUI;
   import UI.base.NormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.arena.ArenaData;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.text.TextField;
   
   public class ArenaUI extends AppNormalUI
   {
      public var labelBox:LabelBox = new LabelBox();
      
      public var infoBox:ArenaInfoBox = new ArenaInfoBox();
      
      public var roomBoard:ArenaRoomBoard = new ArenaRoomBoard();
      
      public var appointBoard:ArenaAppointBoard = new ArenaAppointBoard();
      
      public var topBoard:ArenaTopBoard = new ArenaTopBoard();
      
      public var historyBoard:ArenaHistoryBoard = new ArenaHistoryBoard();
      
      public var newGiftBoard:ArenaNewGiftBoard = new ArenaNewGiftBoard();
      
      public var exchangeBoard:ArenaExchangeBoard = new ArenaExchangeBoard();
      
      protected var noticeSp:ArenaSeasonInfoSp = new ArenaSeasonInfoSp();
      
      private var labelNameArr:Array = ["room","appoint","top","history","exchange"];
      
      private var showLabelArr:Array = ["room","appoint","history","exchange"];
      
      private var showCnArr:Array = ["大厅","对手","战史","兑换"];
      
      private var tipBtn:SimpleButton;
      
      private var labelTag:Sprite;
      
      private var closeBtn:SimpleButton;
      
      private var infoSp:Sprite;
      
      private var linkTxt:TextField;
      
      private var titleTxt:TextField;
      
      private var unlockMc:Sprite;
      
      private var tempTimeStr:String = "";
      
      public function ArenaUI()
      {
         super();
         UICn = "竞技场";
      }
      
      public static function get arenaData() : ArenaData
      {
         return Gaming.PG.da.arena;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["titleTxt","linkTxt","infoSp","labelTag","closeBtn","tipBtn","unlockMc"];
         super.setImg(img0);
         this.labelBox.arg.init(10,1,-9,0);
         this.labelBox.inData("bagLabelBtn",this.showLabelArr,this.showCnArr);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.labelBox.x = this.labelTag.x;
         this.labelBox.y = this.labelTag.y;
         addChild(this.labelBox);
         this.addBoard();
         this.noticeSp.imgInit();
         addChild(this.infoBox);
         this.infoBox.setImg(this.infoSp);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.tipBtn.visible = false;
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
         this.linkTxt.styleSheet = ComMethod.getLinkCss("#FF9900","#FFFFFF");
         this.linkTxt.addEventListener(TextEvent.LINK,this.linkClick);
         this.unlockMc.mouseChildren = false;
         this.unlockMc.mouseEnabled = false;
         addChild(this.unlockMc);
         this.unlockMc.visible = false;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function addBoard() : void
      {
         var n:* = undefined;
         var label0:String = null;
         var name0:String = null;
         var board0:NormalUI = null;
         for(n in this.labelNameArr)
         {
            label0 = this.labelNameArr[n];
            name0 = label0 + "Board";
            if(this.hasOwnProperty(name0))
            {
               board0 = this[name0];
               addChild(board0);
               board0.hide();
               board0.setImg(Gaming.swfLoaderManager.getResource("ArenaUI",name0));
               if(board0.hasOwnProperty("infoBox"))
               {
                  board0["infoBox"] = this.infoBox;
               }
            }
         }
      }
      
      public function outLoginEvent() : void
      {
         this.labelBox.nowLabel = "";
         this.infoBox.outLoginEvent();
      }
      
      public function setGotoLabel(label0:String) : void
      {
         if(this.labelBox.nowLabel == "")
         {
            this.labelBox.nowLabel = this.showLabelArr[0];
         }
         else
         {
            this.labelBox.nowLabel = label0;
         }
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      private function labelOver(e:ClickEvent) : void
      {
         if(e.label == "top")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(ArenaTopCtrl.getTopOpenTip());
         }
      }
      
      private function labelOut(e:ClickEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      public function showBox(label0:String) : void
      {
         var name0:* = null;
         var box0:NormalUI = null;
         var zuobiB0:Boolean = Gaming.PG.da.arena.zuobiPan() != "";
         if(zuobiB0)
         {
            this.hide();
            return;
         }
         if(label0 == "")
         {
            label0 = this.showLabelArr[1];
         }
         this.labelBox.setChoose(label0);
         for each(name0 in this.labelNameArr)
         {
            box0 = this[name0 + "Board"];
            if(label0 == name0)
            {
               box0.show();
            }
            else
            {
               box0.hide();
            }
         }
         if(arenaData.save.tip284 == false)
         {
            arenaData.save.tip284 = true;
            this.showArenaSeasonNotice();
         }
      }
      
      private function showArenaSeasonNotice() : void
      {
         Gaming.uiGroup.alertBox.showInfo("竞技场排行榜关闭，你可以从大厅、指定对手中完成挑战。\n每日挑战次数为2次，并且活跃值礼包将补偿优胜券。\n军队、活跃值中的竞技场任务将自动完成。");
      }
      
      public function fleshByServerTime() : void
      {
         this.titleTxt.text = "竞技场";
         this.linkTxt.htmlText = ComMethod.link("竞技场排行榜暂时关闭","info");
         this.unlockMc.visible = false;
      }
      
      private function tipOver(e:MouseEvent) : void
      {
         var str0:String = "";
         if(this.roomBoard.visible)
         {
            str0 = "当你进入大厅时，系统将根据你的排名获得实力相当的对手。";
         }
         else if(this.appointBoard.visible)
         {
            str0 = "现在你挑战全网任意一个玩家，只需要从对方那里获得玩家代码即可。";
         }
         else if(this.topBoard.visible)
         {
            str0 = "系统会根据你的等级，把你的分数上传至指定排行榜上。";
         }
         else if(this.historyBoard.visible)
         {
            str0 = "你所有挑战的记录都将存与“战史”中，方便你下次快速挑战相同的对手。";
         }
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.setText(str0);
            Gaming.uiGroup.tipBox.textTip.show();
            Gaming.uiGroup.tipBox.followMouseB = true;
         }
      }
      
      private function tipOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      override public function show() : void
      {
         ArenaTopCtrl.openUI(this.afterShow,this.noShow);
      }
      
      private function afterShow(timeStr0:String) : void
      {
         this.tempTimeStr = timeStr0;
         ArenaTopCtrl.uploadScoreBy(this.afterUpload,this.afterUpload);
      }
      
      private function afterUpload(v0:* = null) : void
      {
         super.show();
         this.showBox("history");
         this.infoBox.fleshPlayerData(this.tempTimeStr);
      }
      
      private function noShow(v0:* = null) : void
      {
         Gaming.uiGroup.alertBox.showError("获取服务器时间失败。无法进入排行榜。");
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.hide();
      }
      
      private function linkClick(e:TextEvent) : void
      {
         if(e.text == "info")
         {
            this.showArenaSeasonNotice();
         }
      }
   }
}

