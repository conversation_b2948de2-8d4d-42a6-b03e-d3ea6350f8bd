package UI.bag.find
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.StringMethod;
   import com.sounto.utils.TextMethod;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.find.IO_CnNameFinder;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.text.TextField;
   
   public class ItemsFindCtrlBox extends AutoNormalUI
   {
      private var itemsBox:ItemsGripBox = null;
      
      private var finder:IO_CnNameFinder = null;
      
      private var cnTxt:TextField;
      
      private var tipTxt:TextField;
      
      private var findBtn:NormalBtn;
      
      private var closeBtn:NormalBtn;
      
      private var firstStr:String = "";
      
      public function ItemsFindCtrlBox()
      {
         super();
         mcTypeArr = ["txt","btnSp"];
      }
      
      public function setToNormalImg() : void
      {
         setImg(Gaming.uiGroup.getBasicMovieClip("findCtrlBox"));
         FontDeal.dealOne(this.cnTxt);
         this.closeBtn.setName("关闭");
         this.cnTxt.text = "输入物品名称";
         this.cnTxt.addEventListener(MouseEvent.CLICK,this.cnClick);
         this.cnTxt.addEventListener(Event.CHANGE,this.cnInput);
         this.tipTxt.styleSheet = ComMethod.getLinkCss("#00FFFF","#FFFFFF");
         FontDeal.dealOne(this.tipTxt);
         this.tipTxt.addEventListener(TextEvent.LINK,this.linkClick);
         this.tipTxt.htmlText = "";
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         INIT.showError("该方法必须被覆盖！");
      }
      
      override public function hide() : void
      {
         super.hide();
         this.clearBox();
      }
      
      public function showBox(ig0:ItemsGripBox, finder0:IO_CnNameFinder = null) : void
      {
         if(Boolean(finder0))
         {
            this.finder = finder0;
         }
         else
         {
            this.finder = ig0.fatherData as IO_CnNameFinder;
         }
         if(Boolean(this.finder))
         {
            this.itemsBox = ig0;
            show();
         }
      }
      
      private function clearBox() : void
      {
         this.itemsBox = null;
         this.finder = null;
      }
      
      public function setNewItemsBox(box0:ItemsGripBox) : void
      {
         this.itemsBox = box0;
      }
      
      private function cnInput(e:Event) : void
      {
         this.fleshTip();
      }
      
      private function fleshTip() : void
      {
         var link0:String = null;
         var cnArr0:Array = null;
         var str0:String = this.cnTxt.text;
         this.findBtn.actived = false;
         this.firstStr = "";
         if(str0 != "")
         {
            str0 = StringMethod.toHan2(str0);
            if(str0 != "")
            {
               link0 = "";
               cnArr0 = this.finder.getCnArrByFind(str0);
               if(cnArr0.length > 0)
               {
                  this.firstStr = cnArr0[0];
                  link0 = this.getLinkStr(cnArr0);
                  this.setLinkStr(link0);
                  this.findBtn.actived = true;
               }
               else
               {
                  this.setLinkToNo();
               }
            }
         }
      }
      
      private function getLinkStr(cnArr0:Array) : String
      {
         var cn0:* = null;
         var charMax0:int = 54;
         var s0:String = "";
         var html0:String = "";
         for each(cn0 in cnArr0)
         {
            if(s0.length + cn0.length > charMax0)
            {
               break;
            }
            s0 += cn0 + "  ";
            html0 += TextMethod.link(cn0,cn0) + "  ";
         }
         return "你是否要找：" + html0;
      }
      
      private function setLinkStr(html0:String) : void
      {
         this.tipTxt.htmlText = FontDeal.getDealLeadingStr(this.tipTxt,html0);
      }
      
      private function setLinkToNo() : void
      {
         var str0:String = TextMethod.color("没有找到指定名称的物品","#FF3300");
         this.setLinkStr(str0);
      }
      
      private function cnClick(e:MouseEvent) : void
      {
         if(this.cnTxt.text == "输入物品名称")
         {
            this.cnTxt.text = "";
         }
         else
         {
            this.fleshTip();
         }
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var str0:String = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0 == this.findBtn)
         {
            str0 = this.cnTxt.text;
            str0 = StringMethod.toHan2(str0);
            if(str0 != "")
            {
               if(this.firstStr.indexOf(str0) >= 0)
               {
                  str0 = this.firstStr;
               }
               this.findOneGrip(str0);
            }
         }
         else if(btn0 == this.closeBtn)
         {
            this.hide();
         }
      }
      
      private function linkClick(e:TextEvent) : void
      {
         this.findOneGrip(e.text);
      }
      
      private function findOneGrip(cn0:String) : void
      {
         var grip0:ItemsGrid = null;
         var da0:IO_ItemsData = null;
         var gripArr0:Array = this.itemsBox.gripArr;
         var targetGrip0:ItemsGrid = null;
         for each(grip0 in gripArr0)
         {
            da0 = grip0.itemsData as IO_ItemsData;
            if(Boolean(da0))
            {
               if(da0.getSave().getCnName() == cn0)
               {
                  targetGrip0 = grip0;
                  break;
               }
            }
            else if(grip0.getNameString() == cn0)
            {
               targetGrip0 = grip0;
               break;
            }
         }
         if(Boolean(targetGrip0))
         {
            this.itemsBox.setChoose_byIndex(targetGrip0.index);
            this.itemsBox.gotoChoosePage();
         }
         else
         {
            this.setLinkToNo();
         }
      }
   }
}

