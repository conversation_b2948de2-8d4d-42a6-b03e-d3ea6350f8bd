package UI.love
{
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.button.NormalBtn;
   import dataAll._app.love.define.LoveTalkDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class LoveTalkBoard extends LoveChildBoard
   {
      private var txt:TextField;
      
      private var itemsTag:Sprite;
      
      private var btnSp:MovieClip;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var itemsBox:ItemsGripBox = new ItemsGripBox();
      
      private var textString:String = "";
      
      private var showTextB:Boolean = false;
      
      public function LoveTalkBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["txt","itemsTag","btnSp"];
         super.setImg(img0);
         this.itemsTag.addChild(this.itemsBox);
         this.itemsBox.arg.init(4,1,14,14);
         this.itemsBox.setIconPro("equipGrip");
         this.itemsBox.evt.setWantEvent(true,false,false,true,true);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.itemsBox);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName(">");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.nextTalk();
      }
      
      private function nextTalk() : void
      {
         var d0:LoveTalkDefine = loveData.getRandomTalkDefine();
         this.startShowText(d0.getContext(Gaming.PG.da.getRoleName()));
      }
      
      private function startShowText(str0:String) : void
      {
         this.txt.text = "";
         this.textString = str0;
         this.showTextB = true;
      }
      
      private function showTextTimer() : void
      {
         if(this.txt.length < this.textString.length)
         {
            this.txt.appendText(this.textString.charAt(this.txt.length));
         }
         else
         {
            this.overShowText();
         }
      }
      
      public function overShowText() : void
      {
         this.txt.text = this.textString;
         this.showTextB = false;
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         this.nextTalk();
      }
      
      public function FTimer() : void
      {
         if(this.showTextB)
         {
            this.showTextTimer();
         }
      }
   }
}

