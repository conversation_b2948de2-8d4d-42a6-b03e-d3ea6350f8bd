package UI.union
{
   import UI.UIOrder;
   import UI.base.AppNewUI;
   import UI.base.button.NormalBtn;
   import UI.union.battle.UnionBattleBoard;
   import UI.union.building.UnionBuildingBoard;
   import UI.union.member.UnionMemberBoard;
   import UI.union.my.UnionMyBoard;
   import UI.union.shop.UnionShopBoard;
   import UI.union.task.UnionTaskBoard;
   import UI.union.top.UnionTopBoard;
   import dataAll._app.union.battle.UBattleAgent;
   import dataAll._app.union.info.MemberInfo;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class UnionUI extends AppNewUI
   {
      public static var battleLimitB:Boolean = true;
      
      public var topBoard:UnionTopBoard = new UnionTopBoard();
      
      public var myBoard:UnionMyBoard = new UnionMyBoard();
      
      public var memberBoard:UnionMemberBoard = new UnionMemberBoard();
      
      public var taskBoard:UnionTaskBoard = new UnionTaskBoard();
      
      public var battleBoard:UnionBattleBoard = new UnionBattleBoard();
      
      public var shopBoard:UnionShopBoard = new UnionShopBoard();
      
      public var buildingBoard:UnionBuildingBoard = new UnionBuildingBoard();
      
      public var flesher:UnionExtraFlesher = new UnionExtraFlesher();
      
      private var tempLabel:String = "";
      
      public function UnionUI()
      {
         super();
         UICn = "军队";
         swfLabel = "UnionUI";
         labelArr = ["top","my","member","task","battle","shop","building"];
         labelCnArr = ["军队排行","我的军队","成员","任务","争霸战","功勋商店","建筑"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = elementNameArr.concat([]);
         super.setImg(img0);
         init_addLabel();
         init_addBox();
         init_other();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override protected function getFirstLabel() : String
      {
         return "my";
      }
      
      override public function show() : void
      {
         var bb0:Boolean = UIOrder.zuobiShow();
         if(!bb0)
         {
            labelBox.nowLabel = "my";
            super.show();
         }
      }
      
      override public function hide() : void
      {
         super.hide();
         this.buildingBoard.stopAll();
      }
      
      public function outLoginEvent() : void
      {
         this.topBoard.outLoginEvent();
         this.memberBoard.outLoginEvent();
         this.buildingBoard.outLoginEvent();
      }
      
      override public function showBox(label0:String) : void
      {
         var info0:MemberInfo = null;
         var v0:int = 0;
         var canShowLabelArr0:Array = ["top","my","member","shop"];
         if(Gaming.PG.da.union.unionIsZuobiB)
         {
            if(canShowLabelArr0.indexOf(label0) == -1)
            {
               label0 = "my";
            }
         }
         if(label0 == "battle")
         {
            info0 = Gaming.PG.da.union.nowMember;
            v0 = 0;
            if(Boolean(info0))
            {
               v0 = info0.contribution;
            }
            if(battleLimitB && v0 < 1000)
            {
               label0 = "top";
               Gaming.uiGroup.alertBox.showError("贡献值超过1000才能参加争霸战。");
            }
         }
         this.tempLabel = label0;
         this.showBoxBreak();
      }
      
      private function showBoxBreak() : void
      {
         super.showBox(this.tempLabel);
      }
      
      override protected function getTipText() : String
      {
         var str0:String = "";
         if(this.topBoard.visible)
         {
            str0 += "1、军队等级越高，排名就越高";
            str0 += "\n2、建立军队时的军队名称不能与其他军队同名";
            str0 += "\n3、人物等级需到达30级才能申请加入军队。";
         }
         else if(this.myBoard.visible)
         {
            str0 += "<yellow <b>军队属性：</b>/>";
            str0 += "\n1、军队等级类似于人物等级，它将决定军队的很多特性。";
            str0 += "\n2、军队贡献值类似于人物经验值，不断提高贡献值才能使军队升级。";
            str0 += "\n3、个人军衔还可以对玩家进行战斗力加成，军衔称号越高，加成值就越高。";
            str0 += "\n4、军队战斗力加成可以对每个成员的战斗力进行加成，军队等级越高，加成则越高。";
            str0 += "\n\n";
            str0 += "<yellow <b>个人属性：</b>/>";
            str0 += "\n1、成员通过捐献以及完成军队任务来增加个人贡献值，同时还会增加同等数量的军队贡献值。";
            str0 += "\n2、个人贡献值越高，则获得的军衔称号就越高。";
            str0 += "\n3、个人军衔还可以对玩家进行战斗力加成，军衔称号越高，加成值就越高。";
            str0 += "\n4、每天所有成员可以领取军队福利，福利内容与军衔称号相关。";
            str0 += "\n5、玩家退出军队后，在24小时后才能加入新的军队。";
            str0 += "\n6、加入军队的成员，24小时内才可做贡献。";
         }
         else if(this.memberBoard.visible)
         {
            str0 += "1、成员列表根据个人贡献值进行排行。";
            if(Gaming.PG.da.union.isKingB())
            {
               str0 += "\n2、“副司令”职位设置方法：军队司令点击任意一个成员，输入“副司令”，即可任命该成员为副司令。如果该成员已经为军队管理员，请先取消他的职位。副司令拥有司令的全部权限。";
            }
         }
         else if(this.taskBoard.visible)
         {
            str0 += "1、完成军队指定任务可以获得贡献值。";
            str0 += "\n2、每项任务每天可完成1次。";
         }
         else if(this.battleBoard.visible)
         {
            str0 += "<blue 规则/>";
            str0 += "\n1、一共23个战地地图。";
            str0 += "\n2、每周六8点至周日20点59分之间，都可攻打战地。";
            str0 += "\n3、每个玩家每周只有1次进攻机会。";
            str0 += "\n4、通关后获得个人积分，积分=(100-通关时间)/2，最高" + UBattleAgent.getScoreOneMax() + "分，最低4分。";
            str0 += "\n<orange 5、以下情况只能获得最低分：进入关卡后刷新页面、通关失败、上传成绩失败。/>";
            str0 += "\n6、军队总积分为所有战地第一名的积分之和，根据参战人数也有最低分保底。";
            str0 += "\n\n<blue 加成和奖励/>";
            str0 += "\n1、个人加成由积分决定，包括战斗力、生命值；同时，战斗力加成不超过排名设置的上限。";
            str0 += "\n2、军队加成由总积分决定，只有战斗力加成。";
            str0 += "\n3、奖励由总积分决定，每周只需领取1次，没打过战地的不能领取。";
         }
         else if(this.buildingBoard.visible)
         {
            str0 += "1、军队等级将直接决定建筑等级，培养等级需要通过消耗军队物资来提升，同时培养等级不能超过建筑等级。";
            str0 += "\n2、军事守望者：每升一级都将为玩家开启新功能（包含二段跳、冲刺、技能连技等）。";
            str0 += "\n3、炊事馆：玩家每天可以在炊事馆里进食2次，每次都可以获得一个限时的属性加成。";
            str0 += "\n4、联邦大厦：这是军事物资主要的产出地。你可以和军队成员们完成一个共同的任务来获得物资，也可以通过挂机来获得物资。";
            str0 += "\n5、地质研究所：暂未开放。";
         }
         return str0;
      }
      
      public function fleshLabelShow() : void
      {
         var btn0:NormalBtn = null;
         var isInUnionB0:Boolean = Gaming.PG.da.union.isInUnionB();
         for each(btn0 in labelBox.gripArr)
         {
            if(btn0.label == "top" || isInUnionB0)
            {
               btn0.visible = true;
            }
            else
            {
               btn0.visible = false;
            }
         }
      }
      
      public function mouseUp(e:MouseEvent) : void
      {
         if(visible)
         {
            this.memberBoard.mouseUp(e);
         }
      }
      
      public function FTimerSecond() : void
      {
         if(visible)
         {
            this.buildingBoard.FTimerSecond();
         }
      }
   }
}

