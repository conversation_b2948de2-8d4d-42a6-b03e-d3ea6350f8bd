package UI.partner
{
   import UI.base.AutoNormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import UI.edit.tor.TorEditBox;
   import dataAll._app.edit._def.EditProDefine;
   import dataAll._app.edit.tor.IO_TorEditDefine;
   import dataAll._app.edit.tor.TorEditAgent;
   import dataAll._app.partner.PartnerData;
   import dataAll._app.partner.PartnerSave;
   import dataAll._player.more.NormalPlayerData;
   import flash.display.Sprite;
   
   public class PartnerAIBoard extends AutoNormalUI
   {
      private var torSp:Sprite;
      
      private var torBox:TorEditBox = new TorEditBox();
      
      private var barTag:Sprite;
      
      private var labelBox:LabelBox = new LabelBox();
      
      public function PartnerAIBoard()
      {
         super();
         mcTypeArr = ["tag","txt","btnSp"];
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         this.torBox.setImg(this.torSp);
         addChild(this.torBox);
         this.barTag.addChild(this.labelBox);
         this.labelBox.arg.init(1,PartnerSave.AI_MAX,0,-1);
         this.labelBox.inData("VipUI/labelBtn",["0","1","2"],["方案1","方案2","方案3"]);
         this.labelBox.evt.setWant(true);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
      }
      
      private function get PD() : NormalPlayerData
      {
         return Gaming.PG.DATA;
      }
      
      private function get nowData() : PartnerData
      {
         return this.PD.partner;
      }
      
      public function outLoginEvent() : void
      {
         this.torBox.outLoginEvent();
      }
      
      private function clearShow() : void
      {
         this.torBox.clearData();
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
         this.torBox.clearLine();
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.nowData.save.aiIndex = e.index;
         this.fleshData();
      }
      
      private function fleshData() : void
      {
         var error0:String = this.nowData.getAiOpenError();
         if(error0 != "")
         {
            Gaming.uiGroup.wearUI.setCover(error0);
         }
         else
         {
            Gaming.uiGroup.wearUI.setCover("");
            this.labelBox.setChoose_byIndex(this.nowData.save.aiIndex);
            this.fleshTorBox();
         }
      }
      
      private function fleshTorBox() : void
      {
         var a0:TorEditAgent = null;
         if(Boolean(this.nowData))
         {
            a0 = this.nowData.getNowAITor().getTorEditAgent();
            a0.changeFun = this.changeFun;
            this.torBox.inAgentAndClear(a0);
         }
      }
      
      private function changeFun(proD0:EditProDefine, child0:IO_TorEditDefine) : void
      {
         if(Boolean(this.nowData))
         {
            this.PD.fleshAIHero();
         }
      }
   }
}

