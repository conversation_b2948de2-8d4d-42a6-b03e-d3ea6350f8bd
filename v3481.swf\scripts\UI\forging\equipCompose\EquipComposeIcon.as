package UI.forging.equipCompose
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import dataAll.equip.EquipData;
   import dataAll.items.IO_ItemsData;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class EquipComposeIcon extends NormalUI
   {
      private var txt:TextField;
      
      private var gripTag:Sprite;
      
      private var icon:ItemsGrid = new ItemsGrid();
      
      private var itemsData:IO_ItemsData = null;
      
      private var delBtn:NormalBtn = null;
      
      public function EquipComposeIcon()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["gripTag","txt"];
         super.setImg(img0);
         this.gripTag.addChild(this.icon);
         this.icon.setImgToEquipGrip();
         this.icon.iconMaxHeight = 50;
         this.icon.iconMaxWidth = 50;
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.icon);
         this.icon.addEventListener(MouseEvent.MOUSE_UP,this.gripUp);
      }
      
      public function addDelBtn() : void
      {
         this.delBtn = new NormalBtn();
         this.delBtn.setImg(Gaming.swfLoaderManager.getResourceFull("BasicUI/gripDelBtn"));
         this.delBtn.x = this.icon.x + this.icon.width;
         this.delBtn.y = this.icon.y;
         this.delBtn.addEventListener(MouseEvent.CLICK,this.delBtnClick);
         this.gripTag.addChild(this.delBtn);
      }
      
      private function setDelVisible(bb0:Boolean) : void
      {
         if(Boolean(this.delBtn))
         {
            this.delBtn.visible = bb0;
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function gripUp(e:MouseEvent) : void
      {
         var e0:ClickEvent = new ClickEvent(ClickEvent.ON_UP);
         e0.childData = this.itemsData;
         dispatchEvent(e0);
      }
      
      private function delBtnClick(e:MouseEvent) : void
      {
         var e0:ClickEvent = new ClickEvent(ClickEvent.ON_CTRL_CLICK);
         e0.childData = this.itemsData;
         dispatchEvent(e0);
      }
      
      public function setTxt(str0:String) : void
      {
         this.txt.htmlText = str0;
      }
      
      public function inEquipData(da0:EquipData) : void
      {
         this.itemsData = da0;
         this.icon.inData_equip(da0);
         this.icon.setSmallIcon("");
         this.setDelVisible(true);
      }
      
      public function clearEquipData() : void
      {
         this.itemsData = null;
         this.icon.clearData();
         this.setDelVisible(false);
      }
      
      public function getItemsData() : IO_ItemsData
      {
         return this.itemsData;
      }
      
      public function setItmesData(da0:IO_ItemsData) : void
      {
         this.itemsData = da0;
      }
      
      public function clearData() : void
      {
         this.txt.htmlText = "";
         this.clearEquipData();
      }
   }
}

