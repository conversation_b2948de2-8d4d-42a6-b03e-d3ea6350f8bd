package UI.creator.arms
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.tip.EquipTipBox;
   import UI.creator.chooseOne.ChooseOneBox;
   import dataAll.arms.ArmsData;
   import dataAll.arms.save.ArmsSave;
   import dataAll.ui.choose.ChooseOneDefineGroup;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.text.TextField;
   
   public class ArmsCreatorBox extends NormalUI
   {
      private var btn:NormalBtn = new NormalBtn();
      
      private var chooseTag:Sprite;
      
      private var btnSp:MovieClip;
      
      private var closeBtn:SimpleButton;
      
      private var nameTxt:TextField;
      
      private var lvTxt:TextField;
      
      private var gripTag:Sprite;
      
      private var proTag:Sprite;
      
      private var chooseArr:Array = [];
      
      private var chooseObj:Object = {};
      
      private var tip:EquipTipBox = new EquipTipBox();
      
      private var grip:ItemsGrid = new ItemsGrid();
      
      private var armsSave:ArmsSave;
      
      private var yesFun:Function = null;
      
      public function ArmsCreatorBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["chooseTag","btnSp","closeBtn","nameTxt","lvTxt","gripTag","proTag"];
         super.setImg(img0);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("确定");
         this.btn.addEventListener(MouseEvent.CLICK,this.yesClick);
         this.grip.setImgToEquipGrip();
         addChild(this.grip);
         NormalUICtrl.setTag(this.grip,this.gripTag);
         this.grip.iconMaxHeight = 50;
         this.grip.iconMaxWidth = 50;
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.grip);
         addChild(this.tip);
         NormalUICtrl.setTag(this.tip,this.proTag);
         this.tip.init();
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
      }
      
      private function addChoose() : void
      {
         var dg0:ChooseOneDefineGroup = null;
         var box0:ChooseOneBox = null;
         this.clearAll();
         var arr0:Array = Gaming.defineGroup.choose.arms.arr;
         var height0:int = 0;
         for each(dg0 in arr0)
         {
            box0 = new ChooseOneBox();
            box0.inData(dg0);
            box0.x = this.chooseTag.x;
            box0.y = height0 + this.chooseTag.y;
            height0 += box0.height + 15;
            addChild(box0);
            this.chooseArr.push(box0);
            this.chooseObj[dg0.name] = box0;
            box0.addEventListener(TextEvent.LINK,this.linkClick);
         }
      }
      
      private function clearAll() : void
      {
         var box0:ChooseOneBox = null;
         for each(box0 in this.chooseArr)
         {
            removeChild(box0);
            box0.removeEventListener(TextEvent.LINK,this.linkClick);
         }
         this.chooseArr.length = 0;
         this.grip.clearData();
         this.tip.visible = false;
         this.btn.actived = false;
      }
      
      override public function show() : void
      {
         super.show();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      public function addArmsSave(yesFun0:Function) : void
      {
         this.show();
         this.yesFun = yesFun0;
         this.addChoose();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function linkClick(e:TextEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.fleshArmsSave();
      }
      
      private function fleshArmsSave() : void
      {
         var position0:String = null;
         var skillArr0:Array = null;
         var godSkillArr0:Array = null;
         var da0:ArmsData = null;
         var specialArr0:Array = null;
         var type0:String = this.chooseObj["armsType"].selectedArr[0];
         if(Boolean(type0))
         {
            position0 = this.chooseObj["hurt"].selectedArr[0];
            if(Boolean(position0))
            {
               specialArr0 = this.chooseObj["special"].selectedArr;
            }
            skillArr0 = this.chooseObj["skill"].selectedArr;
            godSkillArr0 = this.chooseObj["godSkill"].selectedArr;
            this.armsSave = Gaming.defineGroup.armsCreator.getSuperSave("red",60,type0,position0,specialArr0,skillArr0,godSkillArr0);
            da0 = this.armsSave.getSimulateData(Gaming.PG.da) as ArmsData;
            this.grip.inData_arms(da0);
            this.nameTxt.htmlText = da0.save.getColorCnName();
            this.lvTxt.htmlText = this.armsSave.getTrueLevel() + "级";
            da0.fleshOriginalData();
            this.tip.setText(da0.getGatherTip(null,true),"","","");
            this.tip.visible = true;
            this.btn.actived = this.getFillB();
            da0.fleshData_byEquip(Gaming.PG.da.getHeroMerge(),false);
         }
         else
         {
            this.grip.clear();
            this.tip.visible = false;
            this.btn.actived = false;
         }
      }
      
      private function getFillB() : Boolean
      {
         var box0:ChooseOneBox = null;
         for each(box0 in this.chooseArr)
         {
            if(!box0.isFillB())
            {
               return false;
            }
         }
         return true;
      }
      
      private function yesClick(e:MouseEvent) : void
      {
         if(this.yesFun is Function)
         {
            this.yesFun(this.armsSave);
            this.hide();
         }
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.hide();
      }
   }
}

