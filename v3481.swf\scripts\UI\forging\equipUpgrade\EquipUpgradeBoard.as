package UI.forging.equipUpgrade
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.must.NormalMustBox;
   import UI.base.tip.OneTextGather;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.equip.EquipData;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.creator.EquipUpgradeCtrl;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class EquipUpgradeBoard extends NormalUI
   {
      private var beforeTxt:OneTextGather = new OneTextGather();
      
      private var afterTxt:OneTextGather = new OneTextGather();
      
      private var beforeTag:Sprite;
      
      private var afterTag:Sprite;
      
      private var mustSp:Sprite;
      
      private var itemsGripSp:MovieClip;
      
      private var btnSp:MovieClip;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var itemsGrip:ItemsGrid = new ItemsGrid();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      public var nowData:EquipData = null;
      
      public function EquipUpgradeBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["beforeTag","afterTag","mustSp","btnSp","itemsGripSp"];
         super.setImg(img0);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("升级");
         addChild(this.itemsGrip);
         this.itemsGrip.setImg(this.itemsGripSp);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.itemsGrip);
         addChild(this.mustBox);
         this.mustBox.setImg(this.mustSp);
         addChild(this.beforeTxt);
         NormalUICtrl.setTag(this.beforeTxt,this.beforeTag);
         this.beforeTxt.init();
         this.beforeTxt.LAST_ICON_GAP = 5;
         addChild(this.afterTxt);
         NormalUICtrl.setTag(this.afterTxt,this.afterTag);
         this.afterTxt.init();
         this.afterTxt.LAST_ICON_GAP = 5;
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,"equip");
         this.showOneEquipDataAndPan(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      public function equipGripClick(da0:EquipData) : void
      {
         if(visible)
         {
            this.showOneEquipDataAndPan(da0);
         }
      }
      
      private function showOneEquipDataAndPan(da0:EquipData) : void
      {
         var dg0:EquipDataGroup = null;
         this.showNone();
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择要升级的装备。");
         if(Boolean(da0))
         {
            dg0 = Gaming.PG.da.findEquipData(da0);
            if(dg0 is EquipDataGroup)
            {
               this.showOneEquipData(da0);
            }
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      private function showOneEquipData(da0:EquipData) : void
      {
         this.nowData = da0;
         var after_da0:EquipData = EquipUpgradeCtrl.getAfterData(da0);
         this.itemsGrip.inData_equip(da0);
         this.beforeTxt.setText(EquipUpgradeCtrl.getStateStr(da0,after_da0));
         this.afterTxt.setText(EquipUpgradeCtrl.getStateStr(after_da0,da0,false));
         var must_d0:MustDefine = EquipUpgradeCtrl.getMust(da0);
         var bodyLv0:int = 0;
         if(this.nowData.normalPlayerData is NormalPlayerData)
         {
            bodyLv0 = this.nowData.normalPlayerData.level;
         }
         var mustB0:Boolean = this.mustBox.inData(must_d0,bodyLv0,"",true);
         var canB0:Boolean = da0.canUpgradeB();
         this.btn.actived = canB0 && mustB0;
      }
      
      private function showNone() : void
      {
         this.nowData = null;
         this.itemsGrip.clearData();
         this.beforeTxt.setText("");
         this.afterTxt.setText("");
         this.mustBox.setShowState(false);
         this.btn.actived = false;
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var must_d0:MustDefine = null;
         if(Boolean(this.nowData))
         {
            must_d0 = EquipUpgradeCtrl.getMust(this.nowData);
            PlayerMustCtrl.deductMust(must_d0,this.afterUpgrade);
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("装备数据不存在！");
         }
      }
      
      private function afterUpgrade() : void
      {
         EquipUpgradeCtrl.upgradeOne(this.nowData);
         this.showOneEquipDataAndPan(this.nowData);
         Gaming.uiGroup.allBagUI.fleshAllBox();
         Gaming.uiGroup.alertBox.showSuccess("升级成功！");
      }
   }
}

