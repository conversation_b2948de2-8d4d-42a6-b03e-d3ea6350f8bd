package UI.forging.equipUpgrade
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.must.NormalMustBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.equip.EquipData;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.creator.EquipUpgradeCtrl;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class EquipFillBoard extends AutoNormalUI
   {
      private var beforeTxt:TextField;
      
      private var beforeGrip:ItemsGrid = new ItemsGrid();
      
      private var afterTxt:TextField;
      
      private var afterGrip:ItemsGrid = new ItemsGrid();
      
      private var mustBox:NormalMustBox;
      
      private var btn:NormalBtn;
      
      private var infoBtn:NormalBtn;
      
      private var nowData:EquipData = null;
      
      public function EquipFillBoard()
      {
         super();
         mcTypeArr = ["btnSp","mustBoxSp","txt","Bx"];
      }
      
      override protected function firstLoad() : void
      {
         setImgUrl("ForgingUI/armsEvoBoard");
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         this.btn.setName("执行");
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.beforeGrip);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.afterGrip);
         this.infoBtn.visible = false;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
         this.bagShowEvent();
         this.showOneAndPan(this.nowData);
      }
      
      protected function bagShowEvent() : void
      {
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,"equip");
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      public function equipGripClick(da0:EquipData) : void
      {
         if(visible && Boolean(img))
         {
            this.showOneAndPan(da0);
         }
      }
      
      protected function showOneAndPan(da0:EquipData) : void
      {
         var dg0:EquipDataGroup = null;
         this.showNone();
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择要操作的装备。");
         if(Boolean(da0))
         {
            dg0 = Gaming.PG.da.findEquipData(da0);
            if(Boolean(dg0))
            {
               this.showOne(da0);
            }
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      protected function showOne(da0:EquipData) : void
      {
         this.nowData = da0;
         var after_da0:EquipData = EquipUpgradeCtrl.getFillDpsData(da0);
         var errorStr0:String = EquipUpgradeCtrl.fillDpsPan(da0);
         this.showOneText(da0,"before");
         if(errorStr0 == "")
         {
            this.showOneText(after_da0,"after");
         }
         else
         {
            this.showOneText(null,"after",ComMethod.color(errorStr0,"#FFFF00"));
         }
         var must_d0:MustDefine = EquipUpgradeCtrl.getFillDpsMust(da0);
         var bodyLv0:int = 0;
         if(this.nowData.normalPlayerData is NormalPlayerData)
         {
            bodyLv0 = this.nowData.normalPlayerData.level;
         }
         var mustB0:Boolean = this.mustBox.inData(must_d0,bodyLv0);
         var canB0:Boolean = errorStr0 == "";
         this.btn.actived = canB0 && mustB0;
      }
      
      private function showOneText(da0:EquipData, label0:String, setText0:String = "") : void
      {
         var grip0:ItemsGrid = this[label0 + "Grip"];
         var text0:TextField = this[label0 + "Txt"];
         var str0:String = "";
         if(Boolean(da0))
         {
            grip0.inData_equip(da0);
            str0 = EquipUpgradeCtrl.getFillDpsProText(da0);
         }
         else
         {
            grip0.clearData();
            str0 = ComMethod.color("无法执行该操作","#00FF00");
         }
         if(setText0 != "")
         {
            str0 = setText0;
         }
         text0.htmlText = str0;
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var must_d0:MustDefine = null;
         if(Boolean(this.nowData))
         {
            if(this.btn.actived)
            {
               must_d0 = EquipUpgradeCtrl.getFillDpsMust(this.nowData);
               PlayerMustCtrl.deductMust(must_d0,this.afterStrengthen);
            }
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("数据不存在！");
         }
      }
      
      private function afterStrengthen() : void
      {
         EquipUpgradeCtrl.fillDps(this.nowData);
         this.yes_save();
      }
      
      private function yes_save(v:* = null) : void
      {
         this.showOneAndPan(this.nowData);
         Gaming.uiGroup.allBagUI.fleshAllBox();
         Gaming.uiGroup.alertBox.showSuccess("成功！");
      }
      
      private function showNone() : void
      {
         this.nowData = null;
         if(Boolean(img))
         {
            this.beforeGrip.clearData();
            this.beforeTxt.text = "";
            this.afterGrip.clearData();
            this.afterTxt.text = "";
            this.mustBox.setShowState(false);
            this.btn.actived = false;
         }
      }
   }
}

