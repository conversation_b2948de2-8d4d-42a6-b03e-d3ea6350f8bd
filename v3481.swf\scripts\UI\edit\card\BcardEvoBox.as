package UI.edit.card
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.UIShow;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.grid.NormalGrid;
   import UI.edit.BosseditPKBoard;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.edit.card.BcardEvoAgent;
   import dataAll._app.edit.card.BossCardCreator;
   import dataAll._app.edit.card.BossCardData;
   import dataAll._app.edit.card.BossCardDataGroup;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class BcardEvoBox extends AutoNormalUI
   {
      private var titleTxt:TextField;
      
      private var titleTxtY:Number = 0;
      
      private var otherTxt:TextField;
      
      private var closeBtn:NormalBtn;
      
      private var boxTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var autoBtn:NormalBtn;
      
      private var upBtn:NormalBtn;
      
      private var box:ItemsGripBox = new ItemsGripBox();
      
      private var beforeBtn:NormalBtn;
      
      private var afterBtn:NormalBtn;
      
      private var DA:BossCardData = null;
      
      private var tempGrip:ItemsGrid = null;
      
      private var evoDa:BossCardData = null;
      
      public function BcardEvoBox()
      {
         super();
         mcTypeArr = ["tag","txt","btnSp"];
      }
      
      override protected function firstLoad() : void
      {
         setImgUrl("BosseditUI/cardEvoBox");
         FontDeal.dealOne(this.titleTxt);
         this.titleTxtY = this.titleTxt.y;
         this.otherTxt.visible = false;
         this.box.setIconPro("BosseditUI/cardBtn");
         this.box.arg.init(6,4,7,7);
         this.box.evt.setWant(true,true);
         addChild(this.box);
         NormalUICtrl.setTag(this.box,this.boxTag);
         this.box.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.box.addEventListener(ClickEvent.ON_OVER,this.gripOver);
         this.box.addEventListener(ClickEvent.ON_OUT,this.gripOut);
         this.box.pageBox.setToNormalBtn();
         this.box.setPagePos(this.pageTag);
         this.beforeBtn.addEventListener(MouseEvent.MOUSE_OVER,this.btnOver);
         this.beforeBtn.addEventListener(MouseEvent.MOUSE_OUT,this.gripOut);
         this.upBtn.setName("进阶");
         this.x = 2;
         this.y = 69;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      protected function get dataG() : BossCardDataGroup
      {
         return Gaming.PG.da.bossCard;
      }
      
      public function getEvoDa() : BossCardData
      {
         return this.evoDa;
      }
      
      public function clearEvoDa() : void
      {
         this.evoDa = null;
      }
      
      public function outLoginEvent() : void
      {
         this.clearData();
      }
      
      public function clearData() : void
      {
         this.DA = null;
         this.tempGrip = null;
         this.evoDa = null;
      }
      
      public function showData(da0:BossCardData) : void
      {
         this.DA = da0;
         this.show();
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      private function fleshData() : void
      {
         var evoAgent0:BcardEvoAgent = null;
         if(Boolean(this.DA))
         {
            BosseditPKBoard.inBigGripFun(this.beforeBtn,this.DA);
            this.beforeBtn.setNewMc("before");
            evoAgent0 = BossCardCreator.getEvoAgent(this.DA);
            this.afterBtn.setName(this.DA.getBodyDefine().cnName);
            this.afterBtn.setIconName(this.DA.getIconUrl());
            this.afterBtn.setStarIcon(this.DA.getStar() + 1);
            this.afterBtn.setNumText(evoAgent0.getTip());
            this.afterBtn.mouseEnabled = false;
            this.fleshBox();
            this.fleshMust();
         }
         else
         {
            this.beforeBtn.clearData();
            this.box.clearAllData();
            this.upBtn.actived = false;
            this.titleTxt.htmlText = "";
         }
      }
      
      private function fleshMust() : void
      {
         var nextNum0:int = 0;
         var now0:int = this.getChooseNum();
         var mustStar0:int = this.DA.getEvoMustStar();
         nextNum0 = BossCardData.getEvoChildNext(mustStar0);
         var nextStar0:int = BossCardData.getEvoChildNextStar(mustStar0);
         var mustNum0:int = this.DA.getEvoMustNum();
         this.titleTxt.htmlText = "需要消耗" + mustNum0 + "张" + ComMethod.color(mustStar0 + "星魂卡","#00FFFF") + "，当前已选" + ComMethod.dropColor(now0,mustNum0,"#FF9900") + "张";
         if(nextNum0 > 0)
         {
            this.titleTxt.y = this.titleTxtY;
            this.otherTxt.visible = true;
            this.otherTxt.htmlText = "（也可选择" + nextStar0 + "星魂卡，1张" + nextStar0 + "星相当于" + nextNum0 + "张" + mustStar0 + "星魂卡）";
         }
         else
         {
            this.titleTxt.y = this.autoBtn.y;
            this.otherTxt.visible = false;
         }
         this.upBtn.actived = false;
         if(now0 == mustNum0)
         {
            this.autoBtn.setName("取消选择");
            this.autoBtn.actived = true;
            this.upBtn.actived = true;
         }
         else
         {
            this.autoBtn.setName("自动选");
            this.autoBtn.actived = mustStar0 <= 5;
         }
      }
      
      private function fleshBox() : void
      {
         var daArr0:Array = this.DA.getEvoChildArr();
         this.box.inData_byArr(daArr0,this.inBarFun);
      }
      
      private function inBarFun(grip0:NormalBtn, da0:BossCardData) : void
      {
         grip0.itemsData = da0;
         if(Boolean(da0))
         {
            grip0.setIconName(da0.getIconUrl());
            grip0.setStarIcon(da0.getStar());
            grip0.setNewMc(da0.getIconNewLabel());
            if(grip0 is NormalGrid)
            {
               (grip0 as NormalGrid).setSecMc(da0.getUISecMc());
            }
            grip0.actived = true;
         }
         else
         {
            grip0.clearShow();
            grip0.setStarIcon("");
         }
      }
      
      private function gripOver(e:ClickEvent) : void
      {
         var da0:BossCardData = e.childData as BossCardData;
         var tip0:String = da0.getGatherTip(false,false);
         UIOrder.showTip(tip0);
      }
      
      private function btnOver(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var da0:BossCardData = btn0.itemsData as BossCardData;
         var tip0:String = da0.getGatherTip(false,false);
         UIOrder.showTip(tip0);
      }
      
      private function gripOut(e:* = null) : void
      {
         UIOrder.showTip("");
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var mda0:BossCardData = null;
         var error0:String = null;
         var alert0:String = null;
         var grip0:ItemsGrid = e.child as ItemsGrid;
         if(grip0.isChosen)
         {
            grip0.isChosen = false;
            this.fleshMust();
         }
         else
         {
            mda0 = e.childData as BossCardData;
            if(this.panClickChildData(mda0))
            {
               error0 = mda0.getEvoChildError();
               if(error0 != "")
               {
                  Gaming.uiGroup.alertBox.showError(error0);
               }
               else
               {
                  alert0 = mda0.getEvoChildAlert();
                  this.tempGrip = grip0;
                  if(alert0 == "")
                  {
                     this.afterClick();
                  }
                  else
                  {
                     Gaming.uiGroup.alertBox.showChoose(alert0,this.afterClick,null,mda0.getIconUrl());
                  }
               }
            }
         }
      }
      
      private function afterClick() : void
      {
         var grip0:ItemsGrid = this.tempGrip;
         if(this.panClickChildData(grip0.itemsData as BossCardData))
         {
            grip0.isChosen = true;
            this.fleshMust();
            this.tempGrip = null;
         }
      }
      
      private function panClickChildData(da0:BossCardData) : Boolean
      {
         var add0:int = 0;
         var now0:int = this.getChooseNum();
         var mustNum0:int = this.DA.getEvoMustNum();
         if(now0 >= mustNum0)
         {
            return false;
         }
         add0 = BossCardData.countEvoChildAdd(this.DA.getStar(),da0.getStar());
         if(now0 + add0 > mustNum0)
         {
            return false;
         }
         return true;
      }
      
      private function getChooseArr() : Array
      {
         var grip0:ItemsGrid = null;
         var daArr0:Array = [];
         for each(grip0 in this.box.gripArr)
         {
            if(grip0.isChosen)
            {
               daArr0.push(grip0.itemsData);
            }
         }
         return daArr0;
      }
      
      private function getChooseNum() : int
      {
         var grip0:ItemsGrid = null;
         var da0:BossCardData = null;
         var add0:int = 0;
         var num0:int = 0;
         for each(grip0 in this.box.gripArr)
         {
            if(grip0.isChosen)
            {
               da0 = grip0.itemsData as BossCardData;
               add0 = BossCardData.countEvoChildAdd(this.DA.getStar(),da0.getStar());
               num0 += add0;
            }
         }
         return num0;
      }
      
      private function checkChoose() : String
      {
         var da0:BossCardData = null;
         var arr0:Array = this.getChooseArr();
         var chooseNum0:int = this.getChooseNum();
         var mustNum0:int = this.DA.getEvoMustNum();
         if(chooseNum0 != mustNum0)
         {
            return "选择数量不对！";
         }
         for each(da0 in arr0)
         {
            if(da0 == this.DA)
            {
               return "不能选中当前卡牌！";
            }
            if(BossCardData.panEvoChildData(this.DA,da0) == false)
            {
               return "选择的卡牌星级不对！";
            }
         }
         return "";
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var error0:String = null;
         var delArr0:Array = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0 == this.upBtn)
         {
            error0 = this.checkChoose();
            if(error0 == "")
            {
               delArr0 = this.getChooseArr();
               this.dataG.removeDataArr(delArr0);
               BossCardCreator.evo(this.DA);
               this.evoDa = this.DA;
               UIOrder.yesSaveTip = "进阶成功！";
               UIOrder.save(true,true,false,this.yes_save,UIShow.hideNowApp,false,true);
            }
            else
            {
               UIOrder.alertError(error0);
            }
         }
         else if(btn0 == this.closeBtn)
         {
            this.clearData();
            this.hide();
         }
         else if(btn0 == this.autoBtn)
         {
            this.autoBtnClick();
         }
      }
      
      private function autoBtnClick() : void
      {
         var chooseNum0:int = 0;
         var grip0:ItemsGrid = null;
         var da0:BossCardData = null;
         var add0:int = 0;
         var now0:int = this.getChooseNum();
         var mustNum0:int = this.DA.getEvoMustNum();
         if(now0 == mustNum0)
         {
            this.box.setChoose_byIndex(-1);
            this.fleshMust();
         }
         else
         {
            this.box.setChoose_byIndex(-1);
            chooseNum0 = 0;
            for each(grip0 in this.box.gripArr)
            {
               da0 = grip0.itemsData as BossCardData;
               if(da0.lockB == false)
               {
                  add0 = BossCardData.countEvoChildAdd(this.DA.getStar(),da0.getStar());
                  if(chooseNum0 + add0 <= mustNum0)
                  {
                     grip0.isChosen = true;
                     chooseNum0 += add0;
                  }
               }
               if(chooseNum0 >= mustNum0)
               {
                  break;
               }
            }
            this.fleshMust();
         }
      }
      
      private function yes_save(v:* = null) : void
      {
         this.hide();
         Gaming.uiGroup.bosseditUI.fleshData();
         this.clearData();
      }
   }
}

