package UI.parts
{
   import UI.UIShow;
   import UI.bag.BagUI;
   import UI.base.AppNormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import dataAll._app.parts.PartsDataGroup;
   import dataAll._app.parts.PartsMethod;
   import dataAll.arms.ArmsData;
   import dataAll.arms.ArmsDataGroup;
   import dataAll.things.ThingsData;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class PartsUI extends AppNormalUI
   {
      public var labelBox:LabelBox = new LabelBox();
      
      public var labelTag:Sprite = null;
      
      private var closeBtn:SimpleButton;
      
      private var tipBtn:SimpleButton;
      
      private var coverSp:Sprite;
      
      private var coverTxt:TextField = null;
      
      public var assemblyBox:PartsAssemblyBoard = new PartsAssemblyBoard();
      
      public var composeBox:PartsComposeBoard = new PartsComposeBoard();
      
      public var boxArr:Array = [this.assemblyBox,this.composeBox];
      
      public function PartsUI()
      {
         super();
         UICn = "零件";
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["labelTag","closeBtn","coverSp","tipBtn"];
         super.setImg(img0);
         this.labelBox.arg.init(5,1,-6,0);
         addChild(this.labelBox);
         this.labelBox.inData("midLabelBtn",["assembly","compose"],["装配","合成"]);
         this.labelBox.setChoose("assembly");
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.labelBox.x = this.labelTag.x;
         this.labelBox.y = this.labelTag.y;
         addChild(this.assemblyBox);
         this.assemblyBox.visible = false;
         this.assemblyBox.setImg(Gaming.swfLoaderManager.getResource("PartsUI","assemblyBoard"));
         addChild(this.composeBox);
         this.composeBox.visible = false;
         this.composeBox.setImg(Gaming.swfLoaderManager.getResource("PartsUI","composeBoard"));
         addChild(this.coverSp);
         this.coverTxt = this.coverSp["txt"];
         this.setCoverText("");
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function setCoverText(str0:String) : void
      {
         this.coverSp.visible = str0 != "";
         this.coverTxt.htmlText = str0;
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      public function showBox(label0:String) : void
      {
         var n:* = undefined;
         if(label0 == "")
         {
            label0 = "assembly";
         }
         this.labelBox.setChoose(label0);
         this.setCoverText("");
         for(n in this.boxArr)
         {
            this.boxArr[n].hide();
         }
         if(label0 == "assembly")
         {
            this.assemblyBox.show();
            if(!this.assemblyBox.nowArmsData)
            {
               Gaming.uiGroup.bagUI.showAndLabel("arms");
            }
         }
         else if(label0 == "compose")
         {
            this.composeBox.show();
            Gaming.uiGroup.bagUI.showAndLabel("parts");
         }
      }
      
      public function fleshNowData() : void
      {
         var n:* = undefined;
         if(!visible)
         {
            return;
         }
         for(n in this.boxArr)
         {
            if(Boolean(this.boxArr[n].visible))
            {
               this.boxArr[n].show();
            }
         }
      }
      
      public function getNowTrueLabel() : String
      {
         if(visible)
         {
            return this.labelBox.nowLabel;
         }
         return "";
      }
      
      public function armsGripClick(da0:ArmsData, dg0:ArmsDataGroup) : void
      {
         if(PartsMethod.canB() && dg0.placeType != "house")
         {
            this.labelBox.nowLabel = "assembly";
            this.assemblyBox.nowArmsData = da0;
            UIShow.showApp("parts",true);
         }
      }
      
      public function partsGripClick(da0:ThingsData, dg0:PartsDataGroup) : void
      {
         if(PartsMethod.canB())
         {
            this.labelBox.nowLabel = "compose";
            this.composeBox.nowData = da0;
            UIShow.showApp("parts",true);
         }
      }
      
      public function partsGripOneClick(da0:ThingsData, dg0:PartsDataGroup) : void
      {
         if(this.visible && this.composeBox.visible)
         {
            this.composeBox.nowData = da0;
            this.composeBox.fleshData();
         }
      }
      
      public function itemsDataRemove() : void
      {
         if(this.visible)
         {
            if(this.assemblyBox.visible)
            {
               this.assemblyBox.fleshData();
            }
            else if(this.composeBox.visible)
            {
               this.composeBox.fleshData();
            }
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.showBox(this.labelBox.nowLabel);
         var bag0:BagUI = Gaming.uiGroup.bagUI;
         if(!bag0.visible)
         {
            bag0.showAndLabel("parts");
         }
      }
      
      override public function hide() : void
      {
         if(Gaming.uiGroup.bagUI.visible)
         {
            Gaming.uiGroup.bagUI.hide();
         }
         super.hide();
      }
      
      public function gotoCompose(da0:ThingsData) : void
      {
         this.composeBox.nowData = da0;
         this.showBox("compose");
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.hide();
      }
      
      private function tipOver(e:MouseEvent) : void
      {
         var str0:String = "";
         if(this.assemblyBox.visible)
         {
            str0 += "1、一把武器上每种零件只能装配1个。";
            str0 += "\n2、武器无法装配高于自身等级的零件。";
            str0 += "\n3、零件装配后，会保存在武器内。";
            str0 += "\n4、装配相同属性的稀有零件时，武器将获得最高的那条属性值。";
         }
         else if(this.composeBox.visible)
         {
            str0 += "1、合成下一级零件需要8个上一级零件。";
            str0 += "\n2、每一级零件相差3个物品等级。";
         }
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.setText(str0);
            Gaming.uiGroup.tipBox.textTip.show();
            Gaming.uiGroup.tipBox.followMouseB = true;
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      private function tipOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
   }
}

