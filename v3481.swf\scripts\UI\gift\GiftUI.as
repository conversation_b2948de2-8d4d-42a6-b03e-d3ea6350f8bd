package UI.gift
{
   import UI.UIShow;
   import UI.base.AppNormalUI;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import UI.gift.active.GiftActiveBoard;
   import UI.gift.activity.GameBoxBoard;
   import UI.gift.activity.MillionpackExchangeBoard;
   import UI.gift.activity.Spring2017Board;
   import UI.gift.activity.WeixinExchangeBoard;
   import UI.gift.daily.DailySignBoard;
   import UI.gift.exchange.CodeExchangeBoard;
   import UI.gift.holiday.HolidayGiftBoard;
   import UI.gift.level.GiftLevelBoard;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class GiftUI extends AppNormalUI
   {
      public var labelBox:LabelBox = new LabelBox();
      
      public var dailySignBoard:DailySignBoard = new DailySignBoard();
      
      public var activeBoard:GiftActiveBoard = new GiftActiveBoard();
      
      public var holidayBoard:HolidayGiftBoard = new HolidayGiftBoard();
      
      public var exchangeBoard:CodeExchangeBoard = new CodeExchangeBoard();
      
      public var millionpackBoard:MillionpackExchangeBoard = new MillionpackExchangeBoard();
      
      public var weixinBoard:WeixinExchangeBoard = new WeixinExchangeBoard();
      
      public var levelBoard:GiftLevelBoard = new GiftLevelBoard();
      
      public var gameBoxBoard:GameBoxBoard = new GameBoxBoard();
      
      public var spring2017Board:Spring2017Board = new Spring2017Board();
      
      private var labelNameArr:Array = ["dailySign","active","exchange","level","gameBox"];
      
      private var nowLabel:String = "";
      
      private var labelTag:Sprite;
      
      private var closeBtn:SimpleButton;
      
      public function GiftUI()
      {
         super();
         UICn = "礼包大全";
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var labelName0:* = null;
         var board0:NormalUI = null;
         elementNameArr = ["labelTag","closeBtn"];
         super.setImg(img0);
         this.labelBox.arg.init(1,7,0,-5);
         this.labelBox.firstUpB = false;
         this.labelBox.evt.setWantEvent(true,false,false,true,true);
         this.labelBox.inData("verBigLabelBtn",this.labelNameArr,["签到礼包","活跃值","活动礼包","等级礼包","游戏盒专属礼包"]);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.labelBox.addEventListener(ClickEvent.ON_OVER,this.labelOver);
         this.labelBox.addEventListener(ClickEvent.ON_OUT,this.labelOut);
         this.labelBox.x = this.labelTag.x;
         this.labelBox.y = this.labelTag.y;
         addChild(this.labelBox);
         this.labelBox.getBtnByLabel("active").setSmallIcon("new");
         for each(labelName0 in this.labelNameArr)
         {
            board0 = this[labelName0 + "Board"];
            addChild(board0);
            board0.setImg(Gaming.swfLoaderManager.getResource("GiftUI",labelName0 + "Board"));
            board0.hide();
         }
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function fleshActiveIfVisible() : void
      {
         if(visible)
         {
            if(this.activeBoard.visible)
            {
               this.activeBoard.show();
            }
         }
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      private function labelOver(e:ClickEvent) : void
      {
      }
      
      private function labelOut(e:ClickEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      public function showBox(label0:String) : void
      {
         var labelName0:* = null;
         var y_btn0:NormalBtn = null;
         if(label0 == "")
         {
            label0 = this.labelNameArr[0];
         }
         this.nowLabel = label0;
         this.labelBox.setChoose(label0);
         for each(labelName0 in this.labelNameArr)
         {
            this[labelName0 + "Board"].hide();
         }
         y_btn0 = this.labelBox.getBtnByLabel("community");
         if(label0 != "")
         {
            this[label0 + "Board"].show();
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.showBox("");
      }
      
      public function gotoShowLabel(label0:String) : void
      {
         UIShow.showApp("gift");
         this.showBox(label0);
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         hide();
      }
      
      override public function get width() : Number
      {
         return 803;
      }
   }
}

