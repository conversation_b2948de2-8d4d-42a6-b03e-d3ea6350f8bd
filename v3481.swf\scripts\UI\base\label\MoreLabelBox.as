package UI.base.label
{
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import dataAll.ui.label.LabelAddData;
   import flash.events.Event;
   
   public class MoreLabelBox extends LabelBox
   {
      public var childObj:Object = {};
      
      public var fatherBox:MoreLabelBox;
      
      public function MoreLabelBox()
      {
         super();
      }
      
      override public function inDataByLabelAddData(da0:LabelAddData) : void
      {
         var n:* = undefined;
         var btn0:NormalBtn = null;
         var chilidDa0:LabelAddData = null;
         var box0:MoreLabelBox = null;
         arg.init(1,999,0,0);
         super.inDataByLabelAddData(da0);
         if(da0.childIsGroupB)
         {
            for(n in da0.childArr)
            {
               btn0 = gripArr[n];
               chilidDa0 = da0.childArr[n];
               box0 = new MoreLabelBox();
               btn0.setSmallIconFrame(1);
               box0.evt.setWantEvent(true,false,false,true,true);
               box0.fatherBox = this;
               box0.fatherUrl = fatherUrl + label + "/";
               box0.inDataByLabelAddData(chilidDa0);
               this.childObj[chilidDa0.label] = box0;
               box0.visible = false;
            }
         }
      }
      
      override public function clear() : void
      {
         var box0:MoreLabelBox = null;
         super.clear();
         for each(box0 in this.childObj)
         {
            box0.clear();
            if(box0.parent == this)
            {
               this.removeChild(box0);
            }
         }
         this.childObj = {};
      }
      
      public function getChildBoxByLabel(labe0:String) : MoreLabelBox
      {
         return this.childObj[labe0];
      }
      
      public function getSunBtn(type0:String, childType0:String) : NormalBtn
      {
         var labelBox0:MoreLabelBox = this.getChildBoxByLabel(type0);
         if(Boolean(labelBox0))
         {
            return labelBox0.getBtnByLabel(childType0);
         }
         return null;
      }
      
      public function fleshPosition() : void
      {
         var n:* = undefined;
         var btn0:NormalBtn = null;
         var box0:MoreLabelBox = null;
         for(n in gripArr)
         {
            btn0 = gripArr[n];
            btn0.extraY = 0;
            box0 = this.getChildBoxByLabel(btn0.label);
            if(box0 is MoreLabelBox)
            {
               if(box0.visible)
               {
                  box0.fleshPosition();
                  btn0.extraY = box0.height;
               }
            }
         }
         arrange();
         this.fleshMyChildExtraY();
      }
      
      protected function fleshMyChildExtraY() : void
      {
         var n:* = undefined;
         var btn0:NormalBtn = null;
         var box0:MoreLabelBox = null;
         for(n in gripArr)
         {
            btn0 = gripArr[n];
            box0 = this.getChildBoxByLabel(btn0.label);
            if(box0 is MoreLabelBox)
            {
               if(box0.visible)
               {
                  box0.x = btn0.x + 10;
                  box0.y = btn0.y + btn0.height;
               }
            }
         }
      }
      
      public function clearAllChoose() : void
      {
         var n:* = undefined;
         var btn0:NormalBtn = null;
         var box0:MoreLabelBox = null;
         for(n in gripArr)
         {
            btn0 = gripArr[n];
            box0 = this.getChildBoxByLabel(btn0.label);
            btn0.isChosen = false;
            btn0.actived = true;
            if(box0 is MoreLabelBox)
            {
               box0.clearAllChoose();
            }
         }
      }
      
      override public function dispatchEvent(event:Event) : Boolean
      {
         var evt0:ClickEvent = null;
         if(event is ClickEvent)
         {
            evt0 = event as ClickEvent;
            if(evt0.type == ClickEvent.ON_CLICK)
            {
               if(evt0.father == this)
               {
                  this.clickMyChild(evt0);
               }
               if(!(this.fatherBox is MoreLabelBox))
               {
                  this.finalClick(evt0);
               }
               else
               {
                  this.fatherBox.dispatchEvent(event);
               }
            }
            else if(this.fatherBox is MoreLabelBox)
            {
               this.fatherBox.dispatchEvent(event);
            }
         }
         return super.dispatchEvent(event);
      }
      
      protected function clickMyChild(e:ClickEvent) : void
      {
         var btn0:NormalBtn = e.child as NormalBtn;
         var box0:MoreLabelBox = this.getChildBoxByLabel(btn0.label);
         if(box0 is MoreLabelBox)
         {
            if(box0.visible)
            {
               box0.hideVisible();
               btn0.setSmallIconFrame(1);
            }
            else
            {
               box0.showVisible(this);
               btn0.setSmallIconFrame(2);
            }
         }
      }
      
      protected function finalClick(e:ClickEvent) : void
      {
         this.fleshPosition();
         var btn0:NormalBtn = e.child as NormalBtn;
         var box0:MoreLabelBox = e.father as MoreLabelBox;
         if(!box0.addData.childIsGroupB)
         {
            this.clearAllChoose();
            btn0.isChosen = true;
            btn0.actived = false;
         }
      }
      
      public function hideVisible() : void
      {
         visible = false;
         if(Boolean(parent))
         {
            parent.removeChild(this);
         }
      }
      
      public function showVisible(parent0:MoreLabelBox) : void
      {
         visible = true;
         parent0.addChild(this);
      }
      
      public function showChildVisibleByName(type0:String, childType0:String) : void
      {
         var btn0:NormalBtn = null;
         var labelBox0:MoreLabelBox = this.getChildBoxByLabel(type0);
         if(Boolean(labelBox0))
         {
            labelBox0.showVisible(this);
            this.fleshPosition();
            labelBox0.setChoose(childType0);
            btn0 = getBtnByLabel(type0);
            if(Boolean(btn0))
            {
               btn0.setSmallIconFrame(2);
            }
         }
      }
      
      public function hideAllChild() : void
      {
         var btn0:NormalBtn = null;
         var box0:MoreLabelBox = null;
         for each(btn0 in gripArr)
         {
            box0 = this.getChildBoxByLabel(btn0.label);
            box0.hideVisible();
         }
         this.fleshPosition();
      }
   }
}

