package UI.count
{
   import UI.UIGroup;
   import UI.base.AppNewUI;
   import UI.count.body.LevelBodyBoard;
   import UI.count.hurt.LevelHurtBoard;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.setting.key.SettingKeySave;
   import flash.text.TextField;
   
   public class LevelCountUI extends AppNewUI
   {
      private var titleTxt:TextField;
      
      private var levelHurtBoard:LevelHurtBoard = new LevelHurtBoard();
      
      private var levelBodyBoard:LevelBodyBoard = new LevelBodyBoard();
      
      public function LevelCountUI()
      {
         super();
         UILabel = "levelCount";
         swfLabel = "OtherUI";
         labelArr = ["levelHurt","levelBody"];
         labelCnArr = ["伤害","单位"];
      }
      
      override protected function firstLoad() : void
      {
         elementNameArr = elementNameArr.concat(["titleTxt"]);
         setImgUrl(swfLabel + "/" + UILabel + "UI");
         init_addLabel();
         init_addBox();
         init_other();
         UIGroup.setUIMiddle(this);
         var keySave0:SettingKeySave = Gaming.PG.da.getMeKeySave();
         if(Boolean(keySave0))
         {
            this.titleTxt.htmlText = "关卡统计" + ComMethod.gray2("[" + keySave0.getCn("levelCount") + "]");
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
      }
      
      public function outLoginEvent() : void
      {
      }
      
      override protected function getTipText() : String
      {
         var str0:String = "";
         if(this.levelHurtBoard.visible)
         {
            str0 += "增伤BUFF=(hurtMul*otherHurtMul+穿透伤害)*附身伤害*allHurtMul";
         }
         return str0;
      }
   }
}

