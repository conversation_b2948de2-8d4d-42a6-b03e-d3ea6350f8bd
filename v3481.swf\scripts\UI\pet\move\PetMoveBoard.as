package UI.pet.move
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.must.NormalMustBox;
   import UI.base.tip.OneTextGather;
   import UI.pet.PetUI;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.equip.define.EquipColor;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.pet.PetData;
   import dataAll.pet.gene.GeneData;
   import dataAll.pet.gene.creator.GeneDataMoveCtrl;
   import dataAll.pet.gene.save.GeneSave;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class PetMoveBoard extends NormalUI
   {
      private var beforeTag:Sprite;
      
      private var afterTag:Sprite;
      
      private var beforeTxt:OneTextGather = new OneTextGather();
      
      private var afterTxt:OneTextGather = new OneTextGather();
      
      private var noTxt:TextField;
      
      private var mustTag:Sprite;
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var gripTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var gripBox:ItemsGripBox = new ItemsGripBox();
      
      private var btnSp:MovieClip;
      
      private var allBtnSp:MovieClip;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var allBtn:NormalBtn = new NormalBtn();
      
      private var nowChooseGene:GeneData;
      
      public function PetMoveBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["noTxt","beforeTag","afterTag","mustTag","btnSp","allBtnSp","gripTag","pageTag"];
         super.setImg(img0);
         addChild(this.beforeTxt);
         NormalUICtrl.setTag(this.beforeTxt,this.beforeTag);
         this.beforeTxt.init();
         this.beforeTxt.LAST_ICON_GAP = 5;
         addChild(this.afterTxt);
         NormalUICtrl.setTag(this.afterTxt,this.afterTag);
         this.afterTxt.init();
         this.afterTxt.LAST_ICON_GAP = 5;
         this.afterTxt.MAX_WIDTH = 140;
         addChild(this.mustBox);
         this.mustBox.setNormalImg();
         NormalUICtrl.setTag(this.mustBox,this.mustTag);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("只替换资质");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         addChild(this.allBtn);
         this.allBtn.setImg(this.allBtnSp);
         this.allBtn.setName("替换资质和技能");
         this.allBtn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.allBtn.addEventListener(MouseEvent.MOUSE_OUT,this.btnOut);
         this.allBtn.addEventListener(MouseEvent.MOUSE_OVER,this.btnOver);
         this.allBtn.activedAndEnabled = false;
         addChild(this.gripBox);
         NormalUICtrl.setTag(this.gripBox,this.gripTag);
         this.gripBox.imgType = "equipGrip";
         this.gripBox.arg.init(3,5,3,3);
         this.gripBox.evt.setWantEvent(true,false,false,true,true);
         this.gripBox.pageBox.setToNormalBtn();
         this.gripBox.pageBox.setXY_bySp(this.pageTag,this.gripBox);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.listClick);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.gripBox);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      public function fleshData() : void
      {
         if(Boolean(PetUI.nowGrip))
         {
            this.showData(PetUI.getNowData());
         }
      }
      
      public function outLoginEvent() : void
      {
         this.nowChooseGene = null;
      }
      
      private function clearAll() : void
      {
         this.gripBox.clearAllData();
         this.beforeTxt.setText("");
         this.afterTxt.setText("");
         this.mustBox.setShowState(false);
         this.btn.actived = false;
         this.allBtn.actived = false;
      }
      
      private function showData(da0:PetData) : void
      {
         var must_d0:MustDefine = null;
         var bb0:Boolean = false;
         this.clearAll();
         this.showList(da0);
         var s0:GeneSave = da0.gene.save;
         var afterObj0:Object = null;
         if(Boolean(this.nowChooseGene))
         {
            afterObj0 = this.nowChooseGene.save.obj;
         }
         var beforeStr0:String = "<i1>|<blue <b>当前" + s0.getTrueLevel() + "级</b>/>";
         beforeStr0 += "\n" + Gaming.defineGroup.geneCreator.getGather_byObj(s0.obj,afterObj0);
         this.beforeTxt.setText(beforeStr0);
         var afterStr0:String = "";
         if(Boolean(afterObj0))
         {
            afterStr0 += "<i1>|<blue <b>替换后" + this.nowChooseGene.save.getTrueLevel() + "级</b>/>";
            afterStr0 += "\n" + Gaming.defineGroup.geneCreator.getGather_byObj(afterObj0,s0.obj);
         }
         else
         {
            afterStr0 += "<i1>|<blue 请在右边选择同类基因体，你可以将当前尸宠的资质替换成该基因体的资质。/>";
            afterStr0 += "\n\n<green 基因体的品质必须好过或者与当前尸宠品质相同。/>";
         }
         this.afterTxt.setText(afterStr0);
         if(Boolean(afterObj0))
         {
            must_d0 = GeneDataMoveCtrl.getMust(this.nowChooseGene.save);
            bb0 = this.mustBox.inData(must_d0,da0.base.save.level,"尸宠");
            this.btn.actived = bb0;
            this.allBtn.actived = this.btn.actived;
         }
         else
         {
            this.mustBox.setShowState(false);
         }
      }
      
      private function showList(da0:PetData) : void
      {
         var colorStr0:String = null;
         var arr0:Array = Gaming.PG.da.geneBag.getArrByPetName(da0.gene.save.name,da0.gene.getColor());
         this.gripBox.inData_byArr(arr0,"inData_gene");
         this.chooseGene(this.nowChooseGene);
         this.noTxt.visible = false;
         if(this.gripBox.gripArr.length == 0)
         {
            this.noTxt.visible = true;
            colorStr0 = EquipColor.getColorCn(da0.gene.getColor());
            this.noTxt.htmlText = "找不到" + colorStr0 + "或" + colorStr0 + "以上的" + da0.gene.getCnName() + "。";
         }
      }
      
      private function chooseGene(da0:GeneData) : void
      {
         var grip0:ItemsGrid = this.gripBox.findGripByData(da0);
         if(Boolean(grip0))
         {
            this.nowChooseGene = da0;
            this.gripBox.setChoose_byIndex(grip0.index);
         }
         else
         {
            this.nowChooseGene = null;
         }
      }
      
      private function listClick(e:ClickEvent) : void
      {
         this.nowChooseGene = e.childData as GeneData;
         this.fleshData();
      }
      
      private function btnOver(e:MouseEvent) : void
      {
         var str0:String = "";
         if(e.target == this.allBtn)
         {
            str0 += ComMethod.color("<b>替换当前尸宠的资质和技能</b>","#FFFF00") + "\n替换时，系统将重置当前尸宠的所有技能，并返还所有银币、石头。";
         }
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
      }
      
      private function btnOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = null;
         var da0:PetData = PetUI.getNowData();
         if(Boolean(da0))
         {
            btn0 = e.target as NormalBtn;
            if(btn0.actived)
            {
               if(btn0 == this.btn)
               {
                  Gaming.uiGroup.alertBox.showNormal("只替换当前尸宠的资质？\n替换后，用于替换的基因体将消失。","yesAndNo",this.after_one);
               }
               else if(btn0 == this.allBtn)
               {
                  Gaming.uiGroup.alertBox.showNormal("确定替换当前尸宠的资质和技能？\n替换后，用于替换的基因体将消失。","yesAndNo",this.after_all);
               }
            }
         }
      }
      
      private function after_one() : void
      {
         var must_d0:MustDefine = GeneDataMoveCtrl.getMust(this.nowChooseGene.save);
         PlayerMustCtrl.deductMust(must_d0,this.afterOneMove);
      }
      
      private function afterOneMove() : void
      {
         var da0:PetData = PetUI.getNowData();
         GeneDataMoveCtrl.moveData(da0,this.nowChooseGene);
         this.fleshData();
         Gaming.uiGroup.alertBox.showSuccess("替换资质成功！");
      }
      
      private function after_all() : void
      {
         var must_d0:MustDefine = GeneDataMoveCtrl.getMust(this.nowChooseGene.save);
         PlayerMustCtrl.deductMust(must_d0,this.afterAllMove);
      }
      
      private function afterAllMove() : void
      {
         var da0:PetData = PetUI.getNowData();
         GeneDataMoveCtrl.moveDataAll(da0,this.nowChooseGene);
         this.fleshData();
         Gaming.uiGroup.alertBox.showSuccess("替换资质和技能成功！");
      }
   }
}

