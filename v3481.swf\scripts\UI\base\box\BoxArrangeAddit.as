package UI.base.box
{
   import UI.base.event.ClickEvent;
   import UI.base.page.IO_PageTable;
   import flash.display.Sprite;
   import flash.events.EventDispatcher;
   import flash.geom.Rectangle;
   
   public class BoxArrangeAddit extends EventDispatcher implements IO_PageTable
   {
      public var rect:Rectangle = new Rectangle();
      
      public var totalPage:int = 1;
      
      public var nowPage:int = 0;
      
      private var xNum:int = 10;
      
      private var yNum:int = 10;
      
      protected var xGap:int = 0;
      
      protected var yGap:int = 0;
      
      protected var firstX:int = 0;
      
      protected var firstY:int = 0;
      
      private var horizontalB:Boolean = true;
      
      private var arr:Array = null;
      
      public function BoxArrangeAddit()
      {
         super();
      }
      
      public function init(_xNum:int, _yNum:int, _xGap:int, _yGap:int, horizontalB0:Boolean = true, x0:int = 0, y0:int = 0) : *
      {
         this.xNum = _xNum;
         this.yNum = _yNum;
         this.xGap = _xGap;
         this.yGap = _yGap;
         this.horizontalB = horizontalB0;
         this.setFirst(x0,y0);
      }
      
      public function setFirst(x0:int, y0:int) : void
      {
         this.firstX = x0;
         this.firstY = y0;
      }
      
      public function inData_byArr(arr0:Array) : *
      {
         this.clear();
         this.arr = arr0;
         this.totalPage = int((this.arr.length - 1) / (this.xNum * this.yNum)) + 1;
         if(this.totalPage < 1)
         {
            this.totalPage = 1;
         }
         this.showPage(this.nowPage);
      }
      
      public function clear() : *
      {
         if(Boolean(this.arr))
         {
            this.arr = null;
         }
      }
      
      public function showPage(_num:int) : void
      {
         if(_num <= 0)
         {
            _num = 0;
         }
         else if(_num >= this.totalPage - 1)
         {
            _num = this.totalPage - 1;
         }
         this.nowPage = _num;
         this.arrange();
         dispatchEvent(new ClickEvent(ClickEvent.ON_SHOW_PAGE));
      }
      
      public function getTotalPage() : int
      {
         return this.totalPage;
      }
      
      public function getOneNum() : int
      {
         return this.xNum * this.yNum;
      }
      
      private function arrange() : *
      {
         var allExtraY0:Number = NaN;
         var n:* = undefined;
         var lb0:ArrangeSprite = null;
         var n0:int = 0;
         var firstIndex:int = this.xNum * this.yNum * this.nowPage;
         var lastIndex:int = firstIndex + this.xNum * this.yNum;
         this.rect.x = 99999999;
         this.rect.y = 99999999;
         this.rect.height = 0;
         this.rect.width = 0;
         var allExtraX0:Number = 0;
         allExtraY0 = 0;
         for(n in this.arr)
         {
            lb0 = this.arr[n];
            if(n >= firstIndex && n < lastIndex)
            {
               lb0.visible = true;
               n0 = n - firstIndex;
               if(this.horizontalB)
               {
                  lb0.x = (this.xGap + lb0.width) * (n0 % this.xNum) + this.firstX;
                  lb0.y = (this.yGap + lb0.height) * int(n0 / this.xNum) + this.firstY;
               }
               else
               {
                  lb0.x = (this.xGap + lb0.width) * int(n0 / this.yNum) + this.firstX;
                  lb0.y = (this.yGap + lb0.height) * (n0 % this.yNum) + this.firstY;
               }
               lb0.x += allExtraX0;
               lb0.y += allExtraY0;
               allExtraX0 += lb0.extraX;
               allExtraY0 += lb0.extraY;
               this.spInRect(lb0);
            }
            else
            {
               lb0.visible = false;
            }
         }
      }
      
      private function spInRect(sp0:Sprite) : void
      {
         if(this.rect.x > sp0.x)
         {
            this.rect.x = sp0.x;
         }
         if(this.rect.y > sp0.y)
         {
            this.rect.y = sp0.y;
         }
         if(this.rect.x + this.rect.width < sp0.x + sp0.width)
         {
            this.rect.width = sp0.x + sp0.width - this.rect.width;
         }
         if(this.rect.y + this.rect.height < sp0.y + sp0.height)
         {
            this.rect.height = sp0.y + sp0.height - this.rect.height;
         }
      }
   }
}

