package UI.gameWorld.task
{
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import com.greensock.TweenLite;
   import dataAll._app.task.TaskData;
   import dataAll._app.union.building.federal.UnionSendTaskDataGroup;
   import fl.motion.easing.Back;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class GameWorldTaskBox extends Sprite
   {
      private var img:Sprite;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var titleTxt:TextField;
      
      private var showMc:MovieClip;
      
      private var backSp:Sprite;
      
      private var textArr:Array = [];
      
      private var showB:Boolean = false;
      
      private var playerClickB:Boolean = false;
      
      private var _t:Number = 0;
      
      private var time:Number = 0;
      
      public function GameWorldTaskBox()
      {
         super();
         this.mouseEnabled = false;
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
      }
      
      public function setImg(img0:Sprite) : void
      {
         this.img = img0;
         addChild(img0);
         this.x = img0.x;
         this.y = img0.y;
         img0.x = 0;
         img0.y = 0;
         this.btn.setImg(img0["btnSp"]);
         this.showMc = img0["showMc"];
         this.showMc.stop();
         this.titleTxt = img0["titleTxt"];
         this.backSp = img0["backSp"];
         addChild(this.btn);
         FontDeal.dealOne(this.titleTxt);
         this.img.mouseChildren = false;
         this.img.mouseEnabled = false;
         this.hideBreak();
      }
      
      public function setTitle(str0:String) : void
      {
         this.titleTxt.text = str0;
      }
      
      public function fleshDataByTaskData() : void
      {
         var n:* = undefined;
         var da0:TaskData = null;
         var tip0:GameWorldTaskText = null;
         this.clearAll();
         var arr0:Array = Gaming.PG.da.task.getTaskBoxTaskDataArr();
         var before_y0:int = 22;
         for(n in arr0)
         {
            da0 = arr0[n];
            tip0 = new GameWorldTaskText();
            tip0.imgInit();
            tip0.inData(da0);
            this.img.addChild(tip0);
            this.img.addChild(this.showMc);
            tip0.y = before_y0 + 10;
            before_y0 = tip0.y + tip0.height;
            this.textArr.push(tip0);
         }
         this.backSp.height = before_y0 + 10 - this.backSp.y;
         if(this.backSp.height > 300)
         {
            this.backSp.height = 300;
         }
         this.visible = this.textArr.length != 0;
      }
      
      public function fleshDataByUnion() : void
      {
         var tip0:GameWorldTaskText = null;
         this.clearAll();
         var da0:UnionSendTaskDataGroup = Gaming.PG.da.union.building.sendTask;
         var before_y0:int = 22;
         if(da0.getAllState() != "no")
         {
            tip0 = new GameWorldTaskText();
            tip0.imgInit();
            tip0.inUnionSendTaskData(da0);
            this.img.addChild(tip0);
            this.img.addChild(this.showMc);
            tip0.y = before_y0 + 10;
            before_y0 = tip0.y + tip0.height;
            this.textArr.push(tip0);
            this.visible = true;
         }
         else
         {
            this.visible = false;
         }
         this.backSp.height = before_y0 + 10 - this.backSp.y;
         if(this.backSp.height > 120)
         {
            this.backSp.height = 120;
         }
      }
      
      public function clearAll() : void
      {
         var n:* = undefined;
         var tip0:GameWorldTaskText = null;
         for(n in this.textArr)
         {
            tip0 = this.textArr[n];
            this.img.removeChild(tip0);
         }
         this.textArr.length = 0;
      }
      
      public function showOrHideByHave() : void
      {
         if(this.textArr.length > 0)
         {
            this.show(99999);
         }
         else
         {
            this.hide();
         }
      }
      
      private function playLight() : void
      {
         this.showMc.gotoAndPlay(2);
      }
      
      public function show(time0:Number = 10) : void
      {
         this.time = time0;
         this._t = 0;
         this.img.visible = true;
         TweenLite.to(this.img,0.4,{
            "x":0,
            "ease":Back.easeOut
         });
         this.showB = true;
         this.playLight();
      }
      
      public function hide() : void
      {
         TweenLite.to(this.img,0.4,{
            "x":this.img.width,
            "ease":Back.easeIn
         });
         this.showB = false;
      }
      
      private function showBreak() : void
      {
         this.showB = true;
         this.img.visible = true;
         this.img.x = 0;
         this.btn.scaleX = 1;
      }
      
      private function hideBreak() : void
      {
         this.showB = false;
         this.img.visible = false;
         this.img.x = this.img.width;
         this.btn.scaleX = -1;
      }
      
      private function btnClick(e:MouseEvent = null) : void
      {
         if(this.showB)
         {
            this.hideBreak();
            this.playerClickB = false;
         }
         else
         {
            this.showBreak();
            this.playerClickB = true;
         }
      }
      
      public function fleshEach() : void
      {
         var n:* = undefined;
         var t0:GameWorldTaskText = null;
         var stateChangeB0:Boolean = false;
         if(!this.playerClickB && this.time > 0 && this.showB)
         {
            if(this._t >= this.time)
            {
               this.hide();
            }
            else
            {
               this._t += 0.03333333333333333;
            }
         }
         if(this.showB)
         {
            for(n in this.textArr)
            {
               t0 = this.textArr[n];
               stateChangeB0 = t0.fleshEach();
               if(stateChangeB0)
               {
                  this.playLight();
               }
            }
         }
      }
      
      public function fleshUnionEach() : void
      {
         var btnIsLightB0:Boolean = false;
         var visibleB0:Boolean = false;
         var n:* = undefined;
         var t0:GameWorldTaskText = null;
         var stateChangeB0:Boolean = false;
         if(!this.playerClickB && this.time > 0 && this.showB)
         {
            if(this._t >= this.time)
            {
               this.hide();
            }
            else
            {
               this._t += 0.03333333333333333;
            }
         }
         if(this.showB)
         {
            btnIsLightB0 = false;
            visibleB0 = false;
            for(n in this.textArr)
            {
               t0 = this.textArr[n];
               stateChangeB0 = t0.fleshEach();
               if(stateChangeB0)
               {
                  this.playLight();
               }
               if(t0.btnIsLightB)
               {
                  btnIsLightB0 = true;
               }
               if(t0.visibleB)
               {
                  visibleB0 = true;
               }
            }
            if(this.btn.isChosen != btnIsLightB0)
            {
               this.btn.isChosen = btnIsLightB0;
            }
            if(visible != visibleB0)
            {
               visible = visibleB0;
            }
         }
      }
   }
}

