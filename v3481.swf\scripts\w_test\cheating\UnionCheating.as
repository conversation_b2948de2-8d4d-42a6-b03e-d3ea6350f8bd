package w_test.cheating
{
   import UI.test.SaveTestBox;
   import UI.union.UnionUI;
   import dataAll._app.union.UnionData;
   import dataAll._app.union.define.MilitaryDefine;
   import dataAll._app.union.task.UnionTaskData;
   
   public class UnionCheating extends OneCheating
   {
      public function UnionCheating()
      {
         super();
      }
      
      private function get unionData() : UnionData
      {
         return Gaming.PG.da.union;
      }
      
      public function showUnionInfo(str0:String, v0:int) : String
      {
         str0 = Gaming.PG.da.union.building.saveGroup.getTestStr();
         SaveTestBox.addText(str0);
         return str0;
      }
      
      public function clearUnionData(str0:String, v0:int) : String
      {
         Gaming.PG.da.union.clearInfo();
         Gaming.testCtrl.cheating.haveUnionDataB = false;
         return "清除军队数据";
      }
      
      public function setUnionRankLevel(str0:String, v0:int) : String
      {
         var d0:MilitaryDefine = Gaming.defineGroup.union.military.getDefine(v0 + "");
         if(Boolean(d0))
         {
            Gaming.testCtrl.cheating.tempContribution = d0.totalMust;
            return "设置军衔等级：" + v0 + "，" + d0.cnName + "，" + d0.totalMust;
         }
         return "";
      }
      
      public function setCreateUnionMoney(str0:String, v0:int) : String
      {
         Gaming.uiGroup.unionUI.topBoard.addUnionMustMoney = v0;
         return "设置创建军队的所需黄金：" + v0;
      }
      
      public function setContribution(str0:String, v0:int) : String
      {
         Gaming.testCtrl.cheating.tempContribution = v0;
         return "设置个人贡献：" + v0;
      }
      
      public function setNowMapMax(str0:String, v0:int) : String
      {
         UnionUI.battleLimitB = false;
         return "取消争霸贡献限制";
      }
      
      public function doVatiables(str0:String, v0:int) : String
      {
         Gaming.api.union.variables.doVariable(Gaming.getSaveIndex(),v0);
         return "执行军队变量" + v0;
      }
      
      public function getVatiables(str0:String, v0:int) : String
      {
         Gaming.api.union.variables.getVariables(Gaming.getSaveIndex(),[v0],this.yes_getVatiables);
         return "执行军队变量" + v0;
      }
      
      private function yes_getVatiables(jsonStr0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         SaveTestBox.addText(jsonStr0);
         Gaming.uiGroup.alertBox.showInfo(jsonStr0);
      }
      
      public function completeAllUnionTask(str0:String, v0:int) : String
      {
         UnionTaskData.TEST_COMPLETE_B = true;
         return "完成所有军队任务";
      }
      
      public function showSuppliesNum(str0:String, v0:int) : String
      {
         str0 = Gaming.PG.da.union.save.building.getNumStr();
         SaveTestBox.addText(str0);
         return str0;
      }
      
      public function doUnionTask(str0:String, v0:int) : String
      {
         if(v0 > 0)
         {
            Gaming.api.union.grow.doTask(Gaming.getSaveIndex(),v0 + "");
            return "完成军队任务id：" + v0;
         }
         return "";
      }
      
      public function fastUpgradeUnionLv(str0:String, v0:int) : String
      {
         if(v0 > 0)
         {
            Gaming.api.union.grow.doTask(Gaming.getSaveIndex(),v0 + "");
            return "快速升级军队";
         }
         return "";
      }
      
      public function setUnionTitle(str0:String, v0:int) : String
      {
         if(Boolean(this.unionData.nowUnion))
         {
            this.unionData.nowUnion.title = str0;
            return "设置名称：" + str0;
         }
         return "未找到军队数据";
      }
      
      public function showBattleBtn(str0:String, v0:int) : String
      {
         var ui0:UnionUI = Gaming.uiGroup.unionUI;
         if(ui0.visible)
         {
            if(ui0.battleBoard.visible)
            {
               return "设置成功";
            }
         }
         return "";
      }
      
      public function openUnionRole(str0:String, v0:int) : String
      {
         UnionData.ROLE_B = !UnionData.ROLE_B;
         return "设置职位权限：" + UnionData.ROLE_B;
      }
      
      public function showLog(str0:String, v0:int) : String
      {
         var arr0:Array = str0.split(",");
         var page0:int = int(arr0[0]);
         var size0:int = int(arr0[1]);
         Gaming.api.union.member.getUnionLog(Gaming.getSaveIndex(),page0,size0,this.yes_getUnionLog);
         return "";
      }
      
      private function yes_getUnionLog(jsonStr0:String) : void
      {
         Gaming.uiGroup.alertBox.showSuccess("军队记录显示在M框中");
         SaveTestBox.addText(jsonStr0);
      }
   }
}

