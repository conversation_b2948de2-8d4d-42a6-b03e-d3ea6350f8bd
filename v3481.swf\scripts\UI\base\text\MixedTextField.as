package UI.base.text
{
   import UI.base.grid.NormalGridIcon;
   import com.sounto.utils.StringMethod;
   import com.sounto.utils.TextMethod;
   import dataAll.ui.text.MixedText;
   import dataAll.ui.text.MixedTextIconData;
   import dataAll.ui.text.MixedTextLabel;
   import flash.display.Sprite;
   import flash.geom.Rectangle;
   import flash.text.TextField;
   
   public class MixedTextField extends Sprite
   {
      private var nowSring:String = "";
      
      private var text:TextField = new TextField();
      
      private var iconArr:Vector.<NormalGridIcon> = new Vector.<NormalGridIcon>();
      
      private var textData:MixedText = null;
      
      private var maxWidth:int = 9999;
      
      private var backColor:String = "";
      
      public function MixedTextField(setNormalFormatB0:Boolean = true, backColor0:String = "")
      {
         super();
         addChild(this.text);
         if(setNormalFormatB0)
         {
            TextMethod.setNormalFormat(this.text);
         }
         this.backColor = backColor0;
      }
      
      public function setMaxWidth(w0:int) : void
      {
         this.maxWidth = w0;
      }
      
      public function inTextFieldData(txt0:TextField) : void
      {
         x = txt0.x;
         y = txt0.y;
         this.maxWidth = txt0.width;
         this.text.textColor = txt0.textColor;
         this.text.defaultTextFormat = txt0.defaultTextFormat;
         TextMethod.setAutoFormat(this.text);
      }
      
      public function clearText() : void
      {
         var icon0:NormalGridIcon = null;
         this.nowSring = "";
         this.text.htmlText = "";
         this.text.width = 10;
         this.text.height = 10;
         this.text.x = 0;
         this.text.y = 0;
         for each(icon0 in this.iconArr)
         {
            icon0.clearData();
            this.removeChild(icon0);
         }
         this.iconArr.length = 0;
      }
      
      public function getTextField() : TextField
      {
         return this.text;
      }
      
      public function setMixedText(str0:String, maxWidth0:int = 9999, getWidthFun0:Function = null) : void
      {
         var iconData0:MixedTextIconData = null;
         var newHtmlText0:String = null;
         var i:int = 0;
         var rect0:Rectangle = null;
         var f0:int = 0;
         var icon0:NormalGridIcon = null;
         if(maxWidth0 == 9999)
         {
            maxWidth0 = this.maxWidth;
         }
         this.clearText();
         this.nowSring = str0;
         if(str0 == "")
         {
            return;
         }
         this.textData = new MixedText(str0,this.backColor);
         this.text.wordWrap = false;
         this.text.htmlText = this.textData.htmlText;
         if(this.text.width > maxWidth0)
         {
            this.text.wordWrap = true;
            this.text.width = maxWidth0;
         }
         if(Boolean(getWidthFun0))
         {
            this.text.wordWrap = true;
            this.text.width = getWidthFun0(this.text);
         }
         this.text.x = 0;
         this.text.y = 0;
         var text0:String = this.text.text;
         var textRect0:Rectangle = this.text.getRect(this.text);
         var c0:String = MixedTextLabel.ICON_CHAR;
         var iconTextIndex0:int = -1;
         var iconNextTextIndex0:int = 0;
         var iconIndex0:int = -1;
         for each(iconData0 in this.textData.iconDataArr)
         {
            for(i = 0; i < 100; i++)
            {
               f0 = int(text0.indexOf(c0,iconNextTextIndex0));
               if(f0 < 0)
               {
                  break;
               }
               iconNextTextIndex0 = f0 + c0.length;
               iconIndex0++;
               if(iconIndex0 == iconData0.index)
               {
                  iconTextIndex0 = f0;
                  break;
               }
            }
            rect0 = this.text.getCharBoundaries(iconTextIndex0);
            if(Boolean(rect0))
            {
               rect0.x -= this.text.x + textRect0.x;
               rect0.y -= this.text.y + textRect0.y;
               icon0 = new NormalGridIcon();
               icon0.setIconName("IconGather/" + iconData0.label);
               icon0.x = rect0.x + rect0.width * iconData0.replaceStr.length / 2;
               icon0.y = rect0.y + rect0.height / 2;
               this.addChild(icon0);
               this.iconArr.push(icon0);
            }
         }
         newHtmlText0 = this.textData.htmlText;
         if(this.textData.iconDataArr.length > 0 && this.backColor == "")
         {
            newHtmlText0 = StringMethod.replaceStr(newHtmlText0,c0," ");
            this.text.htmlText = newHtmlText0;
            this.text.x = 0;
            this.text.y = 0;
         }
         INIT.TRACE("MixedTextField位置：" + this.text.x + "," + this.text.y + textRect0,this);
      }
   }
}

