package UI.base.alert
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.grid.NormalGrid;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.worldMap.WorldMapData;
   import dataAll._app.worldMap.WorldMapDataGroup;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll._app.worldMap.save.WorldMapSave;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.level.LevelDiffGetting;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import gameAll.level.endless.EndlessModelData;
   
   public class DiffAlertBoard extends NormalUI
   {
      public var box:AlertBox;
      
      private var backBtn:NormalBtn = new NormalBtn();
      
      private var closeBtn:SimpleButton;
      
      private var gripTag:Sprite;
      
      private var titleTxt:TextField;
      
      private var backBtnSp:MovieClip;
      
      private var gripBox:ItemsGripBox = new ItemsGripBox();
      
      private var modelGripBox:ItemsGripBox = new ItemsGripBox();
      
      private var _yesFun:Function;
      
      private var nowData:WorldMapData;
      
      private var nowModel:String = "regular";
      
      private var nowEventLabel:String = "";
      
      public function DiffAlertBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var grip0:* = undefined;
         var modelName0:String = null;
         elementNameArr = ["closeBtn","gripTag","titleTxt","backBtnSp"];
         super.setImg(img0);
         FontDeal.dealOne(this.titleTxt);
         addChild(this.backBtn);
         this.backBtn.setImg(this.backBtnSp);
         this.backBtn.setName("返回");
         this.backBtn.visible = false;
         this.backBtn.addEventListener(MouseEvent.CLICK,this.backClick);
         this.gripBox.imgType = "diffBar";
         this.gripBox.arg.init(2,5,3,2);
         addChild(this.gripBox);
         NormalUICtrl.setTag(this.gripBox,this.gripTag);
         this.gripBox.evt.setWantEvent(true,false,false,true,true,false);
         this.gripBox.setCtrlBtnImgType("BasicUI/sweepingBtn");
         this.gripBox.addEventListener(ClickEvent.ON_CTRL_CLICK,this.ctrlBtnClick);
         this.gripBox.addEventListener(ClickEvent.ON_CTRL_OVER,this.ctrlBtnOver);
         this.gripBox.addEventListener(ClickEvent.ON_CTRL_OUT,this.ctrlBtnOut);
         this.gripBox.setNowGripNum(10);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.click);
         this.gripBox.addEventListener(ClickEvent.ON_OVER,this.barOver);
         this.gripBox.addEventListener(ClickEvent.ON_OUT,this.barOut);
         this.modelGripBox.imgType = "mapModelBar";
         this.modelGripBox.arg.init(2,2,3,3);
         addChild(this.modelGripBox);
         NormalUICtrl.setTag(this.modelGripBox,this.gripTag);
         this.modelGripBox.evt.setWantEvent(true,false,false,true,true,false);
         this.modelGripBox.setNowGripNum(4);
         this.modelGripBox.addEventListener(ClickEvent.ON_CLICK,this.modelClick);
         this.modelGripBox.addEventListener(ClickEvent.ON_OVER,this.modelOver);
         this.modelGripBox.addEventListener(ClickEvent.ON_OUT,this.barOut);
         for each(grip0 in this.modelGripBox.gripArr)
         {
            modelName0 = MapMode.MODE_ARR[grip0.index];
            grip0.label = modelName0;
            grip0.setName(MapMode.getFullCnName(modelName0));
            grip0.setSmallIcon(modelName0);
            grip0.activedAndEnabled = false;
         }
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function showDiff(da0:WorldMapData, _yesFun0:Function) : void
      {
         this.box.showCheck("","");
         show();
         this.nowData = da0;
         this._yesFun = _yesFun0;
         this.showModelChoose(da0);
      }
      
      private function showModelChoose(da0:WorldMapData) : void
      {
         var grip0:NormalGrid = null;
         this.gripBox.visible = false;
         this.modelGripBox.visible = true;
         this.backBtn.visible = false;
         this.titleTxt.text = da0.def.cnName;
         for each(grip0 in this.modelGripBox.gripArr)
         {
            this.nowData.dealModeGrip(grip0);
         }
      }
      
      private function showDiffChoose(da0:WorldMapData, mapMode0:String) : void
      {
         var grip0:ItemsGrid = null;
         var openB0:Boolean = false;
         var unlockB0:Boolean = false;
         var challengeNum0:int = 0;
         var save0:WorldMapSave = da0.getSaveNull();
         if(!save0)
         {
            this.gripBox.visible = false;
            return;
         }
         var baseLv0:int = da0.getEnemyLvByModel(mapMode0);
         this.gripBox.visible = true;
         this.modelGripBox.visible = false;
         this.titleTxt.text = "选择难度（" + MapMode.getFullCnName(mapMode0) + "）";
         this.nowModel = mapMode0;
         var diffUnlock0:int = save0.diffUnlock;
         var diff0:int = 0;
         for each(grip0 in this.gripBox.gripArr)
         {
            grip0.activedAndEnabled = false;
            grip0.itemsData = diff0;
            grip0.setName(LevelDiffGetting.getCnName(diff0,mapMode0));
            grip0.setAllVisible(diff0 <= LevelDiffGetting.getDiffLenByMode(mapMode0,da0.def) - 1);
            openB0 = LevelDiffGetting.getDiffOpen(diff0,mapMode0,baseLv0,da0.getName());
            unlockB0 = diff0 <= diffUnlock0;
            if(mapMode0 == MapMode.REGULAR)
            {
               grip0.setNumText("");
               grip0.setAllActived(openB0 && unlockB0);
            }
            else if(mapMode0 == MapMode.CHALLENGE)
            {
               challengeNum0 = save0.getChallengeNum(diff0);
               grip0.setNumText(challengeNum0 + "");
               grip0.setAllActived(openB0 && challengeNum0 > 0);
            }
            if(Boolean(grip0.extraBtn))
            {
               grip0.extraBtn.visible = openB0 && unlockB0 && LevelDiffGetting.getSweeping(diff0,mapMode0,baseLv0,diffUnlock0,da0.getName());
            }
            diff0++;
         }
      }
      
      private function click(e:ClickEvent) : void
      {
         this.box.hide();
         var diff0:int = e.childData as int;
         this._yesFun(this.nowData.id,diff0,this.nowModel,false);
         this._yesFun = null;
         this.nowData = null;
         this.nowModel = "";
      }
      
      private function gotoLevel(diff0:int, sweepingB0:Boolean) : void
      {
         this._yesFun(this.nowData.id,diff0,this.nowModel,sweepingB0);
      }
      
      private function barOver(e:ClickEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
         var grip0:ItemsGrid = e.child as ItemsGrid;
         var diff0:int = e.childData as int;
         var str0:String = this.nowData.getDiffTip(this.nowModel,diff0,grip0.actived);
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
      }
      
      private function barOut(e:ClickEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function ctrlBtnClick(e:ClickEvent) : void
      {
         var diff0:int = e.childData as int;
         Gaming.uiGroup.sweepingBox.showBox(this.gotoLevel,this.nowData,diff0,this.nowModel);
         this.box.hide();
      }
      
      private function ctrlBtnOver(e:ClickEvent) : void
      {
         var sweepingNum0:int = Gaming.PG.da.getSurplusSweepingNum();
         var str0:String = "今天剩余扫荡次数：" + ComMethod.colorEnoughNum(sweepingNum0) + "次";
         Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
      }
      
      private function ctrlBtnOut(e:ClickEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function demonDiffChoose(da0:WorldMapData) : void
      {
         var diff0:int = 0;
         var grip0:ItemsGrid = null;
         var mapModel0:String = MapMode.DEMON;
         var lv0:int = da0.getEnemyLvByModel(mapModel0);
         var save0:WorldMapSave = da0.getSaveNull();
         if(Boolean(save0))
         {
            this.gripBox.visible = true;
            this.modelGripBox.visible = false;
            this.titleTxt.htmlText = save0.getDemDiffTitle();
            this.nowModel = mapModel0;
            diff0 = 0;
            for each(grip0 in this.gripBox.gripArr)
            {
               grip0.activedAndEnabled = false;
               grip0.itemsData = diff0;
               grip0.setName(LevelDiffGetting.getCnName(diff0,mapModel0));
               grip0.setAllActived(LevelDiffGetting.getDemDiffBtnActived(da0.def,diff0));
               grip0.setNumText("");
               grip0.extraBtn.visible = false;
               diff0++;
            }
         }
         else
         {
            this.gripBox.visible = true;
         }
      }
      
      private function getDemonBtnTip(diff0:int) : String
      {
         return "";
      }
      
      private function modelClick(e:ClickEvent) : void
      {
         this.gotoMode(e.label);
      }
      
      public function gotoMode(mode0:String, backVisible0:Boolean = true) : void
      {
         var wg0:WorldMapDataGroup = null;
         var max0:int = 0;
         var noBtn0:NormalBtn = null;
         var now0:int = 0;
         this.backBtn.visible = backVisible0;
         if(mode0 == MapMode.ENDLESS)
         {
            this.nowEventLabel = mode0;
            wg0 = this.nowData.worldMapDataGroup;
            max0 = this.nowData.getSaveNull().maxEndlessGrade + 1;
            if(max0 < 1)
            {
               max0 = 1;
            }
            noBtn0 = Gaming.uiGroup.alertBox.noBtn;
            Gaming.uiGroup.alertBox.showNumChoose(ComMethod.orange(this.nowData.def.cnName) + " 选择无尽层级（最高第" + max0 + "层）",1,max0,1,1,this.gotoEndLess,"yesAndNo",null,this.directGiftEndless);
            max0 = wg0.getEndlessMax();
            now0 = wg0.saveGroup.todayEndlessNum;
            if(now0 > max0)
            {
               now0 = max0;
            }
            noBtn0.setName("直接领奖 " + ComMethod.dropColor(now0,max0));
            noBtn0.actived = now0 < max0 && wg0.endlessDirectGiftB();
            noBtn0.tipString = wg0.getEndlessDirectTip(true);
         }
         else if(mode0 == MapMode.DEMON)
         {
            this.demonDiffChoose(this.nowData);
         }
         else
         {
            this.showDiffChoose(this.nowData,mode0);
         }
      }
      
      private function gotoEndLess(grade0:int) : void
      {
         this.box.hide();
         this._yesFun(this.nowData.id,grade0,this.nowEventLabel);
         this._yesFun = null;
         this.nowData = null;
         this.nowModel = "";
      }
      
      private function directGiftEndless() : void
      {
         var gift0:GiftAddDefineGroup = null;
         var num0:int = this.nowData.worldMapDataGroup.getEndlessCanNum();
         if(num0 > 0)
         {
            gift0 = EndlessModelData.getAllGift();
            gift0.addNumMul(num0);
            this.nowData.worldMapDataGroup.useEndlessNum(num0);
            GiftAddit.addAndTip(gift0);
            Gaming.uiGroup.giftUI.fleshActiveIfVisible();
         }
      }
      
      private function modelOver(e:ClickEvent) : void
      {
         var btn0:NormalBtn = e.child as NormalBtn;
         var str0:String = this.nowData.getModeTip(e.label,btn0.actived);
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
      }
      
      private function backClick(e:MouseEvent) : void
      {
         this.showModelChoose(this.nowData);
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.box.hide();
         this._yesFun = null;
         this.nowData = null;
         this.nowModel = "";
      }
   }
}

