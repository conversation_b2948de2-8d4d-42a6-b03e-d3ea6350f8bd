package UI.skill
{
   import UI.base.button.NormalBtn;
   import UI.base.must.ExpendUIData;
   import UI.base.must.NormalMustBox;
   import dataAll.must.define.MustDefine;
   import dataAll.skill.HeroSkillData;
   import flash.text.TextField;
   
   public class ProfiExpendUIData extends ExpendUIData
   {
      private var fleshFun:Function = null;
      
      private var skillDa:HeroSkillData = null;
      
      public function ProfiExpendUIData()
      {
         super();
         successReSetDataB = false;
      }
      
      public static function get ONE_ADD() : int
      {
         return 3000;
      }
      
      public function setData(da0:HeroSkillData, fleshFun0:Function) : void
      {
         this.skillDa = da0;
         this.fleshFun = fleshFun0;
      }
      
      override public function showDataEvent(okBtn0:NormalBtn, infoTxt0:TextField, mustBox0:NormalMustBox) : void
      {
         var must0:MustDefine = null;
         var mustB0:Boolean = false;
         okBtn0.setName("增加熟练度");
         var now0:Number = this.skillDa.save.profi;
         var max0:Number = HeroSkillData.maxProfi;
         if(now0 >= max0)
         {
            infoTxt0.htmlText = "当前熟练度已满";
            mustBox0.setShowState(false);
            okBtn0.actived = false;
         }
         else
         {
            infoTxt0.htmlText = "增加" + ONE_ADD + "点熟练度";
            must0 = this.getMustDefine();
            mustB0 = mustBox0.inData(must0);
            okBtn0.actived = mustB0;
         }
      }
      
      override public function getMustDefine() : MustDefine
      {
         var d0:MustDefine = new MustDefine();
         d0.inThingsDataByArr(["profiStamp;1"]);
         return d0;
      }
      
      override public function successEvent() : void
      {
         this.skillDa.addProfiNoLimit(ONE_ADD);
         Gaming.uiGroup.alertBox.showSuccess("获得" + ONE_ADD + "点熟练度！");
         if(this.fleshFun is Function)
         {
            this.fleshFun();
         }
      }
   }
}

