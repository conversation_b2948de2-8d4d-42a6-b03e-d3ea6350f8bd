package UI.arena.info
{
   import UI.UIOrder;
   import UI.arena.ArenaTopCtrl;
   import UI.base.NormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import com.sounto.utils.TextMethod;
   import dataAll._app.arena.ArenaData;
   import dataAll._app.top.define.TopBarDefineGroup;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class ArenaInfoNoGiftSp extends NormalUI
   {
      private var closeBtn:SimpleButton;
      
      private var labelTag:Sprite;
      
      private var labelBox:LabelBox = new LabelBox();
      
      private var nowChoose:String = "";
      
      public function ArenaInfoNoGiftSp()
      {
         super();
      }
      
      public static function get arenaData() : ArenaData
      {
         return Gaming.PG.da.arena;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["closeBtn","labelTag"];
         super.setImg(img0);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         this.labelBox.arg.init(1,3,4,4);
         addChild(this.labelBox);
         this.labelBox.x = this.labelTag.x;
         this.labelBox.y = this.labelTag.y;
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.flesh();
      }
      
      private function flesh() : void
      {
         var d0:TopBarDefineGroup = null;
         var darr0:Array = arenaData.getCanChooseTopArr();
         var now0:TopBarDefineGroup = arenaData.getTopBarDefineGroup();
         var nameArr0:Array = [];
         var cnArr0:Array = [];
         for each(d0 in darr0)
         {
            nameArr0.push(d0.name);
            cnArr0.push(d0.cnName);
         }
         this.labelBox.inData("ArenaUI/topChooseBtn",nameArr0,cnArr0);
         this.labelBox.setChoose(now0.name);
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         var d0:TopBarDefineGroup = null;
         var tip0:String = null;
         var now0:TopBarDefineGroup = arenaData.getTopBarDefineGroup();
         if(now0.name != e.label)
         {
            this.nowChoose = e.label;
            d0 = Gaming.defineGroup.top.getDefine(this.nowChoose);
            tip0 = "确定要切换到" + TextMethod.color(d0.cnName,"#FFFF00") + "？";
            tip0 += "\n注意：1天只能切换1次等级榜。";
            Gaming.uiGroup.alertBox.showChoose(tip0,this.yesChoose);
         }
      }
      
      private function yesChoose() : void
      {
         Gaming.uiGroup.connectUI.show("获取数据时间……");
         Gaming.api.save.getServerTime(this.yesTime,this.noTime,true);
      }
      
      private function yesTime(str0:String) : void
      {
         if(!ArenaTopCtrl.noAddScoreB())
         {
            Gaming.uiGroup.connectUI.hide();
            arenaData.chooseTop(this.nowChoose);
            UIOrder.save(true,true,false,this.yesSave,this.noSave);
         }
         else
         {
            this.noTime("");
         }
      }
      
      private function noTime(str0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError("当前时间不能切换等级榜。");
      }
      
      private function yesSave(v0:* = null) : void
      {
         hide();
         Gaming.uiGroup.arenaUI.show();
      }
      
      private function noSave(v0:* = null) : void
      {
         arenaData.recoveryTop();
         Gaming.uiGroup.alertBox.showError("存档失败，无法切换等级榜。");
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         hide();
      }
   }
}

