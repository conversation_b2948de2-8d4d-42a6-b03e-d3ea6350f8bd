package UI.union.building
{
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.must.NormalMustBox;
   import UI.base.tip.TextGatherAnalyze;
   import dataAll._app.union.building.UnionBuildingData;
   import dataAll._app.union.building.define.UnionBuildingDefine;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class UnionBuildingUplevelBox extends AutoNormalUI
   {
      private var closeBtn:NormalBtn;
      
      private var mustBox:NormalMustBox;
      
      private var btn:NormalBtn;
      
      private var txt:TextField;
      
      public function UnionBuildingUplevelBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         this.btn.setName("升级");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      private function fleshData() : void
      {
         this.showData(UnionBuildingBoard.nowData);
      }
      
      public function showData(da0:UnionBuildingData) : void
      {
         var d0:UnionBuildingDefine = null;
         var canUplevelB0:Boolean = false;
         var mustD0:MustDefine = null;
         var bb0:Boolean = false;
         if(Boolean(da0))
         {
            d0 = da0.def;
            canUplevelB0 = da0.canUplevelB();
            mustD0 = da0.getUplevelMust();
            bb0 = this.mustBox.inData(mustD0);
            this.btn.actived = bb0 && canUplevelB0;
            if(canUplevelB0)
            {
               this.txt.htmlText = TextGatherAnalyze.swapText("<orange " + d0.lvName + "/>升至<green " + (da0.save.lv + 1) + "级/>需要：");
            }
            else
            {
               this.txt.htmlText = TextGatherAnalyze.swapText("<red " + da0.getNoUplevelStr() + "/>");
            }
         }
         else
         {
            this.mustBox.setShowState(false);
            this.btn.actived = false;
            this.txt.text = "";
         }
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var da0:UnionBuildingData = null;
         var mustD0:MustDefine = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0 == this.closeBtn)
         {
            hide();
         }
         else if(btn0 == this.btn)
         {
            da0 = UnionBuildingBoard.nowData;
            mustD0 = da0.getUplevelMust();
            PlayerMustCtrl.deductMust(mustD0,this.affter_btnClick);
         }
      }
      
      private function affter_btnClick() : void
      {
         var da0:UnionBuildingData = UnionBuildingBoard.nowData;
         Gaming.PG.da.union.building.uplevelOne(da0);
         Gaming.uiGroup.alertBox.showSuccess("升级成功！");
         Gaming.uiGroup.unionUI.buildingBoard.fleshData();
         this.show();
      }
   }
}

