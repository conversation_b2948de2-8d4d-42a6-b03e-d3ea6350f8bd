package UI.api.realName
{
   import com.adobe.serialization.json.JSON2;
   import com.sounto.process.YesAndNoFun;
   import dataAll._player.realName.RealNameAgent;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   
   public class RealNameApi
   {
      private static const GET_STATUS:String = "getStatus";
      
      private static const SUBMIT:String = "submit";
      
      private var fun:YesAndNoFun = new YesAndNoFun();
      
      private var loader:URLLoader = new URLLoader();
      
      private var urlStr:String = "//save.api.4399.com/index.php";
      
      private var url:URLRequest = new URLRequest();
      
      public function RealNameApi()
      {
         super();
         this.loader.addEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this.loader.addEventListener(Event.COMPLETE,this.loaderCompleteHandler);
      }
      
      public function getStatus(uid0:String, _yesFun:Function = null, _noFun:Function = null) : *
      {
         this.fun.setFun(_yesFun,_noFun);
         var data0:URLVariables = new URLVariables();
         data0.uid = uid0;
         this.url.data = data0;
         this.url.url = this.urlStr + "?c=realname&ac=status&";
         this.url.method = URLRequestMethod.GET;
         this.loader.load(this.url);
      }
      
      public function submit(uid0:String, cn0:String, id0:String, _yesFun:Function = null, _noFun:Function = null) : *
      {
         this.fun.setFun(_yesFun,_noFun);
         if(Gaming.isLocal())
         {
            this.localTest();
            return;
         }
         var data0:URLVariables = new URLVariables();
         data0.uid = uid0;
         data0.realname = cn0;
         data0.idcard = id0;
         this.url.data = data0;
         this.url.url = this.urlStr + "?c=realname&ac=submit&";
         this.url.method = URLRequestMethod.POST;
         this.loader.load(this.url);
      }
      
      private function localTest() : void
      {
         var a0:RealNameAgent = new RealNameAgent();
         a0.success = 1;
         a0.cert = 1;
         a0.reconfirm = 1;
         a0.status = 0;
         if(a0.success == 1)
         {
            this.fun.doYesFun(a0);
         }
         else
         {
            this.fun.doNoFun(a0);
         }
      }
      
      private function loaderCompleteHandler(e:Event) : void
      {
         var a0:RealNameAgent = null;
         var str0:String = this.loader.data;
         var obj0:Object = null;
         try
         {
            obj0 = JSON2.decode(str0);
         }
         catch(e:Error)
         {
            obj0 = null;
         }
         if(Boolean(obj0))
         {
            a0 = new RealNameAgent();
            a0.inData_byObj(obj0);
            if(a0.success == 1)
            {
               this.fun.doYesFun(a0);
            }
            else
            {
               this.fun.doNoFun(a0);
            }
         }
         else
         {
            this.fun.doNoFun(str0);
         }
      }
      
      private function errorHandler(e:IOErrorEvent) : void
      {
         var a0:RealNameAgent = new RealNameAgent();
         a0.desc = "网络连接错误。";
         this.fun.doNoFun(a0);
      }
   }
}

