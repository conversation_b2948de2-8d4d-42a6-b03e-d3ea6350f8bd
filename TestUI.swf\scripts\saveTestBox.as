package
{
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol81")]
   public dynamic class saveTestBox extends MovieClip
   {
      public var inputBtn:SimpleButton;
      
      public var txt:TextField;
      
      public var setArenaGiftBtn:SimpleButton;
      
      public var saveUidTxt:TextField;
      
      public var inputBase64Btn:SimpleButton;
      
      public var inputPCGBtn:SimpleButton;
      
      public var getByUidBtn:SimpleButton;
      
      public function saveTestBox()
      {
         super();
      }
   }
}

