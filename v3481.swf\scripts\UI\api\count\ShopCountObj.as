package UI.api.count
{
   import com.sounto.utils.ClassProperty;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll.must.define.MustDefine;
   
   public class ShopCountObj
   {
      public static var pro_arr:Array = [];
      
      public var f:int = 1;
      
      public var g:int = 0;
      
      public var u:String = "";
      
      public var gm:int = 0;
      
      public var rm:int = 0;
      
      public var pi:String = "";
      
      public var pn:String = "";
      
      public var pp:Number = 0;
      
      public var pc:int = 0;
      
      public var pt:int = 1;
      
      public var m:String = "";
      
      public function ShopCountObj(da0:GoodsData = null)
      {
         super();
         if(<PERSON><PERSON><PERSON>(da0))
         {
            this.inDataByGoodsData(da0);
         }
      }
      
      public function inDataByGoodsData(da0:GoodsData) : void
      {
         var d0:GoodsDefine = da0.def;
         this.pi = d0.propId;
         this.pn = d0.name;
         this.pp = da0.getOnePrice();
         this.pc = da0.nowNum;
         this.pt = d0.getCountType();
         this.m = d0.cnName;
         this.gm = this.pp * this.pc;
         this.rm = Gaming.PG.da.main.money;
      }
      
      public function inDataByMustDefine(d0:MustDefine) : void
      {
         this.pi = d0.propId;
         this.pn = d0.name;
         this.pp = d0.money;
         this.pc = 1;
         this.pt = 1;
         this.m = d0.cnName;
         this.gm = this.pp * this.pc;
         this.rm = Gaming.PG.da.main.money;
      }
      
      public function inDataByGoodsDefine(d0:GoodsDefine, num0:int) : void
      {
         this.pi = d0.propId;
         this.pn = d0.name;
         this.pp = d0.price;
         this.pc = num0;
         this.pt = d0.getCountType();
         this.m = d0.cnName;
         this.gm = 0;
         this.rm = Gaming.PG.da.main.money;
      }
      
      public function getObj() : Object
      {
         return ClassProperty.copyObj(this);
      }
   }
}

