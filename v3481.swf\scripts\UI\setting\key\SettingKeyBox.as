package UI.setting.key
{
   import UI.NormalUICtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import UI.base.scroll.NormalScrollBar;
   import dataAll._app.setting.key.SettingKeySave;
   import dataAll._app.setting.key.SettingKeySaveGroup;
   import flash.display.Sprite;
   import flash.events.KeyboardEvent;
   import flash.events.MouseEvent;
   
   public class SettingKeyBox extends NormalUI
   {
      private static var checkNumlockB:Boolean = false;
      
      private var doubleSp:Sprite;
      
      private var singleSp:Sprite;
      
      private var labelTag:Sprite;
      
      private var labelArr:Array = ["single","double"];
      
      private var deBtn:NormalBtn = new NormalBtn();
      
      private var singleBoard:SettingKeySingleBoard = new SettingKeySingleBoard();
      
      private var doubelBoard:SettingKeyDoubleBoard = new SettingKeyDoubleBoard();
      
      private var labelBox:LabelBox = new LabelBox();
      
      private var con:Sprite = new Sprite();
      
      private var nowBoard:SettingKeySingleBoard;
      
      private var scrollBar:NormalScrollBar;
      
      public function SettingKeyBox()
      {
         super();
      }
      
      private static function get keySave() : SettingKeySaveGroup
      {
         return Gaming.PG.save.setting.key;
      }
      
      public static function showNumlockInfo(guideName0:String = "numlockKeyTip1") : void
      {
      }
      
      public static function staticOutLoginEvent() : void
      {
         checkNumlockB = false;
      }
      
      public static function staticKeyUp(e:KeyboardEvent) : void
      {
         if(e.keyCode == 144 && checkNumlockB)
         {
            Gaming.uiGroup.alertBox.hide();
            checkNumlockB = false;
         }
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["singleSp","doubleSp","labelTag"];
         super.setImg(img0);
         addChild(this.con);
         addChild(this.labelBox);
         NormalUICtrl.setTag(this.labelBox,this.labelTag);
         this.labelBox.inData("ShopUI/secLabelBtn",this.labelArr,["单人","双人"]);
         this.labelBox.setChoose("single");
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         addChild(this.deBtn);
         this.deBtn.setImg(img0["deBtnSp"]);
         this.deBtn.setName("恢复默认设置");
         this.deBtn.addEventListener(MouseEvent.CLICK,this.deBtnClick);
         this.con.addChild(this.singleBoard);
         this.singleBoard.setImg(this.singleSp);
         this.con.addChild(this.doubelBoard);
         this.doubelBoard.setImg(this.doubleSp);
         this.scrollBar = new NormalScrollBar(this.con,img0["maskTargetSp"],img0["scrollBarSp"],img0["scrollLineSp"],1,false,true,true);
         this.scrollBar.speed = 30;
         this.scrollBar.refresh();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         var doubleB0:Boolean = Gaming.PG.da.isDoubleB();
         this.showBox(this.labelArr[doubleB0 ? 1 : 0]);
      }
      
      public function outLoginEvent() : void
      {
         this.singleBoard.outLoginEvent();
         this.doubelBoard.outLoginEvent();
      }
      
      public function keyUp(e:KeyboardEvent) : void
      {
         if(visible)
         {
            this.singleBoard.keyUp(e);
            this.doubelBoard.keyUp(e);
         }
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      public function showBox(label0:String) : void
      {
         var s1:SettingKeySave = null;
         var s2:SettingKeySave = null;
         if(label0 == "")
         {
            label0 = "single";
         }
         this.labelBox.setChoose(label0);
         this.singleBoard.visible = false;
         this.doubelBoard.visible = false;
         var keySave0:SettingKeySaveGroup = keySave;
         if(label0 == "single")
         {
            this.nowBoard = this.singleBoard;
            s1 = keySave0.getKeySave("single");
         }
         else if(label0 == "double")
         {
            this.nowBoard = this.doubelBoard;
            s1 = keySave0.getKeySave("p1");
            s2 = keySave0.getKeySave("p2");
         }
         if(Boolean(this.nowBoard))
         {
            this.nowBoard.visible = true;
            this.nowBoard.inData(label0,s1,s2);
         }
         this.scrollBar.refresh();
         if(!Gaming.PG.save.guide.numlockKeyTip1)
         {
            showNumlockInfo();
         }
      }
      
      public function deBtnClick(e:MouseEvent) : void
      {
         Gaming.uiGroup.alertBox.showChoose("是否将按键恢复默认设置？",this.yesDe);
      }
      
      private function yesDe() : void
      {
         keySave.toDefault(this.labelBox.nowLabel);
         this.showBox(this.labelBox.nowLabel);
      }
   }
}

