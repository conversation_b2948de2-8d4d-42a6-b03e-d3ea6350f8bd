package UI.space
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.base.AutoNormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.grid.NormalGrid;
   import dataAll._app.space.SpaceData;
   import dataAll._app.space.achieve.SpaceAchieveCtrl;
   import dataAll._app.space.achieve.SpaceAchieveDefine;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.Sprite;
   
   public class SpaceAchieveBoard extends AutoNormalUI
   {
      private var gripBox:ItemsGripBox = new ItemsGripBox();
      
      private var gripTag:Sprite;
      
      private var pageTag:Sprite;
      
      public function SpaceAchieveBoard()
      {
         super();
         mcTypeArr = ["btnSp","txt","tag"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         this.gripBox.setIconPro("SpaceUI/achieveBtn");
         this.gripBox.arg.init(2,4,-1,-1);
         this.gripBox.evt.setWant(true,true);
         addChild(this.gripBox);
         NormalUICtrl.setTag(this.gripBox,this.gripTag);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.gripBox.addEventListener(ClickEvent.ON_OVER,this.gripOver);
         this.gripBox.addEventListener(ClickEvent.ON_OUT,Gaming.uiGroup.tipBox.hide);
         this.gripBox.pageBox.setToSmall();
         this.gripBox.setPagePos(this.pageTag);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function get DATA() : SpaceData
      {
         return Gaming.PG.da.space;
      }
      
      private function isGaimgB() : Boolean
      {
         return Gaming.LG.isGaming();
      }
      
      public function outLoginEvent() : void
      {
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      private function fleshData() : void
      {
         var d0:SpaceAchieveDefine = null;
         var arr0:Array = SpaceAchieveCtrl.getUIArr();
         var noArr0:Array = [];
         var giftArr0:Array = [];
         for each(d0 in arr0)
         {
            if(this.DATA.getAchieveGiftB(d0.name))
            {
               giftArr0.push(d0);
            }
            else
            {
               noArr0.push(d0);
            }
         }
         this.gripBox.inData_byArr(noArr0.concat(giftArr0),this.gripFun);
      }
      
      private function gripFun(grip0:ItemsGrid, d0:SpaceAchieveDefine) : void
      {
         var completeB0:Boolean = false;
         var giftB0:Boolean = this.DATA.getAchieveGiftB(d0.name);
         var value0:Number = this.DATA.getAchieveValue(d0);
         var must0:Number = d0.must;
         completeB0 = value0 >= must0;
         var processStr0:String = value0 + "/" + must0;
         grip0.inData_gift(d0.gift);
         grip0.secData = grip0.itemsData;
         grip0.itemsData = d0;
         grip0.setName(d0.getText(),true,true);
         grip0.setLevelText(processStr0);
         grip0.setNewMc(completeB0 ? "complete" : "ing");
         grip0.setSmallIconPer(value0 / must0);
         grip0.getShopBtnBackMc().gotoAndStop(giftB0 ? 2 : 1);
         grip0.getShopBtnBackMc().visible = completeB0;
         grip0.activedAndEnabled = false;
         grip0.actived = !giftB0;
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var grip0:NormalGrid = null;
         var d0:SpaceAchieveDefine = null;
         var giftB0:Boolean = false;
         var completeB0:Boolean = false;
         var g0:GiftAddDefineGroup = null;
         var bb0:Boolean = false;
         if(!this.isGaimgB())
         {
            grip0 = e.child as NormalGrid;
            if(grip0.actived)
            {
               d0 = e.childData as SpaceAchieveDefine;
               giftB0 = this.DATA.getAchieveGiftB(d0.name);
               completeB0 = this.DATA.isAchieveCompleteB(d0);
               if(giftB0 == false && completeB0)
               {
                  g0 = d0.getGiftG();
                  bb0 = GiftAddit.addAndAutoBagSpacePan(g0,"获得成就奖励：\n" + g0.getDescription());
                  if(bb0)
                  {
                     this.DATA.setAchieveGiftB(d0.name);
                     this.fleshData();
                  }
               }
            }
         }
      }
      
      private function gripOver(e:ClickEvent) : void
      {
         var grip0:NormalGrid = e.child as NormalGrid;
         var d0:SpaceAchieveDefine = grip0.itemsData as SpaceAchieveDefine;
         d0.gift.getDescription;
      }
   }
}

