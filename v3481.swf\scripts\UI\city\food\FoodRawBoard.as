package UI.city.food
{
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import dataAll._app.food.FoodData;
   import dataAll._app.food.FoodRawAgent;
   import flash.display.Sprite;
   
   public class FoodRawBoard extends AutoNormalUI
   {
      private var gripTag:Sprite;
      
      private var itemsBox:ItemsGripBox = new ItemsGripBox();
      
      public function FoodRawBoard()
      {
         super();
         mcTypeArr = ["tag"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = [];
         super.setImg(img0);
         addChild(this.itemsBox);
         this.itemsBox.setIconPro("FoodUI/rawGrip");
         this.itemsBox.arg.init(4,5,9,9);
         this.itemsBox.evt.setWant(false,true);
         this.itemsBox.x = this.gripTag.x;
         this.itemsBox.y = this.gripTag.y;
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.itemsBox);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      protected function get foodData() : FoodData
      {
         return Gaming.PG.da.food;
      }
      
      override public function show() : void
      {
         super.show();
         var aarr0:Array = this.foodData.getRawArr();
         this.itemsBox.inData_byArr(aarr0,this.dealGrip,false);
      }
      
      private function dealGrip(grip0:NormalBtn, a0:FoodRawAgent) : void
      {
         grip0.itemsData = a0;
         grip0.setName(a0.cnName);
         grip0.setIconName(a0.iconUrl);
         grip0.setNumText(a0.num + "");
      }
   }
}

