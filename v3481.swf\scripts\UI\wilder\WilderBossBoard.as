package UI.wilder
{
   import UI.api.shop.ShopBuyObject;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.label.LabelBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._app.wilder.WilderData;
   import dataAll._app.wilder.WilderDiff;
   import dataAll._app.wilder.define.WilderDefine;
   import dataAll.drop.BodyDropData;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.level.define.LevelDefine;
   import dataAll.level.define.unit.OneUnitOrderDefine;
   import dataAll.skill.define.SkillDescrip;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import gameAll.drop.DropCtrl;
   import gameAll.drop.sweeping.SweepData;
   import gameAll.level._diy.WilderLevelDiy;
   
   public class WilderBossBoard extends AutoNormalUI
   {
      private var gripSp:MovieClip;
      
      private var numTxt:TextField;
      
      private var allNumTxt:TextField;
      
      private var titleTxt:TextField;
      
      private var dropTxt:TextField;
      
      private var infoTxt:TextField;
      
      private var addBtn:NormalBtn;
      
      private var buyBtn:NormalBtn;
      
      private var challengeBtn:NormalBtn;
      
      private var closeBtn:NormalBtn;
      
      private var sweepBtn:NormalBtn;
      
      private var grip:WilderGrip = new WilderGrip();
      
      private var giftTag:Sprite;
      
      private var diffTag:Sprite;
      
      private var giftBox:ItemsGripBox = new ItemsGripBox();
      
      private var diffBox:LabelBox = new LabelBox();
      
      private var nowData:WilderData = null;
      
      public function WilderBossBoard()
      {
         super();
         mcTypeArr = ["btnSp","txt","tag"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         FontDeal.dealOne(this.titleTxt);
         FontDeal.dealOne(this.dropTxt);
         FontDeal.dealOne(this.infoTxt);
         FontDeal.dealOne(this.numTxt);
         FontDeal.dealLine(this.allNumTxt);
         addChild(this.grip);
         this.grip.setImg(this.gripSp);
         this.grip.mouseChildren = false;
         this.grip.mouseEnabled = false;
         this.giftTag.addChild(this.giftBox);
         this.giftBox.arg.init(6,1,4,4);
         this.giftBox.setIconPro("equipGrip");
         this.giftBox.evt.setWantEvent(true,false,false,true,true);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.giftBox);
         this.diffBox.evt.setWantEvent(true,false,false,true,true);
         this.diffBox.arg.init(WilderDiff.nameArr.length,1,0,0);
         this.diffBox.inData("WilderUI/diffBtn",WilderDiff.nameArr,WilderDiff.cnArr);
         this.diffBox.setChoose_byIndex(0);
         this.diffBox.addEventListener(ClickEvent.ON_CLICK,this.diffClick);
         this.diffBox.addEventListener(ClickEvent.ON_OVER,this.diffOver);
         this.diffBox.addEventListener(ClickEvent.ON_OUT,this.diffOut);
         this.diffBox.x = this.diffTag.x;
         this.diffBox.y = this.diffTag.y;
         addChild(this.diffBox);
         this.grip.mouseEnabled = false;
         this.addBtn.setName("兑换");
         ItemsGripTipCtrl.addNormalBtnTip(this.addBtn);
         this.addBtn.activedAndEnabled = false;
         this.buyBtn.setName("购买");
         ItemsGripTipCtrl.addNormalBtnTip(this.buyBtn);
         this.sweepBtn.setName("扫荡");
         this.sweepBtn.visible = false;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      public function showWilderData(da0:WilderData) : void
      {
         this.show();
         this.nowData = da0;
         this.fleshData();
      }
      
      public function outLoginEvent() : void
      {
      }
      
      private function fleshData() : void
      {
         var da0:WilderData = null;
         var d0:WilderDefine = null;
         var num0:int = 0;
         var allNum0:int = 0;
         var allMax0:int = 0;
         var keyNum0:int = 0;
         var canExchangeNum0:int = 0;
         if(Boolean(this.nowData))
         {
            da0 = this.nowData;
            d0 = da0.def;
            num0 = da0.getCanChallangeNum();
            allNum0 = da0.save.all;
            allMax0 = da0.def.limitAll;
            this.titleTxt.text = d0.cnName;
            this.grip.inData_wilder(da0,false);
            this.grip.setName("");
            this.numTxt.text = num0 + "";
            if(allMax0 > 0)
            {
               this.allNumTxt.htmlText = FontDeal.getDealLeadingStr(this.allNumTxt,ComMethod.color("累计挑战次数上限\n","#999999",12) + ComMethod.moreRed(allNum0,allMax0));
            }
            else
            {
               this.allNumTxt.htmlText = "";
            }
            this.infoTxt.htmlText = FontDeal.getDealLeadingStr(this.infoTxt,d0.getBossSkillTip());
            this.dropTxt.htmlText = da0.getDropStr();
            this.giftBox.inData_byArr(da0.getUIDropGiftG().arr,"inData_gift");
            this.fleshDiffBox();
            this.challengeBtn.setName(da0.getChallangeStr());
            this.challengeBtn.actived = num0 > 0;
            keyNum0 = Gaming.PG.da.wilder.saveGroup.keyNum;
            canExchangeNum0 = da0.getSurplusExchangeNum();
            this.addBtn.actived = keyNum0 > 0 && canExchangeNum0 > 0 && !da0.noExchangeB();
            this.addBtn.setName("兑换");
            this.buyBtn.actived = da0.def.noBuyB == false;
            if(da0.noExchangeB())
            {
               this.addBtn.tipString = "该副本不能使用秘境钥匙兑换。";
            }
            else
            {
               this.addBtn.tipString = "今天还可以兑换当前副本" + ComMethod.colorEnoughNum(canExchangeNum0) + "次";
               if(da0.getActiveExchangeAdd() > 0)
               {
                  this.addBtn.tipString += "<blue (活动加成)/>";
               }
               this.addBtn.tipString += "。";
               this.addBtn.tipString += "\n\n<purple 使用1把秘境钥匙兑换1次副本挑战次数。\n注意！兑换后的次数，如果今天没有使用将会消失。/>";
               this.buyBtn.tipString = "<blue 使用黄金购买副本挑战次数。/>";
               this.buyBtn.tipString += "\n\n<purple 注意！购买后的次数，如果今天没有使用将会消失。/>";
            }
            Gaming.uiGroup.wilderUI.fleshGrip(this.nowData.def.name);
         }
         else
         {
            this.titleTxt.text = "";
            this.grip.clearData();
            this.numTxt.text = "0";
            this.infoTxt.htmlText = "";
            this.dropTxt.htmlText = "";
            this.giftBox.clearAllData();
            this.challengeBtn.actived = false;
         }
      }
      
      private function fleshDiffBox() : void
      {
         var btn0:NormalBtn = null;
         var da0:WilderData = this.nowData;
         var d0:WilderDefine = da0.def;
         this.diffBox.setChoose(da0.save.diff);
         for each(btn0 in this.diffBox.gripArr)
         {
            if(da0.haveDropGift(btn0.label))
            {
               btn0.actived = true;
            }
            else
            {
               btn0.actived = false;
            }
         }
         this.diffBox.setAllPro("lockActivedB",false);
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var funName0:String = btn0.label + "BtnClick";
         if(this.hasOwnProperty(funName0))
         {
            this[funName0](e);
         }
      }
      
      public function addBtnClick(e:MouseEvent) : void
      {
         if(this.addBtn.actived)
         {
            Gaming.PG.da.wilder.exchangeNum(this.nowData);
            Gaming.soundGroup.playSound("uiSound","success");
            Gaming.uiGroup.tipBox.hide();
            this.fleshData();
            Gaming.uiGroup.wilderUI.fleshTxt();
         }
      }
      
      public function challengeBtnClick(e:MouseEvent = null) : void
      {
         WilderLevelDiy.chooseLevel(this.nowData);
      }
      
      public function closeBtnClick(e:MouseEvent) : void
      {
         this.hide();
      }
      
      public function getNowData() : WilderData
      {
         return this.nowData;
      }
      
      public function buyBtnClick(e:MouseEvent) : void
      {
         var goodDa0:GoodsData = this.getBuyGoodsData();
         Gaming.uiGroup.alertBox.shop.showCheck(goodDa0,this.yesBuy);
      }
      
      private function getBuyGoodsData() : GoodsData
      {
         var da0:GoodsData = new GoodsData();
         var d0:GoodsDefine = Gaming.defineGroup.goods.getDefine("wilderNum");
         da0.def = d0;
         da0.playerData = Gaming.PG.da;
         return da0;
      }
      
      private function yesBuy() : void
      {
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         var shopObj0:ShopBuyObject = da0.getShopObj();
         Gaming.uiGroup.connectUI.show();
         Gaming.api.shop.buyPropNd(shopObj0,this.doBuy);
      }
      
      private function doBuy() : void
      {
         Gaming.uiGroup.connectUI.hide();
         var goodDa0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         Gaming.PG.da.goods.addBuyNum(goodDa0.def.name,goodDa0.nowNum);
         this.nowData.addBuyNum(goodDa0.nowNum);
         this.fleshData();
         Gaming.uiGroup.mainUI.fleshCoin();
      }
      
      private function diffClick(e:ClickEvent) : void
      {
         this.nowData.save.diff = e.label;
         this.fleshData();
      }
      
      private function diffOver(e:ClickEvent) : void
      {
         var btn0:NormalBtn = e.child as NormalBtn;
         var diff0:String = btn0.label;
         var lv0:int = this.nowData.getEnemyLvByDiff(diff0);
         var str0:String = "首领等级：" + lv0;
         var skillArr0:Array = this.nowData.getDiffSkillArr(diff0);
         if(skillArr0.length > 0)
         {
            str0 += "\n首领额外技能：<yellow " + SkillDescrip.getCnText(skillArr0) + "/>";
         }
         if(this.nowData.save.d >= int(diff0))
         {
            str0 += "\n<green 该难度已通关/>";
         }
         Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
      }
      
      private function diffOut(e:ClickEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function getSweepingNum() : int
      {
         return this.nowData.getCanSweepingNum();
      }
      
      private function getSweepingNumStr() : String
      {
         var num0:int = this.nowData.getCanSweepingNum();
         return "今日可用于扫荡的挑战次数：" + ComMethod.colorEnoughNum(num0);
      }
      
      private function getSweepingGift() : GiftAddDefineGroup
      {
         var n:* = undefined;
         var pro0:Number = NaN;
         var g0:GiftAddDefineGroup = null;
         var da0:WilderData = this.nowData;
         var d0:LevelDefine = WilderLevelDiy.createLevel(da0);
         var bossName0:String = da0.def.getBodyDefine().name;
         var unitD0:OneUnitOrderDefine = d0.unitG.getOneByName(bossName0);
         var dropData0:BodyDropData = SweepData.getDropData(unitD0,da0.getEnemyLv());
         WilderLevelDiy.chooseLevel(da0,true);
         DropCtrl.drop(dropData0,true);
         var gift0:GiftAddDefineGroup = DropCtrl.clearOtherSweeping();
         for(n in da0.def.dropArr)
         {
            pro0 = da0.getDropPro(n);
            if(Math.random() <= pro0)
            {
               g0 = this.nowData.getDropGiftAddDefineGroup(n);
               if(Boolean(g0))
               {
                  da0.dropEvent(n,g0.getAllNum());
                  gift0.merge(g0);
               }
            }
         }
         da0.useNum(true);
         return gift0;
      }
      
      private function afterSweeping() : void
      {
         this.fleshData();
      }
   }
}

