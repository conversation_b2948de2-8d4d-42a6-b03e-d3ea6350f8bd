package UI.skill
{
   import UI.base.AppNormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class SkillUI extends AppNormalUI
   {
      public var labelBox:LabelBox = new LabelBox();
      
      public var wearBox:SkillWearUI = new SkillWearUI();
      
      public var studyBox:SkillStudyUI = new SkillStudyUI();
      
      public var exploreBox:SkillExploreUI = new SkillExploreUI();
      
      public var upgradeBox:SkillUpgradeUI = new SkillUpgradeUI();
      
      public var resetBox:SkillResetUI = new SkillResetUI();
      
      private var coverTxt:TextField = null;
      
      public var boxArr:Array = [this.studyBox,this.exploreBox,this.upgradeBox,this.resetBox];
      
      public var allBoxNameArr:Array = ["wear","study","explore","upgrade","reset"];
      
      public var nowLabel:String = "";
      
      private var labelTag:Sprite;
      
      private var wearSp:Sprite;
      
      private var studySp:Sprite;
      
      private var exploreSp:Sprite;
      
      private var upgradeSp:Sprite;
      
      private var resetSp:Sprite;
      
      private var closeBtn:SimpleButton;
      
      private var coverSp:Sprite;
      
      public function SkillUI()
      {
         super();
         UICn = "人物技能";
         this.labelBox.arg.init(5,1,-6,0);
         addChild(this.labelBox);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var n:* = undefined;
         var name0:String = null;
         elementNameArr = ["labelTag","wearSp","studySp","exploreSp","upgradeSp","resetSp","closeBtn","coverSp"];
         super.setImg(img0);
         this.labelBox.inData("midLabelBtn",["study","upgrade","reset"],["学习","升级","重置"]);
         this.labelBox.setChoose("study");
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.labelBox.x = this.labelTag.x;
         this.labelBox.y = this.labelTag.y;
         for(n in this.allBoxNameArr)
         {
            name0 = this.allBoxNameArr[n];
            this[name0 + "Box"].setImg(this[name0 + "Sp"]);
            addChild(this[name0 + "Box"]);
         }
         addChild(this.coverSp);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         this.coverTxt = this.coverSp["txt"];
         this.setCoverText("");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function setCoverText(str0:String) : void
      {
         this.coverSp.visible = str0 != "";
         this.coverTxt.htmlText = str0;
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      public function showBox(label0:String) : void
      {
         var n:* = undefined;
         if(label0 == "")
         {
            label0 = "study";
         }
         this.nowLabel = label0;
         this.labelBox.setChoose(label0);
         this.setCoverText("");
         for(n in this.boxArr)
         {
            this.boxArr[n].visible = false;
         }
         if(label0 != "upgrade")
         {
            this.wearBox.bagBox.setChoose_byIndex(-1);
            this.wearBox.wearBox.setChoose_byIndex(-1);
            this.upgradeBox.nowData = null;
         }
         if(label0 == "study")
         {
            this.studyBox.visible = true;
            this.studyBox.fleshData();
         }
         else if(label0 == "explore")
         {
            this.exploreBox.visible = true;
            this.exploreBox.fleshData();
         }
         else if(label0 == "upgrade")
         {
            this.upgradeBox.visible = true;
            this.upgradeBox.fleshData();
         }
         else if(label0 == "reset")
         {
            this.resetBox.visible = true;
            this.resetBox.fleshData();
         }
         Gaming.uiGroup.mustBox.hide();
      }
      
      override public function show() : void
      {
         super.show();
         this.wearBox.fleshData();
         this.showBox(this.nowLabel);
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         hide();
      }
   }
}

