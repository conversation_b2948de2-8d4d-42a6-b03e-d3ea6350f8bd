package UI.api.save
{
   import UI.UIShow;
   import dataAll._app.login.LoginData4399;
   import flash.events.DataEvent;
   import flash.events.Event;
   import flash.external.ExternalInterface;
   import unit4399.events.SaveEvent;
   
   public class Save4399
   {
      public var serviceHold:*;
      
      public var loginDelay:SuperSkillTimer = new SuperSkillTimer();
      
      public var yes_save_fun:Function = null;
      
      public var no_save_fun:Function = null;
      
      public var yes_read_fun:Function = null;
      
      public var no_read_fun:Function = null;
      
      public var yes_list_fun:Function = null;
      
      public var yes_fun:Function = null;
      
      public var no_fun:Function = null;
      
      private var storeState_yes_fun:Function = null;
      
      private var storeState_no_fun:Function = null;
      
      private var affterGetStoreStateErrorTipB:Boolean = true;
      
      private var storeErrorLockB:Boolean = true;
      
      public var yes_outLogin_fun:Function = null;
      
      public var yes_closePanel_fun:Function = null;
      
      public var yes_login_fun:Function = null;
      
      public function Save4399()
      {
         super();
      }
      
      public function addListener(stage0:*) : *
      {
         stage0.addEventListener(SaveEvent.SAVE_GET,this.saveProcess);
         stage0.addEventListener(SaveEvent.SAVE_SET,this.saveProcess);
         stage0.addEventListener(SaveEvent.SAVE_LIST,this.saveProcess);
         stage0.addEventListener("logreturn",this.saveProcess);
         stage0.addEventListener("userLoginOut",this.onUserLogOutHandler,false,0,true);
         stage0.addEventListener("MVC_CLOSE_PANEL",this.closePanelHandler);
         stage0.addEventListener("serverTimeEvent",this.onGetServerTimeHandler);
         stage0.addEventListener("saveBackIndex",this.saveProcess);
         stage0.addEventListener("netSaveError",this.netSaveErrorHandler,false,0,true);
         stage0.addEventListener("netGetError",this.netGetErrorHandler,false,0,true);
         stage0.addEventListener("multipleError",this.multipleErrorHandler,false,0,true);
         stage0.addEventListener("StoreStateEvent",this.getStoreStateHandler,false,0,true);
      }
      
      public function outLoginEvent() : void
      {
      }
      
      public function showLogPanel(_yesFun:Function = null) : *
      {
         this.yes_fun = _yesFun;
         this.loginDelay.overFun = this.yes_showLogPanel;
         this.loginDelay.t = 0.5;
      }
      
      private function yes_showLogPanel() : *
      {
         this.serviceHold.showLogPanel();
         if(this.yes_fun is Function)
         {
            this.yes_fun();
         }
      }
      
      public function userLogOut() : *
      {
         this.serviceHold.userLogOut();
      }
      
      public function isLogin() : Boolean
      {
         var logInfo:Object = null;
         if(this.serviceHold)
         {
            logInfo = this.serviceHold.isLog;
            return !!logInfo;
         }
         return true;
      }
      
      public function getLogUid() : String
      {
         var logInfo:Object = null;
         if(this.serviceHold)
         {
            logInfo = this.serviceHold.isLog;
            if(Boolean(logInfo))
            {
               return logInfo.uid;
            }
         }
         return "";
      }
      
      public function getLogObjNull() : LoginData4399
      {
         var logInfo:Object = null;
         var da0:LoginData4399 = null;
         if(this.serviceHold)
         {
            logInfo = this.serviceHold.isLog;
            if(Boolean(logInfo))
            {
               da0 = new LoginData4399();
               da0.inData_byObj(logInfo);
               return da0;
            }
            return null;
         }
         return new LoginData4399();
      }
      
      public function getServerTime(_yesFun:Function, _noFun:Function = null) : *
      {
         this.yes_fun = _yesFun;
         this.no_fun = _noFun;
         this.serviceHold.getServerTime();
      }
      
      public function uploadScore(score0:int) : *
      {
         if(this.serviceHold)
         {
            this.serviceHold.submitScore(score0);
            ExternalInterface.call("flash_result(" + score0 + ")");
         }
      }
      
      public function save(obj0:Object, nowTitle0:String, nowIndex0:int, _yesFun:Function, _noFun:Function) : *
      {
         this.yes_save_fun = _yesFun;
         this.no_save_fun = _noFun;
         this.serviceHold.saveData(nowTitle0,obj0,false,nowIndex0);
      }
      
      public function read(nowIndex0:int, _yesFun:Function, _noFun:Function) : *
      {
         this.yes_read_fun = _yesFun;
         this.no_read_fun = _noFun;
         this.serviceHold.getData(false,nowIndex0);
      }
      
      public function getList(_yesFun:Function) : *
      {
         this.yes_list_fun = _yesFun;
         this.serviceHold.getList();
      }
      
      private function saveProcess(e:SaveEvent) : void
      {
         var list0:Array = null;
         switch(e.type)
         {
            case "logreturn":
               if(this.yes_login_fun is Function)
               {
                  this.yes_login_fun(e.ret);
               }
               break;
            case SaveEvent.SAVE_GET:
               if(!e.ret)
               {
                  this.yes_read_fun(null);
               }
               else
               {
                  this.yes_read_fun(e.ret.data,e.ret);
               }
               break;
            case SaveEvent.SAVE_SET:
               if(e.ret as Boolean == true)
               {
                  this.yes_save_fun();
               }
               else
               {
                  this.no_save_fun();
               }
               break;
            case SaveEvent.SAVE_LIST:
               list0 = e.ret as Array;
               if(list0 == null)
               {
                  list0 = [];
               }
               this.yes_list_fun(list0);
         }
      }
      
      private function onUserLogOutHandler(evt:Event) : void
      {
         if(this.yes_outLogin_fun is Function)
         {
            this.yes_outLogin_fun();
         }
      }
      
      private function closePanelHandler(e:Event) : void
      {
         if(this.yes_closePanel_fun is Function)
         {
            this.yes_closePanel_fun();
         }
      }
      
      private function netSaveErrorHandler(evt:Event) : void
      {
         this.no_save_fun();
      }
      
      private function netGetErrorHandler(evt:DataEvent) : void
      {
         var tmpStr:String = "网络取" + evt.data + "档失败了！";
         this.no_read_fun(tmpStr);
      }
      
      private function onGetServerTimeHandler(evt:DataEvent) : void
      {
         if(evt.data == "")
         {
            if(this.no_fun is Function)
            {
               this.no_fun(evt.data);
            }
         }
         else if(this.yes_fun is Function)
         {
            Gaming.api.save.setNowServerDate(evt.data);
            this.yes_fun(evt.data);
         }
      }
      
      public function getStoreState(_yesFun:Function, _noFun:Function = null, errorTipB0:Boolean = true, errorLockB0:Boolean = true) : void
      {
         var e0:DataEvent = null;
         this.storeState_yes_fun = _yesFun;
         this.storeState_no_fun = _noFun;
         this.affterGetStoreStateErrorTipB = errorTipB0;
         this.storeErrorLockB = errorLockB0;
         if(this.serviceHold)
         {
            this.serviceHold.getStoreState();
         }
         else
         {
            e0 = new DataEvent("");
            e0.data = "1";
            this.getStoreStateHandler(e0);
         }
      }
      
      private function multipleErrorHandler(evt:Event) : void
      {
      }
      
      private function getStoreStateHandler(evt:DataEvent) : void
      {
         var lockB0:Boolean = false;
         var str0:String = null;
         var state0:int = int(evt.data);
         if(this.affterGetStoreStateErrorTipB)
         {
            lockB0 = true;
            str0 = "";
            if(state0 == 0)
            {
               str0 = "游戏多开了，请刷新页面！";
            }
            else if(state0 == -1)
            {
               str0 = "请求数据出错，请检查您的网络。";
               if(!this.storeErrorLockB)
               {
                  lockB0 = false;
               }
            }
            else if(state0 == -3)
            {
               str0 = "您的账户已经退出登录，请刷新页面！";
            }
            if(str0 != "")
            {
               if(lockB0)
               {
                  UIShow.hideNowApp();
               }
               Gaming.uiGroup.alertBox.showNormal(str0,lockB0 ? "" : "yes",null,null,"no");
               Gaming.uiGroup.connectUI.hide();
            }
         }
         if(this.storeState_yes_fun is Function)
         {
            this.storeState_yes_fun(state0);
         }
      }
   }
}

