package UI.pet.info
{
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.must.NormalMustBox;
   import UI.pet.PetUI;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._player.base.PlayerBaseData;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.pet.PetData;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class PetUplevelBox extends NormalUI
   {
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var mustBoxSp:Sprite;
      
      private var closeBtn:SimpleButton;
      
      private var btnSp:MovieClip;
      
      private var txt:TextField;
      
      public function PetUplevelBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["mustBoxSp","closeBtn","btnSp","txt"];
         super.setImg(img0);
         addChild(this.mustBox);
         this.mustBox.setImg(this.mustBoxSp);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("升级");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         if(Boolean(PetUI.nowGrip))
         {
            super.show();
            this.showData(PetUI.getNowData());
         }
      }
      
      private function showData(da0:PetData) : void
      {
         var mustD0:MustDefine = null;
         var bb0:Boolean = false;
         var lv0:int = da0.base.save.level;
         var heroLv0:int = Gaming.PG.da.level;
         var maxLv0:int = PlayerBaseData.MAX_LEVEL;
         this.btn.actived = false;
         if(lv0 >= maxLv0)
         {
            this.txt.htmlText = ComMethod.color("该尸宠已经升至最高等级","#00FF00");
            this.mustBox.visible = false;
         }
         else if(lv0 >= heroLv0)
         {
            this.txt.htmlText = ComMethod.color("尸宠等级不能超过人物等级","#FF0000");
            this.mustBox.visible = false;
         }
         else
         {
            this.mustBox.visible = true;
            mustD0 = da0.base.getUplevelMustDefine();
            bb0 = this.mustBox.inData(mustD0);
            this.btn.actived = bb0;
            this.txt.htmlText = "直接升级至" + ComMethod.color(lv0 + 1 + "级","#FFFF00") + "需要消耗：";
         }
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var da0:PetData = PetUI.getNowData();
         var mustD0:MustDefine = da0.base.getUplevelMustDefine();
         PlayerMustCtrl.deductMust(mustD0,this.affter_btnClick);
      }
      
      private function affter_btnClick() : void
      {
         var da0:PetData = PetUI.getNowData();
         da0.base.upLevel();
         Gaming.uiGroup.alertBox.showSuccess("升级成功！");
         Gaming.uiGroup.petUI.infoBoard.show();
         this.show();
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         hide();
      }
   }
}

