package UI.api.exchange
{
   import UI.test.SaveTestBox;
   import com.adobe.crypto.MD5;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   
   public class BbsActived_API
   {
      public var yesFun:Function;
      
      public var noFun:Function;
      
      private var loader:URLLoader = new URLLoader();
      
      private var url:URLRequest = new URLRequest("https://my.4399.com/events/2014/fiveyear/login-counter");
      
      private var game_id:int = 6;
      
      private var app_id:int = 38;
      
      public function BbsActived_API()
      {
         super();
         this.loader.addEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this.loader.addEventListener(Event.COMPLETE,this.loaderCompleteHandler);
      }
      
      public function startExchange(uid0:String, time0:int, _yesFun:Function = null, _noFun:Function = null) : *
      {
         var n:* = undefined;
         this.yesFun = _yesFun;
         this.noFun = _noFun;
         if(Gaming.api.save.isLocal())
         {
            uid0 = "607876138";
         }
         var token0:String = this.getToken(this.app_id,this.game_id,time0,Number(uid0));
         var data0:URLVariables = new URLVariables();
         data0.uid = Number(uid0);
         data0.game_id = this.game_id;
         data0.time = time0;
         data0.app_id = this.app_id;
         data0.token = token0;
         this.url.data = data0;
         this.url.method = URLRequestMethod.POST;
         this.loader.load(this.url);
         for(n in data0)
         {
            SaveTestBox.addText(n + "：" + data0[n]);
         }
      }
      
      private function getToken(app_id0:int, game_id0:int, time0:int, uid0:Number) : String
      {
         var token0:String = this.app_id + "||" + game_id0 + "||" + time0 + "||" + uid0 + "||" + "/events/2014/fiveyear/login-counter" + "||" + "919e2199989011d08e1c37f03017a27b";
         return MD5.hash(token0);
      }
      
      private function loaderCompleteHandler(e:Event) : void
      {
         if(this.yesFun is Function)
         {
            this.yesFun(this.loader.data);
         }
      }
      
      private function errorHandler(e:IOErrorEvent) : void
      {
         if(this.noFun is Function)
         {
            this.noFun();
         }
      }
   }
}

