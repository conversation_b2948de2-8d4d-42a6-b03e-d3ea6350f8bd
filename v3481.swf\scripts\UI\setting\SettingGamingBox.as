package UI.setting
{
   import UI.UIOrder;
   import UI.UIShow;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.btnList.BtnBox;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll._data.ConstantDefine;
   import dataAll._player.supple.PlayerDataSupple;
   import fl.motion.ColorMatrix;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.events.TimerEvent;
   import flash.filters.ColorMatrixFilter;
   import flash.text.TextField;
   import flash.utils.Timer;
   import gameAll.level.PlayMode;
   import gameAll.level.data.OverLevelShow;
   import w_test.drop.LevelDropCount;
   
   public class SettingGamingBox extends BtnBox
   {
      public var labelTag:Sprite = null;
      
      public var errorSp:Sprite;
      
      public var verTxt:TextField;
      
      private var saveTimer:Timer = new Timer(1000);
      
      private var SAVE_T:int = 60;
      
      private var save_t:int = -1;
      
      protected var greenFilter:ColorMatrixFilter;
      
      private var longBtnArr:Array = [];
      
      public function SettingGamingBox()
      {
         super();
         this.saveTimer.addEventListener(TimerEvent.TIMER,this.saveTimerFun);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var btn0:NormalBtn = null;
         elementNameArr = ["labelTag","verTxt","errorSp"];
         super.setImg(img0);
         FontDeal.dealLine(this.errorSp["txt"]);
         FontDeal.dealOne(this.verTxt);
         this.verTxt.styleSheet = TextMethod.getLinkCss("#999999","#FFFF00",false);
         this.verTxt.addEventListener(TextEvent.LINK,this.verTxtClick);
         getBtn("resume").setName("返回关卡中");
         getBtn("restart").setName("重玩本关");
         getBtn("main").setName("退出关卡");
         getBtn("save").setName("保存存档");
         this.aboutBtn.setName("关于");
         getBtn("pass").setName("切换P1角色");
         var greenFilterColor0:ColorMatrix = new ColorMatrix();
         greenFilterColor0.SetHueMatrix(184);
         this.greenFilter = new ColorMatrixFilter(greenFilterColor0.GetFlatArray());
         for each(btn0 in btnArr)
         {
            if(btn0.width > 100)
            {
               this.longBtnArr.push(btn0);
            }
         }
         countBtnData(this.longBtnArr);
         this.dropBtn.setName("掉落\n查看");
         ItemsGripTipCtrl.addNormalBtnTip(this.dropBtn);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function get aboutBtn() : NormalBtn
      {
         return getBtn("about");
      }
      
      private function get passBtn() : NormalBtn
      {
         return getBtn("pass");
      }
      
      private function get dropBtn() : NormalBtn
      {
         return getBtn("drop");
      }
      
      private function fleshData() : void
      {
         this.saveTimerFun();
         var gamingB0:Boolean = Gaming.LG.state != "no";
         if(gamingB0)
         {
            showBtnArr(["save","resume","restart","main"],this.longBtnArr);
            getBtn("restart").actived = Gaming.LG.canRestartB();
            if(LevelDropCount.testB)
            {
               getBtn("restart").actived = true;
            }
         }
         else
         {
            showBtnArr(["save","about","pass"],this.longBtnArr);
         }
         this.setBtnNameByMapModel();
         if(this.aboutBtn.visible)
         {
            this.fleshAboutBtn();
         }
         this.fleshPassBtn();
         this.fleshDropBtn(gamingB0);
         this.fleshVerText();
         PlayerDataSupple.oweNuclearStone();
         this.setErrorStr(Gaming.PG.da.getErrorStr());
      }
      
      private function fleshVerText(showUid0:Boolean = false) : void
      {
         var s0:String = ConstantDefine.getAllVer();
         if(showUid0 == false)
         {
            s0 += "  " + TextMethod.link("显示uid","uid");
         }
         else
         {
            s0 += "  " + Gaming.getUid() + "_" + Gaming.getSaveIndex();
         }
         this.verTxt.text = s0;
      }
      
      private function verTxtClick(e:TextEvent) : void
      {
         if(e.text == "uid")
         {
            this.fleshVerText(true);
         }
      }
      
      private function setErrorStr(str0:String) : void
      {
         if(str0 != "")
         {
            str0 = ComMethod.color("重要提示","#FFFF00",14) + "\n" + str0;
            this.errorSp["txt"].htmlText = FontDeal.getDealLeadingStr(this.errorSp["txt"],str0);
            this.errorSp.visible = true;
         }
         else
         {
            this.errorSp.visible = false;
         }
      }
      
      private function fleshDropBtn(gamingB0:Boolean) : void
      {
         this.dropBtn.visible = false;
         this.dropBtn.tipString = "";
      }
      
      private function fleshPassBtn() : void
      {
         if(this.passBtn.visible)
         {
         }
      }
      
      private function setBtnNameByMapModel() : void
      {
         var levelName0:String = this.getLevelName();
         getBtn("restart").setName("重玩" + levelName0);
         getBtn("main").setName("退出" + levelName0);
      }
      
      private function getLevelName() : String
      {
         var model0:String = Gaming.LG.mapMode;
         if(model0 == MapMode.ENDLESS)
         {
            return "无尽模式";
         }
         return "关卡";
      }
      
      private function barChange(v0:Number, label0:String) : void
      {
         if(Boolean(Gaming.PG.save))
         {
            Gaming.PG.save.setting.setValue(label0,v0);
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var mainStr0:String = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         var levelName0:String = this.getLevelName();
         var model0:String = Gaming.LG.mapMode;
         if(btn0.label == "resume")
         {
            Gaming.uiGroup.settingUI.hide();
         }
         else if(btn0.label == "restart")
         {
            Gaming.uiGroup.alertBox.showCheck("确定要重玩" + levelName0 + "？","yesAndNo",0,this.restartFun);
         }
         else if(btn0.label == "main")
         {
            mainStr0 = Gaming.LG.nowLevel.define.info.overWarn;
            if(mainStr0 == "")
            {
               mainStr0 = "确定要退出" + levelName0 + "？";
            }
            if(Gaming.LG.mode == PlayMode.ARENA && !Gaming.LG.nowLevel.dat.winB)
            {
               mainStr0 = "如果退出本关，那么本场竞技挑战就会被判定为失败。\n你确定要退出关卡？";
            }
            else if(model0 == MapMode.ENDLESS)
            {
            }
            Gaming.uiGroup.alertBox.showCheck(mainStr0,"yesAndNo",0,this.mainFun);
         }
         else if(btn0.label == "save")
         {
            this.saveFun();
         }
         else if(btn0.label == "about")
         {
            this.aboutBtnClick(e);
         }
         else if(btn0.label == "pass")
         {
            this.showP1();
         }
      }
      
      private function showPass() : void
      {
         if(Gaming.PG.da.main.havePass())
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("要修改二级密码，请先输入旧的二级密码。","",this.yesOldPass);
         }
         else
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("输入你要设置的二级秘密。\n以后登录该存档都验证这个密码才可进入游戏。","",this.yesPass);
         }
      }
      
      private function yesOldPass(str0:String) : void
      {
         if(Gaming.PG.da.main.panPass(str0))
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("验证成功！请输入新的二级密码（不输入则清空密码）。","",this.yesPass);
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("旧的二级密码输入错误！");
         }
      }
      
      private function yesPass(str0:String) : void
      {
         Gaming.PG.da.main.setPass(str0);
         if(str0 == "")
         {
            Gaming.uiGroup.alertBox.showSuccess("清空密码成功！");
         }
         else
         {
            Gaming.uiGroup.alertBox.showSuccess("设置成功！请牢记二级密码。\n保存存档后密码才能生效。");
         }
         this.fleshData();
      }
      
      private function restartFun() : void
      {
         Gaming.LG.restartLevel();
      }
      
      private function mainFun() : void
      {
         Gaming.TG.task.lowerTaskPan(this.afterMainFun);
      }
      
      private function afterMainFun() : void
      {
         Gaming.LG.overLevel(OverLevelShow.UI_CLICK);
      }
      
      private function saveFun() : void
      {
         UIOrder.saveCanStop();
      }
      
      private function showP1() : void
      {
         Gaming.uiGroup.settingUI.hide();
         Gaming.uiGroup.p1SwapBox.show();
      }
      
      private function fleshAboutBtn() : void
      {
         var doubleB0:Boolean = Gaming.PG.da.isDoubleB();
         this.aboutBtn.setName(doubleB0 ? "双人模式" : "单人模式");
         this.aboutBtn.filters = doubleB0 ? [] : [this.greenFilter];
         this.aboutBtn.visible = !Gaming.LG.isGaming();
      }
      
      private function aboutBtnClick(e:MouseEvent) : void
      {
         var doubleB0:Boolean = Gaming.PG.da.isDoubleB();
         Gaming.uiGroup.alertBox.showChoose("是否切换为" + (doubleB0 ? "单人" : "双人") + "模式？",this.doubleSwap);
      }
      
      private function doubleSwap() : void
      {
         var doubleB0:Boolean = Gaming.PG.da.moreWay.switchDoubleModel();
         this.fleshAboutBtn();
         Gaming.uiGroup.moreBox.fleshData();
         Gaming.soundGroup.playSound("uiSound","changeLabel");
         if(!Gaming.PG.save.guide.firstDoubleB)
         {
            Gaming.uiGroup.alertBox.showNormal("您设置当前游戏为" + (doubleB0 ? "双" : "单") + "人模式，人物控制按键已经改变，\n是否前往系统界面查看按键设置？","yesAndNo",this.gotoSeeDoubleKey);
            Gaming.PG.save.guide.firstDoubleB = true;
         }
      }
      
      private function gotoSeeDoubleKey() : void
      {
         UIShow.showApp("setting",true);
         Gaming.uiGroup.settingUI.showBox("key");
      }
      
      public function saveShow() : void
      {
         this.saveTimer.start();
         this.save_t = 0;
         getBtn("save").actived = false;
         this.saveTimerFun();
      }
      
      private function saveTimerFun(e:TimerEvent = null) : void
      {
         var btn0:NormalBtn = getBtn("save");
         if(this.save_t >= this.SAVE_T)
         {
            this.save_t = -1;
            btn0.setName("保存存档");
            btn0.actived = true;
            this.saveTimer.stop();
         }
         else if(this.save_t >= 0)
         {
            ++this.save_t;
            if(this.visible)
            {
               btn0.setName("保存存档（" + (this.SAVE_T - this.save_t) + "）");
            }
         }
      }
   }
}

