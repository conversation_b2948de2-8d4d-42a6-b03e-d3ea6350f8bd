package w_test.cheating
{
   import UI.test.SaveTestBox;
   import com.adobe.serialization.json.JSON2;
   import dataAll._app.top.define.TopBarDefineGroup;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import gameAll.level.arena.ArenaLevelDiy;
   
   public class ArenaCheating extends OneCheating
   {
      private var temp_str:String = "";
      
      public function ArenaCheating()
      {
         super();
      }
      
      public function setArenaTimeMul(str0:String, v0:int) : String
      {
         ArenaLevelDiy.TIME_MUL = v0;
         return "设置竞技场时间倍数：" + v0;
      }
      
      public function setArenaNum(str0:String, v0:int) : String
      {
         Gaming.PG.save.arena.todayNum = v0;
         return "设置竞技场次数：" + v0;
      }
      
      public function setArenaScore(str0:String, v0:int) : String
      {
         Gaming.PG.save.arena.score = v0;
         return "设置竞技场分数：" + v0;
      }
      
      public function addArenaGift(str0:String, v0:int) : String
      {
         var gift0:GiftAddDefineGroup = Gaming.uiGroup.arenaUI.newGiftBoard.giftCreator.getGift(v0,999999);
         GiftAddit.add(gift0,null);
         return "";
      }
      
      public function toOldTopId(str0:String, v0:int) : String
      {
         this.setTopId("arena_max",1499);
         this.setTopId("arena_1",1500);
         this.setTopId("arena_2",1501);
         this.setTopId("arena_3",1501);
         this.setTopId("arena_4",1501);
         this.setTopId("arena_5",1501);
         return "";
      }
      
      public function toNewTopId(str0:String, v0:int) : String
      {
         this.setTopId("arena_max",1998);
         this.setTopId("arena_1",1999);
         this.setTopId("arena_2",2000);
         this.setTopId("arena_3",2001);
         this.setTopId("arena_4",1500);
         this.setTopId("arena_5",1501);
         return "";
      }
      
      private function setTopId(topName0:String, id0:Number) : void
      {
         var d0:TopBarDefineGroup = Gaming.defineGroup.top.getDefine(topName0);
         d0.id = id0;
      }
      
      public function getTopData(str0:String, v0:int) : String
      {
         var sarr0:Array = str0.split(",");
         var id0:int = int(sarr0[0]);
         var pageSize0:int = int(sarr0[1]);
         var page0:int = int(sarr0[2]);
         if(pageSize0 == 0)
         {
            pageSize0 = 10;
         }
         if(page0 == 0)
         {
            page0 = 1;
         }
         if(id0 == 0 || id0 == NaN)
         {
            return "id不能为空";
         }
         this.temp_str = str0;
         Gaming.api.top.getRankListsData(id0,pageSize0,page0,this.yes_getTopData,this.no_getTopData);
         return "开始获取排行榜" + id0 + " pageSize:" + pageSize0 + "  page:" + page0;
      }
      
      private function yes_getTopData(dataArr0:Array) : void
      {
         var obj0:Object = null;
         SaveTestBox.addText("获取排行榜数据 " + this.temp_str + "  ===============");
         for each(obj0 in dataArr0)
         {
            SaveTestBox.addText(JSON2.encode(obj0));
            SaveTestBox.addText("");
         }
         Gaming.uiGroup.alertBox.showSuccess("获取排行榜" + dataArr0.length + "条数据成功！");
      }
      
      private function no_getTopData(str0:String = "") : void
      {
         Gaming.uiGroup.alertBox.showError("获取排行榜数据失败！\n" + str0);
      }
   }
}

