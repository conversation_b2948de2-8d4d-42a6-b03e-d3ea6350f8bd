package UI.arena.gift
{
   import UI.NormalUICtrl;
   import UI.arena.ArenaTopCtrl;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.box.NormalBox;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import com.sounto.utils.TextMethod;
   import dataAll._app.top.TopBarData;
   import dataAll._app.top.define.TopBarDefineGroup;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.ExchangeGiftAddDefineGroup;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class ArenaNewGiftBoard extends NormalUI
   {
      private var giftTag:Sprite;
      
      private var rankTxt:TextField;
      
      private var btnSp:MovieClip;
      
      private var giftBox:ItemsGripBox = new ItemsGripBox();
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var timeNoOverSp:Sprite;
      
      private var topBox:NormalBox = new NormalBox();
      
      private var allBox:NormalBox = new NormalBox();
      
      public var giftCreator:ArenaSeasonGiftCreator = new ArenaSeasonGiftCreator();
      
      private var tempGift:GiftAddDefineGroup = null;
      
      public function ArenaNewGiftBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["rankTxt","giftTag","btnSp","timeNoOverSp"];
         super.setImg(img0);
         FontDeal.dealOne(this.rankTxt);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("领取奖励");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.btn.actived = false;
         addChild(this.giftBox);
         this.giftBox.evt.setWantEvent(false,false,false,true,true);
         this.giftBox.arg.init(8,3,9,9);
         this.giftBox.setIconPro("equipGrip",50,50);
         NormalUICtrl.setTag(this.giftBox,this.giftTag);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.giftBox);
         addChild(this.timeNoOverSp);
         this.initBox(this.topBox,this.timeNoOverSp["topBoxTag"]);
         this.initTopBox(this.topBox);
         this.initBox(this.allBox,this.timeNoOverSp["allBoxTag"]);
         this.initAllBox(this.allBox);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         if(ArenaTopCtrl.isOverTimeB())
         {
            this.timeNoOverSp.visible = false;
            this.getRank();
         }
         else
         {
            this.timeNoOverSp.visible = true;
         }
      }
      
      private function initBox(box0:NormalBox, con0:Sprite) : void
      {
         con0.addChild(box0);
         box0.evt.setWantEvent(true,false,false,true,true);
         box0.arg.init(1,10,0,0);
         box0.setIconPro("ArenaUI/topGiftBar");
         box0.addEventListener(ClickEvent.ON_CLICK,this.boxClick);
         box0.addEventListener(ClickEvent.ON_OVER,this.boxOver);
         box0.addEventListener(ClickEvent.ON_OUT,this.boxOut);
      }
      
      private function initTopBox(box0:NormalBox) : void
      {
         var i:* = undefined;
         var rank0:int = 0;
         var cn0:String = null;
         var btn0:NormalBtn = null;
         var rankArr0:Array = [1,2,3,4,6,40,70];
         var cnArr0:Array = ["1","2","3","4、5","6~9、20、30、50、60、80、90","10、40","70、100"];
         box0.setNowGripNum(rankArr0.length);
         for(i in rankArr0)
         {
            rank0 = int(rankArr0[i]);
            cn0 = cnArr0[i];
            btn0 = box0.gripArr[i];
            btn0.setName("第" + cn0 + "名");
            btn0.setNewMc(i + 1);
            btn0.setSmallIconFrame(i % 2 + 1);
            btn0.itemsData = this.giftCreator.getRankGift(rank0);
         }
      }
      
      private function initAllBox(box0:NormalBox) : void
      {
         var i:* = undefined;
         var d0:ExchangeGiftAddDefineGroup = null;
         var btn0:NormalBtn = null;
         var cn0:String = null;
         var arr0:Array = Gaming.defineGroup.gift.getArrByFather("arenaSeason");
         var beforeRank0:int = 0;
         box0.setNowGripNum(arr0.length);
         for(i in arr0)
         {
            d0 = arr0[i];
            btn0 = box0.gripArr[i];
            cn0 = "最高榜" + (beforeRank0 + 1) + "~" + d0.mustLevel + "名";
            if(d0.mustLevel == 99999)
            {
               cn0 = "在最高榜之外且积分大于" + d0.codeUrl;
            }
            beforeRank0 = d0.mustLevel;
            btn0.setName(cn0);
            btn0.setNewMc(i + 1);
            btn0.setSmallIconFrame(i % 2 + 1);
            btn0.itemsData = this.giftCreator.getNormalGiftByRank(d0.mustLevel,999999);
         }
      }
      
      private function boxOver(e:ClickEvent) : void
      {
         Gaming.uiGroup.giftTip.showTip(e.childData as GiftAddDefineGroup,e.child as DisplayObject);
      }
      
      private function boxOut(e:ClickEvent) : void
      {
         Gaming.uiGroup.giftTip.hide();
      }
      
      private function boxClick(e:ClickEvent) : void
      {
         Gaming.uiGroup.giftShowBox.setGiftAddDefineGroup(e.childData as GiftAddDefineGroup);
      }
      
      private function getRank() : void
      {
         Gaming.uiGroup.connectUI.show("获取数据……");
         var d0:TopBarDefineGroup = Gaming.defineGroup.top.getDefine("arena_max");
         Gaming.api.top.getOneRankInfo(d0.id,Gaming.PG.loginData.name,Gaming.PG.getSaveIndex(),this.yes_GetRank,this.no_GetRank);
      }
      
      private function yes_GetRank(obj0:Object) : void
      {
         Gaming.uiGroup.connectUI.hide();
         var da0:TopBarData = new TopBarData();
         da0.inData_byObj(obj0,null);
         this.fleshByRank(da0.rank);
      }
      
      private function no_GetRank(str0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         this.fleshByRank(10001);
      }
      
      private function over_getRank() : void
      {
         this.rankTxt.text = "无";
         this.giftBox.clearAllData();
         this.btn.actived = false;
         this.btn.setName("条件不足");
      }
      
      private function fleshByRank(rank0:int) : void
      {
         var lastD0:ExchangeGiftAddDefineGroup = null;
         var gift0:GiftAddDefineGroup = this.giftCreator.getGift(rank0,Gaming.PG.da.arena.save.score);
         this.tempGift = gift0;
         if(gift0.arr.length > 0)
         {
            if(rank0 <= 10000)
            {
               this.rankTxt.htmlText = "在最高等级榜上的排名：" + TextMethod.color(rank0 + "","#FFFF00");
            }
            else
            {
               lastD0 = Gaming.defineGroup.gift.getOne("arena_99999") as ExchangeGiftAddDefineGroup;
               this.rankTxt.htmlText = "在最高榜上无排名，且积分大于" + lastD0.codeUrl + "分";
            }
            this.giftBox.inData_byArr(gift0.arr,"inData_gift");
            this.fleshBtn();
         }
         else
         {
            this.rankTxt.htmlText = TextMethod.color("在最高榜上无排名，并且积分不足","#FF0000");
            this.giftBox.clearAllData();
            this.btn.actived = false;
            this.btn.setName("条件不足");
         }
      }
      
      private function fleshBtn() : void
      {
         var haveGiftB0:Boolean = Gaming.PG.da.arena.save.phaseGiftB;
         var timeOverB0:Boolean = ArenaTopCtrl.isOverTimeB(true);
         if(haveGiftB0)
         {
            this.btn.actived = false;
            this.btn.setName("已领取");
         }
         else if(timeOverB0)
         {
            this.btn.actived = this.tempGift.arr.length > 0;
            this.btn.setName("领取奖励");
         }
         else
         {
            this.btn.actived = false;
            this.btn.setName("下午1点后才可领取");
         }
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var successB0:Boolean = GiftAddit.addAndAutoBagSpacePan(this.tempGift);
         if(successB0)
         {
            Gaming.PG.da.arena.save.phaseGiftB = true;
            this.fleshBtn();
         }
      }
   }
}

