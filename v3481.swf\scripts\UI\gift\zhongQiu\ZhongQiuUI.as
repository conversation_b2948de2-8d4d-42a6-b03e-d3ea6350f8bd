package UI.gift.zhongQiu
{
   import UI.base.AppNormalUI;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class <PERSON>hongQiuUI extends AppNormalUI
   {
      public var child:ZhongQiuBoard = new ZhongQiuBoard();
      
      private var closeBtn:SimpleButton;
      
      public function ZhongQiuUI()
      {
         super();
         UICn = "中秋活动";
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["closeBtn"];
         super.setImg(img0);
         addChild(this.child);
         this.child.setImg(Gaming.swfLoaderManager.getResource("ZhongQiuUI","boardSp"));
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         this.child.show();
         if(this.child.visible)
         {
            super.show();
         }
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         hide();
      }
   }
}

