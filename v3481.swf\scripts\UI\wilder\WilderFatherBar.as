package UI.wilder
{
   import UI.bag.ItemsGripBox;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import dataAll._app.wilder.define.WilderFatherDefine;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class WilderFatherBar extends ItemsGripBox
   {
      private var wilderFatherDefine:WilderFatherDefine = null;
      
      private var nameTxt:TextField;
      
      private var gripTag:Sprite;
      
      private var tipBtn:SimpleButton;
      
      private var elementNameArr:Array = [];
      
      private var img:Sprite;
      
      public function WilderFatherBar()
      {
         super();
      }
      
      public function setImg(img0:Sprite) : void
      {
         var n:* = undefined;
         var name0:String = null;
         this.elementNameArr = ["gripTag","nameTxt","tipBtn"];
         if(Boolean(this.img))
         {
            INIT.showError("已经有了img素材，不能重复添加！");
         }
         this.img = img0;
         for(n in this.elementNameArr)
         {
            name0 = this.elementNameArr[n];
            this.SET(name0,this.img[name0]);
            if(this.img[name0] is MovieClip)
            {
               this.img[name0].stop();
            }
         }
         addChildAt(this.img,0);
         this.x = this.img.x;
         this.y = this.img.y;
         this.img.x = 0;
         this.img.y = 0;
         arg.init(4,2,20,5,true,this.gripTag.x,this.gripTag.y);
         setIconPro("WilderUI/wilderGrip");
         evt.setWantEvent(true,false,false,true,true);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
         this.tipBtn.addEventListener(MouseEvent.CLICK,this.tipClick);
      }
      
      protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function getNewGrip() : NormalBtn
      {
         var btn0:NormalBtn = null;
         btn0 = new WilderGrip();
         btn0.activedAndEnabled = false;
         return btn0;
      }
      
      public function inDataByWilderFatherDefine(d0:WilderFatherDefine, dataArr0:Array) : void
      {
         this.wilderFatherDefine = d0;
         this.nameTxt.text = d0.cnName;
         if(gripArr.length == 0)
         {
            inData_byArr(dataArr0,"inData_wilder");
         }
         else
         {
            doChildFun("fleshData");
         }
      }
      
      public function getOpenNum() : int
      {
         var bar0:WilderGrip = null;
         var num0:int = 0;
         for each(bar0 in gripArr)
         {
            if(bar0.actived)
            {
               num0++;
            }
         }
         return num0;
      }
      
      public function getHeight() : int
      {
         if(Boolean(this.wilderFatherDefine))
         {
            return this.wilderFatherDefine.getHeight();
         }
         return height;
      }
      
      public function getLastOpenTimeValue() : Number
      {
         var bar0:WilderGrip = null;
         var over0:Number = NaN;
         var max0:Number = 0;
         for each(bar0 in gripArr)
         {
            over0 = bar0.getOverTimeValue();
            if(max0 < over0)
            {
               max0 = over0;
            }
         }
         if(Boolean(this.wilderFatherDefine))
         {
            max0 += this.wilderFatherDefine.sortIndex * 100000;
         }
         return max0;
      }
      
      public function setTipVisible(bb0:Boolean) : void
      {
         this.tipBtn.visible = bb0;
      }
      
      private function tipClick(e:MouseEvent) : void
      {
         var e0:ClickEvent = new ClickEvent(ClickEvent.ON_CTRL_CLICK);
         e0.fatherData = this.wilderFatherDefine;
         dispatchEvent(e0);
      }
      
      private function tipOver(e:MouseEvent) : void
      {
         var e0:ClickEvent = new ClickEvent(ClickEvent.ON_CTRL_OVER);
         e0.fatherData = this.wilderFatherDefine;
         dispatchEvent(e0);
      }
      
      private function tipOut(e:MouseEvent) : void
      {
         var e0:ClickEvent = new ClickEvent(ClickEvent.ON_CTRL_OUT);
         e0.fatherData = this.wilderFatherDefine;
         dispatchEvent(e0);
      }
      
      override public function clearAllData() : void
      {
         super.clearAllData();
         this.wilderFatherDefine = null;
      }
   }
}

