package UI.api.audit
{
   import UI.test.SaveTestBox;
   import com.adobe.crypto.MD5;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   
   public class AuditRoleCreate
   {
      private var loader:URLLoader = new URLLoader();
      
      private var url:URLRequest = new URLRequest("https://stat.api.4399.com/audit-log-api/roleCreate");
      
      public function AuditRoleCreate()
      {
         super();
         this.loader.addEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this.loader.addEventListener(Event.COMPLETE,this.loaderCompleteHandler);
      }
      
      public function start() : *
      {
         var data0:URLVariables = new URLVariables();
         data0.game_id = AuditHeartbeat.game_id;
         data0.uid = Gaming.PG.getUid();
         data0.time = Gaming.api.save.getNowTimeValueSec();
         data0.scene = 1;
         data0.sign = MD5.hash(MD5.hash("342sg6sfg46fghrt864rh4" + data0.time));
         this.url.data = data0;
         this.url.method = URLRequestMethod.GET;
         this.loader.load(this.url);
      }
      
      private function loaderCompleteHandler(e:Event) : void
      {
         SaveTestBox.addText("UserLogin:" + this.loader.data);
      }
      
      private function errorHandler(e:IOErrorEvent) : void
      {
         SaveTestBox.addText("UserLogin:" + this.loader.data);
      }
   }
}

