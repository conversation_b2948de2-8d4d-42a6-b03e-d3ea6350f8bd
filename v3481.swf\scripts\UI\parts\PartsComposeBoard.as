package UI.parts
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.must.NormalMustBox;
   import UI.base.numChoose.NumChooseBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.parts.PartsCreator;
   import dataAll._app.parts.PartsDataGroup;
   import dataAll._app.parts.PartsMethod;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.things.ThingsData;
   import dataAll.things.define.ThingsDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class PartsComposeBoard extends NormalUI
   {
      private var affterTag:Sprite;
      
      private var btnSp:MovieClip;
      
      private var mustBoxSp:Sprite;
      
      private var nexTxt:TextField;
      
      private var affterGrip:ItemsGrid = new ItemsGrid();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var numSp:Sprite;
      
      private var numBox:NumChooseBox = new NumChooseBox();
      
      public var nowData:ThingsData;
      
      public function PartsComposeBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["mustBoxSp","btnSp","affterTag","nexTxt","numSp"];
         super.setImg(img0);
         addChild(this.affterGrip);
         this.affterGrip.setImgToEquipGrip();
         NormalUICtrl.setTag(this.affterGrip,this.affterTag);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.affterGrip);
         addChild(this.mustBox);
         this.mustBox.setImg(this.mustBoxSp);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.activedAndEnabled = false;
         this.btn.setName("合成");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         addChild(this.numBox);
         this.numBox.setImg(this.numSp);
         this.numBox.init(999999,1);
         this.numBox.addEventListener(Event.CHANGE,this.fleshData);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      public function fleshData(e:* = null) : void
      {
         this.choosePartsData(this.nowData);
      }
      
      private function choosePartsData(da0:ThingsData) : void
      {
         var nowNum0:int = 0;
         var must_d0:MustDefine = null;
         var must_num0:int = 0;
         var next_d0:ThingsDefine = null;
         var must_b0:Boolean = false;
         var dg0:PartsDataGroup = null;
         var bag_b0:Boolean = false;
         var maxLv0:int = 0;
         var next_da0:ThingsData = null;
         this.nowData = da0;
         this.mustBox.setShowState(false);
         this.btn.actived = false;
         var f0:int = -1;
         if(Boolean(da0))
         {
            f0 = int(Gaming.PG.da.partsBag.dataArr.indexOf(da0));
         }
         if(f0 == -1)
         {
            this.nowData = null;
            Gaming.uiGroup.partsUI.setCoverText("在背包里" + ComMethod.color("双击","#00FF00") + "" + ComMethod.color("零件图标","#FFFF00") + "，\n即可进入该零件的合成界面。");
         }
         else
         {
            Gaming.uiGroup.partsUI.setCoverText("");
            this.numBox.setMax(this.getMaxNum());
            nowNum0 = this.numBox.nowNum;
            must_d0 = PartsMethod.getComposeMustDefine(da0,nowNum0);
            must_num0 = PartsMethod.getComposeMustNum(da0.save,nowNum0);
            next_d0 = da0.save.getPartsNextDefine();
            if(Boolean(next_d0))
            {
               next_da0 = PartsCreator.getThingsDataByPartsDefine(next_d0,nowNum0);
               this.affterGrip.visible = true;
               this.affterGrip.inData_parts(next_da0,true);
               this.nexTxt.text = "下一级零件";
            }
            else
            {
               this.affterGrip.visible = false;
               this.nexTxt.text = "已达最高等级";
            }
            must_b0 = this.mustBox.inData(must_d0);
            dg0 = Gaming.PG.da.partsBag;
            bag_b0 = dg0.getSpaceSiteNum() > 0 || Boolean(next_d0) && Boolean(dg0.getDataBySaveName(next_d0.name));
            this.btn.actived = must_b0 && bag_b0;
            this.btn.setName(bag_b0 ? "合成" : "背包不足");
            maxLv0 = da0.getMaxPartsLevel();
            if(!next_d0 || next_d0.itemsLevel > maxLv0)
            {
               this.mustBox.setShowState(false);
               this.mustBox.getThingsBox().visible = true;
               this.numBox.visible = false;
               this.btn.actived = false;
               this.affterGrip.visible = false;
               this.nexTxt.htmlText = ComMethod.color("该零件最高等级只开放至" + maxLv0 + "级","#00FFFF");
            }
            else
            {
               this.numBox.visible = true;
            }
         }
      }
      
      private function getMaxNum() : int
      {
         var must_num0:int = 0;
         var num0:int = 0;
         if(Boolean(this.nowData))
         {
            must_num0 = PartsMethod.getComposeMustNum(this.nowData.save,1);
            num0 = int(this.nowData.save.nowNum / must_num0);
            if(num0 < 1)
            {
               num0 = 1;
            }
         }
         else
         {
            num0 = 1;
         }
         return num0;
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = null;
         var nowNum0:int = 0;
         var must_d0:MustDefine = null;
         if(Boolean(this.nowData))
         {
            btn0 = e.target as NormalBtn;
            if(btn0.actived)
            {
               nowNum0 = this.numBox.nowNum;
               must_d0 = PartsMethod.getComposeMustDefine(this.nowData,nowNum0);
               PlayerMustCtrl.deductMust(must_d0,this.affter_click);
            }
         }
      }
      
      private function affter_click() : void
      {
         var nowNum0:int = this.numBox.nowNum;
         var da0:ThingsData = this.nowData;
         var next_d0:ThingsDefine = da0.save.getPartsNextDefine();
         var next_da0:ThingsData = Gaming.PG.da.partsBag.addDataByName(next_d0.name,nowNum0) as ThingsData;
         Gaming.uiGroup.alertBox.showCheck("合成" + nowNum0 + "个零件成功！","",0.6,null,null,next_d0.iconUrl,"equip");
         var grip0:ItemsGrid = Gaming.uiGroup.alertBox.getEquipGrip();
         grip0.inData_parts(next_da0,false);
         grip0.setNew(false);
         Gaming.soundGroup.playSound("uiSound","success");
         this.numBox.setNum(1);
         this.fleshData();
         Gaming.uiGroup.bagUI.fleshAllBox();
         Gaming.uiGroup.mainUI.fleshCoin();
      }
   }
}

