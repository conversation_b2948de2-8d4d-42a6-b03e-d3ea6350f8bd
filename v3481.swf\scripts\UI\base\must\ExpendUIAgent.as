package UI.base.must
{
   import UI.base.button.NormalBtn;
   import dataAll.must.define.MustDefine;
   import flash.text.TextField;
   
   public class ExpendUIAgent extends ExpendUIData
   {
      public var must:MustDefine = new MustDefine();
      
      public var yesFun:Function = null;
      
      public var btnCn:String = "";
      
      public var btnActived:Boolean = false;
      
      public var info:String = "";
      
      public function ExpendUIAgent()
      {
         super();
         successReSetDataB = false;
      }
      
      override public function showDataEvent(okBtn0:NormalBtn, infoTxt0:TextField, mustBox0:NormalMustBox) : void
      {
         if(this.info != "")
         {
            infoTxt0.htmlText = this.info;
         }
         else
         {
            infoTxt0.htmlText = "需要消耗：";
         }
         var must0:MustDefine = this.getMustDefine();
         var mustB0:Boolean = mustBox0.inData(must0);
         okBtn0.setName(this.btnCn);
         okBtn0.actived = mustB0 && this.btnActived;
      }
      
      override public function getMustDefine() : MustDefine
      {
         return this.must;
      }
      
      override public function successEvent() : void
      {
         if(this.yesFun is Function)
         {
            this.yesFun();
         }
      }
   }
}

