package UI.api.exchange
{
   import com.adobe.crypto.MD5;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   
   public class ZhuZhan_API
   {
      public var yesFun:Function;
      
      public var noFun:Function;
      
      internal var loader:URLLoader = new URLLoader();
      
      internal var url:URLRequest = new URLRequest("https://huodong2.4399.com/4399game/kxlb2020/api/ajax.php");
      
      public function ZhuZhan_API()
      {
         super();
         this.loader.addEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this.loader.addEventListener(Event.COMPLETE,this.loaderCompleteHandler);
      }
      
      public function startExchange(code0:String, _yesFun:Function = null, _noFun:Function = null) : *
      {
         this.yesFun = _yesFun;
         this.noFun = _noFun;
         var data0:URLVariables = new URLVariables();
         data0.act = "provingCode";
         data0.type = 1;
         data0.code = code0;
         data0.sign = MD5.hash(data0.type + code0 + "asdf%g$d#c@d");
         this.url.data = data0;
         this.url.method = URLRequestMethod.GET;
         this.loader.load(this.url);
      }
      
      private function loaderCompleteHandler(e:Event) : void
      {
         var msg0:String = null;
         var str0:String = this.loader.data;
         var s0:int = int(str0);
         if(s0 == 1)
         {
            if(this.yesFun is Function)
            {
               this.yesFun("");
            }
         }
         else
         {
            msg0 = "未知错误";
            if(s0 == 2)
            {
               msg0 = "校对码不正确";
            }
            if(s0 == 3)
            {
               msg0 = "游戏编号不存在";
            }
            if(s0 == 4)
            {
               msg0 = "激活码不存在";
            }
            if(s0 == 5)
            {
               msg0 = "已经被领取";
            }
            if(this.noFun is Function)
            {
               this.noFun(msg0);
            }
         }
      }
      
      private function errorHandler(e:IOErrorEvent) : void
      {
         if(this.noFun is Function)
         {
            this.noFun("网络连接错误");
         }
      }
   }
}

