package UI.api.exchange
{
   import UI.test.SaveTestBox;
   import com.adobe.crypto.MD5;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   
   public class CodeExchange_API
   {
      public var yesFun:Function;
      
      public var noFun:Function;
      
      internal var loader:URLLoader = new URLLoader();
      
      internal var url:URLRequest = new URLRequest("https://my.4399.com/jifen/activation");
      
      public var uniqueId:int = 120;
      
      public var privateKey:String = "290e545cfdaef97400e3293974e1a587";
      
      public function CodeExchange_API()
      {
         super();
         this.loader.addEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this.loader.addEventListener(Event.COMPLETE,this.loaderCompleteHandler);
      }
      
      public function startExchange(uid0:String, activation0:String, _yesFun:Function = null, _noFun:Function = null) : *
      {
         var n:* = undefined;
         this.yesFun = _yesFun;
         this.noFun = _noFun;
         if(Gaming.api.save.isLocal())
         {
            uid0 = "1069144161";
         }
         var token0:String = MD5.hash(activation0 + "-" + uid0 + "-" + this.uniqueId + "-" + this.privateKey);
         var data0:URLVariables = new URLVariables();
         data0.uid = Number(uid0);
         data0.activation = activation0;
         data0.uniqueId = this.uniqueId;
         data0.token = token0;
         this.url.data = data0;
         this.url.method = URLRequestMethod.POST;
         this.loader.load(this.url);
         SaveTestBox.addText("privateKey：" + this.privateKey);
         var arr0:Array = ["uniqueId","uid","activation","token"];
         for(n in arr0)
         {
            SaveTestBox.addText(arr0[n] + "：" + this.url.data[arr0[n]]);
         }
      }
      
      private function loaderCompleteHandler(e:Event) : void
      {
         if(this.yesFun is Function)
         {
            this.yesFun(this.loader.data);
         }
      }
      
      private function errorHandler(e:IOErrorEvent) : void
      {
         if(this.noFun is Function)
         {
            this.noFun();
         }
      }
   }
}

