package UI.peak
{
   import UI.base.AppNormalUI;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class PeakUI extends AppNormalUI
   {
      private var child:PeakChildBoard = new PeakChildBoard();
      
      protected var tipBtn:SimpleButton;
      
      protected var closeBtn:SimpleButton;
      
      public function PeakUI()
      {
         super();
         UICn = "巅峰等级";
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["closeBtn","tipBtn"];
         super.setImg(img0);
         addChild(this.child);
         this.child.setImg(Gaming.swfLoaderManager.getResource("PeakUI","childBoard"));
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.child.show();
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         hide();
      }
      
      protected function getTipText() : String
      {
         var str0:String = "";
         str0 += "1、人物到达最大等级后，溢出的人物经验可自动转化为巅峰经验（每日有上限）。";
         str0 += "\n2、巅峰经验到达一定值后提升巅峰等级，同时获得1点巅峰点数。";
         str0 += "\n3、巅峰点数可用于升级巅峰属性与技能，你可以无限重置巅峰点数的使用。";
         str0 += "\n4、巅峰属性：包括8种人物属性，每加点1次提升一定属性值。";
         return str0 + "\n5、巅峰技能：包括2种人物技能，升1级需要消耗2点巅峰点数。";
      }
      
      protected function tipOver(e:MouseEvent) : void
      {
         var str0:String = this.getTipText();
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      protected function tipOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
   }
}

