package
{
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol252")]
   public dynamic class settingUI extends MovieClip
   {
      public var labelTag:MovieClip;
      
      public var aboutBox:MovieClip;
      
      public var downSp:MovieClip;
      
      public var continueTipSp:MovieClip;
      
      public var closeBtn:SimpleButton;
      
      public function settingUI()
      {
         super();
      }
   }
}

