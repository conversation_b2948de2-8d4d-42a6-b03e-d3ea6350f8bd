package UI.gift.yuanXiao
{
   import flash.display.Sprite;
   
   public class YuanXiaoCon
   {
      public var front:Sprite = new Sprite();
      
      public var ball:Sprite = new Sprite();
      
      public var back:Sprite = new Sprite();
      
      private var arr:Array = [this.ball,this.front,this.back];
      
      public function YuanXiaoCon()
      {
         super();
      }
      
      public function init(con0:Sprite) : void
      {
         var sp0:Sprite = null;
         for each(sp0 in this.arr)
         {
            con0.addChildAt(sp0,0);
            sp0.y = 300;
         }
      }
      
      public function setBallX(x0:int) : void
      {
         var sp0:Sprite = null;
         for each(sp0 in this.arr)
         {
            sp0.x = -x0 + YuanXiaoGame.FIRST_X;
         }
      }
      
      public function ballToFront() : void
      {
         this.ball.parent.addChild(this.ball);
      }
      
      public function frontToFront() : void
      {
         this.front.parent.addChild(this.front);
      }
   }
}

