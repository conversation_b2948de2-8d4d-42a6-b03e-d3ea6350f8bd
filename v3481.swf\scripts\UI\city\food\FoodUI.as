package UI.city.food
{
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AppNewUI;
   import UI.base.event.ClickEvent;
   import UI.base.grid.NormalGridIcon;
   import UI.base.label.LabelBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.food.FoodBookAgent;
   import dataAll._app.food.FoodData;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class FoodUI extends AppNewUI
   {
      public var rawBoard:FoodRawBoard = new FoodRawBoard();
      
      public var bookBoard:FoodBookBoard = new FoodBookBoard();
      
      private var iconTag:Sprite;
      
      private var iconImg:NormalGridIcon = new NormalGridIcon();
      
      private var nameTxt:TextField;
      
      private var timeTxt:TextField;
      
      private var eatTxt:TextField;
      
      private var profiTxt:TextField;
      
      private var effectMc:MovieClip;
      
      public function FoodUI()
      {
         super();
         UICn = "美食屋";
         swfLabel = "FoodUI";
         labelArr = ["raw","book"];
         labelCnArr = ["食材","美食"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var box0:LabelBox = null;
         elementNameArr = elementNameArr.concat(["iconTag","nameTxt","timeTxt","eatTxt","profiTxt","effectMc"]);
         super.setImg(img0);
         box0 = labelBox;
         box0.arg.init(4,1,-3,0);
         addChild(box0);
         box0.inData("FoodUI/bigLongLabel",labelArr,labelCnArr);
         box0.setChoose(getFirstLabel());
         box0.addEventListener(ClickEvent.ON_CLICK,labelClick);
         box0.x = labelTag.x;
         box0.y = labelTag.y;
         this.iconTag.addChild(this.iconImg);
         this.iconTag.mouseEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.iconImg);
         addChild(this.effectMc);
         this.effectMc.mouseChildren = false;
         this.effectMc.mouseEnabled = false;
         this.effectMc.stop();
         this.effectMc.visible = false;
         init_addBox();
         init_other();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      protected function get foodData() : FoodData
      {
         return Gaming.PG.da.food;
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      public function outLoginEvent() : void
      {
         this.bookBoard.outLoginEvent();
      }
      
      override public function fleshData() : void
      {
         super.fleshData();
         this.fleshTxtInfo();
         this.fleshEatInfo();
      }
      
      public function outfleshData(effectB0:Boolean = false) : void
      {
         this.fleshTxtInfo();
         this.fleshEatInfo(effectB0);
      }
      
      private function fleshTxtInfo() : void
      {
         var t1:String = "今日进食次数：" + ComMethod.mustColor(this.foodData.save.eatNum,this.foodData.getEatMax(),false,"#00FFFF","#00FF00");
         var t2:String = "今日掉落食材：" + ComMethod.mustColor(this.foodData.save.dropNum,this.foodData.getDropMax(),false,"#00FFFF","#00FF00");
         t2 += "\n今日获得厨艺值：" + ComMethod.mustColor(this.foodData.save.profi,this.foodData.getDayProfiMax(),false,"#00FFFF","#00FF00");
         t2 += "\n总厨艺值：" + ComMethod.mustColor(this.foodData.save.profiAll,this.foodData.getProfiMax(),false,"#00FFFF","#00FF00");
         this.eatTxt.htmlText = t1;
         this.profiTxt.htmlText = t2;
      }
      
      private function fleshEatInfo(effectB0:Boolean = false) : void
      {
         if(effectB0)
         {
            this.effectMc.visible = true;
            this.effectMc.gotoAndPlay(1);
         }
         var a0:FoodBookAgent = this.foodData.getEatBookAgent();
         if(Boolean(a0))
         {
            this.fleshEatTime();
            this.nameTxt.text = a0.cnName;
            this.iconImg.setIconName(a0.iconUrl);
            this.iconImg.tipString = a0.getGatherTip();
         }
         else
         {
            this.nameTxt.text = "无食物";
            this.iconImg.setIconName("");
            this.timeTxt.text = "";
            this.iconImg.tipString = "";
         }
      }
      
      private function fleshEatTime() : void
      {
         var time0:Number = this.foodData.save.eatTime;
         this.timeTxt.text = "消化时间  " + ComMethod.getTimeStrTwo(time0);
      }
      
      private function iconOver(e:MouseEvent) : void
      {
      }
      
      private function iconOut(e:MouseEvent) : void
      {
      }
      
      override protected function getTipText() : String
      {
         var str0:String = "";
         if(this.rawBoard.visible)
         {
            str0 += "<yellow 食材：/>";
            str0 += "\n1、现在有9种食材，每种食材都有不一样的技能效果。";
            str0 += "\n2、消灭关卡中的首领必然掉落1个食材，每天最多掉落10个。";
            str0 += "\n\n<yellow 美食：/>";
            str0 += "\n1、制作每种美食都需要2种以上的食材，制作完毕后，美食将包含食材中的技能效果。";
            str0 += "\n2、解锁美食菜谱需要“厨艺值”满足到达指定值。";
            str0 += "\n\n<yellow 厨艺值：/>";
            str0 += "\n1、制作一样美食将获得1点厨艺值。";
            str0 += "\n2、每天最多获得4点厨艺值。";
            str0 += "\n\n<yellow 进食：/>";
            str0 += "\n1、进食美食后，在关卡中，玩家可获得该美食中的所有技能效果。";
            str0 += "\n2、食物消化完毕后，技能效果消失。";
            str0 += "\n3、每天每种食物只能食用1次。";
            str0 += "\n4、每天最多进食8次。";
         }
         return str0;
      }
      
      public function FTimerSecond() : void
      {
         if(visible)
         {
         }
      }
   }
}

