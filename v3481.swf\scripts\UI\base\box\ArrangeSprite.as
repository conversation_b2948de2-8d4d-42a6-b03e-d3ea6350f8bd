package UI.base.box
{
   import UI.base.TipSprite;
   
   public class ArrangeSprite extends TipSprite
   {
      public var extraX:int = 0;
      
      public var extraY:int = 0;
      
      private var _hidingB:Boolean = false;
      
      private var _visible:Boolean = true;
      
      public function ArrangeSprite()
      {
         super();
         this.mouseChildren = false;
      }
      
      public function set hidingB(bb0:Boolean) : void
      {
         this._hidingB = bb0;
         super.visible = this._visible && !this._hidingB;
      }
      
      public function get hidingB() : Boolean
      {
         return this._hidingB;
      }
      
      override public function set visible(bb0:<PERSON><PERSON>an) : void
      {
         this._visible = bb0;
         super.visible = this._visible && !this._hidingB;
      }
      
      override public function get visible() : <PERSON><PERSON><PERSON>
      {
         return this._visible;
      }
   }
}

