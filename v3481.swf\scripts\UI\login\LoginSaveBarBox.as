package UI.login
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import dataAll._app.login.SaveBaseData4399;
   
   public class LoginSaveBarBox extends ItemsGripBox
   {
      public function LoginSaveBarBox()
      {
         super();
      }
      
      public function init() : void
      {
         setNowGripNum(8);
      }
      
      public function inData(d_arr0:Array, annotation0:String) : void
      {
         var n:* = undefined;
         var i:* = undefined;
         var grip0:ItemsGrid = null;
         var d0:SaveBaseData4399 = null;
         var grip2:ItemsGrid = null;
         clearData();
         for(n in gripArr)
         {
            grip0 = gripArr[n];
            grip0.noData_SaveListDefine(annotation0);
         }
         for(i in d_arr0)
         {
            d0 = d_arr0[i];
            grip2 = gripArr[d0.index];
            grip2.inData_SaveListDefine(d0);
         }
         arrange();
         pageBox.inData_byTable(arg,5,arg.nowPage);
      }
   }
}

