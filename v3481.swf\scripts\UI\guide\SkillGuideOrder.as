package UI.guide
{
   import UI.base.button.NormalBtn;
   import flash.display.DisplayObject;
   
   public class SkillGuideOrder extends NormalGuideOrder
   {
      public function SkillGuideOrder()
      {
         super();
         arr = [this.mainBtn,this.skillBtn,this.studyBtn,this.dragBtn,this.moreBtn,this.skillBtn,this.studyBtn,this.dragBtn];
      }
      
      private function mainBtn() : OneGuideData
      {
         Gaming.PG.setNowPlayerData(Gaming.PG.da);
         Gaming.uiGroup.moreBox.show();
         return new OneGuideData(Gaming.uiGroup.mainUI.getBtn("skill"),"可以学习新技能了");
      }
      
      private function skillBtn() : OneGuideData
      {
         return new OneGuideData(Gaming.uiGroup.skillUI.studyBox.gripBox.gripArr[0],"点击你要学的技能","click");
      }
      
      private function studyBtn() : OneGuideData
      {
         return new OneGuideData(Gaming.uiGroup.skillUI.studyBox.btn,"点击学习","click",1);
      }
      
      private function dragBtn() : OneGuideData
      {
         var btn0:DisplayObject = Gaming.uiGroup.skillUI.wearBox.bagBox.gripArr[0];
         var btn2:DisplayObject = Gaming.uiGroup.skillUI.wearBox.wearBox.gripArr[0];
         return new OneGuideData(btn2,"拖拽技能到装备栏\n即可装备技能","mouseUp",0,btn0);
      }
      
      private function moreBtn() : OneGuideData
      {
         var btn0:NormalBtn = Gaming.uiGroup.moreBox.box.gripArr[1];
         if(!btn0.actived)
         {
            btn0 = null;
         }
         return new OneGuideData(btn0,"接着为你的队友\n学习技能");
      }
   }
}

