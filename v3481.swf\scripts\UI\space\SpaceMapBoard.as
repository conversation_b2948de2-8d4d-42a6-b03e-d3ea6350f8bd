package UI.space
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.bag.ItemsGripBox;
   import UI.base.AutoNormalUI;
   import UI.base.button.BtnAgent;
   import UI.base.button.BtnAgentGroup;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.grid.NormalGrid;
   import dataAll._app.space.SpaceData;
   import dataAll._app.space.craft.CraftData;
   import dataAll._app.task.TaskData;
   import dataAll._app.task.define.TaskDefine;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll.level.define.LevelDefine;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class SpaceMapBoard extends AutoNormalUI
   {
      private var gripBox:ItemsGripBox = new ItemsGripBox();
      
      private var gripTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var titleTxt:TextField;
      
      private var infoTxt:TextField;
      
      public function SpaceMapBoard()
      {
         super();
         mcTypeArr = ["btnSp","txt","tag"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         FontDeal.dealOne(this.titleTxt);
         FontDeal.dealLine(this.infoTxt);
         this.titleTxt.htmlText = "";
         this.infoTxt.htmlText = "";
         this.gripBox.setIconPro("SpaceUI/mapBtn");
         this.gripBox.arg.init(5,3,12,12);
         this.gripBox.evt.setWant(true,true);
         addChild(this.gripBox);
         NormalUICtrl.setTag(this.gripBox,this.gripTag);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.gripBox.addEventListener(ClickEvent.ON_OVER,this.gripOver);
         this.gripBox.addEventListener(ClickEvent.ON_OUT,Gaming.uiGroup.tipBox.hide);
         this.gripBox.pageBox.setToSmall();
         this.gripBox.setPagePos(this.pageTag);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function get DATA() : SpaceData
      {
         return Gaming.PG.da.space;
      }
      
      private function isGaimgB() : Boolean
      {
         return Gaming.LG.isGaming();
      }
      
      public function outLoginEvent() : void
      {
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      private function fleshData() : void
      {
         var arr0:Array = this.DATA.getUIMapDefArr();
         this.gripBox.inData_byArr(arr0,this.gripFun);
      }
      
      private function gripFun(grip0:NormalGrid, d0:WorldMapDefine) : void
      {
         var per0:Number = NaN;
         var nowCraft0:CraftData = null;
         var taskD0:TaskDefine = null;
         var diff0:int = this.DATA.getMapDiff(d0.name);
         var winNum0:Number = this.DATA.getMapWinNum(d0.name);
         var nowExp0:Number = this.DATA.getMapExp(d0.name);
         var maxExp0:Number = this.DATA.getMapExpMaxAllDiff(d0.name);
         per0 = nowExp0 / maxExp0;
         grip0.itemsData = d0;
         grip0.setName(d0.cnName);
         grip0.setIconName(d0.getSpaceIconUrl());
         grip0.setSmallIconPer(per0);
         grip0.setSmallIcon(per0 >= 1 ? "green" : "orange");
         grip0.activedAndEnabled = false;
         if(this.DATA.getMapUnlock(d0.name))
         {
            grip0.actived = true;
            grip0.tipString = "";
            grip0.tipString += "累计通关次数：<green " + winNum0 + "/>";
            grip0.tipString += "\n本周获得经验值：<green " + nowExp0 + "/>";
            grip0.tipString += "\n每周经验值上限：<green " + maxExp0 + "/>(最高难度)";
            nowCraft0 = this.DATA.getNowCraft();
            if(Boolean(nowCraft0))
            {
               if(nowCraft0.expIsFillB())
               {
                  grip0.tipString += "\n<red 当前飞船经验已满，将无法获得经验。/>";
               }
            }
            else
            {
               grip0.tipString += "\n<red 没有主力飞船，无法获得经验。/>";
            }
         }
         else
         {
            grip0.actived = false;
            taskD0 = Gaming.defineGroup.task.getOneDefine(d0.unlockTask);
            if(Boolean(taskD0))
            {
               grip0.tipString = "完成任务 " + taskD0.getAlertTitleText() + " 后解锁。";
            }
            else
            {
               grip0.tipString = "未解锁";
            }
         }
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var grip0:NormalGrid = null;
         var d0:WorldMapDefine = null;
         var taskDa0:TaskData = null;
         if(!this.isGaimgB())
         {
            grip0 = e.child as NormalGrid;
            if(grip0.actived)
            {
               d0 = e.childData as WorldMapDefine;
               taskDa0 = Gaming.PG.da.task.getIngTaskDataByWorldMap(d0.name);
               if(Boolean(taskDa0))
               {
                  UIOrder.alertError("当前地图被任务 " + taskDa0.def.getAlertTitleText() + " 占用，\n请取消任务再继续！");
               }
               else
               {
                  Gaming.uiGroup.alertBox.option.showAgent(this.DATA.getDiffAgent(d0.name,this.afterClick));
               }
            }
         }
      }
      
      private function afterClick(a0:BtnAgent, g0:BtnAgentGroup) : void
      {
         var diff0:int = int(a0.name);
         var d0:WorldMapDefine = Gaming.defineGroup.worldMap.getDefine(g0.name);
         var levelD0:LevelDefine = Gaming.defineGroup.level.getDefine(d0.getLevelName());
         Gaming.LG.chooseByLevelDefine(levelD0,diff0);
      }
      
      private function gripOver(e:ClickEvent) : void
      {
         var grip0:NormalGrid = e.child as NormalGrid;
         Gaming.uiGroup.tipBox.showText(grip0.tipString);
      }
   }
}

