package UI.edit
{
   import UI.UIGroup;
   import UI.UIOrder;
   import UI.base.AppNewUI;
   import flash.text.TextField;
   
   public class BosseditUI extends AppNewUI
   {
      private var titleTxt:TextField;
      
      private var editBoard:BosseditEditBoard = new BosseditEditBoard();
      
      private var topBoard:BosseditTopBoard = new BosseditTopBoard();
      
      private var levelBoard:BosseditLevelBoard = new BosseditLevelBoard();
      
      private var armsBoard:BosseditArmsBoard = new BosseditArmsBoard();
      
      private var cardBoard:BosseditCardBoard = new BosseditCardBoard();
      
      private var pkBoard:BosseditPKBoard = new BosseditPKBoard();
      
      public function BosseditUI()
      {
         super();
         UICn = "首领工厂";
         UILabel = "bossedit";
         swfLabel = "BosseditUI";
         labelArr = ["level","top","edit","arms","card","pk"];
         labelCnArr = ["战场","大厅","创造首领","创造武器","魂卡","魂卡PK"];
      }
      
      override protected function firstLoad() : void
      {
         elementNameArr = elementNameArr.concat(["titleTxt"]);
         setImgUrl(swfLabel + "/" + UILabel + "UI");
         init_addLabel();
         init_addBox();
         init_other();
         UIGroup.setUIMiddle(this,false,833,513);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         var bb0:Boolean = UIOrder.openPan("ZhuTou");
         if(bb0)
         {
            Gaming.PG.da.bossEdit.openUI();
            super.show();
         }
      }
      
      override public function hide() : void
      {
         super.hide();
         Gaming.uiGroup.bcardBattleBox.hide();
      }
      
      public function outLoginEvent() : void
      {
         this.editBoard.outLoginEvent();
         this.topBoard.outLoginEvent();
         this.levelBoard.outLoginEvent();
         this.pkBoard.outLoginEvent();
      }
      
      public function FKey() : void
      {
         if(visible)
         {
            this.cardBoard.FKey();
            this.pkBoard.FKey();
         }
      }
      
      public function FTimer() : void
      {
         if(visible)
         {
            this.armsBoard.FTimer();
         }
      }
      
      public function fleshCardBox() : void
      {
         this.cardBoard.outFleshBox();
      }
      
      override protected function getTipText() : String
      {
         var str0:String = "";
         if(this.editBoard.visible || this.topBoard.visible)
         {
            str0 += "1、玩家可以创造首领，为其设置等级生命、伤害，添加技能等等。";
            str0 += "\n\n2、系统会给每个首领一个评分和难度星级，仅供参考。";
            str0 += "\n\n3、战胜首领后，你可以将首领通过代码分享给朋友，供他们挑战。";
            str0 += "\n\n4、战胜首领后，你可以将首领设为主力，主力首领才能上传到首领大厅供所有玩家挑战。";
         }
         else if(this.armsBoard.visible)
         {
            str0 += "1、点击“创造武器”选择一把基础武器进行改造。";
            str0 += "\n\n2、你可以修改武器的射速、弹容、子弹图像等等，多达几十种属性。";
            str0 += "\n\n3、高级属性默认隐藏，需要手动开启；修改过的属性将以蓝色醒目显示。";
            str0 += "\n\n4、你可以在指定关卡中（可选10000倍的难度）测试自己创造的武器。";
            str0 += "\n\n5、你也可以将武器代码分享给别人。";
         }
         else if(this.pkBoard.visible)
         {
            str0 += "1、使用我方魂卡击败敌方魂卡，可获得抽卡次数，次数=敌人魂卡星级。";
            str0 += "\n\n2、魂卡PK有关卡限时，限时一到视为PK失败。";
            str0 += "\n\n3、每周都会更新若干张敌人魂卡，供玩家PK。";
            str0 += "\n\n4、每周每张我方魂卡只能取胜1次，请合理安排出场顺序。";
         }
         return str0;
      }
   }
}

