package UI.base.text
{
   import UI.base.font.FontDeal;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.TextMethod;
   import dataAll.ui.text.LinkTextAgent;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.text.StyleSheet;
   import flash.text.TextField;
   
   public class LinkTextGroup
   {
      private var txtArr:Array = [];
      
      private var agentArr:Array = [];
      
      private var css:StyleSheet = null;
      
      private var tipFun:Function;
      
      private var linkFun:Function;
      
      private var mouseTxt:TextField = null;
      
      private var mouseLine:int = -1;
      
      public function LinkTextGroup()
      {
         super();
      }
      
      public function init(css0:StyleSheet, linkFun0:Function = null, tipFun0:Function = null) : void
      {
         this.css = css0;
         this.linkFun = linkFun0;
         this.tipFun = tipFun0;
      }
      
      public function addTxt(txt0:TextField, autoB0:Boolean) : void
      {
         this.txtArr.push(txt0);
         if(autoB0)
         {
            TextMethod.setAutoFormat(txt0);
         }
         txt0.styleSheet = this.css;
         txt0.addEventListener(TextEvent.LINK,this.linkClick);
         txt0.addEventListener(MouseEvent.MOUSE_MOVE,this.textMove);
         txt0.addEventListener(MouseEvent.MOUSE_OUT,this.textOut);
      }
      
      public function addTxtArr(arr0:Array, autoB0:Boolean) : void
      {
         var txt0:TextField = null;
         for each(txt0 in arr0)
         {
            this.addTxt(txt0,autoB0);
         }
      }
      
      public function getTxt(i0:int) : TextField
      {
         return this.txtArr[i0];
      }
      
      public function getTxtIndex(txt0:TextField) : int
      {
         return this.txtArr.indexOf(txt0);
      }
      
      public function getAgent(i0:int) : LinkTextAgent
      {
         return this.agentArr[i0];
      }
      
      public function getAgentByTxt(txt0:TextField) : LinkTextAgent
      {
         var f0:int = int(this.txtArr.indexOf(txt0));
         return ArrayMethod.getElement(this.agentArr,f0,null,null) as LinkTextAgent;
      }
      
      public function inDataIndex(a0:LinkTextAgent, i0:int) : void
      {
         var txt0:TextField = this.getTxt(i0);
         this.agentArr[i0] = a0;
         txt0.htmlText = FontDeal.getDealLeadingStr(txt0,a0.getText());
      }
      
      public function setMouseActived(bb0:Boolean) : void
      {
         var txt0:TextField = null;
         for each(txt0 in this.txtArr)
         {
            txt0.mouseEnabled = bb0;
         }
      }
      
      private function linkClick(e:TextEvent) : void
      {
         var txt0:TextField = null;
         var a0:LinkTextAgent = null;
         var link0:String = null;
         var data0:* = undefined;
         if(this.linkFun is Function)
         {
            txt0 = e.target as TextField;
            a0 = this.getAgentByTxt(txt0);
            if(Boolean(a0))
            {
               link0 = e.text;
               data0 = a0.getDataByLink(link0);
               this.linkFun(txt0,link0,data0);
            }
         }
      }
      
      private function textMove(e:MouseEvent) : void
      {
         var txt0:TextField = null;
         var a0:LinkTextAgent = null;
         var lineH0:Number = NaN;
         var line0:int = 0;
         var data0:* = undefined;
         var link0:String = null;
         if(this.tipFun is Function)
         {
            txt0 = e.target as TextField;
            a0 = this.getAgentByTxt(txt0);
            if(Boolean(a0))
            {
               lineH0 = TextMethod.getLineHeight(txt0);
               line0 = txt0.mouseY / lineH0;
               if(txt0 != this.mouseTxt || this.mouseLine != line0)
               {
                  this.mouseTxt = txt0;
                  this.mouseLine = line0;
                  if(line0 >= 0 && line0 < a0.getLen())
                  {
                     data0 = a0.getData(line0);
                     link0 = a0.getLink(line0);
                     this.tipFun(txt0,link0,data0);
                  }
               }
            }
         }
      }
      
      private function textOut(e:MouseEvent) : void
      {
         this.mouseTxt = null;
         this.mouseLine = -1;
         Gaming.uiGroup.tipBox.hide();
      }
      
      public function clearMouseData() : void
      {
         this.mouseTxt = null;
         this.mouseLine = -1;
      }
      
      public function clearShow() : void
      {
         var txt0:TextField = null;
         for each(txt0 in this.txtArr)
         {
            txt0.htmlText = "";
         }
      }
   }
}

