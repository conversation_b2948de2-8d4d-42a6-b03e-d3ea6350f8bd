package UI.gift.yuanXiao
{
   import com.sounto.utils.ArrayMethod;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class YuanGift
   {
      private static var normalGift:GiftAddDefineGroup = new GiftAddDefineGroup();
      
      private static var goodGift:GiftAddDefineGroup = new GiftAddDefineGroup();
      
      public function YuanGift()
      {
         super();
      }
      
      public static function init() : void
      {
         if(goodGift.arr.length == 0)
         {
            goodGift.addGiftByStr("things;allBlackEquipCash;5");
            goodGift.addGiftByStr("things;yearMonkey;1");
            goodGift.addGiftByStr("things;yearPig;1");
            goodGift.addGiftByStr("things;yearHourse;1");
            goodGift.addGiftByStr("things;yearMouse;1");
            goodGift.addGiftByStr("things;yearRabbit;1");
            goodGift.addGiftByStr("things;demBall;4");
            goodGift.addGiftByStr("things;zodiacCash;1");
         }
         if(normalGift.arr.length == 0)
         {
            normalGift.addGiftByStr("base;anniCoin;1");
         }
      }
      
      public static function getGift(index0:int) : GiftAddDefine
      {
         var gift0:GiftAddDefine = null;
         init();
         var g0:GiftAddDefineGroup = null;
         if(index0 > 3 && index0 < 150)
         {
            if(index0 % 6 == 0)
            {
               g0 = goodGift;
            }
            else if(Math.random() < 0.5)
            {
               g0 = normalGift;
            }
         }
         if(Boolean(g0))
         {
            return ArrayMethod.getRandomOne(g0.arr);
         }
         return null;
      }
   }
}

