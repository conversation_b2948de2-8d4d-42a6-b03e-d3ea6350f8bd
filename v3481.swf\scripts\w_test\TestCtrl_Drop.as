package w_test
{
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipFatherDefine;
   import dataAll.equip.save.EquipSave;
   import dataAll.level.define.unit.UnitType;
   import dataAll.things.define.ThingsDefine;
   
   public class TestCtrl_Drop extends TestCtrl_Normal
   {
      public function TestCtrl_Drop()
      {
         super();
      }
      
      override public function mouseClick() : void
      {
         var x0:int = 0;
         var y0:int = 0;
         var d0:EquipDefine = null;
         var thingsD0:ThingsDefine = null;
         if(Gaming.LG.isGaming())
         {
            x0 = Gaming.gameSprite.L_game.mouseX;
            y0 = Gaming.gameSprite.L_game.mouseY;
            d0 = Gaming.defineGroup.equip.getBlackEquipDefineByLv(Gaming.PG.da.level);
            if(d0 is EquipDefine)
            {
               ++Gaming.PG.save.drop.blackChipNum;
               thingsD0 = Gaming.defineGroup.things.getDefine(d0.name);
               Gaming.dropGroup.addDropBody("things",d0.name,x0,y0);
            }
         }
      }
      
      override public function FTimer() : void
      {
      }
      
      public function dropCount() : void
      {
         var diff0:int = 0;
         var n:* = undefined;
         var unitType0:String = null;
         var unit_arr0:Array = UnitType.TYPE_ARR;
         for(var i:int = 0; i < 5; i++)
         {
            diff0 = i;
            for(n in unit_arr0)
            {
               unitType0 = unit_arr0[n];
               trace("\n\n***************************【" + unitType0 + "】【" + diff0 + "】");
               trace(this.suitDropCount(unitType0,70,diff0));
            }
         }
      }
      
      public function suitDropCount(unitType0:String, lv0:int, diff0:int) : Number
      {
         var s0:EquipSave = null;
         var f0:EquipFatherDefine = null;
         var newSuitNameArr0:Array = ["greenGale","greenGhost"];
         var num0:int = 0;
         for(var i:int = 0; i < 1000; i++)
         {
            s0 = Gaming.defineGroup.equipCreator.getSaveByBody(unitType0,lv0,"diff_" + diff0);
            f0 = s0.getFatherDefine();
            if(newSuitNameArr0.indexOf(f0.name) >= 0)
            {
               num0++;
            }
         }
         return num0 / 1000;
      }
   }
}

