package UI.city.body
{
   import com.sounto.utils.ArrayMethod;
   import dataAll._data.ConstantDefine;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   
   public class CityCon
   {
      public var ui:Sprite = new Sprite();
      
      public var mouse:Sprite = new Sprite();
      
      private var back:Sprite;
      
      private var floor:Sprite;
      
      public var things:Sprite = new Sprite();
      
      public var shadow:Sprite = new Sprite();
      
      public function CityCon()
      {
         super();
      }
      
      public function init(mapSp0:Sprite) : void
      {
         this.back = mapSp0["back"];
         this.floor = mapSp0["floor"];
         mapSp0.addChild(this.shadow);
         mapSp0.addChild(this.things);
         this.mouse.graphics.beginFill(16711680,0);
         this.mouse.graphics.drawRect(0,0,ConstantDefine.WIDTH,ConstantDefine.HEIGHT);
         mapSp0.addChild(this.mouse);
         mapSp0.addChild(this.ui);
      }
      
      public function sortLayer() : void
      {
         var mc0:DisplayObject = null;
         var sp0:Sprite = this.things;
         var arr0:Array = [];
         var num0:int = sp0.numChildren;
         for(var i:int = 0; i < num0; i++)
         {
            mc0 = sp0.getChildAt(i);
            arr0.push(mc0);
         }
         arr0.sort(this.sortLayerFun);
         for each(mc0 in arr0)
         {
            sp0.addChild(mc0);
         }
      }
      
      private function sortLayerFun(a0:DisplayObject, b0:DisplayObject) : int
      {
         if(a0.y < b0.y)
         {
            return -1;
         }
         if(a0.y > b0.y)
         {
            return 1;
         }
         return ArrayMethod.sortNumberFun(a0.x,b0.x);
      }
   }
}

