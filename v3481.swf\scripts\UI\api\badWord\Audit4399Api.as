package UI.api.badWord
{
   import UI.test.SaveTestBox;
   import com.adobe.crypto.MD5;
   import com.adobe.serialization.json.JSON2;
   import com.adobe.test.json.JSON3;
   import com.sounto.process.YesAndNoFun;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestHeader;
   import flash.net.URLRequestMethod;
   
   public class Audit4399Api
   {
      private var fun:YesAndNoFun = new YesAndNoFun();
      
      private var loader:URLLoader = new URLLoader();
      
      public function Audit4399Api()
      {
         super();
         this.loader.addEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this.loader.addEventListener(Event.COMPLETE,this.loaderCompleteHandler);
      }
      
      public function checkText(content0:String, _yesFun:Function = null, _noFun:Function = null) : *
      {
         this.fun.setFun(_yesFun,_noFun);
         var urlRequest:URLRequest = new URLRequest("//game-audit.for399.com/v1.0/bqtj/check/text.json");
         var requestTime:uint = uint(Gaming.api.save.getNowServerDate().getDateClass().getTime());
         var content:String = "bqtj/v1.0/bqtj/check/text.json" + requestTime + "********************************";
         var sign:String = MD5.hash(content);
         var headersArr0:Array = [];
         headersArr0.push(new URLRequestHeader("XApiTime",requestTime + ""));
         headersArr0.push(new URLRequestHeader("XApiSign",sign));
         urlRequest.requestHeaders = headersArr0;
         var data0:Object = new Object();
         data0.content = content0;
         data0.uid = Gaming.getUid();
         data0.forumsId = "bqtj";
         urlRequest.data = JSON3.encode(data0);
         urlRequest.method = URLRequestMethod.POST;
         urlRequest.contentType = "application/json";
         this.loader.load(urlRequest);
         SaveTestBox.addText("$secret：********************************");
         SaveTestBox.addText("$requestTime：" + requestTime);
         SaveTestBox.addText("$uri：/v1.0/bqtj/check/text.json");
         SaveTestBox.addText("$content：" + content);
         SaveTestBox.addText("$sign：" + sign);
         SaveTestBox.addText("url：" + urlRequest.url);
         SaveTestBox.addText("headers:" + JSON3.encode(urlRequest.requestHeaders));
         SaveTestBox.addText("data：" + JSON3.encode(urlRequest.data));
      }
      
      private function loaderCompleteHandler(e:Event) : void
      {
         var str0:String = null;
         var obj0:Object = null;
         var code0:int = 0;
         var message0:String = null;
         SaveTestBox.addText("返回:" + this.loader.data);
         try
         {
            str0 = this.loader.data;
            obj0 = JSON2.decode(str0);
            code0 = int(obj0.code);
            message0 = obj0.message;
            if(code0 == 100)
            {
               if(Boolean(obj0.result))
               {
                  if(obj0.result.result == 0)
                  {
                     this.fun.doYesFun(message0);
                  }
                  else
                  {
                     this.fun.doNoFun("可能包含敏感词，请重新填写。");
                  }
               }
               else
               {
                  this.fun.doNoFun("没有result");
               }
            }
            else
            {
               this.fun.doNoFun(code0 + ":" + message0);
            }
         }
         catch(e:Error)
         {
            fun.doNoFun(loader.data + "\n" + String(e));
         }
      }
      
      private function errorHandler(e:IOErrorEvent) : void
      {
         SaveTestBox.addText("errorHandler");
         this.fun.doNoFun("网络连接错误。");
      }
   }
}

