package w_test.cheating
{
   import UI.UIShow;
   import UI.count.CountCtrl;
   import UI.test.SaveTestBox;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.system.Capabilities;
   import flash.utils.getTimer;
   
   public class SystemCheating extends OneCheating
   {
      private var mc:MovieClip;
      
      private var nowS:Number = 0;
      
      private var nowInt:int = 0;
      
      private var beforeT:Number = 0;
      
      public function SystemCheating()
      {
         super();
      }
      
      public function setFrame(str0:String, v0:int) : String
      {
         Gaming.ME.stage.frameRate = v0;
         return "设置帧数：" + v0;
      }
      
      public function setToLocalTime(str0:String, v0:int) : String
      {
         Gaming.api.save.localTimeB = !Gaming.api.save.localTimeB;
         return "本地时间开关：" + Gaming.api.save.localTimeB;
      }
      
      public function newDay(str0:String, v0:int) : String
      {
         Gaming.PG.da.newDayCtrl(Gaming.api.save.getNowServerDate().getStr());
         UIShow.main();
         return "新的一天！";
      }
      
      public function newWeek(str0:String, v0:int) : String
      {
         Gaming.PG.da.newWeek(Gaming.api.save.getNowServerDate().getStr());
         UIShow.main();
         return "新的一周！";
      }
      
      public function newWeek6(str0:String, v0:int) : String
      {
         Gaming.PG.da.newWeek6(Gaming.api.save.getNowServerDate().getStr());
         UIShow.main();
         return "新的一周（周六刷新）！";
      }
      
      public function speedOnlineTime(str0:String, v0:int) : String
      {
         CountCtrl.onlineTimePer = v0;
         return "在线时间加速倍数：" + v0;
      }
      
      public function timerTest(str0:String, v0:int) : String
      {
         if(!this.mc)
         {
            this.mc = new MovieClip();
            this.mc.addEventListener(Event.ENTER_FRAME,this.testTimerEvent);
            this.beforeT = getTimer() / 1000;
         }
         return "时钟测试开始";
      }
      
      public function showFlashPlayerVer(str0:String, v0:int) : String
      {
         return "FlashPlayer 版本号：" + Capabilities.version;
      }
      
      public function setInitErrorB(str0:String, v0:int) : String
      {
         INIT.errorB = !INIT.errorB;
         return "设置错误输出开关：" + INIT.errorB;
      }
      
      private function testTimerEvent(e:Event) : void
      {
         var now0:Number = getTimer() / 1000;
         var c0:Number = now0 - this.beforeT;
         this.beforeT = now0;
         this.nowS += c0;
         if(int(this.nowS) > this.nowInt)
         {
            this.nowInt = int(this.nowS);
            SaveTestBox.addText(this.nowInt + "");
         }
      }
   }
}

