package UI.partner
{
   import UI.UIShow;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripBtnListCtrl;
   import UI.base.HaveConSprite;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import dataAll._player.more.MoreData;
   import dataAll.items.ItemsDataGroup;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.filters.ColorMatrixFilter;
   
   public class MoreBox extends HaveConSprite
   {
      protected var greenFilter:ColorMatrixFilter;
      
      protected var img:Sprite = null;
      
      private var modelBtn:NormalBtn = new NormalBtn();
      
      private var gripTag:Sprite = null;
      
      private var modelBtnSp:MovieClip;
      
      public var box:ItemsGripBox = new ItemsGripBox();
      
      public function MoreBox()
      {
         super();
         this.box.arg.init(1,6,0,2);
         this.box.evt.setWantEvent(true,false,false,true,true,true);
         this.box.imgType = "moreGrip";
         addChild(this.box);
         this.box.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.box.addEventListener(ClickEvent.ON_OVER,this.gripOver);
         this.box.addEventListener(ClickEvent.ON_OUT,this.gripOut);
         this.box.addEventListener(ClickEvent.ON_DOUBLE_CLICK,this.gripDouleClick);
      }
      
      public function setImg(img0:Sprite) : void
      {
         this.img = img0;
         addChildAt(this.img,0);
         this.x = this.img.x;
         this.y = this.img.y;
         this.img.x = 0;
         this.img.y = 0;
         this.gripTag = this.img["gripTag"];
         this.modelBtnSp = this.img["modelBtnSp"];
         addChild(this.modelBtn);
         this.modelBtn.setImg(this.modelBtnSp);
         this.modelBtn.visible = false;
         this.box.y = this.modelBtn.y;
         this.box.x = this.gripTag.x;
      }
      
      public function show() : void
      {
         this.showNoFlesh();
         this.fleshData();
      }
      
      public function showNoFlesh() : void
      {
         inCon();
         this.visible = true;
      }
      
      public function hide() : void
      {
         outCon();
         this.visible = false;
      }
      
      public function setShow(bb0:Boolean) : void
      {
         if(bb0)
         {
            this.show();
         }
         else
         {
            this.hide();
         }
      }
      
      public function fleshData() : void
      {
         var arr0:Array = Gaming.PG.da.getUIMoreDataArr();
         this.box.inData_byArr(arr0,"inData_UIMore");
         this.showChooseData(Gaming.PG.DATA.heroData);
      }
      
      public function fleshIconOnly() : void
      {
         var btn0:NormalBtn = null;
         var da0:MoreData = null;
         for each(btn0 in this.box.gripArr)
         {
            da0 = btn0.itemsData as MoreData;
            if(Boolean(da0))
            {
               btn0.setIconName(da0.getMoreUIIcon(),false);
            }
         }
      }
      
      private function showChooseData(da0:MoreData) : void
      {
         var btn0:ItemsGrid = this.box.findGripByData(da0);
         if(Boolean(btn0))
         {
            this.box.arrange();
            this.box.setChoose_byIndex(btn0.index).x = this.box.setChoose_byIndex(btn0.index).x + 10;
         }
      }
      
      public function chooseMainData() : void
      {
         var da0:MoreData = Gaming.PG.da.heroData;
         this.showChooseData(da0);
         Gaming.PG.setNowMoreData(da0);
      }
      
      public function chooseRoleName(name0:String) : Boolean
      {
         var da0:MoreData = null;
         var btn0:NormalBtn = this.box.getBtnByLabel(name0);
         if(Boolean(btn0))
         {
            if(btn0.actived)
            {
               da0 = btn0.itemsData as MoreData;
               if(Boolean(da0))
               {
                  this.showChooseData(da0);
                  Gaming.PG.setNowMoreData(da0);
                  return true;
               }
            }
         }
         return false;
      }
      
      private function gripClickOne(e:ClickEvent, doubleB0:Boolean = false) : void
      {
         var grip0:ItemsGrid = e.child as ItemsGrid;
         var da0:MoreData = e.childData as MoreData;
         var dg1:ItemsDataGroup = da0.placeType == ItemsDataGroup.PLACE_BAG ? Gaming.PG.da.moreBag : Gaming.PG.da.more;
         if(Boolean(da0.save) && !Gaming.uiGroup.askUI.visible)
         {
            this.showChooseData(da0);
            Gaming.PG.setNowMoreData(da0);
            if(doubleB0)
            {
               UIShow.showApp("wear",true);
               ItemsGripBtnListCtrl.hide();
            }
            else
            {
               if(Gaming.uiGroup.noShowBagArr.indexOf(UIShow.nowApp) >= 0)
               {
                  UIShow.reShowNowApp();
               }
               else
               {
                  UIShow.showApp("wear",true);
               }
               if(!Gaming.LG.isGaming())
               {
                  ItemsGripBtnListCtrl.show(grip0,dg1);
               }
            }
         }
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         this.gripClickOne(e);
      }
      
      private function gripDouleClick(e:ClickEvent) : void
      {
         this.gripClickOne(e,true);
      }
      
      private function gripOver(e:ClickEvent) : void
      {
         var da0:MoreData = e.childData as MoreData;
         if(!da0.save)
         {
            Gaming.uiGroup.tipBox.textTip.setText(da0.def.addMoreText);
            Gaming.uiGroup.tipBox.textTip.show();
            Gaming.uiGroup.tipBox.followMouseB = true;
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      private function gripOut(e:ClickEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
   }
}

