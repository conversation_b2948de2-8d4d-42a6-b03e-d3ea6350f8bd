package UI.api.shop
{
   import com.sounto.oldUtils.SountoCF;
   
   public class ShopBuyObject
   {
      private var CF:SountoCF = new SountoCF();
      
      public var propId:String = "";
      
      public var idx:int = 0;
      
      public var tag:String = "";
      
      private var father:IO_ShopObjectFather = null;
      
      public function ShopBuyObject()
      {
         super();
         this.count = 0;
         this.price = 0;
      }
      
      public function get count() : Number
      {
         return this.CF.getAttribute("count");
      }
      
      public function set count(v0:Number) : void
      {
         this.CF.setAttribute("count",v0);
      }
      
      public function get price() : Number
      {
         return this.CF.getAttribute("price");
      }
      
      public function set price(v0:Number) : void
      {
         this.CF.setAttribute("price",v0);
      }
      
      public function getTotalPrice() : Number
      {
         return this.count * this.price;
      }
      
      public function getFather() : IO_ShopObjectFather
      {
         return this.father;
      }
      
      public function setFather(f0:IO_ShopObjectFather) : void
      {
         this.father = f0;
      }
   }
}

