package UI.bag
{
   import UI.base.box.NormalBox;
   import UI.base.button.NormalBtn;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.must.define.MustDefine;
   import dataAll.must.define.MustSimpleThingsDefine;
   
   public class ItemsGripBox extends NormalBox
   {
      public var fatherData:ItemsDataGroup = null;
      
      public function ItemsGripBox()
      {
         super();
         evt = new GripBoxEventAddit(this);
      }
      
      public function setFatherData(dg0:ItemsDataGroup) : void
      {
         this.fatherData = dg0;
         (evt as GripBoxEventAddit).fatherData = dg0;
      }
      
      public function inData_byDataGroup(dg0:ItemsDataGroup, fleshDataB0:Boolean = true) : void
      {
         var d_arr0:Array = null;
         var n:* = undefined;
         var i:* = undefined;
         var da0:IO_ItemsData = null;
         var site0:int = 0;
         var grip0:ItemsGrid = null;
         var xx0:int = 0;
         var grip2:ItemsGrid = null;
         var lockB0:Boolean = false;
         this.clearData();
         this.setFatherData(dg0);
         if(fleshDataB0)
         {
            d_arr0 = dg0.dataArr;
            setGripNum(dg0.getSaveGroup().gripMaxNum,imgType,false);
            for(n in d_arr0)
            {
               da0 = d_arr0[n];
               site0 = int(da0.getSave().site);
               grip0 = gripArr[site0];
               if(Boolean(grip0))
               {
                  if(grip0.canInItemsB() == false)
                  {
                     xx0 = 0;
                  }
                  else
                  {
                     grip0["inData_" + dg0.dataType](da0);
                  }
               }
            }
            arrange();
            for(i in gripArr)
            {
               grip2 = gripArr[i];
               lockB0 = !dg0.getSaveGroup().getUnlockBySite(i);
               if(lockB0)
               {
                  grip2.setState("lock");
               }
            }
         }
         fleshPageBox();
      }
      
      public function refleshItemsDataNow() : void
      {
         this.inData_byDataGroup(this.fatherData);
      }
      
      public function inData_byDataGroupAndArr(dg0:ItemsDataGroup, arr0:Array) : void
      {
         this.clearData();
         this.setFatherData(dg0);
         inData_byArr(arr0,"inData_" + dg0.dataType,false);
      }
      
      public function inData_byArrAndKeep(d_arr0:Array, childInDataFunName:String, childNoDataFunName:String) : void
      {
         var n:* = undefined;
         var da0:Object = null;
         var grip0:ItemsGrid = null;
         this.clearData();
         for(n in gripArr)
         {
            da0 = d_arr0[n];
            grip0 = gripArr[n];
            if(Boolean(da0))
            {
               grip0[childInDataFunName](da0);
            }
            else
            {
               grip0[childNoDataFunName]();
            }
         }
         arrange();
         fleshPageBox();
      }
      
      public function inMustDefine(d0:MustDefine, meMoreDef0:Object = null) : Boolean
      {
         var n:int = 0;
         var td0:MustSimpleThingsDefine = null;
         var name0:String = null;
         var mustNum0:int = 0;
         var grip0:ItemsGrid = null;
         var bb2:Boolean = false;
         this.clearData();
         var arr0:Array = d0.getThingsArr();
         var bb0:Boolean = true;
         if(arr0 is Array)
         {
            setGripNum(arr0.length,imgType,false);
            n = 0;
            for each(td0 in arr0)
            {
               name0 = td0.name;
               mustNum0 = int(td0.num);
               grip0 = gripArr[n];
               bb2 = grip0.inData_thingsMust(name0,mustNum0,d0.thingsType,Gaming.PG.da,meMoreDef0);
               if(!bb2)
               {
                  bb0 = false;
               }
               n++;
            }
            arg.inData_byArr(gripArr);
         }
         else
         {
            bb0 = true;
         }
         return bb0;
      }
      
      public function doChildFun(funName:String) : void
      {
         var n:* = undefined;
         var grip0:ItemsGrid = null;
         for(n in gripArr)
         {
            grip0 = gripArr[n];
            grip0[funName]();
         }
      }
      
      public function setAllLevelTxtBySite() : void
      {
         var i:* = undefined;
         var grip2:ItemsGrid = null;
         for(i in gripArr)
         {
            grip2 = gripArr[i];
            grip2.setLevelText(String(grip2.index + 1));
         }
      }
      
      public function findGripByData(data0:Object) : ItemsGrid
      {
         var i:* = undefined;
         var grip2:ItemsGrid = null;
         for(i in gripArr)
         {
            grip2 = gripArr[i];
            if(grip2.itemsData == data0)
            {
               return grip2;
            }
         }
         return null;
      }
      
      override protected function getNewGrip() : NormalBtn
      {
         return new ItemsGrid();
      }
      
      override public function clearData() : void
      {
         var n:* = undefined;
         var grip0:ItemsGrid = null;
         for(n in gripArr)
         {
            grip0 = gripArr[n];
            grip0.clearData();
         }
         (evt as GripBoxEventAddit).fatherData = null;
      }
      
      override public function clear() : void
      {
         this.clearData();
         super.clear();
      }
   }
}

