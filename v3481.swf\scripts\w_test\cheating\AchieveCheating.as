package w_test.cheating
{
   import UI.test.SaveTestBox;
   import dataAll._app.achieve.AchieveData;
   import dataAll._app.edit.card.BossCardCreator;
   import dataAll._app.edit.card.BossCardData;
   import dataAll._app.edit.card.BossCardSave;
   import dataAll.body.define.NormalBodyDefine;
   
   public class AchieveCheating extends OneCheating
   {
      public function AchieveCheating()
      {
         super();
      }
      
      public function reachieve(str0:String, v0:int) : String
      {
         var da0:AchieveData = null;
         var arr0:Array = Gaming.PG.da.achieve.getArrByFather(Gaming.uiGroup.achieveUI.listBoard.nowChildType);
         for each(da0 in arr0)
         {
            da0.clear();
         }
         Gaming.PG.da.achieve.fleshAffterComplete();
         return "清空了" + Gaming.uiGroup.achieveUI.listBoard.nowChildType + "列表的状态。";
      }
      
      public function completeAllAchieve(str0:String, v0:int) : String
      {
         Gaming.PG.da.achieve.completeAll();
         return "完成所有成就！";
      }
      
      public function createBosscard(str0:String, v0:int) : String
      {
         var s0:BossCardSave = null;
         var sarr0:Array = str0.split(" ");
         var cn0:String = sarr0[0];
         var star0:int = int(sarr0[1]);
         var d0:NormalBodyDefine = Gaming.defineGroup.body.getCnDefine(cn0);
         if(Boolean(d0) && star0 > 0)
         {
            s0 = BossCardCreator.getSaveStar(star0,null,d0.name);
            SaveTestBox.addText(s0.getXMLStr());
            return "已输入到SaveTestBox文本框";
         }
         if(Boolean(d0))
         {
            return "星级太低";
         }
         return "找不到单位";
      }
      
      public function addBosscardByXml(str0:String, v0:int) : String
      {
         var xml0:XML = null;
         var s0:BossCardSave = null;
         var da0:BossCardData = null;
         if(str0 != "")
         {
            try
            {
               xml0 = new XML(str0);
               s0 = new BossCardSave();
               s0.inData_byXML(xml0);
               da0 = Gaming.PG.da.bossCard.addSave(s0) as BossCardData;
               return "添加魂卡:" + da0.cnName;
            }
            catch(e:Error)
            {
               return "XML格式错误";
            }
         }
         else
         {
            return "";
         }
      }
   }
}

