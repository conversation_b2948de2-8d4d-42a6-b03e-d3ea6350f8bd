package UI.pet.transfer
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.grid.NormalGridIcon;
   import UI.base.must.NormalMustBox;
   import UI.pet.PetUI;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.pet.PetData;
   import dataAll.pet.gene.define.GeneDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class PetTransferBoard extends NormalUI
   {
      private var beforeTag:Sprite;
      
      private var beforeIcon:NormalGridIcon = new NormalGridIcon();
      
      private var afterTag:Sprite;
      
      private var afterIcon:NormalGridIcon = new NormalGridIcon();
      
      private var mustTag:Sprite;
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var gripTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var gripBox:ItemsGripBox = new ItemsGripBox();
      
      private var btnSp:MovieClip;
      
      private var allBtnSp:MovieClip;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var noTxt:TextField;
      
      private var nowChooseData:PetData;
      
      public function PetTransferBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["noTxt","beforeTag","afterTag","mustTag","btnSp","gripTag","pageTag"];
         super.setImg(img0);
         this.beforeTag.addChild(this.beforeIcon);
         this.afterTag.addChild(this.afterIcon);
         this.beforeTag.scaleX = -1;
         addChild(this.mustBox);
         this.mustBox.setNormalImg();
         NormalUICtrl.setTag(this.mustBox,this.mustTag);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("转移");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         addChild(this.gripBox);
         NormalUICtrl.setTag(this.gripBox,this.gripTag);
         this.gripBox.imgType = "equipGrip";
         this.gripBox.arg.init(2,5,3,3);
         this.gripBox.evt.setWantEvent(true,false,false,true,true);
         this.gripBox.pageBox.setToNormalBtn();
         this.gripBox.pageBox.setXY_bySp(this.pageTag,this.gripBox);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.listClick);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.gripBox);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      private function fleshList(da0:PetData) : void
      {
         var arr0:Array = null;
         da0 = PetUI.getNowData();
         if(Boolean(da0))
         {
            arr0 = Gaming.PG.da.pet.getCanTransferArrByDa(da0);
            this.gripBox.inData_byArr(arr0,"inData_smallPet");
            this.noTxt.visible = arr0.length == 0;
            this.gripBox.setChooseByItemsData(this.nowChooseData);
         }
         else
         {
            this.gripBox.clearAllData();
         }
      }
      
      private function listClick(e:ClickEvent) : void
      {
         this.nowChooseData = e.childData as PetData;
         this.fleshData();
      }
      
      private function fleshData() : void
      {
         var da0:PetData = PetUI.getNowData();
         this.fleshList(da0);
         if(!this.gripBox.getBtnByItemsData(this.nowChooseData))
         {
            this.nowChooseData = null;
         }
         this.showData(da0,this.nowChooseData);
      }
      
      private function showData(da0:PetData, secDa0:PetData) : void
      {
         this.showOneDataImage(da0,this.beforeIcon);
         this.showOneDataImage(secDa0,this.afterIcon);
         this.showMust(da0,secDa0);
      }
      
      private function showMust(da0:PetData, secDa0:PetData) : void
      {
         var must_d0:MustDefine = null;
         var bb0:Boolean = false;
         if(Boolean(da0) && Boolean(secDa0))
         {
            must_d0 = new MustDefine();
            bb0 = this.mustBox.inData(must_d0,da0.base.save.level,"尸宠");
            this.btn.actived = bb0;
         }
         else
         {
            this.clearMust();
         }
      }
      
      private function clearMust() : void
      {
         this.btn.actived = false;
         this.mustBox.setShowState(false);
      }
      
      private function showOneDataImage(da0:PetData, icon0:NormalGridIcon) : void
      {
         var d0:GeneDefine = null;
         var sx0:Number = NaN;
         var sy0:Number = NaN;
         icon0.scaleX = 1;
         icon0.scaleY = 1;
         if(Boolean(da0))
         {
            d0 = da0.getGeneDefine();
            icon0.setIconName(d0.getBodyImgUrl());
            sx0 = 156 / icon0.width;
            sy0 = 200 / icon0.height;
            sx0 = sx0 > sy0 ? sy0 : sx0;
            if(sx0 > 1)
            {
               sx0 = 1;
            }
            icon0.scaleX = sx0;
            icon0.scaleY = sx0;
         }
         else
         {
            icon0.clearData();
         }
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var must_d0:MustDefine = null;
         var da0:PetData = PetUI.getNowData();
         if(Boolean(da0) && Boolean(this.nowChooseData))
         {
            must_d0 = new MustDefine();
            PlayerMustCtrl.deductMust(must_d0,this.afterGrow);
         }
      }
      
      private function afterGrow() : void
      {
         var da0:PetData = PetUI.getNowData();
         da0.transferOther(this.nowChooseData);
         Gaming.uiGroup.alertBox.showSuccess("转移成功！");
         Gaming.uiGroup.petUI.fleshData();
      }
   }
}

