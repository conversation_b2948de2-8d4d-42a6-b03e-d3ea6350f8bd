package UI.vehicle
{
   import UI.bag.ItemsGrid;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.must.NormalMustBox;
   import dataAll.equip.vehicle.VehicleData;
   import dataAll.equip.vehicle.VehicleDataCreator;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class VehicleEvolutionBoard extends NormalUI
   {
      public var vehicleUI:VehicleUI;
      
      private var coverSp:Sprite;
      
      private var mustTag:Sprite;
      
      private var btnSp:MovieClip;
      
      private var beforeSp:Sprite;
      
      private var afterSp:Sprite;
      
      private var beforeBox:VehicleUpgradeOneBox = new VehicleUpgradeOneBox();
      
      private var afterBox:VehicleUpgradeOneBox = new VehicleUpgradeOneBox();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var btn:NormalBtn = new NormalBtn();
      
      public function VehicleEvolutionBoard()
      {
         super();
      }
      
      public function get nowData() : VehicleData
      {
         return Gaming.uiGroup.vehicleUI.nowData;
      }
      
      public function set nowData(v0:VehicleData) : void
      {
         Gaming.uiGroup.vehicleUI.nowData = v0;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["coverSp","mustTag","btnSp","beforeSp","afterSp"];
         super.setImg(img0);
         addChild(this.beforeBox);
         this.beforeBox.setImg(this.beforeSp);
         addChild(this.afterBox);
         this.afterBox.setImg(this.afterSp);
         this.afterBox.evoTipB = true;
         addChild(this.mustBox);
         this.mustBox.setLongImg();
         this.mustBox.x = this.mustTag.x;
         this.mustBox.y = this.mustTag.y;
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.btn.setName("进阶");
         this.btn.actived = false;
         this.setCoverText("");
         addChild(this.coverSp);
         this.clearMust();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      public function outLoginEvent() : void
      {
      }
      
      protected function setCoverText(str0:String) : void
      {
         if(str0 == "")
         {
            this.coverSp.visible = false;
         }
         else
         {
            this.coverSp.visible = true;
            this.coverSp["txt"].text = str0;
         }
      }
      
      private function fleshData() : void
      {
         this.showData(this.nowData);
      }
      
      public function setNowData(da0:VehicleData) : void
      {
         this.nowData = da0;
      }
      
      public function gripClick(e:ClickEvent) : void
      {
         if(visible)
         {
            this.showData(e.childData as VehicleData);
         }
      }
      
      private function showData(da0:VehicleData) : void
      {
         var afterDa0:VehicleData = null;
         var must_d0:MustDefine = null;
         var bb0:Boolean = false;
         this.nowData = da0;
         var grip0:ItemsGrid = this.vehicleUI.itemsBox.findGripByData(this.nowData);
         if(grip0 is ItemsGrid)
         {
            this.setCoverText("");
            this.vehicleUI.itemsBox.setChoose_byIndex(grip0.index);
            afterDa0 = da0.getEvolutionData();
            this.beforeBox.inEvoData(da0,"进化前：");
            this.afterBox.inEvoData(afterDa0,"进化后：");
            if(Boolean(afterDa0))
            {
               must_d0 = VehicleDataCreator.getEvolutionMust(da0);
               bb0 = this.mustBox.inData(must_d0,da0.normalPlayerData.level);
               this.btn.actived = bb0 && !da0.getVehicleSave().isDiggersComposeB();
            }
            else
            {
               this.clearMust();
            }
         }
         else
         {
            this.setCoverText("请在右边选择你要进化的载具");
            this.vehicleUI.itemsBox.setChoose_byIndex(-1);
            this.clearMust();
         }
      }
      
      private function clearMust() : void
      {
         this.btn.actived = false;
         this.mustBox.setShowState(false);
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var must_d0:MustDefine = VehicleDataCreator.getEvolutionMust(this.nowData);
         PlayerMustCtrl.deductMust(must_d0,this.affterBtnClick);
      }
      
      private function affterBtnClick() : void
      {
         var da0:VehicleData = this.nowData;
         var afterDa0:VehicleData = da0.getEvolutionData();
         da0.changeToOneData(afterDa0);
         Gaming.uiGroup.alertBox.showSuccess("进化成功！");
         ++Gaming.PG.save.headCount.vehicleEvoNum;
         this.vehicleUI.fleshBag();
         this.fleshData();
      }
   }
}

