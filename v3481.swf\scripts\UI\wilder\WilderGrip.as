package UI.wilder
{
   import UI.bag.ItemsGrid;
   import UI.base.grid.NormalGridIcon;
   import dataAll._app.wilder.WilderData;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.MovieClip;
   import flash.geom.ColorTransform;
   
   public class WilderGrip extends ItemsGrid
   {
      private static var NO_CF:ColorTransform = new ColorTransform();
      
      private static var GRAY_CF:ColorTransform = new ColorTransform(0.3,0.3,0.3);
      
      private static var BACK_GRAY_CF:ColorTransform = new ColorTransform(0.3,0.3,0.3);
      
      private var backIcon:NormalGridIcon = new NormalGridIcon();
      
      private var giftIconArr:Array = null;
      
      private var overTimeValue:Number = 0;
      
      public function WilderGrip()
      {
         super();
         mouseIconEffectB = false;
         activedAndGray = true;
      }
      
      override public function setImg(img0:MovieClip) : void
      {
         super.setImg(img0);
         if(Boolean(iconCon))
         {
            iconCon.addChildAt(this.backIcon,0);
         }
      }
      
      public function inData_wilderOrFlesh(da0:WilderData) : void
      {
         if(Boolean(itemsData))
         {
            this.fleshData();
         }
         else
         {
            this.inData_wilder(da0);
         }
      }
      
      public function inData_wilder(da0:WilderData, iconEffectB0:Boolean = true) : void
      {
         itemsData = da0;
         label = da0.def.name;
         setName(da0.def.cnName);
         setIconName(da0.def.getIconUrl());
         this.backIcon.setIconName(da0.def.getBackUrl());
         this.fleshData(iconEffectB0);
         if(iconEffectB0)
         {
            this.showGift(da0);
         }
      }
      
      private function showGift(da0:WilderData) : void
      {
         var max0:int = 0;
         var arr0:Array = null;
         var g0:GiftAddDefineGroup = null;
         var scale0:Number = NaN;
         var gap0:Number = NaN;
         var len0:int = 0;
         var num0:int = 0;
         var d0:GiftAddDefine = null;
         var icon0:NormalGridIcon = null;
         if(this.giftIconArr == null)
         {
            max0 = 3;
            arr0 = [];
            g0 = da0.getUIDropGiftG();
            scale0 = 1;
            gap0 = 7;
            if(g0.arr.length > 1)
            {
               scale0 = 0.5;
               gap0 = 0;
            }
            len0 = int(g0.arr.length);
            if(len0 <= 0)
            {
               setShopBtnBackMc("");
            }
            else
            {
               if(len0 > max0)
               {
                  len0 = max0;
               }
               setShopBtnBackMc(len0);
            }
            num0 = 0;
            for each(d0 in g0.arr)
            {
               num0++;
               if(num0 <= max0)
               {
                  icon0 = new NormalGridIcon();
                  shopBtnBackMc.addChild(icon0);
                  icon0.x = gap0 + (num0 - 1) * 56 * scale0;
                  icon0.y = -gap0;
                  icon0.setIconName(d0.getIconUrl());
                  icon0.scaleX = scale0;
                  icon0.scaleY = scale0;
                  arr0.push(icon0);
               }
            }
            this.giftIconArr = arr0;
         }
      }
      
      override public function clearShow() : void
      {
         super.clearShow();
         this.backIcon.clearData();
      }
      
      public function getOverTimeValue() : Number
      {
         return this.overTimeValue;
      }
      
      public function fleshData(iconEffectB0:Boolean = true) : void
      {
         var da0:WilderData = itemsData as WilderData;
         var state0:int = da0.getTimeState();
         var grayB0:Boolean = false;
         var canNum0:int = da0.getCanChallangeNum();
         if(state0 == 0)
         {
            setName(da0.def.cnName);
            grayB0 = false;
            actived = true;
            this.overTimeValue = da0.getOverTimeValue();
         }
         else
         {
            actived = true;
            this.overTimeValue = 0;
            if(canNum0 > 0)
            {
               grayB0 = false;
            }
            else
            {
               grayB0 = true;
            }
            if(state0 == -1)
            {
               setName(da0.getOpenStr());
            }
            else
            {
               setName(da0.def.cnName);
            }
         }
         if(canNum0 > 0 && iconEffectB0)
         {
            setNumText(canNum0 + "");
         }
         else
         {
            setNumText("");
         }
         if(iconEffectB0)
         {
            if(grayB0)
            {
               icon.transform.colorTransform = GRAY_CF;
               this.backIcon.transform.colorTransform = BACK_GRAY_CF;
            }
            else
            {
               icon.transform.colorTransform = NO_CF;
               this.backIcon.transform.colorTransform = NO_CF;
            }
         }
      }
      
      override public function clearImg() : void
      {
         var icon0:NormalGridIcon = null;
         super.clear();
         if(Boolean(this.giftIconArr))
         {
            for each(icon0 in this.giftIconArr)
            {
            }
         }
      }
   }
}

