package w_test
{
   import dataAll.bullet.BulletDefine;
   import dataAll.level.define.event.LevelEventDefine;
   import flash.display.Graphics;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.ai.Normal_AI;
   import gameAll.body.motion.NormalGroundMotion;
   import gameAll.bullet.BulletBody;
   
   public class TestCtrl_AI extends TestCtrl_Normal
   {
      public static var mode:String = "";
      
      public static const enemyHurt:String = "enemyHurt";
      
      public static const weHurt:String = "weHurt";
      
      public static const bulletHit:String = "bulletHit";
      
      public static const enemyFollow:String = "enemyFollow";
      
      public var tempPoint:Point = null;
      
      public function TestCtrl_AI()
      {
         super();
      }
      
      override public function mouseClick() : void
      {
      }
      
      public function getSpiderShape() : *
      {
         if(Gaming.gameSprite.L_gold.numChildren == 0 && Gaming.LG.state == "ing")
         {
            Gaming.gameSprite.L_gold.addChildAt(Gaming.sceneGroup.map.spider.getShape(),0);
         }
      }
      
      override public function FTimer() : void
      {
         if(mode == enemyHurt)
         {
            this.showHurtRect(Gaming.BG.ENEMY_ARR);
         }
         else if(mode == weHurt)
         {
            this.showHurtRect(Gaming.BG.WE_ARR);
         }
         else if(mode == bulletHit)
         {
            this.showBulletHitRect();
         }
         else if(mode == enemyFollow)
         {
            this.showBodyFollowPoint();
         }
      }
      
      protected function showHurtRect(arr2:Array) : void
      {
         var n:* = undefined;
         var b0:IO_NormalBody = null;
         var arr0:Array = null;
         var rect0:Rectangle = null;
         var gr0:Graphics = Gaming.gameSprite.L_testShape.graphics;
         gr0.clear();
         gr0.beginFill(16711680,0.5);
         for(n in arr2)
         {
            b0 = arr2[n];
            if(Boolean(b0))
            {
               arr0 = b0.getAttack().nowHurtRectArr;
               for(n in arr0)
               {
                  rect0 = arr0[n];
                  gr0.drawRect(rect0.x,rect0.y,rect0.width,rect0.height);
               }
            }
         }
      }
      
      protected function showBulletHitRect() : void
      {
         var arr0:Array = null;
         var b0:BulletBody = null;
         var w0:int = 0;
         var x0:int = 0;
         var y0:int = 0;
         var gr0:Graphics = Gaming.gameSprite.L_testShape.graphics;
         gr0.clear();
         if(Gaming.LG.isGaming())
         {
            arr0 = Gaming.bulletGroup.enemyBullet_arr;
            for each(b0 in arr0)
            {
               if(b0.define.hitType == BulletDefine.RECT)
               {
                  w0 = b0.define.bulletWidth;
                  x0 = b0.mot.x;
                  y0 = b0.mot.y;
                  gr0.beginFill(16711680,0.5);
                  gr0.drawRect(x0 - w0,y0 - w0,w0 * 2,w0 * 2);
               }
            }
         }
      }
      
      protected function showVehicle() : void
      {
         var b0:IO_NormalBody = null;
         var mot0:NormalGroundMotion = null;
         var gr0:Graphics = Gaming.gameSprite.L_testShape.graphics;
         gr0.clear();
         if(Gaming.LG.isGaming())
         {
            b0 = Gaming.PG.ctrlHero.vehicleCtrl.vehicleBody;
            if(Boolean(b0))
            {
               mot0 = b0.getMot();
               gr0.beginFill(16711680,0.5);
               gr0.drawCircle(mot0.x,mot0.y,4);
               gr0.beginFill(65280,0.5);
               gr0.drawCircle(mot0.mx,mot0.my,4);
            }
         }
      }
      
      protected function showBodyFollowPoint() : void
      {
         var b0:IO_NormalBody = null;
         var ai0:Normal_AI = null;
         var gr0:Graphics = Gaming.gameSprite.L_testShape.graphics;
         gr0.clear();
         if(Gaming.LG.isGaming())
         {
            b0 = Gaming.BG.ENEMY_ARR[0];
            if(Boolean(b0))
            {
               ai0 = b0.getAi();
               gr0.beginFill(16711680,0.5);
               gr0.drawCircle(ai0.followAI.mx,ai0.followAI.my,40);
            }
         }
      }
   }
}

