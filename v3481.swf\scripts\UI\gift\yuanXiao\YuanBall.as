package UI.gift.yuanXiao
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   
   public class YuanBall extends YuanMotion
   {
      private var img:MovieClip;
      
      private var vx:Number = 0;
      
      private var vy:Number = 0;
      
      private var g:Number = 2;
      
      private var floorB:Boolean = false;
      
      private var accTime:Number = 0;
      
      public function YuanBall()
      {
         super();
      }
      
      public function init(con0:Sprite) : void
      {
         if(<PERSON><PERSON><PERSON>(this.img))
         {
            INIT.showError("YuanBall已存在img");
         }
         this.img = Gaming.swfLoaderManager.getResourceFull("YuanXiaoUI/ball");
         this.img.stop();
         con0.addChildAt(this.img,0);
      }
      
      public function initState() : void
      {
         this.vx = 0;
         this.vy = 0;
         this.floorB = false;
         this.accTime = 0;
      }
      
      override public function set x(value:Number) : void
      {
         super.x = value;
         if(Boolean(this.img))
         {
            this.img.x = value;
         }
      }
      
      override public function set y(value:Number) : void
      {
         super.y = value;
         if(<PERSON><PERSON><PERSON>(this.img))
         {
            this.img.y = value;
         }
      }
      
      public function removeMe() : void
      {
         if(<PERSON><PERSON><PERSON>(this.img))
         {
            this.img.stop();
            this.img.parent.removeChild(this.img);
            this.img = null;
         }
      }
      
      public function addAccTime() : void
      {
         this.accTime += 0.03333333333333333;
      }
      
      private function getRangeAcc() : Number
      {
         var v0:Number = this.accTime + 1;
         if(v0 > 2.5)
         {
            v0 = 2.5;
         }
         return v0;
      }
      
      public function getArrowVisible() : Boolean
      {
         return this.accTime > 0;
      }
      
      public function getArrawLen() : int
      {
         return this.getRangeAcc() * 30;
      }
      
      public function getShootRa() : Number
      {
         return -0.25 * Math.PI;
      }
      
      public function shoot() : void
      {
         this.img.gotoAndStop("stop");
         this.vx = this.getRangeAcc() * 10;
         this.vy = Math.tan(this.getShootRa()) * this.vx;
         this.floorB = false;
         this.accTime = 0;
      }
      
      public function motionCount() : void
      {
         if(_y > 0)
         {
            _y = 0;
            this.vx = 0;
            this.vy = 0;
            this.floorB = true;
         }
         else
         {
            this.vy += this.g;
         }
         _x += this.vx;
         _y += this.vy;
         this.x = _x;
         this.y = _y;
      }
      
      public function isFloorB() : Boolean
      {
         return this.floorB;
      }
      
      public function hitEvent() : void
      {
         this.vx = 0;
         this.vy = 0;
         this.img.gotoAndPlay("hit");
      }
   }
}

