package w_test
{
   import UI.bag.BagUI;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.bag.WearUI;
   import com.common.net.SaveImage;
   import dataAll.arms.ArmsData;
   import dataAll.arms.ArmsDataGroup;
   import dataAll.arms.creator.GiftArmsDataCreator;
   import dataAll.arms.creator.GunImage;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.arms.define.ArmsType;
   import dataAll.arms.define.GunPart;
   import dataAll.arms.save.ArmsSave;
   import dataAll.equip.EquipData;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.creator.GiftEquipDataCreator;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipFatherDefine;
   import dataAll.equip.save.EquipSave;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.items.save.ItemsSave;
   import dataAll.level.define.unit.UnitType;
   import dataAll.pet.gene.GeneDataGroup;
   import dataAll.pet.gene.save.GeneSave;
   import dataAll.things.define.ThingsDefine;
   import flash.display.Bitmap;
   import flash.display.Sprite;
   import flash.events.KeyboardEvent;
   import flash.ui.Keyboard;
   import gameAll.arms.GameArmsCtrl;
   import w_test.count.TestCount_Items;
   
   public class TestCtrl_Arms extends TestCtrl_Normal
   {
      public var sp0:Sprite = new Sprite();
      
      public function TestCtrl_Arms()
      {
         super();
      }
      
      override public function keyDown(e:KeyboardEvent) : void
      {
         var armsDa0:ArmsData = null;
         var armsImg0:GunImage = null;
         if(!Gaming.PG.da)
         {
            return;
         }
         var bagUI:BagUI = Gaming.uiGroup.bagUI;
         var wearUI:WearUI = Gaming.uiGroup.wearUI;
         var arms_dg0:ArmsDataGroup = Gaming.PG.da.armsBag;
         var equip_dg0:EquipDataGroup = Gaming.PG.da.equipBag;
         var gene_dg0:GeneDataGroup = Gaming.PG.da.geneBag;
         if(!bagUI.visible)
         {
            return;
         }
         if(e.keyCode == Keyboard.M)
         {
            wearUI.visible = !wearUI.visible;
            bagUI.visible = !bagUI.visible;
         }
         else if(e.keyCode == Keyboard.N)
         {
            if(bagUI.equipBox.visible)
            {
               this.addSuit();
            }
         }
         else if(e.keyCode == Keyboard.J)
         {
            if(bagUI.armsBox.visible)
            {
               if(arms_dg0.getSpaceSiteNum() == 0)
               {
                  arms_dg0.removeData(arms_dg0.dataArr[0]);
               }
               this.addRandomArms();
            }
            else if(bagUI.equipBox.visible)
            {
               if(equip_dg0.getSpaceSiteNum() == 0)
               {
                  equip_dg0.removeData(equip_dg0.dataArr[0]);
               }
               this.addRandomEquip();
            }
            else if(bagUI.geneBox.visible)
            {
               if(gene_dg0.getSpaceSiteNum() == 0)
               {
                  gene_dg0.removeData(gene_dg0.dataArr[0]);
               }
               this.addRandomGene();
            }
         }
         else if(e.keyCode == Keyboard.L)
         {
            if(bagUI.armsBox.visible && arms_dg0.dataArr.length > 0)
            {
               arms_dg0.removeData(arms_dg0.dataArr[0]);
            }
            else if(bagUI.equipBox.visible && equip_dg0.dataArr.length > 0)
            {
               equip_dg0.removeData(equip_dg0.dataArr[0]);
            }
         }
         else if(e.keyCode == Keyboard.K)
         {
            armsDa0 = Gaming.PG.da.armsBag.getDataBySite(0) as ArmsData;
            if(Boolean(armsDa0))
            {
               armsImg0 = Gaming.gunImageManager.getImage(armsDa0.save.getImgLabel());
               if(Boolean(armsImg0.iconBmp))
               {
                  SaveImage.SaveBmp(armsImg0.iconBmp,armsDa0.cnName,true);
               }
            }
         }
         wearUI.fleshAllBox();
         bagUI.fleshAllBox();
      }
      
      public function addSuit() : void
      {
         var da0:EquipData = null;
         var f0:EquipFatherDefine = null;
         var grip0:ItemsGrid = ItemsGripTipCtrl.nowOverGrip as ItemsGrid;
         if(grip0 is ItemsGrid)
         {
            da0 = grip0.itemsData as EquipData;
            if(da0 is EquipData)
            {
               f0 = da0.save.getFatherDefine();
               this.addOneSuit(f0,null);
            }
         }
      }
      
      public function addAllSuit() : void
      {
         var n:* = undefined;
         var f0:EquipFatherDefine = null;
         var proObj2:Object = null;
         var obj0:Object = Gaming.defineGroup.equip.fatherObj;
         var proObj0:Object = null;
         for(n in obj0)
         {
            f0 = obj0[n];
            if(f0.haveSuitB)
            {
               proObj2 = this.addOneSuit(f0,proObj0);
               if(!proObj0)
               {
                  proObj0 = proObj2;
               }
            }
         }
      }
      
      public function addSuitByName(name0:String) : void
      {
         var f0:EquipFatherDefine = Gaming.defineGroup.equip.getFatherDefine(name0);
         this.addOneSuit(f0,null);
      }
      
      private function addOneSuit(f0:EquipFatherDefine, proObj0:Object) : Object
      {
         var n:* = undefined;
         var d0:EquipDefine = null;
         var s0:EquipSave = null;
         var newProObj0:Object = {};
         for(n in f0.partObj)
         {
            d0 = f0.partObj[n];
            s0 = Gaming.defineGroup.equipCreator.getSuperSave("red",Gaming.PG.da.level,d0.name);
            s0.setImgName(d0.name);
            s0.cnName = d0.cnName;
            newProObj0[n] = s0.getTrueObj();
            Gaming.PG.da.equipBag.addSave(s0);
         }
         return newProObj0;
      }
      
      public function addRandomEquip() : void
      {
         var dg0:EquipDataGroup = Gaming.PG.da.equipBag;
         var s0:EquipSave = Gaming.defineGroup.equipCreator.getBlackSaveByLv(Gaming.PG.da.level);
         dg0.addSave(s0);
      }
      
      public function addRandomArms() : void
      {
         var n:* = undefined;
         var s0:ArmsSave = null;
         var dg0:ArmsDataGroup = Gaming.PG.da.armsBag;
         var c_arr0:Array = EquipColor.TYPE_ARR.concat([]);
         c_arr0.pop();
         c_arr0 = [c_arr0[int(Math.random() * c_arr0.length)]];
         for(n in c_arr0)
         {
            s0 = Gaming.defineGroup.armsCreator.getSave(c_arr0[n],Gaming.PG.da.level,ArmsType.TYPE_ARR[int(Math.random() * 5)],"diff_4");
            GameArmsCtrl.addArmsSaveResoure(s0);
            dg0.addSave(s0);
         }
      }
      
      public function initArmsAndEquip() : void
      {
         var s0:ArmsSave = null;
         var da0:ArmsData = null;
         var dg0:ArmsDataGroup = Gaming.PG.da.arms;
         if(dg0.dataArr.length == 0)
         {
            s0 = Gaming.defineGroup.armsCreator.getSave_byArmsRangeName("firstRifle",1,EquipColor.WHITE,"diff_0");
            GameArmsCtrl.addArmsSaveResoure(s0);
            da0 = dg0.addSave(s0) as ArmsData;
            Gaming.PG.changeEquip();
            Gaming.PG.changeArms();
            if(isNaN(da0.hurtRatio))
            {
               INIT.showError("武器dps出现错误！");
            }
         }
      }
      
      public function addRandomGene() : void
      {
         var colorArr0:Array = ["green","blue","purple","orange","red"];
         var color0:String = colorArr0[int(Math.random() * colorArr0.length)];
         var s0:GeneSave = Gaming.defineGroup.geneCreator.getSave(color0,Gaming.PG.da.level,Gaming.defineGroup.gene.getRandomGene().name);
         Gaming.PG.da.geneBag.addSave(s0);
      }
      
      override public function mouseClick() : void
      {
      }
      
      public function getRandomImageLabel_byDefine() : GunImage
      {
         var arr0:Array = Gaming.defineGroup.bullet.rangeTypeObj["rocket"];
         var d0:ArmsRangeDefine = arr0[int(arr0.length * Math.random())];
         var label0:String = d0.getArmsSave("blue").getImgLabel();
         return Gaming.gunImageManager.addImage(label0);
      }
      
      public function getRandomImageLabel(father0:String, haveGlassB:Boolean = false, haveStockB:Boolean = true) : String
      {
         var i:* = undefined;
         var part_name0:String = null;
         var label0:String = null;
         var p_arr0:Array = GunPart.ARR;
         var name0:String = "";
         for(i in p_arr0)
         {
            part_name0 = p_arr0[i];
            label0 = father0 + "$" + part_name0;
            if(!haveGlassB && part_name0 == "glass")
            {
               label0 = "0";
            }
            if(!haveStockB && part_name0 == "stock")
            {
               label0 = "0";
            }
            if(part_name0 == "texture")
            {
               label0 = "texture$texture5";
            }
            if(i == 0)
            {
               name0 = label0;
            }
            else
            {
               name0 += "_" + label0;
            }
         }
         return name0;
      }
      
      public function dropCount() : void
      {
         var diff0:int = 0;
         var n:* = undefined;
         var unitType0:String = null;
         var unit_arr0:Array = UnitType.TYPE_ARR;
         for(var i:int = 0; i < 5; i++)
         {
            diff0 = i;
            for(n in unit_arr0)
            {
               unitType0 = unit_arr0[n];
               trace("\n\n***************************【" + unitType0 + "】【" + diff0 + "】");
               trace(this.oneDropCount(unitType0,54,diff0,"armsCreator"));
            }
         }
      }
      
      private function oneDropCount(unitType0:String, lv0:int, diff0:int, mType0:String) : String
      {
         var n:* = undefined;
         var s0:ItemsSave = null;
         var c0:TestCount_Items = null;
         var c2:TestCount_Items = null;
         var obj:Object = this.getCountObj();
         for(var i:int = 0; i < 1000; i++)
         {
            s0 = Gaming.defineGroup[mType0].getSaveByBody(unitType0,lv0,"diff_" + diff0);
            c0 = obj[s0.color];
            c0.inData(s0);
         }
         var str0:String = "";
         var type_arr0:Array = EquipColor.TYPE_ARR;
         for(n in type_arr0)
         {
            c2 = obj[type_arr0[n]];
            str0 += "\n----------------------------------";
            str0 += "\n【" + type_arr0[n] + "】--- 总共：" + c2.num;
            str0 += "\n" + c2.getLvString();
         }
         return str0;
      }
      
      public function giftCount() : void
      {
         var n:* = undefined;
         var name0:String = null;
         var nameArr0:Array = ["equipSuperBox","armsSuperBox","rifleSuperBox","sniperSuperBox","shotgunSuperBox","pistolSuperBox","roketSuperBox"];
         for(n in nameArr0)
         {
            name0 = nameArr0[n];
         }
      }
      
      private function oneGiftCount(name0:String) : String
      {
         var n:* = undefined;
         var s0:ItemsSave = null;
         var c0:TestCount_Items = null;
         var c2:TestCount_Items = null;
         var d0:ThingsDefine = Gaming.defineGroup.things.getDefine(name0);
         var gift_d0:GiftAddDefine = d0.giftD.arr[0];
         var obj:Object = this.getCountObj();
         for(var i:int = 0; i < 1000; i++)
         {
            if(name0 == "equipSuperBox")
            {
               s0 = GiftEquipDataCreator.getEquipSave(gift_d0);
            }
            else
            {
               s0 = GiftArmsDataCreator.getArmsSave(gift_d0);
            }
            c0 = obj[s0.color];
            c0.inData(s0);
         }
         var str0:String = "";
         var type_arr0:Array = EquipColor.TYPE_ARR;
         for(n in type_arr0)
         {
            c2 = obj[type_arr0[n]];
            str0 += "\n----------------------------------";
            str0 += "\n【" + type_arr0[n] + "】--- 总共：" + c2.num;
            str0 += "\n" + c2.getLvString();
         }
         return str0;
      }
      
      private function getCountObj() : Object
      {
         var n:* = null;
         var obj0:Object = {};
         for each(n in EquipColor.TYPE_ARR)
         {
            obj0[n] = new TestCount_Items();
         }
         return obj0;
      }
   }
}

