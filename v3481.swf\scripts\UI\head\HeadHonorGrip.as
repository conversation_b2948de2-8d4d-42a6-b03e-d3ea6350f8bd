package UI.head
{
   import UI.base.font.FontDeal;
   import dataAll._app.head.define.HeadHonorDefine;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class HeadHonorGrip extends MovieClip
   {
      private var valueTxt:TextField;
      
      private var proTxt:TextField;
      
      private var img:MovieClip;
      
      public var honorDefine:HeadHonorDefine;
      
      public function HeadHonorGrip()
      {
         super();
         this.mouseChildren = false;
      }
      
      public function setImg(img0:MovieClip) : void
      {
         if(Boolean(this.img))
         {
            INIT.showError("按钮已经有了img素材，不能重复添加！");
         }
         img0.visible = true;
         this.valueTxt = img0["valueTxt"];
         this.proTxt = img0["proTxt"];
         FontDeal.dealOne(this.valueTxt);
         FontDeal.dealOne(this.proTxt);
         this.img = img0;
         this.img.stop();
         addChild(this.img);
      }
      
      public function inData_byHonorDefine(d0:HeadHonorDefine) : void
      {
         this.honorDefine = d0;
         this.valueTxt.text = d0.min + "";
         var proStr0:String = d0.getProStr();
         this.proTxt.htmlText = FontDeal.getDealLeadingStr(this.proTxt,proStr0);
      }
      
      public function fleshByNowHonor(nowHonor0:Number) : void
      {
         var d0:HeadHonorDefine = this.honorDefine;
         var bb0:Boolean = d0.panValue(nowHonor0);
         this.valueTxt.textColor = !bb0 ? 10066329 : 16776960;
         this.proTxt.textColor = !bb0 ? 6710886 : 65280;
         this.img.gotoAndStop(!bb0 ? 1 : 2);
      }
   }
}

