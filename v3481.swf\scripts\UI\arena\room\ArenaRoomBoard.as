package UI.arena.room
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.arena.ArenaTopCtrl;
   import UI.arena.info.ArenaInfoBox;
   import UI.base.NormalUI;
   import UI.base.event.ClickEvent;
   import UI.test.SaveTestBox;
   import com.adobe.serialization.json.JSON2;
   import dataAll._app.top.TopBarData;
   import dataAll._app.top.TopBarDataGroup;
   import dataAll._app.top.define.TopBarDefineGroup;
   import flash.display.Sprite;
   import flash.text.TextField;
   import gameAll.level.arena.ArenaCtrl;
   
   public class ArenaRoomBoard extends NormalUI
   {
      private var pageTag:Sprite;
      
      private var gripTag:Sprite;
      
      private var noTxt:TextField;
      
      private var gripBox:ArenaRivalBox = new ArenaRivalBox();
      
      private var nowDefine:TopBarDefineGroup;
      
      public var infoBox:ArenaInfoBox;
      
      public function ArenaRoomBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["pageTag","gripTag","noTxt"];
         super.setImg(img0);
         this.noTxt.visible = false;
         addChild(this.gripBox);
         this.gripBox.imgType = "ArenaUI/rivalGrip";
         this.gripBox.arg.init(3,2,5,5);
         this.gripBox.setNowGripNum(12);
         this.gripBox.pageBox.setToNormalBtn();
         this.gripBox.pageBox.x = this.pageTag.x - this.gripTag.x;
         this.gripBox.pageBox.y = this.pageTag.y - this.gripTag.y;
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.rivalClick);
         NormalUICtrl.setTag(this.gripBox,this.gripTag);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         var bb0:Boolean = UIOrder.zuobiPan();
         if(bb0)
         {
            Gaming.uiGroup.arenaUI.hide();
            return;
         }
         this.uploadScore();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      private function uploadScore() : void
      {
         this.nowDefine = ArenaTopCtrl.getNowTopBarDefineGroup();
         ArenaTopCtrl.uploadScoreBy(this.yes_unloadScore,this.no_unloadScore);
      }
      
      private function yes_unloadScore(returnDataObj:Object) : void
      {
         this.getRival();
      }
      
      private function no_unloadScore(str0:String = "") : void
      {
         this.getRival();
      }
      
      private function getRival() : void
      {
         Gaming.uiGroup.connectUI.show("获取对手数据……");
         var myRank0:int = Gaming.PG.da.arena.myRank;
         if(myRank0 > 9987)
         {
            myRank0 = 9987;
         }
         else if(myRank0 < 12)
         {
            myRank0 = 12;
         }
         var page0:int = myRank0 / 12;
         Gaming.api.top.getRankListsData(this.nowDefine.id,12,page0,this.yes_getRival,this.no_getRival);
      }
      
      private function yes_getRival(dataArr0:Array) : void
      {
         var obj0:Object = null;
         var dg0:TopBarDataGroup = new TopBarDataGroup();
         try
         {
            dg0.inData_byArr(dataArr0,this.nowDefine.name);
            Gaming.uiGroup.connectUI.hide();
            this.showRival(dg0);
         }
         catch(e:Error)
         {
            for each(obj0 in dataArr0)
            {
               try
               {
                  SaveTestBox.addText(JSON2.encode(obj0));
               }
               catch(e2:Error)
               {
                  SaveTestBox.addText("无效数据：" + e2);
               }
               SaveTestBox.addText("");
            }
            Gaming.uiGroup.arenaUI.hide();
            Gaming.uiGroup.alertBox.showError("排行榜数据读取错误：" + e);
         }
      }
      
      private function no_getRival(str0:String) : void
      {
         Gaming.uiGroup.alertBox.showError("获取对手数据失败！\n" + str0);
         Gaming.uiGroup.connectUI.hide();
         this.showRival(null);
      }
      
      private function showRival(dg0:TopBarDataGroup = null) : void
      {
         if(Boolean(dg0))
         {
            dg0.delOneByUid(Gaming.PG.loginData.uid);
            if(dg0.arr.length == 0)
            {
               this.gripBox.visible = false;
            }
            else
            {
               this.gripBox.visible = true;
               this.gripBox.inDataByRivalDataArr(dg0.arr);
            }
         }
         else
         {
            this.gripBox.visible = false;
         }
         this.noTxt.visible = !this.gripBox.visible;
         if(!Gaming.PG.da.worldMap.saveGroup.getWinB("WoTu"))
         {
            if(!Gaming.PG.save.guide.firstSaveB)
            {
               Gaming.PG.save.guide.firstSaveB = true;
               UIOrder.save();
            }
         }
      }
      
      private function rivalClick(e:ClickEvent) : void
      {
         var da0:TopBarData = e.childData as TopBarData;
         ArenaCtrl.chooseArivalByTopBarData(da0);
      }
   }
}

