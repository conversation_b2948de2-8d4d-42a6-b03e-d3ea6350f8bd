package UI.setting
{
   import UI.UIOrder;
   import UI.base.btnList.BtnBox;
   import UI.base.button.NormalBtn;
   import UI.base.scroll.SliderBar;
   import dataAll._app.setting.SettingSave;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class SettingCtrlBox extends BtnBox
   {
      private var barNameArr:Array = [];
      
      private var barObj:Object = {};
      
      public var sensitivityBar:SliderBar = new SliderBar();
      
      private var sensitivitySp:Sprite;
      
      public var shootShakeBar:SliderBar = new SliderBar();
      
      private var shootShakeSp:Sprite;
      
      public var screenShakeBar:SliderBar = new SliderBar();
      
      private var screenShakeSp:Sprite;
      
      public function SettingCtrlBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var n:* = undefined;
         var overArr0:Array = null;
         var btnName2:* = null;
         var ele0:String = null;
         var label0:String = null;
         var sb0:SliderBar = null;
         var btn2:NormalBtn = null;
         elementNameArr = ["sensitivitySp","shootShakeSp","screenShakeSp"];
         super.setImg(img0);
         for(n in elementNameArr)
         {
            ele0 = elementNameArr[n];
            label0 = ele0.substr(0,ele0.length - 2);
            this.barNameArr[n] = label0;
            sb0 = new SliderBar();
            sb0.label = label0;
            addChild(sb0);
            sb0.setImg(this[ele0]);
            sb0.setChangeFun(this.barChange);
            this.barObj[label0] = sb0;
         }
         overArr0 = ["endlessB","firstChooseB"];
         for each(btnName2 in overArr0)
         {
            btn2 = getBtn(btnName2);
            btn2.addEventListener(MouseEvent.MOUSE_OVER,this.btnOver);
            btn2.addEventListener(MouseEvent.MOUSE_OUT,Gaming.uiGroup.tipBox.hide);
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function get settingSave() : SettingSave
      {
         return Gaming.PG.save.setting;
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      public function fleshData() : void
      {
         var n:* = undefined;
         var sb0:SliderBar = null;
         if(!Gaming.PG.save)
         {
            return;
         }
         for(n in this.barObj)
         {
            sb0 = this.barObj[n];
            sb0.setPer(this.settingSave.getValue(n));
         }
         this.fleshChooseBtn();
      }
      
      private function fleshChooseBtn() : void
      {
         var btn0:NormalBtn = null;
         var label0:String = null;
         var bb0:Boolean = false;
         if(!Gaming.PG.save)
         {
            return;
         }
         var save0:SettingSave = this.settingSave;
         for each(btn0 in btnArr)
         {
            label0 = btn0.label;
            if(this.settingSave.haveValue(label0))
            {
               bb0 = this.settingSave.getValue(label0);
               btn0.activedAndIgnoreChosen = true;
               btn0.isChosen = bb0;
            }
         }
         Gaming.BGHit.fleshSetting();
         UIOrder.fleshSettingReadSave();
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var bb0:Boolean = false;
         if(!Gaming.PG.save)
         {
            return;
         }
         var btn0:NormalBtn = e.target as NormalBtn;
         var label0:String = btn0.label;
         if(this.settingSave.haveValue(label0))
         {
            bb0 = this.settingSave.getValue(label0);
            this.settingSave.setValue(label0,!bb0);
            this.fleshChooseBtn();
         }
      }
      
      private function barChange(v0:Number, label0:String) : void
      {
         if(Boolean(Gaming.PG.save))
         {
            this.settingSave.setValue(label0,v0);
         }
      }
      
      protected function btnOver(e:MouseEvent) : void
      {
         var tip0:String = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(Boolean(btn0))
         {
            tip0 = "";
            if(btn0.label == "endlessB")
            {
               tip0 = "注意，开启该选项，你在通关后只有3秒的时间拾取掉落物。";
            }
            else if(btn0.label == "firstChooseB")
            {
               tip0 = "如果不希望角色切枪，请在角色AI中设置“主动切枪”为×。";
            }
            UIOrder.showTip(tip0);
         }
      }
   }
}

