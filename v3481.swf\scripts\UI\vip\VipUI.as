package UI.vip
{
   import UI.UIOrder;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AppNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.label.LabelBox;
   import UI.base.loadBar.LoadBar;
   import UI.pay.PayCtrl;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.vip.VipData;
   import dataAll._app.vip.define.VipLevelDefine;
   import dataAll.gift.GiftAddit;
   import dataAll.ui.GatherColor;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class VipUI extends AppNormalUI
   {
      private static const otherNameArr:Array = ["props","buy","skin"];
      
      private var otherObj:Object = {};
      
      public var labelBox:LabelBox = new LabelBox();
      
      public var vipBar:LoadBar = new LoadBar();
      
      public var getGiftBtn:NormalBtn = new NormalBtn();
      
      public var dayGiftBtn:NormalBtn = new NormalBtn();
      
      private var giftBox:ItemsGripBox = new ItemsGripBox();
      
      private var dayGiftBox:ItemsGripBox = new ItemsGripBox();
      
      private var txtObj:Object = {};
      
      private var vipIconMc:MovieClip;
      
      private var barSp:Sprite;
      
      private var payBtn:SimpleButton;
      
      private var titleTxt:TextField;
      
      private var titlePayTxt:TextField;
      
      private var getGiftBtnSp:MovieClip;
      
      private var dayGiftBtnSp:MovieClip;
      
      private var buyTag:Sprite;
      
      private var labelTag:Sprite;
      
      private var giftTag:Sprite;
      
      private var dayGiftTag:Sprite;
      
      private var closeBtn:SimpleButton;
      
      private var nowDefine:VipLevelDefine;
      
      private var buyBarArr:Array = [];
      
      private var excSp:Sprite;
      
      private var excBox:VipExcBox = new VipExcBox();
      
      public function VipUI()
      {
         super();
         UICn = "VIP";
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["excSp","vipIconMc","barSp","payBtn","titleTxt","titlePayTxt","getGiftBtnSp","dayGiftBtnSp","giftTag","dayGiftTag","labelTag","closeBtn","buyTag"];
         super.setImg(img0);
         FontDeal.dealOne(this.titleTxt);
         FontDeal.dealOne(this.titlePayTxt);
         this.vipIconMc.stop();
         addChild(this.vipBar);
         this.vipBar.setImg(this.barSp);
         this.giftTag.addChild(this.giftBox);
         this.giftBox.imgType = "equipGrip";
         this.giftBox.evt.setWantEvent(true,false,false,true,true);
         this.giftBox.arg.init(18,1,2,2);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.giftBox);
         this.dayGiftTag.addChild(this.dayGiftBox);
         this.dayGiftBox.imgType = "equipGrip";
         this.dayGiftBox.evt.setWantEvent(true,false,false,true,true);
         this.dayGiftBox.arg.init(8,2,2,2);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.dayGiftBox);
         addChild(this.getGiftBtn);
         this.getGiftBtn.setImg(this.getGiftBtnSp);
         this.getGiftBtn.setName("领取");
         this.getGiftBtn.addEventListener(MouseEvent.CLICK,this.getGiftClick);
         addChild(this.dayGiftBtn);
         this.dayGiftBtn.setImg(this.dayGiftBtnSp);
         this.dayGiftBtn.setName("领取");
         this.dayGiftBtn.addEventListener(MouseEvent.CLICK,this.dayGiftClick);
         ItemsGripTipCtrl.addNormalBtnTip(this.dayGiftBtn);
         this.payBtn.addEventListener(MouseEvent.CLICK,this.payClick);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         this.initTxt(img0);
         this.initLabel();
         this.initOther(img0);
         this.excBox.setImg(this.excSp);
         addChild(this.excBox);
         this.excBox.hide();
      }
      
      private function initOther(img0:Sprite) : void
      {
         var name0:* = null;
         var mcName0:String = null;
         var mc0:MovieClip = null;
         var btn0:NormalBtn = null;
         for each(name0 in otherNameArr)
         {
            mcName0 = name0 + "BtnSp";
            mc0 = img0[mcName0];
            btn0 = new NormalBtn();
            btn0.label = name0;
            addChild(btn0);
            btn0.setImg(mc0);
            btn0.setName(name0);
            btn0.addEventListener(MouseEvent.MOUSE_OVER,this.otherBtnOver);
            this.otherObj[name0] = btn0;
         }
      }
      
      private function initTxt(img0:Sprite) : void
      {
         var mc0:DisplayObject = null;
         var name0:String = null;
         var index0:int = 0;
         var label0:String = null;
         var num0:int = img0.numChildren;
         for(var i:int = 0; i < num0; i++)
         {
            mc0 = img0.getChildAt(i);
            if(mc0 is TextField)
            {
               name0 = mc0.name;
               index0 = int(name0.indexOf("_txt"));
               if(index0 > 0)
               {
                  label0 = name0.substring(0,index0);
                  this.txtObj[label0] = mc0;
                  FontDeal.dealOne(mc0 as TextField);
               }
            }
         }
      }
      
      private function initLabel() : void
      {
         var n:* = undefined;
         var grip0:NormalBtn = null;
         var d0:VipLevelDefine = null;
         var arr0:Array = Gaming.defineGroup.vip.arr;
         var len0:int = arr0.length + 1;
         this.labelBox.imgType = "VipUI/labelBtn";
         this.labelBox.arg.init(1,len0,0,-1);
         this.labelBox.evt.setWantEvent(true,false,false,true,true);
         this.labelBox.setNowGripNum(len0);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.labelBox.x = this.labelTag.x;
         this.labelBox.y = this.labelTag.y;
         addChild(this.labelBox);
         var gripArr0:Array = this.labelBox.gripArr;
         for(n in gripArr0)
         {
            grip0 = gripArr0[n];
            if(n <= arr0.length - 1)
            {
               d0 = arr0[n];
               grip0.setName("VIP." + d0.getTrueLevel());
               grip0.itemsData = d0;
            }
            else
            {
               grip0.setName("专属奖励");
               grip0.label = "exc";
            }
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      private function no_getTotalRecharged(str0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError(str0);
         this.hide();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      public function fleshData() : void
      {
         var next_must0:Number = NaN;
         var c0:Number = NaN;
         var totalRecharged0:Number = Gaming.PG.da.main.totalRecharged;
         var d0:VipLevelDefine = Gaming.PG.da.vip.def;
         var lv0:int = d0.getTrueLevel();
         var next_d0:VipLevelDefine = Gaming.defineGroup.vip.getNextDefineByTrueLevel(lv0);
         this.vipIconMc.gotoAndStop(lv0 + 1);
         if(next_d0 is VipLevelDefine)
         {
            next_must0 = next_d0.must;
            c0 = next_must0 - totalRecharged0;
            this.vipBar.setPer(totalRecharged0 / next_must0);
            this.vipBar.setText(totalRecharged0 + "/" + next_must0);
            this.vipBar.setTopText("再充值" + ComMethod.color(c0 + "黄金","#FFFF00") + "就可免费升级至" + ComMethod.color(next_d0.getName(),"#FFFF00") + "。" + ComMethod.color("1人民币=10黄金。","#00FF00"));
         }
         else
         {
            this.vipBar.setPer(1);
            this.vipBar.setText(totalRecharged0 + "/" + d0.must);
            this.vipBar.setTopText("您当前已是最高VIP等级。" + ComMethod.color("1人民币=10黄金。","#00FF00"));
         }
         this.labelBox.setAllFun("setSmallIcon","");
         if(lv0 > 0)
         {
            this.labelBox.gripArr[lv0 - 1]["setSmallIcon"]("now");
         }
         this.showLevel(lv0);
         this.fleshLabelGift();
      }
      
      private function fleshLabelGift() : void
      {
         var n:* = undefined;
         var btn0:NormalBtn = null;
         var d2:VipLevelDefine = null;
         var vData0:VipData = null;
         var haveGiftB0:Boolean = false;
         var haveDayGiftB0:Boolean = false;
         var d0:VipLevelDefine = Gaming.PG.da.vip.def;
         var lv0:int = d0.getTrueLevel();
         var gripArr0:Array = this.labelBox.gripArr;
         for(n in gripArr0)
         {
            btn0 = gripArr0[n];
            d2 = btn0.itemsData as VipLevelDefine;
            vData0 = Gaming.PG.da.vip;
            haveGiftB0 = false;
            if(n < lv0)
            {
               haveGiftB0 = !vData0.save.getGiftB(d2.must);
            }
            haveDayGiftB0 = false;
            if(vData0.def == d2)
            {
               haveDayGiftB0 = !vData0.save.getDayGiftB(d2.must);
            }
            btn0.setNew(haveDayGiftB0 || haveGiftB0);
         }
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         if(e.label == "exc")
         {
            this.labelBox.setChoose_byIndex(e.index);
            this.excBox.show();
         }
         else
         {
            this.showLevel(e.index + 1);
         }
      }
      
      private function showLevel(lv0:int) : void
      {
         var n:* = undefined;
         var haveGetGiftB0:Boolean = false;
         var txt0:TextField = null;
         var str0:String = null;
         var beforeGetD0:VipLevelDefine = null;
         var dayGiftb0:Boolean = false;
         this.excBox.hide();
         if(lv0 < 1)
         {
            lv0 = 1;
         }
         var vData0:VipData = Gaming.PG.da.vip;
         var index0:int = lv0 - 1;
         this.labelBox.setChoose_byIndex(index0);
         var d0:VipLevelDefine = Gaming.defineGroup.vip.arr[index0];
         this.nowDefine = d0;
         var must0:int = d0.must;
         var vipB0:Boolean = Gaming.PG.da.main.totalRecharged >= must0;
         this.titleTxt.text = "VIP" + d0.getTrueLevel() + "特权";
         this.titlePayTxt.text = "累计充值" + must0 + "黄金";
         for(n in this.txtObj)
         {
            txt0 = this.txtObj[n];
            str0 = d0.getValueStringByPro(n);
            txt0.htmlText = str0;
         }
         this.giftBox.inData_byGift(d0.gift);
         haveGetGiftB0 = vData0.save.getGiftB(must0);
         if(!vipB0)
         {
            this.getGiftBtn.actived = false;
            this.getGiftBtn.setName("领取");
            this.giftBox.alpha = 0.5;
         }
         else
         {
            this.getGiftBtn.actived = !haveGetGiftB0;
            this.getGiftBtn.setName(haveGetGiftB0 ? "已领" : "领取");
            this.giftBox.alpha = haveGetGiftB0 ? 0.5 : 1;
         }
         this.dayGiftBox.inData_byGift(d0.dayGift);
         if(vData0.def == d0 && vipB0)
         {
            beforeGetD0 = vData0.getVipBeforeDayGetDefine();
            dayGiftb0 = vData0.save.getDayGiftB(must0);
            this.dayGiftBtn.visible = true;
            this.dayGiftBtn.actived = !dayGiftb0;
            this.dayGiftBtn.setName(dayGiftb0 ? "已领" : "领取");
            this.dayGiftBtn.tipString = Boolean(beforeGetD0) ? "今日已经领取过更低VIP等级的礼包，因此领取当前礼包时将扣除之前领取过的物品。" : "";
         }
         else
         {
            this.dayGiftBtn.visible = false;
         }
         this.fleshOther();
      }
      
      private function getGiftClick(e:MouseEvent) : void
      {
         var vData0:VipData = null;
         var d0:VipLevelDefine = this.nowDefine;
         var must0:int = d0.must;
         var bb0:Boolean = GiftAddit.addAndAutoBagSpacePan(d0.gift);
         if(bb0)
         {
            vData0 = Gaming.PG.da.vip;
            vData0.save.getGiftEvent(must0);
            this.showLevel(d0.index + 1);
            this.fleshLabelGift();
         }
      }
      
      private function dayGiftClick(e:MouseEvent) : void
      {
         var vData0:VipData = null;
         var d0:VipLevelDefine = this.nowDefine;
         var must0:int = d0.must;
         var bb0:Boolean = GiftAddit.addAndAutoBagSpacePan(Gaming.PG.da.vip.getNowDayGift());
         if(bb0)
         {
            vData0 = Gaming.PG.da.vip;
            vData0.save.getDayGiftEvent(must0);
            this.showLevel(d0.index + 1);
            this.fleshLabelGift();
         }
      }
      
      private function payClick(e:MouseEvent) : void
      {
         PayCtrl.gotoPay();
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.hide();
      }
      
      private function fleshOther() : void
      {
         var btn0:NormalBtn = null;
         var label0:String = null;
         var skinNum0:int = 0;
         var lvStr0:String = ComMethod.color(" Lv" + this.nowDefine.getTrueLevel(),GatherColor.yellowColor);
         for each(btn0 in this.otherObj)
         {
            label0 = btn0.label;
            if(label0 == "props")
            {
               btn0.setName("道具特权" + lvStr0);
               btn0.setNumText("战斗中道具使用次数增加");
            }
            else if(label0 == "buy")
            {
               btn0.setName("购买特权" + lvStr0);
               btn0.setNumText("特殊商品每日限购增加");
            }
            else if(label0 == "skin")
            {
               skinNum0 = int(this.nowDefine.getArmsSkinArr().length);
               btn0.setNumText("武器的变色皮肤");
               btn0.activedAndEnabled = false;
               if(skinNum0 > 0)
               {
                  btn0.actived = true;
                  btn0.setName("武器皮肤" + ComMethod.color("+" + skinNum0,GatherColor.yellowColor));
               }
               else
               {
                  btn0.actived = false;
                  btn0.setName("武器皮肤");
               }
            }
         }
      }
      
      private function otherBtnOver(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var s0:String = this.nowDefine[btn0.label + "GatherTip"]();
         UIOrder.showTip(s0);
      }
      
      private function otherBtnOut(e:MouseEvent) : void
      {
         UIOrder.showTip("");
      }
   }
}

