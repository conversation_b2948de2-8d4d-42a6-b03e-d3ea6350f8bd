package w_test.cheating
{
   import UI.test.SaveTestBox;
   import dataAll.equip.EquipPropertyData;
   import w_test.drop.LevelDropCount;
   
   public class DropCheating extends OneCheating
   {
      public function DropCheating()
      {
         super();
      }
      
      public function dropTest(str0:String, v0:int) : String
      {
         LevelDropCount.setTextB(true,v0);
         return "开启掉落测试：" + v0;
      }
      
      public function closeDropTest(str0:String, v0:int) : String
      {
         LevelDropCount.setTextB(!LevelDropCount.testB,v0);
         return LevelDropCount.testB ? "开启掉落测试" : "关闭掉落测试";
      }
      
      public function showDropAdd(str0:String, v0:int) : String
      {
         var name0:* = null;
         str0 = "";
         var proArr0:Array = Gaming.PG.da.time.getTestProArr();
         var me0:EquipPropertyData = Gaming.PG.da.getDropMerge();
         for each(name0 in proArr0)
         {
            str0 += "\n" + name0 + "：" + me0[name0];
         }
         SaveTestBox.addText(str0);
         return "";
      }
   }
}

