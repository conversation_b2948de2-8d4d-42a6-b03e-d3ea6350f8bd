package UI.skill
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripMoveCtrl;
   import UI.bag.ItemsGripTipCtrl;
   import UI.bag.ItemsGripUnlockCtrl;
   import UI.base.NormalUI;
   import UI.base.event.ClickEvent;
   import dataAll.skill.HeroSkillData;
   import flash.display.Sprite;
   
   public class SkillWearUI extends NormalUI
   {
      public var wearBox:ItemsGripBox = new ItemsGripBox();
      
      public var bagBox:ItemsGripBox = new ItemsGripBox();
      
      private var wearTag:Sprite;
      
      private var bagTag:Sprite;
      
      private var pageBoxTag:Sprite;
      
      public function SkillWearUI()
      {
         super();
         this.wearBox.imgType = "equipGrip";
         this.wearBox.arg.init(5,2,5,5);
         this.wearBox.evt.setWantEvent(true,true,true,true,true,true);
         addChild(this.wearBox);
         this.bagBox.imgType = "equipGrip";
         this.bagBox.arg.init(5,3,5,5);
         this.bagBox.evt.setWantEvent(true,true,true,true,true,true);
         addChild(this.bagBox);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["wearTag","bagTag","pageBoxTag"];
         super.setImg(img0);
         NormalUICtrl.setTag(this.wearBox,this.wearTag);
         NormalUICtrl.setTag(this.bagBox,this.bagTag);
         this.bagBox.pageBox.x = this.pageBoxTag.x - this.bagBox.x;
         this.bagBox.pageBox.y = this.pageBoxTag.y - this.bagBox.y;
         ItemsGripMoveCtrl.addEvent_byItemsGripBox(this.wearBox);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.wearBox);
         ItemsGripUnlockCtrl.addEvent_byItemsGripBox(this.wearBox);
         ItemsGripMoveCtrl.addEvent_byItemsGripBox(this.bagBox);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.bagBox);
         this.bagBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.bagBox.addEventListener(ClickEvent.ON_DOUBLE_CLICK,this.gripDoubleClick);
         this.wearBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.wearBox.addEventListener(ClickEvent.ON_DOUBLE_CLICK,this.gripDoubleClick);
         this.bagBox.pageBox.setToNormalBtn();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function fleshData() : void
      {
         this.wearBox.inData_byDataGroup(Gaming.PG.DATA.skill);
         this.wearBox.setAllPro("doubleClickEnabled",true);
         this.wearBox.setAllLevelTxtBySite();
         this.bagBox.inData_byDataGroup(Gaming.PG.DATA.skillBag);
         this.bagBox.setAllPro("doubleClickEnabled",true);
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var grip0:ItemsGrid = e.child as ItemsGrid;
         var skillUpgradeUI:SkillUpgradeUI = Gaming.uiGroup.skillUI.upgradeBox;
         if(skillUpgradeUI.visible)
         {
            this.gripDoubleClick(e);
         }
      }
      
      public function chooseGripByData(data0:HeroSkillData) : ItemsGrid
      {
         var grip0:ItemsGrid = this.bagBox.findGripByData(data0);
         if(Boolean(grip0))
         {
            this.bagBox.setChoose_byIndex(grip0.index);
            this.wearBox.setChoose_byIndex(-1);
         }
         else
         {
            grip0 = this.wearBox.findGripByData(data0);
            if(Boolean(grip0))
            {
               this.bagBox.setChoose_byIndex(-1);
               this.wearBox.setChoose_byIndex(grip0.index);
            }
         }
         return grip0;
      }
      
      private function gripDoubleClick(e:ClickEvent) : void
      {
         var skillUpgradeUI:SkillUpgradeUI = null;
         var grip0:ItemsGrid = e.child as ItemsGrid;
         if(Boolean(grip0.itemsData))
         {
            skillUpgradeUI = Gaming.uiGroup.skillUI.upgradeBox;
            skillUpgradeUI.nowData = grip0.itemsData as HeroSkillData;
            Gaming.uiGroup.skillUI.showBox("upgrade");
            if(e.target == this.bagBox)
            {
               this.bagBox.setChoose_byIndex(e.index);
               this.wearBox.setChoose_byIndex(-1);
            }
            else
            {
               this.bagBox.setChoose_byIndex(-1);
               this.wearBox.setChoose_byIndex(e.index);
            }
         }
      }
   }
}

