package UI.base
{
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.geom.Rectangle;
   
   public class NormalUI extends TipSprite
   {
      protected var firstB:Boolean = false;
      
      public var UILabel:String = "";
      
      public var UICn:String = "";
      
      protected var img:Sprite = null;
      
      protected var sizeSp:Sprite;
      
      protected var elementNameArr:Array = [];
      
      protected var repeatSetImgB:Boolean = false;
      
      public function NormalUI()
      {
         super();
      }
      
      protected function firstLoad() : void
      {
      }
      
      public function setImgUrl(url0:String) : void
      {
         this.setImg(Gaming.swfLoaderManager.getResourceFull(url0));
      }
      
      public function setImg(img0:Sprite) : void
      {
         var n:* = undefined;
         var name0:String = null;
         if(Boolean(this.img) && !this.repeatSetImgB)
         {
            INIT.showError("已经有了img素材，不能重复添加！");
         }
         this.img = img0;
         for(n in this.elementNameArr)
         {
            name0 = this.elementNameArr[n];
            this.SET(name0,this.img[name0]);
            if(this.img[name0] is MovieClip)
            {
               this.img[name0].stop();
            }
         }
         this.sizeSp = img0.getChildByName("sizeSp") as Sprite;
         this.getEleCon().addChildAt(this.img,0);
         this.x = this.img.x;
         this.y = this.img.y;
         this.img.x = 0;
         this.img.y = 0;
      }
      
      protected function getEleCon() : DisplayObjectContainer
      {
         return this;
      }
      
      protected function loopMc(img0:Sprite) : void
      {
         var mc0:DisplayObject = null;
         var name0:String = null;
         var num0:int = img0.numChildren;
         for(var i:int = 0; i < num0; i++)
         {
            mc0 = img0.getChildAt(i);
            name0 = mc0.name;
            if(this.loopOne(mc0,name0))
            {
               i--;
               num0--;
            }
         }
      }
      
      protected function loopOne(mc0:DisplayObject, name0:String) : Boolean
      {
         return false;
      }
      
      protected function SET(str0:String, value0:Object) : void
      {
         INIT.showError("该方法必须被覆盖！");
      }
      
      public function show() : void
      {
         if(!this.firstB)
         {
            this.firstLoad();
            this.firstB = true;
         }
         this.visible = true;
         inCon();
      }
      
      public function hide() : void
      {
         this.visible = false;
         outCon();
      }
      
      public function showOrHide() : void
      {
         if(this.visible)
         {
            this.hide();
         }
         else
         {
            this.show();
         }
      }
      
      override public function get width() : Number
      {
         if(this.sizeSp is Sprite)
         {
            return this.sizeSp.width;
         }
         return super.width;
      }
      
      override public function get height() : Number
      {
         if(this.sizeSp is Sprite)
         {
            return this.sizeSp.height;
         }
         return super.height;
      }
      
      override public function getRect(sp0:DisplayObject) : Rectangle
      {
         if(this.sizeSp is Sprite)
         {
            return this.sizeSp.getRect(sp0);
         }
         return super.getRect(sp0);
      }
   }
}

