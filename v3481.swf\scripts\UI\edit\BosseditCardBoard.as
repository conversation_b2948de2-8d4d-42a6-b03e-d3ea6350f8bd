package UI.edit
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.api.shop.ShopBuyObject;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripBtnListCtrl;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.grid.NormalGrid;
   import UI.base.numChoose.NumChooseBox;
   import UI.base.tip.OneTextGather;
   import UI.edit.card.BcardEvoBox;
   import UI.edit.card.BcardGiftBox;
   import UI.edit.card.BcardRemakeBox;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.StringMethod;
   import dataAll._app.edit.card.BcardPKCode;
   import dataAll._app.edit.card.BossCardCreator;
   import dataAll._app.edit.card.BossCardData;
   import dataAll._app.edit.card.BossCardDataGroup;
   import dataAll._app.edit.card.BossCardSave;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll.body.define.BodyFather;
   import dataAll.ui.GatherColor;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class BosseditCardBoard extends AutoNormalUI
   {
      private var titleTxt:TextField;
      
      private var fightTitleTxt:TextField;
      
      private var fightTxt:TextField;
      
      private var addTitleTxt:TextField;
      
      private var addTxt:TextField;
      
      private var valueTxt:OneTextGather = new OneTextGather(10);
      
      private var bagTxt:TextField;
      
      private var autoBtn:NormalBtn;
      
      private var cardBtn:NormalBtn;
      
      private var highBtn:NormalBtn;
      
      private var star7Btn:NormalBtn;
      
      private var delBtn:NormalBtn;
      
      private var delStarBtn:NormalBtn;
      
      private var sortBtn:NormalBtn;
      
      private var addBagBtn:NormalBtn;
      
      private var fightBtn:NormalBtn;
      
      private var fightPerSp:Sprite;
      
      private var pageTag:Sprite;
      
      private var barTag:Sprite;
      
      private var giftBoxSp:Sprite;
      
      private var achieveGiftBox:BcardGiftBox = new BcardGiftBox();
      
      private var giftBtn:NormalBtn;
      
      private var evoBox:BcardEvoBox = new BcardEvoBox();
      
      private var remakeBox:BcardRemakeBox = new BcardRemakeBox();
      
      private var box:ItemsGripBox = new ItemsGripBox();
      
      private var overDa:BossCardData = null;
      
      private var delB:Boolean = false;
      
      private var tempDa:BossCardData = null;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      private var tempBtn:NormalBtn = null;
      
      private var delStar:int = 2;
      
      public function BosseditCardBoard()
      {
         super();
         mcTypeArr = ["tag","txt","btnSp"];
         this.x = 2;
         this.y = 69;
      }
      
      public static function inBarFun(grip0:NormalBtn, da0:BossCardData) : void
      {
         grip0.itemsData = da0;
         grip0.setIconName(da0.getIconUrl());
         grip0.setStarIcon(da0.getStar());
         if(grip0 is NormalGrid)
         {
            (grip0 as NormalGrid).setSecMc(da0.getUISecMc());
         }
      }
      
      private function get cardNum() : Number
      {
         return this.CF.getAttribute("cardNum");
      }
      
      private function set cardNum(v0:Number) : void
      {
         this.CF.setAttribute("cardNum",v0);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         FontDeal.dealOne(this.titleTxt);
         FontDeal.dealLine(this.fightTitleTxt);
         FontDeal.dealLine(this.fightTxt);
         FontDeal.dealLine(this.bagTxt);
         FontDeal.dealLine(this.addTitleTxt);
         addChild(this.valueTxt);
         NormalUICtrl.setTag(this.valueTxt,this.addTxt);
         this.valueTxt.init();
         this.valueTxt.MAX_WIDTH = this.addTxt.width;
         this.valueTxt.TEXT2_MAX_X = 80;
         this.valueTxt.setNormalTextColor(this.addTxt.textColor);
         this.giftBtn.setName("送卡");
         this.sortBtn.setName("排序");
         this.delStarBtn.setName("星级删除");
         this.cardBtn.setName("抽卡");
         this.cardBtn.activedAndEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.cardBtn);
         this.highBtn.setName("高级抽");
         this.highBtn.activedAndEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.highBtn);
         this.star7Btn.setName("7星抽");
         this.star7Btn.activedAndEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.star7Btn);
         this.autoBtn.tipString = "勾选后，击杀首领后会自动退下已出战的魂卡。";
         ItemsGripTipCtrl.addNormalBtnTip(this.autoBtn);
         this.titleTxt.addEventListener(MouseEvent.MOUSE_OVER,this.numTxtOver);
         this.titleTxt.addEventListener(MouseEvent.MOUSE_OUT,this.gripOut);
         this.fightBtn.addEventListener(MouseEvent.MOUSE_OVER,this.fightBtnOver);
         this.fightBtn.addEventListener(MouseEvent.MOUSE_OUT,this.gripOut);
         this.fightTxt.htmlText = "";
         this.fightTxt.addEventListener(MouseEvent.MOUSE_OVER,this.fightTxtOver);
         this.fightTxt.addEventListener(MouseEvent.MOUSE_OUT,this.gripOut);
         this.valueTxt.addEventListener(MouseEvent.MOUSE_OVER,this.addTxtOver);
         this.valueTxt.addEventListener(MouseEvent.MOUSE_OUT,this.gripOut);
         this.box.setIconPro("BosseditUI/cardBtn");
         this.box.arg.init(10,5,11,11);
         this.box.evt.setWant(true,true);
         addChild(this.box);
         NormalUICtrl.setTag(this.box,this.barTag);
         this.box.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.box.addEventListener(ClickEvent.ON_OVER,this.gripOver);
         this.box.addEventListener(ClickEvent.ON_OUT,this.gripOut);
         this.box.pageBox.setToNormalBtn();
         this.box.setPagePos(this.pageTag);
         this.achieveGiftBox.setImg(this.giftBoxSp);
         this.achieveGiftBox.hide();
         this.achieveGiftBox.setCon(this);
         this.evoBox.hide();
         this.evoBox.setCon(this);
         this.remakeBox.hide();
         this.remakeBox.setCon(this);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      protected function get dataG() : BossCardDataGroup
      {
         return Gaming.PG.da.bossCard;
      }
      
      public function outLoginEvent() : void
      {
         this.delB = false;
         this.tempDa = null;
         this.tempBtn = null;
         this.cardNum = 0;
         this.evoBox.outLoginEvent();
         this.remakeBox.outLoginEvent();
      }
      
      override public function show() : void
      {
         super.show();
         this.delB = false;
         this.overDa = null;
         this.fleshData();
         Gaming.uiGroup.btnList.hide();
      }
      
      override public function hide() : void
      {
         super.hide();
         this.delB = false;
         this.overDa = null;
         Gaming.uiGroup.btnList.hide();
         this.achieveGiftBox.hide();
         this.evoBox.hide();
         this.remakeBox.hide();
      }
      
      private function fleshData() : void
      {
         this.fleshBox();
         this.fleshDrawCard();
         this.fleshGripState();
         this.fleshFightState();
         this.fleshAutoBtn();
         this.fleshAddText();
      }
      
      public function outFleshBox() : void
      {
         this.fleshBox();
         this.fleshGripState();
      }
      
      private function fleshBox() : void
      {
         var daArr0:Array = this.dataG.getDataArr();
         this.box.inData_byArr(daArr0,this.inGripFun);
         this.bagTxt.htmlText = "背包：" + this.dataG.getBagNum() + "/" + this.dataG.getBagMax();
      }
      
      private function inGripFun(grip0:NormalBtn, da0:BossCardData, smallB0:Boolean = false) : void
      {
         var fightB0:Boolean = false;
         inBarFun(grip0,da0);
         if(smallB0)
         {
            fightB0 = this.dataG.fightPan(da0);
            grip0.setSmallIconFrame(da0.getSmallFrame(fightB0,this.delB));
         }
         if(da0 == this.evoBox.getEvoDa())
         {
            grip0.isChosen = true;
         }
      }
      
      private function delStarBtnClick(e:MouseEvent) : void
      {
         Gaming.uiGroup.alertBox.showNumChoose(this.getDelStarText(this.delStar),this.delStar,5,1,1,this.yesDelStar,"yesAndNo",this.delStarChangeFun);
      }
      
      private function delStarChangeFun(box0:NumChooseBox) : String
      {
         this.delStar = box0.nowNum;
         return this.getDelStarText(box0.nowNum);
      }
      
      private function getDelStarText(star0:int) : String
      {
         this.delStar = star0;
         return "批量删除小于等于" + ComMethod.color(star0 + "星","#FFFF00") + "的魂卡。";
      }
      
      private function yesDelStar(star0:int) : void
      {
         var num0:int = this.dataG.removeLessStar(star0);
         if(num0 == 0)
         {
            Gaming.uiGroup.alertBox.showInfo("没有找到指定的魂卡。");
         }
         else
         {
            this.fleshData();
            Gaming.soundGroup.playAndAdd("uiSound/sell");
            Gaming.uiGroup.alertBox.showSuccess("删除" + num0 + "张魂卡。");
         }
      }
      
      private function delBtnClick(e:MouseEvent) : void
      {
         this.delB = !this.delB;
         this.fleshGripState();
      }
      
      private function sortBtnClick(e:MouseEvent) : void
      {
         this.dataG.sortByStar();
         this.fleshBox();
         this.fleshGripState();
      }
      
      private function clearDelState() : void
      {
         if(this.delB)
         {
            this.delB = false;
            this.fleshGripState();
         }
      }
      
      private function fleshDrawCard() : void
      {
         var bugB0:Boolean = this.dataG.isBug();
         var bugTip0:String = bugB0 ? "<red " + this.dataG.getBugOpenTip() + "/>\n\n" : "";
         var noBagStr0:String = "";
         if(this.dataG.getBagSurplus() <= 0)
         {
            noBagStr0 = "<red 背包没有空位了/>\n\n";
         }
         this.cardBtn.actived = this.dataG.getDrawCan() > 0;
         this.cardBtn.setName("抽卡 <b>" + ComMethod.color(this.dataG.getDrawNum(),GatherColor.whiteColor) + "</b>/" + this.dataG.getDrawMax());
         this.cardBtn.tipString = noBagStr0 + "抽卡次数小于2000时，随机抽取1~7星的魂卡。\n抽卡次数大于等于2000时，随机抽取3~7星的魂卡。";
         this.cardBtn.tipString += "\n\n<b><blue 抽卡次数获得方式：/></b>";
         this.cardBtn.tipString += "\n1、魂卡PK，获得次数=敌人魂卡星级。";
         this.cardBtn.tipString += "\n2、击败战场首领，获得次数=难度。";
         this.highBtn.actived = this.dataG.getHighDrawCan() > 0 && bugB0 == false;
         this.highBtn.setName("高级抽\n<b>" + ComMethod.color(this.dataG.getHighDrawNum(),GatherColor.whiteColor) + "</b>/" + this.dataG.getHighDrawMax(),true,true);
         this.highBtn.tipString = bugTip0 + noBagStr0 + "必然抽到5星或5星以上的魂卡。\n\n每抽" + BossCardDataGroup.HIGH_MUST + "次卡，就可获得1次高级抽卡次数。";
         this.star7Btn.actived = this.dataG.get7DrawCan() > 0 && bugB0 == false;
         this.star7Btn.setName("7星抽\n<b>" + ComMethod.color(this.dataG.get7DrawNum(),GatherColor.whiteColor) + "</b>/" + this.dataG.get7DrawMax(),true,true);
         this.star7Btn.tipString = bugTip0 + noBagStr0 + "必然抽到7星魂卡，并带有1条随机属性。\n\n每抽" + BossCardDataGroup.DRAW7_MUST + "次卡，就可获得1次7星抽卡次数。";
      }
      
      private function numTxtOver(e:MouseEvent) : void
      {
         var s0:String = "1、获得1分战场积分，就能获得1次抽卡次数；";
         s0 += "\n\n2、魂卡PK胜利后也能获得抽卡次数。";
         s0 += "\n\n3、每抽" + BossCardDataGroup.HIGH_MUST + "次卡，就可获得1次高级抽卡次数。";
         s0 += "\n\n4、6星魂卡包含人物“战斗力/神级”属性加成（5%~10%）。";
         s0 += "\n\n5、无法抽取以下魂卡：" + BodyFather.noSumCnStr + "。";
         UIOrder.showTip(s0);
      }
      
      protected function cardBtnClick(e:MouseEvent) : void
      {
         this.clearDelState();
         var sur0:int = this.dataG.getDrawCan();
         var s0:String = "选择抽卡次数：\n";
         s0 += ComMethod.color("次数剩余" + this.dataG.getDrawSurplus() + "  背包剩余" + this.dataG.getBagSurplus());
         Gaming.uiGroup.alertBox.showNumChoose(s0,1,sur0,1,1,this.getStoreState);
         this.tempBtn = this.cardBtn;
      }
      
      protected function highBtnClick(e:MouseEvent) : void
      {
         this.clearDelState();
         var sur0:int = this.dataG.getHighDrawCan();
         var s0:String = "选择高级抽卡次数：\n";
         s0 += ComMethod.color("次数剩余" + this.dataG.getHighDrawSurplus() + "  背包剩余" + this.dataG.getBagSurplus());
         Gaming.uiGroup.alertBox.showNumChoose(s0,1,sur0,1,1,this.getStoreState);
         this.tempBtn = this.highBtn;
      }
      
      protected function star7BtnClick(e:MouseEvent) : void
      {
         this.clearDelState();
         var sur0:int = this.dataG.get7DrawCan();
         var s0:String = "选择7星抽卡次数：\n";
         s0 += ComMethod.color("次数剩余" + this.dataG.get7DrawSurplus() + "  背包剩余" + this.dataG.getBagSurplus());
         Gaming.uiGroup.alertBox.showNumChoose(s0,1,sur0,1,1,this.getStoreState);
         this.tempBtn = this.star7Btn;
      }
      
      private function getStoreState(num0:int) : void
      {
         this.cardNum = num0;
         UIOrder.getStoreState(this.affterGetStoreState);
      }
      
      private function affterGetStoreState(v0:int) : void
      {
         if(v0 == 1 || v0 == -2)
         {
            Gaming.uiGroup.connectUI.hide();
            this.yesCard();
         }
      }
      
      private function yesCard() : void
      {
         var highB0:Boolean = false;
         var num0:int = 0;
         var sur0:int = 0;
         var i:int = 0;
         var s0:BossCardSave = null;
         if(BossCardCreator.panCodeB())
         {
            if(this.tempBtn == this.star7Btn)
            {
               this.yesCard7();
            }
            else
            {
               highB0 = this.tempBtn == this.highBtn;
               num0 = this.cardNum;
               sur0 = highB0 ? this.dataG.getHighDrawCan() : this.dataG.getDrawCan();
               if(num0 > sur0)
               {
                  num0 = sur0;
               }
               if(num0 > 0)
               {
                  for(i = 0; i < num0; i++)
                  {
                     s0 = BossCardCreator.getSave(this.dataG.getDrawNum(),highB0,this.dataG.getHSVMul());
                     this.dataG.addSave(s0);
                     this.dataG.drawEvent(highB0);
                  }
                  UIOrder.yesSaveTip = "成功抽得" + num0 + "张魂卡！";
                  UIOrder.save(true,false,false,this.yesSave,Gaming.uiGroup.bosseditUI.hide,false,true);
               }
            }
         }
         else
         {
            UIOrder.alertError("数据错误！");
         }
      }
      
      private function yesCard7() : void
      {
         var i:int = 0;
         var s0:BossCardSave = null;
         var num0:int = this.cardNum;
         var sur0:int = this.dataG.get7DrawCan();
         if(num0 > sur0)
         {
            num0 = sur0;
         }
         if(num0 > 0)
         {
            for(i = 0; i < num0; i++)
            {
               s0 = BossCardCreator.getSaveStar7();
               this.dataG.addSave(s0);
               this.dataG.drawEvent7();
            }
            UIOrder.yesSaveTip = "成功抽得" + num0 + "张魂卡！";
            UIOrder.save(true,false,false,this.yesSave,Gaming.uiGroup.bosseditUI.hide,false,true);
         }
      }
      
      private function yesSave(v:* = null) : void
      {
         this.fleshData();
      }
      
      private function fleshGripState() : void
      {
         var grip0:NormalBtn = null;
         var da0:BossCardData = null;
         var fightB0:Boolean = false;
         this.delBtn.setName(this.delB ? "结束" : "单个删除");
         this.delStarBtn.actived = !this.delB;
         for each(grip0 in this.box.gripArr)
         {
            da0 = grip0.itemsData as BossCardData;
            fightB0 = false;
            if(Boolean(da0))
            {
               fightB0 = this.dataG.fightPan(da0);
            }
            grip0.setSmallIconFrame(da0.getSmallFrame(fightB0,this.delB));
            grip0.setNewMc(da0.getIconNewLabel());
            da0.newB = false;
         }
      }
      
      private function fleshFightState() : void
      {
         var fightDa0:BossCardData = this.dataG.getFightData();
         if(Boolean(fightDa0))
         {
            this.inGripFun(this.fightBtn,fightDa0,true);
         }
         else
         {
            this.fightBtn.clearData();
            this.fightBtn.setSmallIconFrame(1);
            this.fightBtn.setStarIcon(0);
         }
         UIOrder.showTip("");
         this.fightTxt.htmlText = FontDeal.getDealLeadingStr(this.fightTxt,this.dataG.getUITimeTxt());
         this.fightPerSp.scaleX = this.dataG.getTimeSurplus() / this.dataG.getTimeMax();
      }
      
      protected function fightBtnClick(e:MouseEvent) : void
      {
         var da0:BossCardData = null;
         var grip0:NormalBtn = e.target as NormalBtn;
         if(Boolean(grip0))
         {
            da0 = grip0.itemsData as BossCardData;
            if(Boolean(da0))
            {
               this.gripDataClick(da0);
            }
         }
      }
      
      private function fightBtnOver(e:MouseEvent) : void
      {
         var da0:BossCardData = null;
         var grip0:NormalBtn = e.target as NormalBtn;
         if(Boolean(grip0))
         {
            da0 = grip0.itemsData as BossCardData;
            if(Boolean(da0))
            {
               this.gripDataOver(da0);
            }
         }
      }
      
      private function fightTxtOver(e:MouseEvent) : void
      {
         var s0:String = "1、禁止出战的关卡：主线任务、每周任务、军队争霸、禁止尸宠的关卡。";
         s0 += "\n\n2、每天有出战时间限制。";
         s0 += "\n\n3、每周还有取胜次数限制。敌人首领死亡视为一次取胜次数。";
         UIOrder.showTip(s0);
      }
      
      protected function autoBtnClick(e:MouseEvent) : void
      {
         this.dataG.autoDownB = !this.dataG.autoDownB;
         this.fleshAutoBtn();
      }
      
      protected function fleshAutoBtn() : void
      {
         this.autoBtn.isChosen = this.dataG.autoDownB;
      }
      
      protected function fleshAddText() : void
      {
         this.valueTxt.setText(this.dataG.getUIAddGather(3));
      }
      
      private function addTxtOver(e:MouseEvent) : void
      {
         var addS0:String = this.dataG.getUIAddGather();
         var s0:String = "系统会在所有魂卡中寻找最高属性作为加成。\n\n<yellow 当前加成：/>\n" + addS0;
         UIOrder.showTip(s0);
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var cn0:String = null;
         var listArr0:Array = null;
         Gaming.uiGroup.tipBox.hide();
         var btn0:NormalBtn = e.child as NormalBtn;
         var da0:BossCardData = e.childData as BossCardData;
         var fightB0:Boolean = this.dataG.fightPan(da0);
         if(this.delB)
         {
            if(fightB0 == false && da0.lockB == false)
            {
               cn0 = ComMethod.color(da0.cnName,GatherColor.yellowColor);
               cn0 += "(" + da0.getStar() + "星)";
               this.tempDa = da0;
               Gaming.uiGroup.alertBox.showChoose("是否删除魂卡：" + cn0,this.yesDel,null,da0.getIconUrl());
            }
         }
         else
         {
            btn0.isChosen = false;
            this.evoBox.clearEvoDa();
            ItemsGripBtnListCtrl.clearData();
            listArr0 = [];
            listArr0.push(fightB0 ? "defence" : "fight");
            listArr0.push(da0.lockB ? "unlock" : "lock");
            listArr0.push("copy");
            if(da0.canEvoB())
            {
               listArr0.push("advanced");
            }
            if(da0.canRemakeB())
            {
               listArr0.push("remake");
            }
            if(Gaming.isLocal())
            {
               listArr0.push("editPiano");
            }
            this.tempDa = da0;
            Gaming.uiGroup.btnList.showFun(listArr0,this.btnListClick,btn0);
         }
      }
      
      private function btnListClick(label0:String) : void
      {
         var da0:BossCardData = this.tempDa;
         var clearTempB0:Boolean = true;
         if(Boolean(da0))
         {
            if(label0 == "fight" || label0 == "defence")
            {
               this.gripDataClick(da0);
            }
            else if(label0 == "lock")
            {
               da0.lockB = true;
               Gaming.soundGroup.playAndAdd("uiSound/star");
               this.fleshGripState();
            }
            else if(label0 == "unlock")
            {
               da0.lockB = false;
               this.fleshGripState();
            }
            else if(label0 == "copy")
            {
               BcardPKCode.copyCode(da0);
            }
            else if(label0 == "advanced")
            {
               this.evoBox.showData(da0);
            }
            else if(label0 == "remake")
            {
               this.remakeBox.showData(da0);
            }
            else if(label0 == "editPiano")
            {
               Gaming.uiGroup.alertBox.textInput.showTextInput("输入技能代码","",this.editSkillByXml,"yesAndNo",999);
               clearTempB0 = false;
            }
            if(clearTempB0)
            {
               this.tempDa = null;
            }
         }
      }
      
      private function editSkillByXml(str0:String) : void
      {
         if(str0 != "")
         {
            this.tempDa.getCardSave().sr = str0.split(",");
            Gaming.uiGroup.alertBox.showSuccess("设置技能：" + StringMethod.concatStringArr(Gaming.defineGroup.skill.getCnArrByNameArr(this.tempDa.getSkillNameArr()),4));
         }
      }
      
      private function yesDel() : void
      {
         if(Boolean(this.tempDa))
         {
            this.dataG.removeData(this.tempDa);
            this.fleshData();
            Gaming.soundGroup.playAndAdd("uiSound/sell");
         }
         this.tempDa = null;
      }
      
      private function gripDataClick(da0:BossCardData) : void
      {
         var fightB0:Boolean = this.dataG.fightPan(da0);
         if(fightB0)
         {
            this.dataG.toDown(da0);
            Gaming.soundGroup.playSound("uiSound","giveupTask");
         }
         else
         {
            this.dataG.toFight(da0);
            Gaming.soundGroup.playAndAdd("uiSound/loadGem");
         }
         this.delB = false;
         this.fleshGripState();
         this.fleshFightState();
      }
      
      private function gripOver(e:ClickEvent) : void
      {
         var da0:BossCardData = e.childData as BossCardData;
         this.gripDataOver(da0);
      }
      
      private function gripDataOver(da0:BossCardData) : void
      {
         var tip0:String = da0.getGatherTip(this.delB);
         UIOrder.showTip(tip0);
         this.overDa = da0;
      }
      
      private function gripOut(e:* = null) : void
      {
         this.overDa = null;
         UIOrder.showTip("");
      }
      
      private function addBagBtnClick(e:MouseEvent) : void
      {
         var da0:GoodsData = this.getAddBagGoodsData();
         Gaming.uiGroup.alertBox.shop.showCheck(da0,this.yes_addBagBtnClick);
      }
      
      private function yes_addBagBtnClick() : void
      {
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         var price0:Number = da0.getPrice();
         var shopObj0:ShopBuyObject = da0.getShopObj();
         Gaming.uiGroup.connectUI.show();
         Gaming.api.shop.buyPropNd(shopObj0,this.do_addBagBtnClick);
      }
      
      private function do_addBagBtnClick() : void
      {
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         this.dataG.addBagNum(da0.nowNum);
         Gaming.PG.da.goods.addBuyNum(da0.def.name,da0.nowNum);
         Gaming.soundGroup.playSound("uiSound","changeLabel");
         this.fleshBox();
      }
      
      private function getAddBagGoodsData() : GoodsData
      {
         var da0:GoodsData = new GoodsData();
         var d0:GoodsDefine = Gaming.defineGroup.goods.getDefine("bcardBag");
         da0.def = d0;
         da0.playerData = Gaming.PG.da;
         da0.showTextType = "addPetBag";
         return da0;
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var funName0:String = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0.actived)
         {
            funName0 = btn0.label + "BtnClick";
            this[funName0](e);
         }
      }
      
      private function giftBtnClick(e:MouseEvent) : void
      {
         if(this.dataG.isBugMax())
         {
            Gaming.uiGroup.alertBox.showInfo("该功能暂时关闭。");
         }
         else
         {
            this.clearDelState();
            this.achieveGiftBox.show();
         }
      }
      
      public function FKey() : void
      {
         var tip0:String = null;
         if(visible && Boolean(this.overDa))
         {
            tip0 = this.overDa.getFTip();
            UIOrder.showTip(tip0);
         }
      }
   }
}

