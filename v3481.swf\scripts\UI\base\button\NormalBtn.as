package UI.base.button
{
   import UI.base.box.ArrangeSprite;
   import UI.base.font.FontDeal;
   import UI.base.grid.NormalGridIcon;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.MovieClipMethod;
   import fl.motion.ColorMatrix;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.filters.ColorMatrixFilter;
   import flash.filters.GlowFilter;
   import flash.geom.ColorTransform;
   import flash.text.TextField;
   
   public class NormalBtn extends ArrangeSprite
   {
      public static var over_CF:ColorTransform = new ColorTransform(1,1,1,1,50,50,50);
      
      public static var down_CF:ColorTransform = new ColorTransform(1,1,1,1,-100,-100,-100);
      
      public static var no_CF:ColorTransform = new ColorTransform(1,1,1,1,-100,-100,-100);
      
      public static var normal_CF:ColorTransform = new ColorTransform();
      
      public static var up_CF:ColorTransform = over_CF;
      
      public static var no_filter:ColorMatrixFilter = new ColorMatrixFilter([0.3086,0.6094,0.082,0,0,0.3086,0.6094,0.082,0,0,0.3086,0.6094,0.082,0,0,0,0,0,1,0]);
      
      protected static var lessGray_filter:ColorMatrixFilter = new ColorMatrixFilter([0.6542999744415283,0.30469998717308044,0.04100000113248825,0,0,0.1543000042438507,0.8047000169754028,0.04100000113248825,0,0,0.1543000042438507,0.30469998717308044,0.5410000085830688,0,0,0,0,0,1,0]);
      
      public var itemsData:Object = null;
      
      public var secData:* = null;
      
      public var data3:* = null;
      
      public var nameTxtColor:uint = 0;
      
      public var label:String = "";
      
      public var fatherUrl:String = "";
      
      public var index:int = 0;
      
      public var secoundEnabled:Boolean = true;
      
      protected var nowBtnLabel:String = "normal";
      
      public var mouseIconEffectB:Boolean = false;
      
      public var iconEffectB:Boolean = false;
      
      protected var _actived:Boolean = true;
      
      public var activedAndEnabled:Boolean = true;
      
      public var activedAndTextGray:Boolean = false;
      
      public var activedAndGray:Boolean = false;
      
      public var activedAndIgnoreChosen:Boolean = false;
      
      public var lockActivedB:Boolean = false;
      
      protected var _isChosen:Boolean = false;
      
      protected var img:MovieClip = null;
      
      public var iconCon:Sprite;
      
      public var icon:NormalGridIcon = null;
      
      protected var iconCon2:Sprite;
      
      protected var icon2:NormalGridIcon = null;
      
      protected var btnMc:MovieClip = null;
      
      protected var nameTxt:TextField;
      
      protected var chooseMc:MovieClip = null;
      
      protected var chooseBackMc:MovieClip = null;
      
      public var newMc:MovieClip;
      
      public var smallIconMc:MovieClip;
      
      public var starMc:MovieClip;
      
      public var numTxt:TextField;
      
      public var numBackSp:Sprite;
      
      public var sizeSp:Sprite;
      
      public var iconMaxWidth:int = 0;
      
      public var iconMaxHeight:int = 0;
      
      public var tipFollowMouseB:Boolean = false;
      
      public var extraBtn:NormalBtn = null;
      
      public function NormalBtn()
      {
         super();
         this.mouseChildren = false;
         this.buttonMode = true;
      }
      
      public function setImgUrl(url0:String) : void
      {
         this.setImg(Gaming.swfLoaderManager.getResourceFull(url0));
      }
      
      public function getImg() : MovieClip
      {
         return this.img;
      }
      
      public function setImg(img0:MovieClip) : void
      {
         if(Boolean(this.img))
         {
            INIT.showError("按钮已经有了img素材，不能重复添加！");
         }
         img0.visible = true;
         this.btnMc = img0["btnMc"];
         if(Boolean(this.btnMc))
         {
            this.btnMc.stop();
         }
         this.nameTxt = img0["nameTxt"];
         if(Boolean(this.nameTxt))
         {
            this.nameTxtColor = this.nameTxt.textColor;
         }
         this.chooseBackMc = img0["chooseBackMc"];
         this.chooseMc = img0["chooseMc"];
         if(Boolean(this.chooseMc))
         {
            this.chooseMc.visible = this._isChosen;
         }
         this.iconCon = img0["iconCon"];
         this.iconCon2 = img0["iconCon2"];
         this.newMc = img0["newMc"];
         if(Boolean(this.newMc))
         {
            if(this.newMc is MovieClip)
            {
               (this.newMc as MovieClip).stop();
            }
         }
         this.smallIconMc = img0["smallIconMc"];
         this.starMc = img0["starMc"];
         this.sizeSp = img0["sizeSp"];
         this.numTxt = img0["numTxt"];
         this.numBackSp = img0["numBackSp"];
         this.setNew(false);
         this.setSmallIcon("");
         this.setStarIcon();
         this.isChosen = false;
         if(Boolean(this.iconCon))
         {
            if(!this.icon)
            {
               this.icon = new NormalGridIcon();
            }
            this.iconCon.addChild(this.icon);
         }
         if(Boolean(this.iconCon2))
         {
            if(!this.icon2)
            {
               this.icon2 = new NormalGridIcon();
            }
            this.iconCon2.addChild(this.icon2);
         }
         this.img = img0;
         this.img.stop();
         this.x = this.img.x;
         this.y = this.img.y;
         this.img.x = 0;
         this.img.y = 0;
         addChild(this.img);
         this.addEL();
         this.dealFont();
      }
      
      protected function dealFont() : void
      {
         FontDeal.dealOne(this.nameTxt);
         FontDeal.dealOne(this.numTxt);
      }
      
      public function clear() : void
      {
         this.removeEL();
         this.clearData();
      }
      
      public function clearData() : void
      {
         this.itemsData = null;
         this.clearShow();
      }
      
      public function clearShow() : void
      {
         if(Boolean(this.icon))
         {
            this.icon.clearData();
         }
         if(Boolean(this.icon2))
         {
            this.icon2.clearData();
         }
         this.isChosen = false;
         hidingB = false;
      }
      
      public function clearImg() : void
      {
         this.removeEL();
         if(Boolean(this.icon))
         {
            this.icon.clear();
         }
         if(Boolean(this.icon2))
         {
            this.icon2.clear();
         }
         if(Boolean(this.img))
         {
            removeChild(this.img);
            this.img = null;
         }
      }
      
      public function stopAll() : void
      {
         if(Boolean(this.icon))
         {
            this.icon.stopAll();
         }
         if(Boolean(this.icon2))
         {
            this.icon2.stopAll();
         }
         if(Boolean(this.chooseMc))
         {
            this.setChosenPlay(false);
         }
         if(Boolean(this.smallIconMc))
         {
            if(Boolean(this.smallIconMc["mc"]))
            {
               this.smallIconMc["mc"].stop();
            }
         }
      }
      
      public function playAll() : void
      {
         if(Boolean(this.icon))
         {
            this.icon.playAll();
         }
         if(Boolean(this.icon2))
         {
            this.icon2.playAll();
         }
         if(Boolean(this.chooseMc))
         {
            this.setChosenPlay(true);
         }
         if(Boolean(this.smallIconMc))
         {
            if(Boolean(this.smallIconMc["mc"]))
            {
               this.smallIconMc["mc"].play();
            }
         }
      }
      
      public function setName(str0:String, htmlB0:Boolean = true, leadPanB0:Boolean = false) : void
      {
         if(this.nameTxt is TextField)
         {
            if(htmlB0)
            {
               if(str0 != "")
               {
                  if(leadPanB0)
                  {
                     str0 = FontDeal.getDealLeadingStr(this.nameTxt,str0);
                  }
               }
               this.nameTxt.htmlText = str0;
            }
            else
            {
               this.nameTxt.text = str0;
            }
         }
      }
      
      public function getNameTxt() : TextField
      {
         return this.nameTxt;
      }
      
      public function getNameString() : String
      {
         if(this.nameTxt is TextField)
         {
            return this.nameTxt.text;
         }
         return "";
      }
      
      public function setGrayFilter(bb0:Boolean) : void
      {
         this.filters = bb0 ? [no_filter] : [];
      }
      
      public function setLessGrayFilter(bb0:Boolean) : void
      {
         this.filters = bb0 ? [lessGray_filter] : [];
      }
      
      public function setIconGrayFilter(bb0:Boolean) : void
      {
         this.iconCon.filters = bb0 ? [no_filter] : [];
      }
      
      public function setActivedAndLock(bb0:Boolean) : void
      {
         this.actived = bb0;
         this.lockActivedB = true;
      }
      
      public function set actived(bb:Boolean) : void
      {
         this._actived = bb;
         if(Boolean(this.nameTxt) && this.activedAndTextGray)
         {
            this.nameTxt.textColor = bb ? this.nameTxtColor : 10066329;
         }
         if(this.activedAndEnabled)
         {
            this.mouseEnabled = bb;
         }
         this.buttonMode = bb;
         if(this.activedAndGray)
         {
            this.setGrayFilter(!bb);
         }
         if(bb || this._isChosen && !this.activedAndIgnoreChosen)
         {
            this.setBtnBack("normal");
         }
         else
         {
            this.setBtnBack("no");
         }
      }
      
      public function get actived() : Boolean
      {
         return this._actived;
      }
      
      public function setAllActived(bb0:Boolean) : void
      {
         this.actived = bb0;
         if(Boolean(this.extraBtn))
         {
            this.extraBtn.actived = bb0;
         }
      }
      
      public function setAllVisible(bb0:Boolean) : void
      {
         visible = bb0;
         if(Boolean(this.extraBtn))
         {
            this.extraBtn.visible = bb0;
         }
      }
      
      public function setBtnBack(str0:String) : void
      {
         this.nowBtnLabel = str0;
         if(this.btnMc is Sprite)
         {
            this.btnMc.gotoAndStop(str0);
         }
         else if(this.img.totalFrames > 1)
         {
            this.img.gotoAndStop(str0);
         }
         if(this.chooseBackMc is MovieClip)
         {
            if(this.chooseBackMc.totalFrames > 1)
            {
               this.chooseBackMc.gotoAndStop(str0);
            }
         }
         if(this.mouseIconEffectB)
         {
            if(Boolean(this.icon))
            {
               this.icon.transform.colorTransform = NormalBtn[this.nowBtnLabel + "_CF"];
            }
            if(Boolean(this.icon2))
            {
               this.icon2.transform.colorTransform = NormalBtn[this.nowBtnLabel + "_CF"];
            }
         }
         if(this.iconEffectB)
         {
            this.transform.colorTransform = NormalBtn[this.nowBtnLabel + "_CF"];
         }
      }
      
      public function set isChosen(bb0:Boolean) : void
      {
         this._isChosen = bb0;
         if(this.chooseMc is Sprite)
         {
            if(Boolean(this.chooseBackMc))
            {
               this.chooseMc.visible = this.chooseBackMc.visible && bb0;
            }
            else
            {
               this.chooseMc.visible = bb0;
            }
            if(bb0)
            {
               this.setChosenPlay(true);
            }
            else
            {
               this.setChosenPlay(false);
            }
         }
      }
      
      public function get isChosen() : Boolean
      {
         return this._isChosen;
      }
      
      public function setChosenLabel(label0:String) : void
      {
         if(Boolean(this.chooseMc))
         {
            this.chooseMc.gotoAndStop(label0);
         }
      }
      
      private function setChosenPlay(bb0:Boolean) : void
      {
         if(Boolean(this.chooseMc))
         {
            if(bb0)
            {
               if(this.chooseMc.visible)
               {
                  if(this.chooseMc.totalFrames > 3)
                  {
                     this.chooseMc.play();
                  }
               }
            }
            else
            {
               this.chooseMc.stop();
            }
         }
      }
      
      public function setChooseVisible(bb0:Boolean) : *
      {
         if(Boolean(this.chooseBackMc))
         {
            this.chooseBackMc.visible = bb0;
            this.chooseMc.visible = bb0 && this._isChosen;
         }
      }
      
      public function getChooseMc() : MovieClip
      {
         return this.chooseMc;
      }
      
      public function setIconName(str0:String, middleB0:Boolean = true) : void
      {
         if(this.icon is Sprite)
         {
            this.icon.setIconName(str0,middleB0);
         }
      }
      
      public function setIconName2(str0:String, middleB0:Boolean = true) : void
      {
         if(this.icon2 is Sprite)
         {
            this.icon2.setIconName(str0,middleB0);
         }
      }
      
      public function setIconScale(scale0:Number) : void
      {
         if(this.icon is Sprite)
         {
            this.icon.scaleX = scale0;
            this.icon.scaleY = scale0;
         }
      }
      
      public function setSaturationFilter(v0:Number) : void
      {
         var m0:ColorMatrix = new ColorMatrix();
         m0.SetSaturationMatrix(v0);
         var f0:ColorMatrixFilter = new ColorMatrixFilter(m0.GetFlatArray());
         this.filters = [f0];
      }
      
      public function setNew(bb0:Boolean) : void
      {
         if(this.newMc is Sprite)
         {
            this.newMc.visible = bb0;
         }
      }
      
      public function setNewMc(label0:*) : void
      {
         MovieClipMethod.mcGoto(this.newMc,label0);
      }
      
      public function setSmallIcon(str0:String, samePanB0:Boolean = false) : void
      {
         if(this.getSmallIconStr() == str0 && samePanB0)
         {
            return;
         }
         if(this.smallIconMc is Sprite)
         {
            this.smallIconMc.gotoAndStop(1);
            if(str0 == "")
            {
               this.smallIconMc.visible = false;
            }
            else
            {
               this.smallIconMc.visible = true;
               this.smallIconMc.gotoAndStop(str0);
            }
         }
      }
      
      public function setSmallVisible(bb0:Boolean) : void
      {
         if(Boolean(this.smallIconMc))
         {
            this.smallIconMc.visible = bb0;
         }
      }
      
      public function setSmallIconFrame(f0:int) : void
      {
         if(this.smallIconMc is Sprite)
         {
            this.smallIconMc.visible = true;
            this.smallIconMc.gotoAndStop(f0);
         }
      }
      
      public function setSmallIconPer(per0:Number) : void
      {
         if(per0 > 1)
         {
            per0 = 1;
         }
         if(per0 < 0)
         {
            per0 = 0;
         }
         this.smallIconMc.scaleX = per0;
         this.smallIconMc.visible = true;
      }
      
      public function getSmallIconStr() : String
      {
         if(this.smallIconMc is Sprite)
         {
            return this.smallIconMc.currentFrameLabel;
         }
         return "";
      }
      
      public function setStarIcon(v0:Object = "") : void
      {
         if(this.starMc is MovieClip)
         {
            this.starMc.gotoAndStop(1);
            if(v0 == "" || v0 == 0)
            {
               this.starMc.visible = false;
            }
            else
            {
               this.starMc.visible = true;
               this.starMc.gotoAndStop(v0);
            }
         }
      }
      
      public function setNumText(str0:String, leadPanB0:Boolean = false) : void
      {
         if(this.numTxt is TextField)
         {
            if(leadPanB0)
            {
               str0 = FontDeal.getDealLeadingStr(this.numTxt,str0);
            }
            this.numTxt.htmlText = str0;
            this.numTxt.visible = str0 != "";
            if(Boolean(this.numBackSp))
            {
               this.numBackSp.visible = this.numTxt.visible;
            }
         }
      }
      
      public function setMustNumText(now0:int, must0:int, enoughColor0:String = "#00FF00", noColor0:String = "#FF6D48") : void
      {
         if(this.numTxt is TextField)
         {
            if(must0 <= now0)
            {
               this.numTxt.htmlText = ComMethod.color(now0 + "",enoughColor0) + "/" + must0;
            }
            else
            {
               this.numTxt.htmlText = ComMethod.color(now0 + "",noColor0) + "/" + must0;
            }
            this.numTxt.visible = true;
            if(Boolean(this.numBackSp))
            {
               this.numBackSp.visible = this.numTxt.visible;
            }
         }
      }
      
      public function getIcon() : DisplayObject
      {
         return this.icon;
      }
      
      public function getIcon2() : DisplayObject
      {
         return this.icon2;
      }
      
      public function removeIcon() : Sprite
      {
         return this.icon.removeIcon();
      }
      
      public function iconLeave() : void
      {
         if(this.icon is Sprite)
         {
            this.icon.visible = false;
         }
         if(this.icon2 is Sprite)
         {
            this.icon2.visible = false;
         }
         if(this.nameTxt is TextField)
         {
            this.nameTxt.visible = false;
         }
      }
      
      public function iconReturn() : void
      {
         if(this.icon is Sprite)
         {
            this.icon.visible = true;
         }
         if(this.icon2 is Sprite)
         {
            this.icon2.visible = true;
         }
         if(this.nameTxt is TextField)
         {
            this.nameTxt.visible = true;
         }
      }
      
      public function setIconLight(color0:uint = 0, size:int = 5, strength0:int = 1) : void
      {
         var f0:GlowFilter = null;
         if(this.icon is Sprite)
         {
            if(color0 == 0)
            {
               this.icon.filters = [];
            }
            else
            {
               f0 = new GlowFilter(color0,1,size,size,strength0);
               this.icon.filters = [f0];
            }
            if(Boolean(this.icon2))
            {
               this.icon2.filters = this.icon.filters;
            }
         }
      }
      
      protected function addEL() : void
      {
         this.addEventListener(MouseEvent.MOUSE_OVER,this.MOver);
         this.addEventListener(MouseEvent.MOUSE_DOWN,this.MDown);
         this.addEventListener(MouseEvent.MOUSE_UP,this.MUp);
         this.addEventListener(MouseEvent.MOUSE_OUT,this.MOut);
      }
      
      protected function removeEL() : void
      {
         this.removeEventListener(MouseEvent.MOUSE_OVER,this.MOver);
         this.removeEventListener(MouseEvent.MOUSE_DOWN,this.MDown);
         this.removeEventListener(MouseEvent.MOUSE_UP,this.MUp);
         this.removeEventListener(MouseEvent.MOUSE_OUT,this.MOut);
      }
      
      protected function MOver(event:MouseEvent) : *
      {
         if(this._actived)
         {
            this.setBtnBack("over");
         }
      }
      
      protected function MOut(event:MouseEvent) : *
      {
         if(this._actived)
         {
            this.setBtnBack("normal");
         }
      }
      
      protected function MDown(event:MouseEvent) : *
      {
         if(this._actived)
         {
            Gaming.soundGroup.playSound("uiSound","click");
            this.setBtnBack("down");
         }
      }
      
      protected function MUp(event:MouseEvent) : *
      {
         if(this._actived)
         {
            this.setBtnBack("over");
         }
      }
      
      override public function get width() : Number
      {
         if(this.sizeSp is Sprite)
         {
            return this.sizeSp.width;
         }
         if(this.btnMc is MovieClip)
         {
            return this.btnMc.width;
         }
         return super.width;
      }
      
      override public function get height() : Number
      {
         if(this.sizeSp is Sprite)
         {
            return this.sizeSp.height;
         }
         if(this.btnMc is MovieClip)
         {
            return this.btnMc.height;
         }
         return super.height;
      }
   }
}

