package UI.vehicle
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.UIShow;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.grid.NormalGridIcon;
   import UI.base.must.NormalMustBox;
   import UI.base.oneKey.OneKeyCtrl;
   import dataAll.equip.vehicle.VehicleData;
   import dataAll.equip.vehicle.VehicleDataCreator;
   import dataAll.equip.vehicle.VehicleDefine;
   import dataAll.equip.vehicle.VehicleOneProData;
   import dataAll.equip.vehicle.VehicleOneProDataGroup;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class VehicleStrengthenBoard extends NormalUI
   {
      private var proTag:Sprite;
      
      private var imgTag:Sprite;
      
      private var mustTag:Sprite;
      
      private var btnSp:MovieClip;
      
      private var remakeBtnSp:MovieClip;
      
      private var coverSp:Sprite;
      
      private var numTxt:TextField;
      
      private var numHook:MovieClip;
      
      private var pointer:Sprite;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var remakeBtn:NormalBtn = new NormalBtn();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var proBox:ItemsGripBox = new ItemsGripBox();
      
      private var imgIcon:NormalGridIcon = new NormalGridIcon();
      
      private var tipStr:String = "";
      
      private var nowIsRemakeB:Boolean = false;
      
      private var proG:VehicleOneProDataGroup = null;
      
      public var vehicleUI:VehicleUI;
      
      private var numKey:OneKeyCtrl = new OneKeyCtrl();
      
      private var numKeyBtnSp:MovieClip;
      
      private var numKeyBtn:NormalBtn = new NormalBtn();
      
      public function VehicleStrengthenBoard()
      {
         super();
      }
      
      public function get nowData() : VehicleData
      {
         return Gaming.uiGroup.vehicleUI.nowData;
      }
      
      public function set nowData(v0:VehicleData) : void
      {
         Gaming.uiGroup.vehicleUI.nowData = v0;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["pointer","remakeBtnSp","numHook","numTxt","proTag","imgTag","mustTag","btnSp","coverSp","numKeyBtnSp"];
         super.setImg(img0);
         FontDeal.dealOne(this.numTxt);
         this.imgTag.mouseChildren = false;
         this.imgTag.mouseEnabled = false;
         this.imgTag.addChild(this.imgIcon);
         addChild(this.mustBox);
         this.mustBox.setNormalImg();
         this.mustBox.x = this.mustTag.x;
         this.mustBox.y = this.mustTag.y;
         this.numHook.stop();
         addChild(this.proBox);
         NormalUICtrl.setTag(this.proBox,this.proTag);
         this.proBox.arg.init(1,6,0,4);
         this.proBox.evt.setWantEvent(true,false,false,true,true);
         this.proBox.setIconPro("VehicleUI/proBar",50,50);
         this.proBox.addEventListener(ClickEvent.ON_CLICK,this.proBarClick);
         this.proBox.addEventListener(ClickEvent.ON_OVER,this.proBarOver);
         this.proBox.addEventListener(ClickEvent.ON_OUT,this.proBarOut);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.activedAndEnabled = false;
         this.btn.actived = false;
         this.btn.setName("强化");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.btn.addEventListener(MouseEvent.MOUSE_OVER,this.btnOver);
         this.btn.addEventListener(MouseEvent.MOUSE_OUT,this.btnOut);
         addChild(this.remakeBtn);
         this.remakeBtn.setImg(this.remakeBtnSp);
         this.remakeBtn.activedAndEnabled = false;
         this.remakeBtn.actived = false;
         this.remakeBtn.setName("洗炼");
         this.remakeBtn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.remakeBtn.addEventListener(MouseEvent.MOUSE_OVER,this.btnOver);
         this.remakeBtn.addEventListener(MouseEvent.MOUSE_OUT,this.btnOut);
         addChild(this.numKeyBtn);
         this.numKeyBtn.setImg(this.numKeyBtnSp);
         this.numKeyBtn.activedAndEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.numKeyBtn);
         this.numKeyBtn.addEventListener(MouseEvent.CLICK,this.numKeyBtnClick);
         this.numKeyBtn.tipString = "一键强化";
         this.numKey.startFun = this.numKeyStart;
         this.numKey.endFun = this.numKeyEnd;
         this.setCoverText("");
         addChild(this.coverSp);
         this.clearMust();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      protected function setCoverText(str0:String) : void
      {
         if(str0 == "")
         {
            this.coverSp.visible = false;
         }
         else
         {
            this.coverSp.visible = true;
            (this.coverSp["txt"] as TextField).htmlText = str0;
         }
      }
      
      private function getNowAddLv() : int
      {
         if(Boolean(this.nowData))
         {
            return this.nowData.getVehicleSave().getAllAddLv();
         }
         return 0;
      }
      
      override public function show() : void
      {
         super.show();
         this.vehicleUI.itemsBox.setChoose_byIndex(-1);
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      private function fleshData() : void
      {
         this.showData(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.clearData();
      }
      
      public function gripClick(e:ClickEvent) : void
      {
         if(visible)
         {
            this.showData(e.childData as VehicleData);
         }
      }
      
      private function clearData() : void
      {
         this.nowData = null;
         this.proG = null;
         this.proBox.clearAllData();
         this.setCoverText("请在右边选择你要强化的载具");
      }
      
      private function showData(da0:VehicleData) : void
      {
         var proB0:Boolean = false;
         var mustB0:Boolean = false;
         var noMaxB0:Boolean = false;
         var bb0:Boolean = false;
         var grip0:ItemsGrid = this.vehicleUI.itemsBox.findGripByData(da0);
         if(!(grip0 is ItemsGrid))
         {
            this.clearData();
         }
         else
         {
            this.setCoverText("");
            this.vehicleUI.itemsBox.setChoose_byIndex(grip0.index);
            this.nowData = da0;
            this.imgIcon.setIconName(da0.getVehicleSave().getVehicleDefine().getBodyDefine().bmpUrl);
            this.proG = VehicleDataCreator.getStrengthenProDataGroup(da0,this.proG);
            proB0 = this.showPro(this.proG);
            mustB0 = this.showMust(da0,this.proG);
            noMaxB0 = this.showNum(da0);
            bb0 = proB0 && mustB0 && noMaxB0;
            if(this.nowIsRemakeB)
            {
               this.remakeBtn.actived = bb0;
               this.remakeBtn.scaleX = 1;
               this.remakeBtn.scaleY = this.remakeBtn.scaleX;
               this.btn.scaleX = 0.9;
               this.btn.scaleY = this.btn.scaleX;
               this.btn.actived = false;
               this.pointer.x = this.remakeBtn.x;
            }
            else
            {
               this.btn.actived = bb0;
               this.remakeBtn.actived = false;
               this.remakeBtn.scaleX = 0.9;
               this.remakeBtn.scaleY = this.remakeBtn.scaleX;
               this.btn.scaleX = 1;
               this.btn.scaleY = this.btn.scaleX;
               this.pointer.x = this.btn.x;
            }
            this.numKeyBtn.actived = this.btn.actived;
         }
      }
      
      private function showPro(proG0:VehicleOneProDataGroup) : Boolean
      {
         this.proBox.inData_byArr(this.proG.arr,"inData_proData");
         return proG0.isCanStrengthenB();
      }
      
      private function showMust(da0:VehicleData, proG0:VehicleOneProDataGroup) : Boolean
      {
         var d0:VehicleDefine = da0.getVehicleSave().getVehicleDefine();
         var must_d0:MustDefine = VehicleDataCreator.getStrengthenMust(da0,proG0,this.nowIsRemakeB);
         return this.mustBox.inData(must_d0,Gaming.PG.da.level);
      }
      
      private function showNum(da0:VehicleData) : Boolean
      {
         var now0:int = 0;
         now0 = da0.getVehicleSave().getAllAddLv();
         var max0:int = VehicleDataCreator.getStrengthenAllLv();
         this.numTxt.htmlText = now0 + "/" + max0;
         this.numHook.gotoAndStop(now0 < max0 ? 1 : 2);
         if(this.nowIsRemakeB)
         {
            this.numHook.visible = false;
            return now0 >= 1;
         }
         this.numHook.visible = true;
         return now0 < max0;
      }
      
      private function clearMust() : void
      {
         this.btn.actived = false;
         this.numKeyBtn.actived = this.btn.actived;
         this.mustBox.setShowState(false);
      }
      
      private function proBarClick(e:ClickEvent) : void
      {
      }
      
      private function proBarOver(e:ClickEvent) : void
      {
         var da0:VehicleOneProData = e.childData as VehicleOneProData;
         Gaming.uiGroup.tipBox.textTip.showFollowText(da0.tipString);
      }
      
      private function proBarOut(e:ClickEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function btnOver(e:MouseEvent) : void
      {
         if(this.numKey.ingB)
         {
            return;
         }
         Gaming.soundGroup.playSound("uiSound","changeLabel");
         if(e.target == this.btn)
         {
            this.nowIsRemakeB = false;
            Gaming.uiGroup.tipBox.textTip.showFollowText("随机挑选1条属性进行强化，强化1级。");
         }
         else
         {
            this.nowIsRemakeB = true;
            Gaming.uiGroup.tipBox.textTip.showFollowText("随机每条属性的强化等级，保证总强化等级不变。");
         }
         this.fleshData();
      }
      
      private function btnOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function btnClick(e:MouseEvent = null, btn0:NormalBtn = null) : void
      {
         if(btn0 == null)
         {
            btn0 = e.target as NormalBtn;
         }
         if(btn0.actived)
         {
            PlayerMustCtrl.deductMust(this.getMust(),this.affterBtnClick,this.stopNumKey);
         }
         else
         {
            this.stopNumKey();
         }
      }
      
      private function getMust() : MustDefine
      {
         return VehicleDataCreator.getStrengthenMust(this.nowData,this.proG,this.nowIsRemakeB);
      }
      
      private function affterBtnClick() : void
      {
         if(this.numKey.ingB)
         {
            this.numKey.useMust(this.getMust());
         }
         var da0:VehicleData = this.nowData;
         var proG0:VehicleOneProDataGroup = this.proG;
         if(this.nowIsRemakeB)
         {
            this.tipStr = VehicleDataCreator.remakeStrengthenOne(da0,proG0);
         }
         else
         {
            this.tipStr = VehicleDataCreator.strengthenOne(da0,proG0);
         }
         if(this.numKey.ingB)
         {
            this.nextNumKey(true);
         }
         else
         {
            this.fleshData();
            Gaming.uiGroup.vehicleUI.fleshBag();
            UIOrder.save(true,true,false,this.affterSave,UIShow.hideNowApp,false,true);
         }
      }
      
      private function affterSave(v:*) : void
      {
         Gaming.uiGroup.alertBox.showSuccess(this.tipStr);
      }
      
      private function numKeyBtnClick(e:MouseEvent) : void
      {
         var now0:int = 0;
         var max0:int = 0;
         if(this.numKeyBtn.actived)
         {
            if(Boolean(this.nowData))
            {
               now0 = this.getNowAddLv();
               max0 = VehicleDataCreator.getStrengthenAllLv();
               Gaming.uiGroup.alertBox.showNumChoose("选择强化次数",1,max0 - now0,1,1,this.yesNumKey);
            }
         }
      }
      
      private function yesNumKey(num0:int) : void
      {
         if(Boolean(this.nowData))
         {
            Gaming.uiGroup.connectUI.show("数据处理中……",1);
            this.numKey.start(num0,this.nowData.getVehicleSave().getAllAddLv());
         }
      }
      
      private function numKeyStart() : void
      {
         this.btnClick(null,this.btn);
      }
      
      private function numKeyEnd() : void
      {
         if(this.numKey.getSaveB())
         {
            UIOrder.save(true,true,false,this.yes_numKeyEnd,UIShow.hideNowApp,false,true);
         }
         else
         {
            this.yes_numKeyEnd();
         }
      }
      
      private function yes_numKeyEnd(v:* = null) : void
      {
         var tip0:String = null;
         Gaming.uiGroup.connectUI.hide();
         if(this.numKey.getNum() > 0)
         {
            this.fleshData();
            Gaming.uiGroup.vehicleUI.fleshBag();
            tip0 = "强化等级变化：" + this.numKey.getLevelChangeStr();
            tip0 += "\n强化次数：" + this.numKey.getNum() + "次";
            if(Boolean(this.numKey.getGift()))
            {
               tip0 += "\n消耗材料：" + this.numKey.getGift().getDescription();
            }
            Gaming.uiGroup.alertBox.showSuccess(tip0);
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("当前无法一键强化。");
         }
         this.numKey.clear();
      }
      
      private function nextNumKey(saveB0:Boolean) : void
      {
         this.fleshData();
         this.numKey.next(this.getNowAddLv(),saveB0);
      }
      
      private function stopNumKey(v0:* = null) : void
      {
         this.numKey.doErrorIng("强化中止！");
      }
   }
}

