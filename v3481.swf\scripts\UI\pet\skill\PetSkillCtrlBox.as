package UI.pet.skill
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import UI.base.must.NormalMustBox;
   import UI.pet.PetUI;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.pet.PetData;
   import dataAll.skill.HeroSkillData;
   import dataAll.skill.define.HeroSkillDefine;
   import dataAll.skill.save.HeroSkillSave;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.system.System;
   import flash.text.TextField;
   
   public class PetSkillCtrlBox extends NormalUI
   {
      public var father:PetSkillBoard;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var skillGrip:ItemsGrid = new ItemsGrid();
      
      private var mustTag:Sprite;
      
      private var btnSp:MovieClip;
      
      private var gripTag:Sprite;
      
      private var nameTxt:TextField;
      
      private var infoTxt:TextField;
      
      private var beforeTxt:TextField;
      
      private var nextTxt:TextField;
      
      private var nowData:HeroSkillData;
      
      private var nowDefine:HeroSkillDefine;
      
      public function PetSkillCtrlBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["mustTag","btnSp","gripTag","nameTxt","infoTxt","beforeTxt","nextTxt"];
         super.setImg(img0);
         FontDeal.dealOne(this.nameTxt);
         FontDeal.dealLine(this.infoTxt);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("升级");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         addChild(this.skillGrip);
         this.skillGrip.setImgToEquipGrip();
         NormalUICtrl.setTag(this.skillGrip,this.gripTag);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.skillGrip);
         addChild(this.mustBox);
         this.mustBox.setNormalImg();
         NormalUICtrl.setTag(this.mustBox,this.mustTag);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function showObj(obj0:Object) : void
      {
         this.nowData = null;
         this.nowDefine = null;
         if(obj0 is HeroSkillData)
         {
            this.nowData = obj0 as HeroSkillData;
            this.showUpgradeByData(this.nowData);
            if(Gaming.testCtrl.cheating.enabled)
            {
               System.setClipboard(this.nowData.save.getDefine().getProjectText());
            }
         }
         else if(obj0 is HeroSkillDefine)
         {
            this.nowDefine = obj0 as HeroSkillDefine;
            this.showMustByDefine(this.nowDefine);
            if(Gaming.testCtrl.cheating.enabled)
            {
               System.setClipboard(this.nowDefine.getProjectText());
            }
         }
         else
         {
            this.clearAll();
         }
      }
      
      public function getSkillGrip() : ItemsGrid
      {
         return this.skillGrip;
      }
      
      public function clearAll() : void
      {
         this.nowData = null;
         this.nowDefine = null;
         this.mustBox.setShowState(false);
         this.btn.actived = false;
         this.beforeTxt.htmlText = "";
         this.nextTxt.htmlText = "";
         this.skillGrip.clearData();
         this.nameTxt.text = "";
         this.infoTxt.htmlText = "";
      }
      
      public function getNowData() : HeroSkillData
      {
         return this.nowData;
      }
      
      public function getNowBaseLabel() : String
      {
         if(Boolean(this.nowDefine))
         {
            return this.nowDefine.baseLabel;
         }
         return this.nowData.save.baseLabel;
      }
      
      private function showUpgradeByData(da0:HeroSkillData) : void
      {
         var must_d0:MustDefine = null;
         var bb0:Boolean = false;
         var petData0:PetData = PetUI.getNowData();
         var s0:HeroSkillSave = da0.save;
         var lv_d0:HeroSkillDefine = s0.getDefine();
         var next_d0:HeroSkillDefine = s0.getNextDefine();
         var maxLevelB0:Boolean = !(next_d0 is HeroSkillDefine);
         this.skillGrip.inData_skill(da0);
         this.nameTxt.text = lv_d0.cnName;
         this.infoTxt.htmlText = lv_d0.getDescriptionNoActiveCd();
         this.beforeTxt.htmlText = lv_d0.getUIInfoText("当前");
         this.btn.actived = false;
         this.btn.setName("升级");
         if(maxLevelB0)
         {
            this.mustBox.setShowState(false);
            this.nextTxt.htmlText = ComMethod.color("已升至最高等级了","#FF9900");
         }
         else
         {
            this.nextTxt.htmlText = next_d0.getUIInfoText("升级后");
            must_d0 = s0.getUpradeMust();
            bb0 = this.mustBox.inData(must_d0,petData0.base.save.level,"尸宠");
            this.btn.actived = bb0;
         }
      }
      
      private function showMustByDefine(d0:HeroSkillDefine) : void
      {
         var petData0:PetData = PetUI.getNowData();
         var lv_d0:HeroSkillDefine = d0;
         this.skillGrip.inData_SkillDefine(d0);
         this.nameTxt.text = lv_d0.cnName;
         this.infoTxt.htmlText = lv_d0.getDescriptionNoActiveCd();
         this.beforeTxt.htmlText = ComMethod.color("未学习","#FF9900");
         this.nextTxt.htmlText = lv_d0.getUIInfoText("学习后");
         var mustLv0:int = petData0.getSkillStudyMustLv();
         var must_d0:MustDefine = d0.getThisStudyMust(mustLv0);
         var bb0:Boolean = this.mustBox.inData(must_d0,petData0.base.save.level,"尸宠");
         this.btn.actived = bb0;
         this.btn.setName("学习");
      }
      
      public function btnClick(e:MouseEvent) : void
      {
         var da0:HeroSkillData = null;
         var s0:HeroSkillSave = null;
         var mustLv0:int = 0;
         var must_d0:MustDefine = null;
         if(Boolean(this.nowData))
         {
            da0 = this.nowData;
            s0 = da0.save;
            must_d0 = s0.getUpradeMust();
         }
         else if(Boolean(this.nowDefine))
         {
            mustLv0 = PetUI.getNowData().getSkillStudyMustLv();
            must_d0 = this.nowDefine.getThisStudyMust(mustLv0);
         }
         if(Boolean(must_d0))
         {
            PlayerMustCtrl.deductMust(must_d0,Boolean(this.nowData) ? this.affter_upgrade : this.affter_study);
         }
      }
      
      private function affter_upgrade() : void
      {
         var da0:HeroSkillData = this.nowData;
         var s0:HeroSkillSave = da0.save;
         s0.upgrade();
         Gaming.soundGroup.playSound("uiSound","success");
         Gaming.uiGroup.alertBox.showCheck("升级成功！","",0.4,null,null,"yes","icon");
         this.father.fleshData();
      }
      
      private function affter_study() : void
      {
         var d0:HeroSkillDefine = this.nowDefine;
         var mustLv0:int = PetUI.getNowData().getSkillStudyMustLv();
         PetUI.getNowData().skill.addSkillByLabel(d0.name,mustLv0);
         Gaming.soundGroup.playSound("uiSound","success");
         var tipText0:String = "获得新技能：" + ComMethod.color(d0.cnName,"#FF6600") + "。";
         Gaming.uiGroup.alertBox.showCheck(tipText0,"",0.5,null,null,d0.iconUrl,"equip");
         this.father.fleshData();
         Gaming.uiGroup.mainUI.fleshCoin();
      }
   }
}

