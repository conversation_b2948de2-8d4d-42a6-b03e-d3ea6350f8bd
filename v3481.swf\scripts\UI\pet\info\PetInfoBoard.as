package UI.pet.info
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.bag.wear.NameChangeBox;
   import UI.base.btnList.BtnBox;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.loadBar.LoadBar;
   import UI.base.must.NormalExpendBox;
   import UI.pet.PetUI;
   import UI.pet.strengthen.PetStrengthenBoard;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.equip.EquipPropertyData;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.pet.PetData;
   import dataAll.pet.PetDataGroup;
   import dataAll.pet.PetState;
   import dataAll.pet.base.PetBaseData;
   import dataAll.pet.base.PetBaseSave;
   import dataAll.pro.PropertyArrayDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   import flash.text.TextField;
   
   public class PetInfoBoard extends BtnBox
   {
      private var nameTxt:TextField;
      
      private var dpsTxt:TextField;
      
      private var typeTxt:TextField;
      
      private var skillTag:Sprite;
      
      private var imgTag:Sprite;
      
      private var loadBarSp:Sprite;
      
      private var lvTxt:TextField;
      
      private var baseTxt:TextField;
      
      private var baseValueTxt:TextField;
      
      private var qualTxt:TextField;
      
      private var qualValueTxt:TextField;
      
      private var uplevelBoxSp:Sprite;
      
      private var nameChangeSp:Sprite;
      
      private var nameChangeBox:NameChangeBox = new NameChangeBox();
      
      private var uplevelBox:PetUplevelBox = new PetUplevelBox();
      
      private var expBar:LoadBar = new LoadBar();
      
      private var skillBox:ItemsGripBox = new ItemsGripBox();
      
      private var imgMc:MovieClip;
      
      private var suppleExpendData:PetSuppleExpendUIData = new PetSuppleExpendUIData();
      
      public function PetInfoBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var label0:* = null;
         elementNameArr = ["nameChangeSp","uplevelBoxSp","lvTxt","nameTxt","dpsTxt","typeTxt","skillTag","imgTag","loadBarSp","baseTxt","baseValueTxt","qualTxt","qualValueTxt"];
         super.setImg(img0);
         FontDeal.dealLine(this.baseTxt);
         FontDeal.dealLine(this.baseValueTxt);
         FontDeal.dealLine(this.qualTxt);
         FontDeal.dealOne(this.qualValueTxt);
         addChild(this.expBar);
         this.expBar.setImg(this.loadBarSp);
         addChild(this.skillBox);
         this.skillBox.arg.init(10,1,0,0);
         this.skillBox.setIconPro("BasicUI/smallThingsGripNoScale",40,40);
         this.skillBox.evt.setWantEvent(true,false,false,true,true);
         this.skillBox.addEventListener(ClickEvent.ON_CLICK,this.skillClick);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.skillBox);
         NormalUICtrl.setTag(this.skillBox,this.skillTag);
         getBtn("defence").setName("退防");
         getBtn("fight").setName("出战");
         getBtn("secFight").setName("出战");
         getBtn("supple").setName("替补");
         getBtn("suppleFun").setName("替补功能");
         getBtn("giveUp").setName("放生");
         getBtn("uplevel").setName("升级");
         getBtn("topping").setName("置顶");
         for each(label0 in PetBaseSave.addArr)
         {
            getBtn(label0).setName("强化");
         }
         addChild(this.uplevelBox);
         this.uplevelBox.setImg(this.uplevelBoxSp);
         this.uplevelBox.hide();
         addChild(getBtn("uplevel"));
         addChild(this.nameChangeBox);
         this.nameChangeBox.setImg(this.nameChangeSp);
         this.nameChangeBox.setWeekOneB(true);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function get petData() : PetDataGroup
      {
         return Gaming.PG.da.pet;
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
         this.uplevelBox.hide();
         Gaming.uiGroup.mustBox.hide();
      }
      
      private function fleshData() : void
      {
         if(Boolean(PetUI.nowGrip))
         {
            this.showInfo(PetUI.nowGrip.itemsData as PetData);
         }
      }
      
      public function outLoginEvent() : void
      {
         this.nameChangeBox.outLoginEvent();
      }
      
      private function clearAllShow() : void
      {
         this.clearBodyImg();
         this.skillBox.clearAllData();
      }
      
      private function showInfo(da0:PetData) : void
      {
         this.showInfo_left(da0);
         this.showInfo_base(da0);
         this.showInfo_qual(da0);
      }
      
      private function showInfo_left(da0:PetData) : void
      {
         var base0:PetBaseData = da0.base;
         this.nameTxt.text = base0.save.playerName;
         this.dpsTxt.text = "战斗力 " + base0.getShowDps();
         this.typeTxt.htmlText = da0.gene.getPetHead();
         this.lvTxt.htmlText = "Lv." + base0.save.level + "";
         this.expBar.setText(base0.getNowExp() + "/" + base0.getNextMaxExp());
         this.expBar.setPer(base0.getNowExp() / base0.getNextMaxExp());
         this.skillBox.inData_byArr(da0.getUISkillArr(),"inData_UIPetSkill");
         this.showBodyImg(da0);
         if(this.suppleExpendData.petData != da0)
         {
            Gaming.uiGroup.mustBox.hide();
         }
         this.suppleExpendData.setData(da0);
         this.suppleExpendData.fleshFun = this.fleshData;
         this.fleshBtn();
      }
      
      private function showBodyImg(da0:PetData) : void
      {
         this.clearBodyImg();
         var d0:NormalBodyDefine = da0.getBodyDefine();
         var mc0:MovieClip = Gaming.swfLoaderManager.getResource(d0.name,"stand");
         this.imgMc = mc0;
         this.imgTag.addChild(this.imgMc);
         var rect0:Rectangle = this.imgMc.getRect(this.imgMc);
         this.imgMc.x -= rect0.x + rect0.width / 2;
         this.imgMc.y -= rect0.y + rect0.height / 2;
      }
      
      private function clearBodyImg() : void
      {
         if(this.imgMc is MovieClip)
         {
            if(Boolean(this.imgMc.parent))
            {
               this.imgMc.parent.removeChild(this.imgMc);
            }
            this.imgMc = null;
         }
      }
      
      private function fleshBtn() : void
      {
         var nowData0:PetData = PetUI.nowGrip.itemsData as PetData;
         var index0:int = int(this.petData.arr.indexOf(nowData0));
         var state0:String = nowData0.getState();
         var defenceB0:Boolean = state0 == PetState.DEFENCE;
         var suppleFunB0:Boolean = nowData0.save.base.suppleFunB;
         if(suppleFunB0)
         {
            getBtn("secFight").visible = defenceB0;
            getBtn("supple").visible = defenceB0;
            getBtn("fight").visible = false;
            Gaming.uiGroup.mustBox.hide();
         }
         else
         {
            getBtn("fight").visible = defenceB0;
            getBtn("secFight").visible = false;
            getBtn("supple").visible = false;
         }
         getBtn("suppleFun").visible = !suppleFunB0;
         getBtn("defence").visible = !defenceB0;
         getBtn("giveUp").visible = defenceB0;
         getBtn("topping").visible = index0 != 0;
      }
      
      private function showInfo_base(da0:PetData) : void
      {
         this.nameChangeBox.setNowData(da0,Gaming.uiGroup.petUI.fleshData);
         var base0:PetBaseData = da0.base;
         var s0:String = "";
         var v0:String = "";
         s0 += "名称";
         v0 += base0.save.playerName;
         s0 += "\n战斗力";
         v0 += "\n" + base0.getShowDps();
         s0 += "\n生命值";
         v0 += "\n" + base0.getMaxLife();
         s0 += "\n头部防御";
         v0 += "\n" + base0.getHeadDefence();
         this.baseTxt.text = s0;
         this.baseValueTxt.text = v0;
      }
      
      private function showInfo_qual(da0:PetData) : void
      {
         var n:* = undefined;
         var pro0:String = null;
         var proD0:PropertyArrayDefine = null;
         var nStr0:String = null;
         var qual_v0:Number = NaN;
         var grow_v0:Number = NaN;
         var qualObj0:EquipPropertyData = da0.gene.getAddData();
         var growObj0:Object = da0.gene.save.growObj;
         var s0:String = "";
         var v0:String = "";
         var proArr0:Array = Gaming.defineGroup.gene.allProNameArr;
         for(n in proArr0)
         {
            pro0 = proArr0[n];
            proD0 = Gaming.defineGroup.gene.pro.getDefine(pro0);
            nStr0 = n == 0 ? "" : "\n";
            qual_v0 = Number(qualObj0[pro0]);
            grow_v0 = Number(growObj0[pro0]);
            if(isNaN(grow_v0))
            {
               grow_v0 = 0;
            }
            s0 += nStr0 + proD0.cnName;
            v0 += nStr0;
            if(qual_v0 > 0.0001)
            {
               v0 += proD0.getNoUnitValueString(qual_v0);
            }
            else
            {
               v0 += ComMethod.color(proD0.getNoUnitValueString(qual_v0),"#777777");
            }
            if(grow_v0 > 0.0001)
            {
               v0 += ComMethod.color(" +" + TextWay.numberToPer(grow_v0,0),"#FFFF00");
            }
         }
         this.qualTxt.text = s0;
         this.qualValueTxt.htmlText = FontDeal.getDealLeadingStr(this.qualValueTxt,v0);
      }
      
      private function skillClick(e:ClickEvent) : void
      {
         var grip0:ItemsGrid = e.child as ItemsGrid;
         Gaming.uiGroup.petUI.skillBoard.setNowChooseIndex(grip0.index);
         Gaming.uiGroup.petUI.showBox("skill");
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var expendBox:NormalExpendBox = null;
         var returnGift0:GiftAddDefineGroup = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         var nowData0:PetData = PetUI.nowGrip.itemsData as PetData;
         var label0:String = btn0.label;
         if(btn0.label == "defence")
         {
            Gaming.PG.da.pet.setOneState(nowData0,PetState.DEFENCE);
            Gaming.PG.da.fleshAllByEquip();
            Gaming.soundGroup.playSound("uiSound","giveupTask");
         }
         else if(btn0.label == "fight" || btn0.label == "secFight")
         {
            Gaming.PG.da.pet.setOneState(nowData0,PetState.FIGHT);
            Gaming.PG.da.fleshAllByEquip();
            Gaming.soundGroup.playSound("uiSound","getTask");
         }
         else if(btn0.label == "supple")
         {
            Gaming.PG.da.pet.setOneState(nowData0,PetState.SUPPLE);
            Gaming.PG.da.fleshAllByEquip();
            Gaming.soundGroup.playSound("uiSound","getTask");
         }
         else if(btn0.label == "suppleFun")
         {
            expendBox = Gaming.uiGroup.mustBox;
            if(expendBox.visible)
            {
               expendBox.hide();
            }
            else
            {
               expendBox.showData(this.suppleExpendData,getBtn("suppleFun"));
            }
         }
         else if(btn0.label == "giveUp")
         {
            if(this.petData.dispatch.isIng() && this.petData.dispatch.panDataIn(nowData0))
            {
               Gaming.uiGroup.alertBox.showError("该宠物正在派遣中，无法放生。");
            }
            else
            {
               returnGift0 = nowData0.getReturnThingsAffterGiveUp();
               Gaming.uiGroup.alertBox.showNormal("放生" + nowData0.getColorPlayerName() + "可返还以下物品：\n" + returnGift0.getDescription(),"yesAndNo",this.giveUpCheck1);
            }
         }
         else if(label0 == "uplevel")
         {
            if(this.uplevelBox.visible)
            {
               this.uplevelBox.hide();
            }
            else
            {
               this.uplevelBox.show();
            }
         }
         else if(PetBaseSave.addArr.indexOf(label0) >= 0)
         {
            this.strengthenClick(label0);
         }
         else if(btn0.label == "topping")
         {
            this.petData.toppingData(nowData0);
            Gaming.uiGroup.petUI.fleshBar();
            Gaming.uiGroup.petUI.chooseData(nowData0);
         }
         this.fleshBtn();
         Gaming.uiGroup.petUI.refleshBar();
      }
      
      private function giveUpCheck1() : void
      {
         var nowData0:PetData = PetUI.nowGrip.itemsData as PetData;
         Gaming.uiGroup.alertBox.showNormal("你确定要放生" + nowData0.getColorPlayerName() + "？\n放生之后，你将永远失去它！","yesAndNo",this.giveUpCheck2);
      }
      
      private function giveUpCheck2() : void
      {
         var nowData0:PetData = PetUI.getNowData();
         this.clearAllShow();
         var returnGift0:GiftAddDefineGroup = nowData0.getReturnThingsAffterGiveUp();
         Gaming.PG.da.pet.giveUpData(nowData0);
         GiftAddit.add(returnGift0);
         Gaming.uiGroup.petUI.fleshData();
         Gaming.soundGroup.playSound("uiSound","swapHero");
      }
      
      private function strengthenClick(label0:String) : void
      {
         var board0:PetStrengthenBoard = Gaming.uiGroup.petUI.strengthenBoard;
         board0.setNowLabel(label0);
         Gaming.uiGroup.petUI.showBox("strengthen");
      }
      
      override public function set visible(bb0:Boolean) : void
      {
         super.visible = bb0;
         if(Boolean(this.imgMc))
         {
            if(bb0)
            {
               this.imgMc.play();
            }
            else
            {
               this.imgMc.stop();
            }
         }
      }
   }
}

