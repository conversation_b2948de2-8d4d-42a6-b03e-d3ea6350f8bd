package UI.bag.wear
{
   import UI.base.button.NormalBtn;
   import UI.base.loadBar.LoadBar;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   
   public class PlayerInfoBar extends LoadBar
   {
      private var styleSpObj:Object = {};
      
      public var nowStyle:String = "";
      
      public var btn:NormalBtn = new NormalBtn();
      
      private var btnSp:MovieClip;
      
      public function PlayerInfoBar()
      {
         super();
      }
      
      override public function setImg(img0:Sprite, followXYB0:Boolean = true) : void
      {
         super.setImg(img0,followXYB0);
         this.btnSp = img0["btnSp"];
         if(this.btnSp is MovieClip)
         {
            addChild(this.btn);
            this.btn.setImg(this.btnSp);
         }
      }
      
      public function addStyle(name0:String, img0:Sprite) : void
      {
         if(!this.styleSpObj.hasOwnProperty(name0))
         {
            this.styleSpObj[name0] = img0;
            if(!img)
            {
               this.changeStyle(name0);
            }
         }
      }
      
      public function changeStyle(name0:String) : void
      {
         if(name0 == this.nowStyle)
         {
            return;
         }
         if(!this.styleSpObj.hasOwnProperty(name0))
         {
            INIT.showError("找不到外观：" + name0);
         }
         clearData();
         if(Boolean(img))
         {
            img.parent.removeChild(img);
         }
         this.setImg(this.styleSpObj[name0],false);
         this.nowStyle = name0;
      }
   }
}

