package UI.login
{
   import UI.UIOrder;
   import UI.bag.ItemsGrid;
   import UI.base.NormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import dataAll._app.login.SaveBaseData4399;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class LoginSaveBox extends NormalUI
   {
      public var box:LoginSaveBarBox = new LoginSaveBarBox();
      
      private var gripTag:Sprite;
      
      public var titleTxt:TextField;
      
      public var closeBtn:SimpleButton;
      
      public var newBtn:SimpleButton;
      
      public var nowSaveListData:SaveBaseData4399;
      
      public function LoginSaveBox()
      {
         super();
         this.box.imgType = "LoginUI/saveBar";
         this.box.arg.init(2,4,5,5);
         this.box.evt.setWantEvent(true,false,false,true,true);
         this.box.addEventListener(ClickEvent.ON_OVER,this.gripOver);
         this.box.addEventListener(ClickEvent.ON_OUT,this.gripOut);
      }
      
      public function inData(arr0:Array, readSaveB0:Boolean) : void
      {
         this.box.inData(arr0,readSaveB0 ? "无存档记录" : "点击新建存档");
         this.titleTxt.text = readSaveB0 ? "读取存档" : "保存存档";
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["gripTag","closeBtn","newBtn","titleTxt"];
         super.setImg(img0);
         this.box.init();
         this.box.x = this.gripTag.x;
         this.box.y = this.gripTag.y;
         addChild(this.box);
         this.newBtn.visible = false;
         addChild(this.newBtn);
         FontDeal.dealOne(this.titleTxt);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function gripOver(e:ClickEvent) : void
      {
         var grip0:ItemsGrid = e.child as ItemsGrid;
         var da0:SaveBaseData4399 = e.childData as SaveBaseData4399;
         this.newBtn.visible = false;
      }
      
      private function gripOut(e:ClickEvent) : void
      {
         UIOrder.showTip("");
         if(this.newBtn.visible && !this.newBtn.hitTestPoint(Gaming.ME.mouseX,Gaming.ME.mouseY))
         {
            this.hideNewBtn();
         }
      }
      
      public function hideNewBtn() : void
      {
         this.newBtn.visible = false;
         this.nowSaveListData = null;
      }
   }
}

