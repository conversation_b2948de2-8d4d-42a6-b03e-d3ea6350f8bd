package UI.parts
{
   import UI.bag.GripBoxEventAddit;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import dataAll._app.parts.define.PartsType;
   import dataAll.arms.ArmsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.things.ThingsData;
   import flash.display.MovieClip;
   
   public class WearPartsGripBox extends ItemsGripBox
   {
      private var gripObj:Object = {};
      
      public function WearPartsGripBox()
      {
         super();
      }
      
      override public function inData_byDataGroup(dg0:ItemsDataGroup, fleshDataB0:Boolean = true) : void
      {
         var i:* = undefined;
         var grip0:ItemsGrid = null;
         var da0:ThingsData = null;
         var lockB0:Boolean = false;
         clearData();
         (evt as GripBoxEventAddit).fatherData = dg0;
         var d_arr0:Array = dg0.dataArr;
         var beforeIconB0:Boolean = !Gaming.PG.save.setting.partsSaB;
         for(i in gripArr)
         {
            grip0 = gripArr[i];
            da0 = dg0.getDataBySite(i) as ThingsData;
            if(Boolean(da0))
            {
               if(grip0.canInItemsB() == false)
               {
                  INIT.showError("不能把物品放在非空的格子上，site：" + i);
               }
               grip0.inData_parts(da0,false,beforeIconB0);
            }
            lockB0 = !dg0.getSaveGroup().getUnlockBySite(i);
            if(lockB0)
            {
               grip0.setState("lock");
            }
         }
      }
      
      public function setLockByArmsDa(da0:ArmsData) : void
      {
         var i:* = undefined;
         var grip2:ItemsGrid = null;
         var lockB0:Boolean = false;
         var type0:String = null;
         var tda0:ThingsData = null;
         var supportB0:Boolean = false;
         for(i in gripArr)
         {
            grip2 = gripArr[i];
            lockB0 = false;
            type0 = PartsType.getWearType(i);
            tda0 = da0.partsData.getDataBySite(i) as ThingsData;
            if(!tda0)
            {
               supportB0 = PartsType.supportType(da0,type0);
               if(!supportB0)
               {
                  lockB0 = true;
               }
            }
            if(lockB0)
            {
               grip2.setState("lock");
            }
         }
      }
      
      public function createWearPartsGrip() : void
      {
         var type0:* = null;
         var name0:String = null;
         var grip_img0:MovieClip = null;
         var grip0:ItemsGrid = null;
         if(gripArr.length > 0)
         {
            INIT.showError("只能创建一次");
         }
         var typeArr0:Array = PartsType.wearArr;
         for each(type0 in typeArr0)
         {
            name0 = type0 + "Grip";
            grip_img0 = Gaming.uiGroup.getBasicMovieClip(imgType);
            grip0 = addGrip(grip_img0) as ItemsGrid;
            grip0.setAnnotation(PartsType.getCn(type0));
            grip0.equipType = type0;
            this.gripObj[type0] = grip0;
         }
         arrange();
      }
      
      override public function clearAllData() : void
      {
         var grip0:ItemsGrid = null;
         for each(grip0 in gripArr)
         {
            grip0.clearData();
         }
      }
   }
}

