package UI.setting.key
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGripBox;
   import UI.base.event.ClickEvent;
   import dataAll._app.setting.key.SettingKeySave;
   import flash.display.Sprite;
   
   public class SettingKeyDoubleBoard extends SettingKeySingleBoard
   {
      protected var p2Box:ItemsGripBox = new ItemsGripBox();
      
      protected var p2Tag:Sprite;
      
      protected var p2Save:SettingKeySave;
      
      public function SettingKeyDoubleBoard()
      {
         super();
         elementNameArr = ["txt","p1Tag","p2Tag","pubTxt","pubTag","pubBox"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         addChild(this.p2Box);
         this.p2Box.arg.init(1,20,0,8);
         this.p2Box.evt.setWantEvent(true,false,false,true,true);
         this.p2Box.setIconPro("BasicUI/keyBtn");
         this.p2Box.addEventListener(ClickEvent.ON_CLICK,btnClick);
         NormalUICtrl.setTag(this.p2Box,this.p2Tag);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function inData(uiType0:String, s1:SettingKeySave, s2:SettingKeySave = null) : void
      {
         super.inData(uiType0,s1,s2);
         this.p2Save = s2;
         inBoxData(this.p2Box,s2,false);
      }
      
      override protected function fleshData() : void
      {
         if(p1Save is SettingKeySave)
         {
            this.inData(uiType,p1Save,this.p2Save);
         }
      }
      
      override public function outLoginEvent() : void
      {
         super.outLoginEvent();
         this.p2Save = null;
      }
      
      override protected function getSaveByBox(box0:ItemsGripBox) : SettingKeySave
      {
         if(box0 == this.p2Box)
         {
            return this.p2Save;
         }
         return p1Save;
      }
   }
}

