package w_test.cheating
{
   import UI.test.SaveTestBox;
   import dataAll.body.define.BodyCamp;
   import gameAll.body.IO_NormalBody;
   import gameAll.hero.HeroBody;
   import gameAll.skill.InstantEffectCtrl;
   import w_test.TestCtrl_AI;
   
   public class AICheating extends OneCheating
   {
      public function AICheating()
      {
         super();
      }
      
      public static function openHeroAI() : void
      {
         if(Boolean(Gaming.PG.ctrlHero))
         {
            Gaming.PG.ctrlHero.openAi();
         }
         Gaming.LG.autoTestB = true;
      }
      
      public static function closeHeroAI() : void
      {
         if(<PERSON>olean(Gaming.PG.ctrlHero))
         {
            Gaming.PG.ctrlHero.closeAi();
         }
         Gaming.LG.autoTestB = false;
      }
      
      public static function setEnemyAI(bb0:Boolean) : void
      {
         var b0:IO_NormalBody = null;
         var arr0:Array = Gaming.BG.ENEMY_ARR;
         for each(b0 in arr0)
         {
            b0.getAi().attackAI.haveAttackOrderB = bb0;
         }
         Gaming.BG.enemyAttackB = bb0;
      }
      
      public function heroAI(str0:String, v0:int) : String
      {
         Gaming.PG.da.main.swapAi();
         if(Gaming.PG.da.main.getAi())
         {
            return "开启主角AI";
         }
         return "关闭主角AI";
      }
      
      public function stopEnemy(str0:String, v0:int) : String
      {
         setEnemyAI(!Gaming.BG.enemyAttackB);
         return "开关怪物攻击命令：" + Gaming.BG.enemyAttackB;
      }
      
      public function stopWe(str0:String, v0:int) : String
      {
         var b0:IO_NormalBody = null;
         var arr0:Array = Gaming.BG.WE_ARR;
         for each(b0 in arr0)
         {
            b0.getAi().attackAI.haveAttackOrderB = !b0.getAi().attackAI.haveAttackOrderB;
         }
         Gaming.BG.weAttackB = !Gaming.BG.weAttackB;
         return "开关怪物攻击命令：" + Gaming.BG.weAttackB;
      }
      
      public function parasiticBoss(str0:String, v0:int) : String
      {
         var hero0:HeroBody = null;
         var boss0:IO_NormalBody = Gaming.BG.filter.getEnemyBoss();
         if(Boolean(boss0))
         {
            hero0 = Gaming.PG.ctrlHero;
            if(Boolean(hero0))
            {
               hero0.transCtrl.parasiticBody(boss0,300);
               return "寄生首领：" + boss0.getDefine().cnName;
            }
         }
         return "寄生失败";
      }
      
      public function parasiticPet(str0:String, v0:int) : String
      {
         var b0:IO_NormalBody = null;
         var hero0:HeroBody = null;
         var arr0:Array = Gaming.PG.da.pet.getFightAndSuppleBodyArr();
         if(arr0.length > 0)
         {
            b0 = arr0[0];
            hero0 = Gaming.PG.ctrlHero;
            if(Boolean(hero0))
            {
               hero0.transCtrl.parasiticBody(b0,300);
               return "寄生宠物：" + b0.getDefine().cnName;
            }
         }
         return "寄生失败";
      }
      
      public function parasiticId(str0:String, v0:int) : String
      {
         var hero0:HeroBody = Gaming.PG.ctrlHero;
         var b0:IO_NormalBody = Gaming.BG.filter.getBody_byId(str0);
         if(Boolean(hero0) && Boolean(b0))
         {
            hero0.transCtrl.everParasiticBody(b0);
            return "寄生id：" + b0 + "    " + b0.getDefine().cnName;
         }
         return "寄生失败";
      }
      
      public function charmAndParasiticNear(str0:String, v0:int) : String
      {
         var b0:IO_NormalBody = null;
         var camp0:String = null;
         var hero0:HeroBody = Gaming.PG.ctrlHero;
         if(Boolean(hero0))
         {
            b0 = Gaming.BG.filter.getNearEnemyBody_Camp(BodyCamp.WE,hero0.mot.x,hero0.mot.y,400);
            if(Boolean(b0))
            {
               camp0 = hero0.dat.camp;
               InstantEffectCtrl.changeCamp(b0,camp0);
               b0.getImg().setToCF(camp0);
               hero0.transCtrl.parasiticBody(b0,999999);
               return "寄生敌人：" + b0.getDefine().cnName;
            }
         }
         return "寄生失败";
      }
      
      public function drawMapSniper(str0:String, v0:int) : String
      {
         if(Boolean(Gaming.LG.nowLevel))
         {
            Gaming.testCtrl.ai.getSpiderShape();
         }
         return "";
      }
      
      public function showWeWarningRange(str0:String, v0:int) : String
      {
         var b0:IO_NormalBody = null;
         var hero0:HeroBody = null;
         var arr0:Array = Gaming.BG.WE_ARR;
         for each(b0 in arr0)
         {
            hero0 = b0 as HeroBody;
            if(Boolean(hero0))
            {
               SaveTestBox.addText(hero0.def.cnName + ":" + hero0.getAi().attackAI.warningRange);
            }
         }
         return "";
      }
      
      public function aiTestMode(str0:String, v0:int) : String
      {
         TestCtrl_AI.mode = str0;
         return "设置模式：" + str0;
      }
   }
}

