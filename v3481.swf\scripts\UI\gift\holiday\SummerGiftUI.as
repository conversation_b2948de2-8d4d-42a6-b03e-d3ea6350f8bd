package UI.gift.holiday
{
   import UI.base.AppNormalUI;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class SummerGift<PERSON> extends AppNormalUI
   {
      private var board:SummerGiftBoard = new SummerGiftBoard();
      
      private var closeBtn:SimpleButton;
      
      public function SummerGiftUI()
      {
         super();
      }
      
      public function imgInit() : void
      {
         this.setImg(Gaming.swfLoaderManager.getResource("GiftUI","summerGiftUI"));
         this.board.closeFun = this.closeClick;
         this.board.setImg(Gaming.swfLoaderManager.getResource("GiftUI","holidaySignBoard"));
         addChild(this.board);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["closeBtn"];
         super.setImg(img0);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
      }
      
      override public function show() : void
      {
         super.show();
         this.board.show();
      }
      
      private function closeClick(e:* = null) : void
      {
         hide();
      }
   }
}

