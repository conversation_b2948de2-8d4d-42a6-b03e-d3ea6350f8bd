package UI.forging.shieldUpgrade
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.must.NormalMustBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.shield.ShieldData;
   import dataAll.equip.shield.ShieldDataCreator;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.skill.define.HeroSkillDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class ShieldUpgradeBoard extends NormalUI
   {
      private var btn:NormalBtn = new NormalBtn();
      
      private var infoBtnSp:MovieClip;
      
      private var infoBtn:NormalBtn = new NormalBtn();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var skillGrip:ItemsGrid = new ItemsGrid();
      
      public var nowData:ShieldData = null;
      
      private var mustBoxSp:Sprite;
      
      private var btnSp:MovieClip;
      
      private var gripTag:Sprite;
      
      private var nameTxt:TextField;
      
      private var infoTxt:TextField;
      
      private var beforeTxt:TextField;
      
      private var nextTxt:TextField;
      
      public function ShieldUpgradeBoard()
      {
         super();
         addChild(this.btn);
         addChild(this.mustBox);
         addChild(this.skillGrip);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.skillGrip);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["mustBoxSp","btnSp","infoBtnSp","gripTag","nameTxt","infoTxt","beforeTxt","nextTxt"];
         super.setImg(img0);
         this.mustBox.setImg(this.mustBoxSp);
         this.btn.setImg(this.btnSp);
         this.btn.setName("进阶");
         addChild(this.infoBtn);
         this.infoBtn.setImg(this.infoBtnSp);
         this.infoBtn.setName("进阶所需");
         ItemsGripTipCtrl.addNormalBtnTip(this.infoBtn);
         this.skillGrip.setImgToEquipGrip();
         NormalUICtrl.setTag(this.skillGrip,this.gripTag);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,"shield");
         this.showOneShieldDataAndPan(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      public function equipGripClick(da0:ShieldData) : void
      {
         if(visible)
         {
            this.showOneShieldDataAndPan(da0);
         }
      }
      
      private function showOneShieldDataAndPan(da0:ShieldData) : void
      {
         var dg0:EquipDataGroup = null;
         this.showNone();
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择要进阶的护盾。");
         if(Boolean(da0))
         {
            dg0 = Gaming.PG.da.findEquipData(da0);
            if(dg0 is EquipDataGroup)
            {
               this.showOneShieldData(da0);
            }
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      private function showOneShieldData(da0:ShieldData) : void
      {
         var must_d0:MustDefine = null;
         var bb0:Boolean = false;
         this.nowData = da0;
         var d0:HeroSkillDefine = da0.shieldDefine.getSkillDefine();
         var next_d0:HeroSkillDefine = da0.shieldDefine.getNextSkillDefine();
         if(Boolean(d0))
         {
            this.skillGrip.inData_equip(da0);
            this.skillGrip.setNumText("");
            this.nameTxt.text = d0.cnName;
            this.infoTxt.text = d0.getDescriptionNoActiveCd();
            this.beforeTxt.htmlText = ComMethod.color("当前第" + d0.lv + "级","#FF9900") + "\n" + d0.getNowChangeText();
            if(!next_d0)
            {
               this.mustBox.setShowState(false);
               this.btn.actived = false;
               this.nextTxt.htmlText = ComMethod.color("已进阶至最高等级了","#FF9900");
            }
            else
            {
               this.nextTxt.htmlText = ComMethod.color("进阶后第" + next_d0.lv + "级","#FF9900") + "\n" + next_d0.getNowChangeText();
               must_d0 = da0.getUpradeMust();
               bb0 = this.mustBox.inData(must_d0);
               this.btn.actived = bb0;
            }
         }
         else
         {
            this.showNone();
         }
         this.infoBtn.tipString = ShieldDataCreator.getMustTip(da0.shieldDefine);
      }
      
      private function showNone() : void
      {
         this.infoBtn.tipString = "";
         this.skillGrip.clearData();
         this.nameTxt.text = "";
         this.infoTxt.text = "";
         this.beforeTxt.text = "";
         this.nextTxt.text = "";
         this.mustBox.setShowState(false);
         this.btn.actived = false;
      }
      
      public function btnClick(e:MouseEvent) : void
      {
         var mustBagNum0:int = 0;
         var must_d0:MustDefine = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0.actived)
         {
            if(btn0 == this.btn)
            {
               if(Boolean(this.nowData))
               {
                  mustBagNum0 = 0;
                  if(this.nowData.placeType == "bag")
                  {
                     mustBagNum0 = 1;
                  }
                  if(Gaming.PG.da.equipBag.getSpaceSiteNum() < mustBagNum0)
                  {
                     Gaming.uiGroup.alertBox.showError("当前装备背包至少需要 " + ComMethod.color(mustBagNum0 + "个","#00FF00") + " 空位！");
                  }
                  else
                  {
                     must_d0 = this.nowData.getUpradeMust();
                     PlayerMustCtrl.deductMust(must_d0,this.affter_click);
                  }
               }
            }
         }
      }
      
      private function affter_click() : void
      {
         var upgradeData0:ShieldData = this.nowData.getUpradeData();
         var must_d0:MustDefine = this.nowData.getUpradeMust();
         if(this.nowData.placeType == "wear" || must_d0.thingsType == MustDefine.THINGS)
         {
            this.nowData.changeToOneData(upgradeData0);
         }
         else if(this.nowData.placeType == "bag")
         {
            this.nowData = Gaming.PG.da.equipBag.addHaveNumData(upgradeData0) as ShieldData;
         }
         this.show();
         Gaming.uiGroup.alertBox.showSuccess("进阶成功！");
      }
   }
}

