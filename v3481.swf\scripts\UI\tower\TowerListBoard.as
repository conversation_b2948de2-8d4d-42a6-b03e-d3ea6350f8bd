package UI.tower
{
   import UI.NormalUICtrl;
   import UI.base.AutoNormalUI;
   import UI.base.box.NormalBox;
   import UI.base.button.BtnAgent;
   import UI.base.button.BtnAgentGroup;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.grid.NormalGrid;
   import dataAll._app.tower.TowerData;
   import dataAll._app.tower.TowerDefine;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class TowerListBoard extends AutoNormalUI
   {
      private var gripBox:NormalBox = new NormalBox();
      
      private var gripTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var pointerSp:Sprite;
      
      private var coverBtn:NormalBtn;
      
      private var tempDef:TowerDefine = null;
      
      private var fDef:TowerDefine = null;
      
      private var tipIndex:int = -1;
      
      private var tipDefine:TowerDefine = null;
      
      public function TowerListBoard()
      {
         super();
         mcTypeArr = ["btnSp","txt","tag"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = elementNameArr.concat(["pointerSp"]);
         super.setImg(img0);
         this.gripBox.btnClass = TowerGrid;
         this.gripBox.setIconPro("TowerUI/bossBtn");
         this.gripBox.arg.init(2,5,0,0);
         this.gripBox.evt.setWant(true,true,false,false,true);
         addChild(this.gripBox);
         NormalUICtrl.setTag(this.gripBox,this.gripTag);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.gripBox.addEventListener(ClickEvent.ON_OVER,this.gripOver);
         this.gripBox.addEventListener(ClickEvent.ON_MOVE,this.gripMove);
         this.gripBox.addEventListener(ClickEvent.ON_OUT,this.gripOut);
         this.gripBox.pageBox.setToSmall();
         this.gripBox.setPagePos(this.pageTag);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function get DATA() : TowerData
      {
         return Gaming.PG.da.tower;
      }
      
      override public function show() : void
      {
         super.show();
         this.openPan();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      private function openPan() : void
      {
         var g0:GiftAddDefineGroup = this.DATA.getSave().getNoGiftAndClear();
         if(Boolean(g0))
         {
            GiftAddit.add(g0);
            Gaming.uiGroup.alertBox.showSuccess("由于虚化塔改版，你将获得以下补偿：\n" + g0.getDescription(3));
         }
      }
      
      private function fleshData() : void
      {
         var arr0:Array = this.DATA.getUIArr();
         this.gripBox.inData_byArr(arr0,this.gripFun);
         this.coverBtn.isChosen = !this.DATA.winShowB();
         this.fleshPointer();
      }
      
      private function fleshPointer() : void
      {
         var num0:int = this.DATA.getWinMaxNum();
         var gap0:Number = -3.91;
         this.pointerSp.y = 485 + gap0 * num0;
         if(num0 > 0)
         {
            this.pointerSp["txt"].text = "通关了" + num0 + "层";
         }
         else
         {
            this.pointerSp["txt"].text = "准备挑战";
         }
      }
      
      private function gripFun(grip0:TowerGrid, d0:TowerDefine) : void
      {
         var canGiftB0:Boolean = this.DATA.haveCanGift(d0);
         var winDiff0:int = this.DATA.getWinDiff(d0);
         var winMaxB0:Boolean = this.DATA.isWinMax(d0);
         var shopFrame0:int = winDiff0 + 1;
         grip0.inData_gift(d0.getMaxGift());
         grip0.itemsData = d0;
         grip0.setName(d0.sort + "");
         grip0.activedAndEnabled = false;
         grip0.actived = true;
         grip0.setNew(canGiftB0);
         grip0.setShopBtnBackMc(shopFrame0);
         grip0.setIconUrlArr(d0.getBossIconUrlArr());
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var bb0:Boolean = false;
         var a0:BtnAgentGroup = null;
         this.tempDef = null;
         var d0:TowerDefine = e.childData as TowerDefine;
         var g0:GiftAddDefineGroup = this.DATA.getCanGift(d0);
         if(Boolean(g0))
         {
            bb0 = GiftAddit.addAndAutoBagSpacePan(g0,"获得：\n" + g0.getDescription());
            if(bb0)
            {
               this.DATA.giftEvent(d0);
               this.fleshData();
            }
         }
         else
         {
            this.tempDef = d0;
            a0 = this.DATA.getDiffAgent(d0,this.gotoMap);
            Gaming.uiGroup.alertBox.option.showAgent(a0);
         }
      }
      
      private function gotoMap(a0:BtnAgent, g0:BtnAgentGroup) : void
      {
         TowerData.gotoMap(this.tempDef,int(a0.name));
      }
      
      private function gripOver(e:ClickEvent) : void
      {
         this.tipIndex = -1;
         this.tipDefine = null;
         this.gripMove(e);
      }
      
      private function gripMove(e:ClickEvent) : void
      {
         var grip0:NormalGrid = e.child as NormalGrid;
         var d0:TowerDefine = e.childData as TowerDefine;
         var x0:int = grip0.mouseX;
         var i0:int = 1;
         if(x0 > 200)
         {
            i0 = 2;
         }
         if(this.tipDefine != d0 || i0 != this.tipIndex)
         {
            this.tipIndex = i0;
            this.fDef = d0;
            if(this.tipIndex == 1)
            {
               Gaming.uiGroup.tipBox.textTip.showFollowText(d0.getGatherTip());
            }
            else if(this.tipIndex == 2)
            {
               Gaming.uiGroup.tipBox.textTip.showFollowText(this.DATA.getGiftTip(d0));
            }
         }
         this.tipDefine = d0;
      }
      
      private function gripOut(e:ClickEvent) : void
      {
         this.tipIndex = -1;
         this.tipDefine = null;
         Gaming.uiGroup.tipBox.hide();
      }
      
      public function FKey() : void
      {
         var giftD0:GiftAddDefine = null;
         if(visible && Boolean(this.fDef))
         {
            if(this.tipIndex == 2)
            {
               giftD0 = this.fDef.getMaxGift();
               Gaming.uiGroup.tipBox.equipTip.setText(giftD0.getGatherTip(),giftD0.getCnName(),"","");
               Gaming.uiGroup.tipBox.equipTip.show();
               Gaming.uiGroup.tipBox.followMouseB = true;
            }
            else
            {
               Gaming.uiGroup.tipBox.showText(this.fDef.getFTip(),15);
            }
         }
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0.actived)
         {
            if(btn0 == this.coverBtn)
            {
               this.DATA.swapWinShow();
               this.fleshData();
            }
         }
      }
   }
}

