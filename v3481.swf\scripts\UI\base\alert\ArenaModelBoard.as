package UI.base.alert
{
   import UI.NormalUICtrl;
   import UI.arena.ArenaTopCtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.base.NormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.grid.NormalGrid;
   import dataAll._app.arena.ArenaData;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import gameAll.level.arena.ArenaCtrl;
   import gameAll.level.arena.ArenaType;
   
   public class ArenaModelBoard extends NormalUI
   {
      public var box:AlertBox;
      
      private var closeBtn:SimpleButton;
      
      private var gripTag:Sprite;
      
      private var gripBox:ItemsGripBox = new ItemsGripBox();
      
      private var _yesFun:Function;
      
      public function ArenaModelBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var n:* = undefined;
         var grip0:NormalGrid = null;
         var name0:String = null;
         elementNameArr = ["closeBtn","gripTag"];
         super.setImg(img0);
         this.gripBox.imgType = "arenaModelBar";
         this.gripBox.arg.init(2,2,3,3);
         addChild(this.gripBox);
         NormalUICtrl.setTag(this.gripBox,this.gripTag);
         this.gripBox.evt.setWantEvent(true,false,false,true,true,false);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.click);
         this.gripBox.addEventListener(ClickEvent.ON_OVER,this.barOver);
         this.gripBox.addEventListener(ClickEvent.ON_OUT,this.barOut);
         this.gripBox.setNowGripNum(4);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         var nameArr0:Array = ArenaType.model_arr;
         for(n in this.gripBox.gripArr)
         {
            grip0 = this.gripBox.gripArr[n];
            name0 = nameArr0[n];
            grip0.label = name0;
            grip0.setSmallIcon(name0);
            grip0.activedAndEnabled = false;
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function showModel(_yesFun0:Function) : void
      {
         this.box.showCheck("","");
         show();
         this.fleshBtnActived();
         this._yesFun = _yesFun0;
      }
      
      private function fleshBtnActived() : void
      {
         var da0:ArenaData = Gaming.PG.da.arena;
         var numB0:Boolean = da0.save.todayNum > 0;
         var timeOverB0:Boolean = ArenaTopCtrl.noAddScoreB();
         var haveMoreB0:Boolean = Gaming.PG.da.more.dataArr.length > 0;
         var isChallengedB0:Boolean = ArenaCtrl.dat.rivalTopData.isChallengedB();
         this.setBtnActived([numB0 && !isChallengedB0,numB0 && haveMoreB0 && !isChallengedB0 && !timeOverB0,true,haveMoreB0]);
      }
      
      private function setBtnActived(arr0:Array) : void
      {
         var n:* = undefined;
         var grip0:ItemsGrid = null;
         for(n in this.gripBox.gripArr)
         {
            grip0 = this.gripBox.gripArr[n];
            if(n < arr0.length)
            {
               grip0.actived = Boolean(arr0[n]);
            }
         }
      }
      
      private function click(e:ClickEvent) : void
      {
         this.box.hide();
         this._yesFun(e.label);
         this._yesFun = null;
      }
      
      private function barOver(e:ClickEvent) : void
      {
         var da0:ArenaData = null;
         var timeOverB0:Boolean = false;
         var numB0:Boolean = false;
         var haveMoreB0:Boolean = false;
         var isChallengedB0:Boolean = false;
         Gaming.uiGroup.tipBox.hide();
         var grip0:ItemsGrid = e.child as ItemsGrid;
         var str0:String = "";
         if(!grip0.actived)
         {
            da0 = Gaming.PG.da.arena;
            timeOverB0 = ArenaTopCtrl.noAddScoreB();
            numB0 = da0.save.todayNum > 0;
            haveMoreB0 = Gaming.PG.da.more.dataArr.length > 0;
            isChallengedB0 = ArenaCtrl.dat.rivalTopData.isChallengedB();
            if(e.label == ArenaType.SINGLE || e.label == ArenaType.TEAM)
            {
               if(!numB0)
               {
                  str0 = "今日挑战次数已经使用完毕。";
               }
               else if(isChallengedB0)
               {
                  str0 = "今天你已经挑战过该对手了。";
               }
            }
            if(!haveMoreB0 && (e.label == ArenaType.TEAM || e.label == ArenaType.TEAM_TRAIN))
            {
               str0 = "你没有队友，无法进行团战。";
            }
         }
         else if(e.label == ArenaType.SINGLE || e.label == ArenaType.TEAM)
         {
         }
         if(str0 == "")
         {
            if(ArenaType.getType(e.label) == ArenaType.TYPE_NORMAL)
            {
            }
         }
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.followMouseB = true;
            Gaming.uiGroup.tipBox.textTip.setText(str0);
            Gaming.uiGroup.tipBox.textTip.show();
         }
      }
      
      private function barOut(e:ClickEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.box.hide();
         this._yesFun = null;
      }
   }
}

