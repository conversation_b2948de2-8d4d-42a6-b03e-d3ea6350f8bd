package UI.test
{
   import UI.base.NormalUI;
   import com.common.text.TextWay;
   import com.sounto.cf.ObjectToXml;
   import dataAll._player.PlayerCtrl;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.system.System;
   import flash.text.TextField;
   import flash.text.TextFieldType;
   
   public class Save4399Box extends NormalUI
   {
      public var outOutBtn:SimpleButton;
      
      private var inputBeforeBtn:SimpleButton;
      
      private var inputAffterBtn:SimpleButton;
      
      private var addBtn:SimpleButton;
      
      private var thingsInSaveBtn:SimpleButton;
      
      private var converToGiftBtn:SimpleButton;
      
      private var beforeTxt:TextField;
      
      private var affterTxt:TextField;
      
      private var addTxt:TextField;
      
      public function Save4399Box()
      {
         super();
      }
      
      public function initImg() : void
      {
         var img0:Sprite = null;
         try
         {
            img0 = Gaming.swfLoaderManager.getResource("TestUI","saveTest4399");
         }
         catch(e:Error)
         {
            // 忽略SWF加载错误
         }

         if(Bo<PERSON>an(img0))
         {
            this.setImg(img0);
         }
         else
         {
            this.createEmbedded4399UI();
         }
      }

      private function createEmbedded4399UI() : void
      {
         var bg:Sprite = new Sprite();
         bg.graphics.beginFill(0x222222, 1);
         bg.graphics.drawRect(0, 0, 580, 360);
         bg.graphics.endFill();

         // 创建文本区域
         this.beforeTxt = this.createTextField(10, 10, 180, 100);
         this.affterTxt = this.createTextField(200, 10, 180, 100);
         this.addTxt = this.createTextField(390, 10, 180, 100);

         bg.addChild(this.beforeTxt);
         bg.addChild(this.affterTxt);
         bg.addChild(this.addTxt);

         // 创建按钮
         this.inputBeforeBtn = this.createButton("导入前", 10, 120);
         this.inputAffterBtn = this.createButton("导入后", 200, 120);
         this.addBtn = this.createButton("添加", 390, 120);
         this.thingsInSaveBtn = this.createButton("物品存档", 10, 160);
         this.converToGiftBtn = this.createButton("转换礼品", 200, 160);
         this.outOutBtn = this.createButton("输出", 390, 160);

         bg.addChild(this.inputBeforeBtn);
         bg.addChild(this.inputAffterBtn);
         bg.addChild(this.addBtn);
         bg.addChild(this.thingsInSaveBtn);
         bg.addChild(this.converToGiftBtn);
         bg.addChild(this.outOutBtn);

         this.setImg(bg);
      }

      private function createTextField(x:Number, y:Number, w:Number, h:Number) : TextField
      {
         var txt:TextField = new TextField();
         txt.x = x;
         txt.y = y;
         txt.width = w;
         txt.height = h;
         txt.multiline = true;
         txt.wordWrap = true;
         txt.border = true;
         txt.borderColor = 0x666666;
         txt.background = true;
         txt.backgroundColor = 0x000000;
         txt.textColor = 0xFFFFFF;
         txt.type = TextFieldType.INPUT;
         return txt;
      }

      private function createButton(label:String, x:Number, y:Number) : SimpleButton
      {
         var upState:Sprite = new Sprite();
         upState.graphics.beginFill(0x666666, 1);
         upState.graphics.drawRect(0, 0, 100, 25);
         upState.graphics.endFill();

         var overState:Sprite = new Sprite();
         overState.graphics.beginFill(0x888888, 1);
         overState.graphics.drawRect(0, 0, 100, 25);
         overState.graphics.endFill();

         var downState:Sprite = new Sprite();
         downState.graphics.beginFill(0x444444, 1);
         downState.graphics.drawRect(0, 0, 100, 25);
         downState.graphics.endFill();

         var hitArea:Sprite = new Sprite();
         hitArea.graphics.beginFill(0x000000, 0);
         hitArea.graphics.drawRect(0, 0, 100, 25);
         hitArea.graphics.endFill();

         var btn:SimpleButton = new SimpleButton(upState, overState, downState, hitArea);
         btn.x = x;
         btn.y = y;

         return btn;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["converToGiftBtn","addBtn","outOutBtn","beforeTxt","affterTxt","addTxt","inputBeforeBtn","inputAffterBtn","thingsInSaveBtn"];
         super.setImg(img0);
         this.beforeTxt.text = "";
         this.affterTxt.text = "";
         this.addTxt.text = "";
         if(Boolean(this.outOutBtn))
         {
            this.outOutBtn.addEventListener(MouseEvent.CLICK,this.outClick);
         }
         this.inputAffterBtn.addEventListener(MouseEvent.CLICK,this.inputClick);
         this.inputBeforeBtn.addEventListener(MouseEvent.CLICK,this.inputClick);
         this.addBtn.addEventListener(MouseEvent.CLICK,this.addClick);
         if(Boolean(this.thingsInSaveBtn))
         {
            this.thingsInSaveBtn.addEventListener(MouseEvent.CLICK,this.thingsInSaveClick);
         }
         if(Boolean(this.converToGiftBtn))
         {
            this.converToGiftBtn.addEventListener(MouseEvent.CLICK,this.converToGiftClick);
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function outClick(e:MouseEvent) : void
      {
         var pg0:PlayerCtrl = Gaming.PG;
         var str0:String = ObjectToXml.decode4399Two(pg0.save.getCopyObj()).toString();
         System.setClipboard(str0);
         Gaming.uiGroup.alertBox.showSuccess("输出当前存档的4399代码到剪贴板！");
      }
      
      private function inputClick(e:MouseEvent) : void
      {
         var saveStr0:String = "";
         if(e.target == this.inputBeforeBtn)
         {
            saveStr0 = this.beforeTxt.text;
         }
         else
         {
            saveStr0 = this.affterTxt.text;
         }
         this.inputStr(saveStr0);
      }
      
      private function inputStr(saveStr0:String) : void
      {
         var obj0:Object = {};
         obj0 = ObjectToXml.encode4399(XML(saveStr0));
         Gaming.uiGroup.loginUI.outLoginEvent();
         Gaming.PG.loginData.newSave(0);
         Gaming.uiGroup.loginUI.yes_read(obj0);
      }
      
      public function inputBeforeTxt() : void
      {
         this.inputStr(this.beforeTxt.text);
      }
      
      public function getBeforeTextStr() : String
      {
         if(Boolean(this.beforeTxt))
         {
            return this.beforeTxt.text;
         }
         return "";
      }
      
      private function addClick(e:MouseEvent) : void
      {
         var gg0:GiftAddDefineGroup = this.getThingsGiftAddDefineGroup();
         if(!gg0)
         {
            return;
         }
         var saveStr0:String = this.beforeTxt.text;
         var obj0:Object = {};
         obj0 = ObjectToXml.encode4399(XML(saveStr0));
         var pg0:PlayerCtrl = new PlayerCtrl();
         pg0.readSave(obj0,"me",Gaming.api.save.s4399.getLogObjNull());
         var successB0:Boolean = GiftAddit.addDefineGroupByOtherPlayerData(gg0,pg0.da);
         if(successB0)
         {
            this.affterTxt.text = ObjectToXml.decode4399(pg0.save.getCopyObj()).toString();
            System.setClipboard(this.affterTxt.text);
            Gaming.uiGroup.alertBox.showSuccess("成功添加物品，并且已复制存档代码到剪贴板！");
         }
      }
      
      private function thingsInSaveClick(e:MouseEvent) : void
      {
         var gg0:GiftAddDefineGroup = this.getThingsGiftAddDefineGroup();
         if(!gg0)
         {
            return;
         }
         GiftAddit.addAndAutoBagSpacePan(gg0);
      }
      
      private function converToGiftClick(e:MouseEvent) : void
      {
         var gg0:GiftAddDefineGroup = this.getThingsGiftAddDefineGroup();
         this.affterTxt.text = gg0.getXMLString();
      }
      
      private function getThingsGiftAddDefineGroup() : GiftAddDefineGroup
      {
         var n:* = undefined;
         var str0:String = null;
         var errorStr0:String = "";
         var gg0:GiftAddDefineGroup = new GiftAddDefineGroup();
         var addStr0:String = this.addTxt.text;
         var strArr0:Array = addStr0.split(addStr0.indexOf("\r") > 0 ? "\r" : "\n");
         for(n in strArr0)
         {
            str0 = TextWay.toHanSpace(strArr0[n]);
            errorStr0 = gg0.addGiftByCnStr(str0);
            if(errorStr0 != "")
            {
               break;
            }
         }
         if(errorStr0 != "")
         {
            Gaming.uiGroup.alertBox.showError("格式不对：\n" + errorStr0);
            return null;
         }
         return gg0;
      }
   }
}

