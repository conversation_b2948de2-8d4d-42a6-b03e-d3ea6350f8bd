package UI.main
{
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import dataAll._app.worldMap.define.BigMapName;
   import dataAll._app.worldMap.save.WorldMapSaveGroup;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class MainSmallMapBox extends AutoNormalUI
   {
      private var sunglowBtn:NormalBtn;
      
      private var twilightBtn:NormalBtn;
      
      private var dawnBtn:NormalBtn;
      
      private var ziguangBtn:NormalBtn;
      
      private var greenIsBtn:NormalBtn;
      
      public function MainSmallMapBox()
      {
         super();
         mcTypeArr = ["btnSp","txt","tag"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
      }
      
      public function fleshData() : void
      {
         var btn0:NormalBtn = null;
         var f0:int = 0;
         var sg0:WorldMapSaveGroup = Gaming.PG.save.worldMap;
         var now0:String = Gaming.uiGroup.worldMapBox.getNowBigMap();
         for each(btn0 in btnObj)
         {
            f0 = int(BigMapName.arr.indexOf(btn0.label));
            if(f0 >= 0)
            {
               btn0.setName(BigMapName.getCn(btn0.label));
               btn0.actived = now0 != btn0.label;
            }
         }
         this.sunglowBtn.visible = true;
         this.twilightBtn.visible = sg0.getSave("LvSen");
         this.dawnBtn.visible = sg0.getSave("PrisonDoor");
         this.ziguangBtn.visible = sg0.getSave("HanGuang1");
         this.greenIsBtn.visible = sg0.getSave("GreenTown1");
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         Gaming.soundGroup.playSound("uiSound","mapChange");
         Gaming.uiGroup.worldMapBox.setBigMap(btn0.label);
      }
   }
}

