package UI.vehicle
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.font.FontDeal;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.equip.vehicle.VehicleData;
   import dataAll.equip.vehicle.VehicleDataCreator;
   import dataAll.equip.vehicle.VehicleDefine;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class VehicleUpgradeOneBox extends NormalUI
   {
      public var evoTipB:Boolean = false;
      
      private var grip:ItemsGrid = new ItemsGrid();
      
      private var gripTag:Sprite;
      
      private var titleTxt:TextField;
      
      private var txt:TextField;
      
      public function VehicleUpgradeOneBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["gripTag","titleTxt","txt"];
         super.setImg(img0);
         FontDeal.dealOne(this.titleTxt);
         this.grip.setImgToEquipGrip();
         this.gripTag.addChild(this.grip);
         this.grip.addEventListener(MouseEvent.MOUSE_OVER,this.gripOver);
         this.grip.addEventListener(MouseEvent.MOUSE_OUT,Gaming.uiGroup.tipBox.hide);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function inData(da0:VehicleData, title0:String) : void
      {
         var str0:String = null;
         if(!da0)
         {
            this.grip.visible = false;
            this.titleTxt.htmlText = ComMethod.color("已升级至最高等级","#00FF00");
            this.txt.text = "";
         }
         else
         {
            this.grip.visible = true;
            this.grip.inData_equip(da0);
            this.titleTxt.text = title0;
            str0 = VehicleDataCreator.getUpgradeTip(da0);
            this.txt.htmlText = str0;
         }
      }
      
      public function inEvoData(da0:VehicleData, title0:String) : void
      {
         var str0:String = null;
         if(!da0)
         {
            this.grip.visible = false;
            this.titleTxt.htmlText = ComMethod.color("已进化至最高等级","#00FF00");
            this.txt.text = "";
         }
         else
         {
            this.grip.visible = true;
            this.grip.inData_equip(da0);
            this.titleTxt.text = title0;
            str0 = VehicleDataCreator.getEvolutionTip(da0,this.evoTipB);
            this.txt.htmlText = str0;
         }
      }
      
      private function gripOver(e:MouseEvent) : void
      {
         var da0:VehicleData = this.grip.itemsData as VehicleData;
         var d0:VehicleDefine = da0.vehicleDefine;
         ItemsGripTipCtrl.equipTip(da0,null,false,false);
         if(this.evoTipB == true && d0.getAddSkillArr().length > 0)
         {
            Gaming.uiGroup.compareTipBox.showText(d0.getAddSkillGather());
         }
         Gaming.uiGroup.tipBox.setPositionBySp(this.grip);
      }
   }
}

