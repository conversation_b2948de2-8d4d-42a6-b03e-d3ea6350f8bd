package UI.union.building
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.base.AutoNormalUI;
   import UI.base.event.ClickEvent;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.union.building.define.UnionWatchmenDefine;
   import flash.display.Sprite;
   
   public class Union<PERSON>atchmenBox extends AutoNormalUI
   {
      private var gripTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var box:ItemsGripBox = new ItemsGripBox();
      
      public function UnionWatchmenBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         this.box.setIconPro("UnionUI/watchmenGrip");
         this.box.arg.init(1,10,0,-1);
         this.box.x = this.gripTag.x;
         this.box.y = this.gripTag.y;
         this.box.evt.setWantEvent(false,false,false,true,true);
         this.box.addEventListener(ClickEvent.ON_OVER,this.barOver);
         this.box.addEventListener(ClickEvent.ON_OUT,this.barOut);
         addChild(this.box);
         this.box.pageBox.setToSmall();
         this.box.setPagePos(this.pageTag);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      private function fleshData() : void
      {
         var n:* = undefined;
         var d0:UnionWatchmenDefine = null;
         var grip0:ItemsGrid = null;
         var unlockB0:Boolean = false;
         var dArr0:Array = Gaming.defineGroup.union.building.watchmenArr;
         var unlockArr0:Array = Gaming.PG.da.union.building.getWatchmenUnlockDefineArr();
         if(this.box.gripArr.length < dArr0.length)
         {
            this.box.setNowGripNum(dArr0.length);
         }
         for(n in dArr0)
         {
            d0 = dArr0[n];
            grip0 = this.box.gripArr[n];
            unlockB0 = unlockArr0.indexOf(d0) >= 0;
            grip0.itemsData = d0;
            grip0.setLevelText(d0.unlockLv + "");
            grip0.setName(unlockB0 ? ComMethod.color(d0.cnName,"#00FF00") : d0.cnName);
            grip0.setSmallIcon(unlockB0 ? "have" : "");
         }
         this.box.fleshPageBox();
      }
      
      private function barClick(e:ClickEvent) : void
      {
      }
      
      private function barOver(e:ClickEvent) : void
      {
         var d0:UnionWatchmenDefine = e.childData as UnionWatchmenDefine;
         var unlockB0:Boolean = Gaming.PG.da.union.building.panWatchmenUnlock(d0);
         var str0:String = d0.getInfo(unlockB0);
         Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
      }
      
      private function barOut(e:ClickEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
   }
}

