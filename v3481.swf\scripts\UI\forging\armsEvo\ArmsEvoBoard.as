package UI.forging.armsEvo
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.must.NormalMustBox;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.NumberMethod;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.arms.ArmsData;
   import dataAll.arms.ArmsDataGroup;
   import dataAll.arms.creator.ArmsEvoCtrl;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class ArmsEvoBoard extends AutoNormalUI
   {
      private var beforeTxt:TextField;
      
      private var beforeGrip:ItemsGrid = new ItemsGrid();
      
      private var afterTxt:TextField;
      
      private var afterGrip:ItemsGrid = new ItemsGrid();
      
      private var mustBox:NormalMustBox;
      
      private var btn:NormalBtn;
      
      private var infoBtn:NormalBtn;
      
      public var nowData:ArmsData = null;
      
      public function ArmsEvoBoard()
      {
         super();
         mcTypeArr = ["btnSp","mustBoxSp","txt","Bx"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         this.btn.setName("进化");
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.beforeGrip);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.afterGrip);
         ItemsGripTipCtrl.addNormalBtnTip(this.infoBtn);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,"armsEvo");
         this.showOneArmsDataAndPan(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      public function armsGripClick(da0:ArmsData) : void
      {
         if(visible)
         {
            this.showOneArmsDataAndPan(da0);
         }
      }
      
      private function showOneArmsDataAndPan(da0:ArmsData) : void
      {
         var dg0:ArmsDataGroup = null;
         this.showNone();
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择要进化的武器。");
         if(Boolean(da0))
         {
            dg0 = Gaming.PG.da.findArmsData(da0);
            if(dg0 is ArmsDataGroup)
            {
               this.showOneArmsData(da0);
            }
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      private function showOneArmsData(da0:ArmsData) : void
      {
         this.nowData = da0;
         var after_da0:ArmsData = ArmsEvoCtrl.getAfterData(da0);
         this.showOneText(da0,"before");
         this.showOneText(after_da0,"after");
         var must_d0:MustDefine = ArmsEvoCtrl.getMust(da0);
         var bodyLv0:int = 0;
         if(this.nowData.normalPlayerData is NormalPlayerData)
         {
            bodyLv0 = this.nowData.normalPlayerData.level;
         }
         var mustB0:Boolean = this.mustBox.inData(must_d0,bodyLv0);
         var canB0:Boolean = da0.canEvoB();
         this.btn.actived = canB0 && mustB0;
         this.infoBtn.tipString = ArmsEvoCtrl.getAllEvoTip(this.nowData);
      }
      
      private function showOneText(da0:ArmsData, label0:String) : void
      {
         var grip0:ItemsGrid = this[label0 + "Grip"];
         var text0:TextField = this[label0 + "Txt"];
         var str0:String = "";
         if(Boolean(da0))
         {
            grip0.inData_arms(da0);
            str0 = da0.getColorCnName();
            str0 += "\n伤害：" + ComMethod.color(NumberMethod.toBigWan(da0.hurtRatio),"#00FF00");
         }
         else
         {
            grip0.clearData();
            str0 = ComMethod.color("已进化至最高等级","#00FF00");
         }
         text0.htmlText = str0;
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var must_d0:MustDefine = null;
         if(e.target == this.btn)
         {
            if(Boolean(this.nowData))
            {
               if(this.btn.actived)
               {
                  must_d0 = ArmsEvoCtrl.getMust(this.nowData);
                  PlayerMustCtrl.deductMust(must_d0,this.afterStrengthen);
               }
            }
            else
            {
               Gaming.uiGroup.alertBox.showError("武器数据不存在！");
            }
         }
      }
      
      private function afterStrengthen() : void
      {
         ArmsEvoCtrl.evo(this.nowData);
         this.yes_save();
      }
      
      private function yes_save(v:* = null) : void
      {
         this.showOneArmsDataAndPan(this.nowData);
         Gaming.uiGroup.allBagUI.fleshAllBox();
         Gaming.uiGroup.alertBox.showSuccess("进化成功！");
      }
      
      private function showNone() : void
      {
         this.nowData = null;
         this.beforeGrip.clearData();
         this.beforeTxt.text = "";
         this.afterGrip.clearData();
         this.afterTxt.text = "";
         this.mustBox.setShowState(false);
         this.btn.actived = false;
      }
   }
}

