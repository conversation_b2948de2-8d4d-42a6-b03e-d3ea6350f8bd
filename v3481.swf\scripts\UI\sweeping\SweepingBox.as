package UI.sweeping
{
   import UI.UIOrder;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.scroll.NormalScrollBar;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.worldMap.WorldMapData;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.level.LevelDiffGetting;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import gameAll.drop.sweeping.SweepingCtrl;
   
   public class SweepingBox extends AutoNormalUI
   {
      private var levelTxt:TextField;
      
      private var numTxt:TextField;
      
      private var closeBtn:NormalBtn;
      
      private var sweepingBtn:NormalBtn;
      
      private var throwBtn:NormalBtn;
      
      private var giftBtn:NormalBtn;
      
      private var numKeyBtn:NormalBtn;
      
      private var giftTag:Sprite;
      
      private var gripSp:Sprite;
      
      private var giftBox:ItemsGripBox = new ItemsGripBox();
      
      private var maskTargetSp:Sprite;
      
      private var scrollBarSp:Sprite;
      
      private var scrollLineSp:Sprite;
      
      private var doFun:Function = null;
      
      private var nowDiff:int = 0;
      
      private var scrollBar:NormalScrollBar;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public function SweepingBox()
      {
         super();
         mcTypeArr = ["btnSp","txt","tag"];
      }
      
      public function get nowNum() : Number
      {
         return this.CF.getAttribute("nowNum");
      }
      
      public function set nowNum(v0:Number) : void
      {
         this.CF.setAttribute("nowNum",v0);
      }
      
      public function get maxNum() : Number
      {
         return this.CF.getAttribute("maxNum");
      }
      
      public function set maxNum(v0:Number) : void
      {
         this.CF.setAttribute("maxNum",v0);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         FontDeal.dealOne(this.levelTxt);
         this.giftTag.addChild(this.giftBox);
         this.giftBox.arg.init(7,999,2,2);
         this.giftBox.setIconPro("equipGrip",50,50);
         this.giftBox.evt.setWantEvent(true,false,false,true,true);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.giftBox);
         this.giftTag.addChild(this.gripSp);
         this.gripSp.visible = false;
         this.gripSp.mouseChildren = false;
         this.gripSp.mouseEnabled = false;
         this.giftBox.addEventListener(ClickEvent.ON_CLICK,this.giftClick);
         this.giftBox.addEventListener(ClickEvent.ON_OVER,this.giftOver);
         this.giftBox.addEventListener(ClickEvent.ON_OUT,this.giftOut);
         this.sweepingBtn.setName("扫荡");
         this.giftBtn.setName("领取奖励");
         this.throwBtn.setName("全部丢弃");
         this.scrollBar = new NormalScrollBar(this.giftTag,img0["maskTargetSp"],img0["scrollBarSp"],img0["scrollLineSp"],1,false,true,true);
         this.scrollBar.speed = 30;
         this.scrollBar.refresh();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
      }
      
      public function showBox(fun0:Function, nowData0:WorldMapData, diff0:int, model0:String) : void
      {
         this.show();
         this.doFun = fun0;
         this.nowDiff = diff0;
         var lv0:int = nowData0.getEnemyLvByModel(model0,diff0);
         var cnName0:String = nowData0.getMapCnName();
         var diffStr0:String = "(" + LevelDiffGetting.getCnName(diff0,model0) + ")";
         this.levelTxt.htmlText = "准备扫荡 " + lv0 + "级" + cnName0 + diffStr0;
         this.fleshData();
      }
      
      public function outLoginEvent() : void
      {
         hide();
         this.doFun = null;
         this.nowDiff = 0;
         SweepingCtrl.outLoginEvent();
      }
      
      private function fleshData() : void
      {
         this.fleshGift();
         this.fleshInfo();
      }
      
      private function fleshGift() : void
      {
         var btn0:NormalBtn = null;
         var g0:GiftAddDefine = null;
         var gift0:GiftAddDefineGroup = SweepingCtrl.dat.getUIGift();
         this.giftBox.inData_byArr(gift0.arr,"inData_giftSec");
         this.scrollBar.refresh();
         for each(btn0 in this.giftBox.gripArr)
         {
            g0 = btn0.secData as GiftAddDefine;
            if(SweepingCtrl.dat.isAutoGiftB(g0))
            {
               btn0.setSmallIcon("get");
               btn0.actived = false;
            }
            else
            {
               btn0.setSmallIcon("");
               btn0.actived = true;
            }
            btn0.activedAndEnabled = false;
            btn0.mouseEnabled = true;
         }
      }
      
      private function fleshInfo() : void
      {
         var num0:int = Gaming.PG.da.getSurplusSweepingNum();
         this.numTxt.htmlText = "今天还剩余扫荡次数：" + ComMethod.colorEnoughNum(num0) + "次";
         this.sweepingBtn.actived = num0 > 0;
         this.numKeyBtn.actived = num0 > 0;
         this.giftBtn.actived = SweepingCtrl.dat.gift.arr.length > 0;
         this.throwBtn.visible = SweepingCtrl.dat.gift.arr.length > 0;
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var funName0:String = btn0.label + "BtnClick";
         if(this.hasOwnProperty(funName0))
         {
            this[funName0](e);
         }
      }
      
      public function numKeyBtnClick(e:MouseEvent) : void
      {
         var max0:int = Gaming.PG.da.getSurplusSweepingNum();
         if(max0 > 0)
         {
            Gaming.uiGroup.alertBox.showNumChoose("选择扫荡次数",max0,max0,1,1,this.sweepingNum);
         }
      }
      
      public function sweepingBtnClick(e:MouseEvent) : void
      {
         this.sweepingNum(1);
      }
      
      private function sweepingNum(v0:int) : void
      {
         this.nowNum = 0;
         this.maxNum = v0;
         UIOrder.getStoreState(this.affterGetStoreState);
      }
      
      private function affterGetStoreState(v0:int) : void
      {
         if(v0 == 1 || v0 == -2)
         {
            this.toDoFun();
         }
      }
      
      private function toDoFun() : void
      {
         if(this.doFun is Function)
         {
            this.doFun(this.nowDiff,true);
         }
      }
      
      public function giftBtnClick(e:MouseEvent) : void
      {
         var gift0:GiftAddDefineGroup = SweepingCtrl.dat.gift;
         var bb0:Boolean = GiftAddit.addAndAutoBagSpacePan(gift0);
         if(bb0)
         {
            SweepingCtrl.dat.getGiftEvent();
            this.fleshData();
            UIOrder.save(true,false,false);
         }
      }
      
      public function throwBtnClick(e:MouseEvent) : void
      {
         Gaming.uiGroup.alertBox.showNormal("是否要丢弃所有奖励？","yesAndNo",this.throwAll);
      }
      
      private function throwAll() : void
      {
         SweepingCtrl.dat.clearGift();
         this.fleshData();
      }
      
      public function closeBtnClick(e:MouseEvent) : void
      {
         hide();
      }
      
      private function giftClick(e:ClickEvent) : void
      {
         var allGift0:GiftAddDefineGroup = null;
         var g0:GiftAddDefine = null;
         var gift0:GiftAddDefineGroup = null;
         var bb0:Boolean = false;
         var btn0:NormalBtn = e.child as NormalBtn;
         if(Boolean(btn0) && btn0.actived)
         {
            allGift0 = SweepingCtrl.dat.gift;
            g0 = btn0.secData as GiftAddDefine;
            if(allGift0.arr.indexOf(g0) >= 0)
            {
               gift0 = new GiftAddDefineGroup();
               gift0.arr = [g0];
               bb0 = GiftAddit.addAndAutoBagSpacePan(gift0,"");
               if(bb0)
               {
                  SweepingCtrl.dat.getGiftOne(g0);
                  this.fleshData();
                  this.gripSp.visible = false;
               }
            }
         }
      }
      
      private function giftOver(e:ClickEvent) : void
      {
         var grip0:ItemsGrid = e.child as ItemsGrid;
         if(grip0.actived)
         {
            this.gripSp.visible = true;
            this.gripSp.x = grip0.x;
            this.gripSp.y = grip0.y;
         }
         else
         {
            this.gripSp.visible = false;
         }
      }
      
      private function giftOut(e:ClickEvent) : void
      {
         this.gripSp.visible = false;
      }
      
      public function sweepingOver() : void
      {
         ++this.nowNum;
         ++Gaming.PG.da.worldMap.saveGroup.sweepingNum;
         var surplus0:int = Gaming.PG.da.getSurplusSweepingNum();
         if(this.nowNum >= this.maxNum || surplus0 <= 0)
         {
            this.fleshData();
            UIOrder.save(true,false,false);
            Gaming.uiGroup.mainUI.fleshCoin();
         }
         else
         {
            this.toDoFun();
         }
      }
   }
}

