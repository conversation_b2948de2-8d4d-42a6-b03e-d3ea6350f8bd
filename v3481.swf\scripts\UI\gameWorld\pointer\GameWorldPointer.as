package UI.gameWorld.pointer
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class GameWorldPointer extends Sprite
   {
      private var txtSp:Sprite = null;
      
      private var pointerMc:MovieClip = null;
      
      private var mPoint:Point = new Point();
      
      private var R:Rectangle = new Rectangle(-125,-125,250,250);
      
      public function GameWorldPointer()
      {
         super();
      }
      
      public function setImg(sp0:Sprite = null) : void
      {
         if(Boolean(this.txtSp) || Boolean(this.pointerMc))
         {
            INIT.showError("不能重复输入素材");
         }
         if(!sp0)
         {
            sp0 = Gaming.swfLoaderManager.getResource("GameWorldUI","overPointer");
         }
         this.txtSp = sp0["txtSp"];
         this.pointerMc = sp0["pointerMc"];
         this.pointerMc.stop();
         addChild(sp0);
         visible = false;
      }
      
      public function show() : void
      {
         visible = true;
         this.pointerMc.gotoAndPlay(1);
         Gaming.gameSprite.L_text.addChild(this);
      }
      
      public function setPoint(x1:int, y1:int) : void
      {
         this.mPoint = new Point(x1,y1);
         this.fleshEach();
         this.show();
      }
      
      private function fleshPoint() : void
      {
         var x1:int = this.mPoint.x;
         var y1:int = this.mPoint.y;
         var gs_x:int = Gaming.sceneGroup.nowViewRect.x;
         var gs_y:int = Gaming.sceneGroup.nowViewRect.y;
         var rect0:Rectangle = Gaming.sceneGroup.rangeViewRect;
         var mx0:int = gs_x + rect0.x + rect0.width / 2;
         var my0:int = gs_y + rect0.y + rect0.height / 2;
         var ra0:Number = Math.atan2(y1 - my0,x1 - mx0);
         var x0:int = x1 - Math.cos(ra0) * 150;
         var y0:int = y1 - Math.sin(ra0) * 150;
         if(x0 + this.R.x < gs_x + rect0.x)
         {
            x0 = gs_x + rect0.x - this.R.x;
         }
         else if(x0 + this.R.x + this.R.width > gs_x + rect0.x + rect0.width)
         {
            x0 = gs_x + rect0.x + rect0.width - (this.R.x + this.R.width);
         }
         if(y0 + this.R.y < gs_y + rect0.y)
         {
            y0 = gs_y + rect0.y - this.R.y;
         }
         else if(y0 + this.R.y + this.R.height > gs_y + rect0.y + rect0.height)
         {
            y0 = gs_y + rect0.y + rect0.height - (this.R.y + this.R.height);
         }
         this.x = x0;
         this.y = y0;
         this.pointerMc.rotation = ra0 * 180 / Math.PI;
      }
      
      public function overGamingClear() : void
      {
         if(Boolean(parent))
         {
            this.pointerMc.stop();
            parent.removeChild(this);
         }
         this.mPoint = null;
      }
      
      public function fleshEach() : void
      {
         if(Boolean(this.mPoint))
         {
            this.fleshPoint();
         }
      }
   }
}

