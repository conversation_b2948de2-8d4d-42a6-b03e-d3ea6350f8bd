package UI.gameWorld.headBox
{
   import UI.base.AutoNormalUI;
   import UI.base.grid.NormalGridIcon;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   
   public class ShieldSmallGrip extends AutoNormalUI
   {
      private var icon:NormalGridIcon = new NormalGridIcon();
      
      protected var cdSp:MovieClip = null;
      
      public function ShieldSmallGrip()
      {
         super();
         this.mouseChildren = false;
         this.mouseEnabled = false;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var index0:int = 0;
         if(!img)
         {
            super.setImg(img0);
            this.cdSp.stop();
            index0 = img.getChildIndex(this.cdSp);
            img.addChildAt(this.icon,index0);
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function setIconName(str0:String) : void
      {
         this.icon.setIconName(str0);
      }
      
      public function setCdPer(per0:Number) : void
      {
         if(Boolean(this.cdSp))
         {
            if(per0 >= 1)
            {
               this.cdSp.visible = false;
            }
            else
            {
               if(per0 < 0)
               {
                  per0 = 0;
               }
               this.cdSp.visible = true;
               this.cdSp.gotoAndStop(int(per0 * this.cdSp.totalFrames));
            }
         }
      }
   }
}

