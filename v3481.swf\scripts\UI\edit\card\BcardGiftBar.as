package UI.edit.card
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.edit.card.BossCardData;
   import dataAll._app.edit.card.BossCardDataGroup;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class BcardGiftBar extends AutoNormalUI
   {
      private var giftBtn:NormalBtn;
      
      private var infoTxt:TextField;
      
      private var icon:ItemsGrid = new ItemsGrid();
      
      public var fleshFun:Function;
      
      public function BcardGiftBar()
      {
         super();
         mcTypeArr = ["btnSp","txt","Bx"];
         this.mouseChildren = true;
         this.mouseEnabled = false;
      }
      
      public function setToNormalImg() : void
      {
         setImgUrl("BosseditUI/cardGiftBar");
         FontDeal.dealLine(this.infoTxt);
         this.giftBtn.setName("领取");
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.icon);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      protected function get dataG() : BossCardDataGroup
      {
         return Gaming.PG.da.bossCard;
      }
      
      public function inData(da0:BossCardData) : void
      {
         var str0:String = "次数满足\n" + ComMethod.color(da0.getAchieveGiftMust() + "","#FFFF00");
         var vipMust0:Number = da0.getVipGiftMust();
         if(vipMust0 > 0)
         {
            str0 += "·<b>" + ComMethod.color("VIP" + vipMust0,"#FF6600") + "</b>";
         }
         this.infoTxt.htmlText = FontDeal.getDealLeadingStr(this.infoTxt,str0);
         this.icon.inData_bossCardData(da0);
         this.giftBtn.actived = false;
         show();
      }
      
      public function get itemsData() : BossCardData
      {
         return this.icon.itemsData as BossCardData;
      }
      
      public function setGiftBtn(cn0:String, actived0:Boolean, visible0:Boolean = true) : void
      {
         this.giftBtn.actived = actived0;
         this.giftBtn.visible = visible0;
         this.giftBtn.setName(cn0);
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var bb0:Boolean = false;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0 == this.giftBtn)
         {
            if(btn0.actived && btn0.visible && Boolean(this.itemsData))
            {
               if(this.dataG.getBagSurplus() >= 1)
               {
                  bb0 = this.dataG.setAchivevGiftGetted(this.itemsData,true);
                  if(bb0)
                  {
                     this.dataG.addByDefId(this.itemsData.id);
                     Gaming.uiGroup.alertBox.showSuccess("领取成功！");
                     this.fleshFun();
                     Gaming.uiGroup.bosseditUI.fleshCardBox();
                  }
               }
               else
               {
                  Gaming.uiGroup.alertBox.showError("背包空位不足！");
               }
            }
         }
      }
   }
}

