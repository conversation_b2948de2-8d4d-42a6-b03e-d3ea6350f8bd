package UI.api.union
{
   import UI.test.SaveTestBox;
   import dataAll._app.union.info.OwnUnionInfo;
   import unit4399.events.UnionEvent;
   
   public class UnionVisitor_API extends UnionBase_API
   {
      public function UnionVisitor_API()
      {
         super();
      }
      
      public function unionCreate(idx:int, title:String, extra:String, yesFun0:Function = null, noFun0:Function = null) : void
      {
         setFun(yesFun0,noFun0);
         if(Gaming.isLocal())
         {
            Gaming.defineGroup.unionInfoGroup.unionCreate(idx,title,extra);
            this.yes_unionCreate(true);
         }
         else
         {
            serviceHold.unionCreate(idx,title,extra);
         }
      }
      
      private function yes_unionCreate(bb0:Boolean) : void
      {
         doYesFun(bb0);
      }
      
      public function getUnionList(idx:int, pageNum:int, pageSize:int, yesFun0:Function = null, noFun0:Function = null) : void
      {
         setFun(yesFun0,noFun0);
         if(Gaming.isLocal())
         {
            this.yes_getUnionList(Gaming.defineGroup.unionInfoGroup.getUnionList(idx,pageNum,pageSize));
         }
         else
         {
            serviceHold.getUnionList(idx,pageNum,pageSize);
         }
      }
      
      private function yes_getUnionList(jsonStr0:String) : void
      {
         doYesFun(jsonStr0);
      }
      
      public function applyUnion(idx:int, unionId:Number, extra:String, yesFun0:Function = null, noFun0:Function = null) : void
      {
         setFun(yesFun0,noFun0);
         if(Gaming.isLocal())
         {
            this.yes_applyUnion(true);
         }
         else
         {
            serviceHold.applyUnion(idx,unionId,extra);
         }
      }
      
      private function yes_applyUnion(bb0:Boolean) : void
      {
         doYesFun(bb0);
      }
      
      public function getOwnUnion(idx:int, yesFun0:Function = null, noFun0:Function = null) : void
      {
         setFun(yesFun0,noFun0);
         if(Gaming.isLocal())
         {
            this.yes_getOwnUnion(OwnUnionInfo.getSimulatedJson());
         }
         else
         {
            serviceHold.getOwnUnion(idx);
         }
      }
      
      private function yes_getOwnUnion(jsonStr0:String) : void
      {
         SaveTestBox.addText(jsonStr0);
         doYesFun(jsonStr0);
      }
      
      public function onVisitorSuccess(e:UnionEvent) : *
      {
         var dataObj:Object = e.data;
         var data0:* = dataObj.data;
         switch(dataObj.apiName)
         {
            case UnionEvent.UNI_API_BHCJ:
               this.yes_unionCreate(data0);
               break;
            case UnionEvent.UNI_API_BHLB:
               this.yes_getUnionList(data0);
               break;
            case UnionEvent.UNI_API_BHSQ:
               this.yes_applyUnion(data0);
               break;
            case UnionEvent.UNI_API_SSBH:
               this.yes_getOwnUnion(data0);
         }
      }
   }
}

