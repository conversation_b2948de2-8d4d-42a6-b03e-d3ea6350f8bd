package UI.api.union
{
   public class UnionBase_API
   {
      public var father:Union_API;
      
      protected var yes_fun:Function = null;
      
      protected var yesFunObj:Object = {};
      
      public function UnionBase_API()
      {
         super();
      }
      
      protected function get serviceHold() : Object
      {
         return this.father.serviceHold;
      }
      
      protected function get no_fun() : Function
      {
         return this.father.no_fun;
      }
      
      protected function set no_fun(fun0:Function) : void
      {
         this.father.no_fun = fun0;
      }
      
      public function setFun(yesFun0:Function, noFun0:Function = null, target0:String = "") : void
      {
         if(target0 == "")
         {
            this.yes_fun = yesFun0;
         }
         else
         {
            this.yesFunObj[target0] = yesFun0;
         }
         this.no_fun = noFun0;
      }
      
      protected function doYesFun(data0:*, target0:String = "") : void
      {
         var fun0:Function = null;
         if(target0 == "")
         {
            fun0 = this.yes_fun;
            this.yes_fun = null;
         }
         else
         {
            fun0 = this.yesFunObj[target0];
            this.yesFunObj[target0] = null;
         }
         if(fun0 is Function)
         {
            fun0(data0);
         }
      }
      
      protected function doNoFun(str0:String) : void
      {
         this.father.doNoFun(str0);
      }
      
      public function outLoginEvent() : void
      {
         this.yes_fun = null;
         this.yesFunObj = {};
      }
   }
}

