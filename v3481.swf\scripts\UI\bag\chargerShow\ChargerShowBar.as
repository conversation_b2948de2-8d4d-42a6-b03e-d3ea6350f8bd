package UI.bag.chargerShow
{
   import UI.base.NormalUI;
   import UI.base.font.FontDeal;
   import dataAll.arms.ArmsChargerData;
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   public class ChargerShowBar extends NormalUI
   {
      private var iconMc:MovieClip = null;
      
      private var numTxt:TextField = null;
      
      public var itemsData:ArmsChargerData = null;
      
      public function ChargerShowBar()
      {
         super();
         this.mouseChildren = false;
         elementNameArr = ["iconMc","numTxt"];
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         this[str0] = value0;
      }
      
      public function setToNormalImg() : void
      {
         setImg(Gaming.uiGroup.getBasicMovieClip("chargerShowBar"));
         FontDeal.dealOne(this.numTxt);
      }
      
      public function setIcon(label0:String) : void
      {
         this.iconMc.gotoAndStop(label0);
      }
      
      public function setNum(num0:int) : void
      {
         this.numTxt.text = num0 + "";
      }
   }
}

