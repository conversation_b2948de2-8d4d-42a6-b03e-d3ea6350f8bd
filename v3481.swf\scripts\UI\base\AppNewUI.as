package UI.base
{
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class AppNewUI extends AppNormalUI
   {
      protected var labelBox:LabelBox = new LabelBox();
      
      protected var labelArr:Array = [];
      
      protected var labelCnArr:Array = [];
      
      protected var boxArr:Array = [];
      
      protected var tipBtn:SimpleButton;
      
      protected var closeBtn:SimpleButton;
      
      protected var labelTag:Sprite;
      
      protected var swfLabel:String = "";
      
      public function AppNewUI()
      {
         super();
         elementNameArr = ["tipBtn","closeBtn","labelTag"];
      }
      
      public function setBoxLabel(label0:String) : void
      {
         this.labelBox.nowLabel = label0;
      }
      
      protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      protected function init_other() : void
      {
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
      }
      
      protected function init_addLabel() : void
      {
         var tag0:Sprite = null;
         var box0:LabelBox = this.labelBox;
         tag0 = this.labelTag;
         box0.arg.init(8,1,-6,0);
         addChild(box0);
         box0.inData("HeadUI/midLabelBtn",this.labelArr,this.labelCnArr);
         box0.setChoose(this.getFirstLabel());
         box0.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         box0.x = tag0.x;
         box0.y = tag0.y;
      }
      
      protected function init_addBox() : void
      {
         var label0:String = null;
         var normalUI0:NormalUI = null;
         var boardArr0:Array = this.labelArr;
         for each(label0 in boardArr0)
         {
            label0 += "Board";
            normalUI0 = this.GET(label0) as NormalUI;
            if(Boolean(normalUI0))
            {
               addChild(normalUI0);
               normalUI0.visible = false;
               normalUI0.setImg(Gaming.swfLoaderManager.getResource(this.swfLabel,label0));
               this.boxArr.push(normalUI0);
            }
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      public function fleshData() : void
      {
         this.showBox(this.labelBox.nowLabel);
      }
      
      protected function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      public function showBox(label0:String) : void
      {
         var n:* = undefined;
         var box0:NormalUI = null;
         if(label0 == "")
         {
            label0 = this.getFirstLabel();
         }
         this.labelBox.setChoose(label0);
         for(n in this.boxArr)
         {
            this.boxArr[n].hide();
         }
         box0 = this.GET(label0 + "Board") as NormalUI;
         if(Boolean(box0))
         {
            box0.show();
         }
      }
      
      protected function getFirstLabel() : String
      {
         return this.labelArr[0];
      }
      
      public function showAndChooseBox(label0:String) : void
      {
         this.labelBox.nowLabel = label0;
         this.show();
      }
      
      protected function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         hide();
      }
      
      protected function getTipText() : String
      {
         return "";
      }
      
      protected function tipOver(e:MouseEvent) : void
      {
         var str0:String = this.getTipText();
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      protected function tipOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
   }
}

