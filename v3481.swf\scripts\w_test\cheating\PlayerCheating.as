package w_test.cheating
{
   import UI.test.SaveTestBox;
   import com.sounto.utils.StringMethod;
   import dataAll._app.love.LoveData;
   import dataAll._player.more.MorePlayerData;
   import dataAll.body.define.HeroDefine;
   import dataAll.body.define.NormalBodyDefine;
   import flash.geom.Point;
   import gameAll.hero.HeroBody;
   
   public class PlayerCheating extends OneCheating
   {
      public function PlayerCheating()
      {
         super();
      }
      
      public function noZuobi(str0:String, v0:int) : String
      {
         Gaming.PG.save.main.isZuobiB = false;
         return "解除作弊";
      }
      
      public function isZuobi(str0:String, v0:int) : String
      {
         Gaming.PG.save.main.isZuobiB = true;
         return "设定为作弊";
      }
      
      public function showZuobi(str0:String, v0:int) : String
      {
         SaveTestBox.addText("作弊原因：" + Gaming.PG.save.main.zuobiReason);
         return "已显示在M框里";
      }
      
      public function setHeroLv(str0:String, v0:int) : String
      {
         Gaming.PG.DATA.base.save.level = v0;
         return "设定人物等级至：" + v0;
      }
      
      public function addHeroExp(str0:String, v0:int) : String
      {
         Gaming.PG.DATA.addExp(v0);
         return "增加人物经验：" + v0;
      }
      
      public function setHeroExp(str0:String, v0:int) : String
      {
         Gaming.PG.DATA.base.save.exp = v0;
         return "设置当前经验值：" + v0;
      }
      
      public function addCoin(str0:String, v0:int) : String
      {
         Gaming.PG.da.main.addCoin(v0);
         return "添加银币：" + v0;
      }
      
      public function addSore(str0:String, v0:int) : String
      {
         Gaming.PG.da.main.addScore(v0);
         return "添加积分：" + v0;
      }
      
      public function clearAllDoubleAdd(str0:String, v0:int) : String
      {
         Gaming.PG.da.time.clearAllTime();
         return "清除所有双倍时间";
      }
      
      public function setLoveValue(str0:String, v0:int) : String
      {
         var loveData0:LoveData = null;
         var value0:int = 0;
         var morePd0:MorePlayerData = Gaming.PG.DATA as MorePlayerData;
         if(Boolean(morePd0))
         {
            loveData0 = morePd0.love;
            value0 = v0 - loveData0.save.value;
            loveData0.addValue(value0);
            return "设置" + morePd0.heroData.def.cnName + "的" + loveData0.getCn() + "：" + v0;
         }
         return "没找到队友";
      }
      
      public function initKeySetting(str0:String, v0:int) : String
      {
         Gaming.PG.save.setting.key.initObj();
         return "初始化按键设置";
      }
      
      public function setMainXY(str0:String, v0:int) : String
      {
         var p0:Point = StringMethod.toPoint(str0);
         var hero0:HeroBody = Gaming.PG.ctrlHero;
         if(Boolean(hero0))
         {
            hero0.setXY(p0.x,p0.y);
            Gaming.targetInput.breakSightByP1();
         }
         return "";
      }
      
      public function openNewPartner(str0:String, v0:int) : String
      {
         Gaming.PG.da.more.saveGroup.unlockNewOne();
         return "目前可上场队友个数为：" + Gaming.PG.da.more.saveGroup.getCanFillLen();
      }
      
      public function setHeroDefine(str0:String, v0:int) : String
      {
         var d0:HeroDefine = Gaming.defineGroup.body.getCnDefine(str0) as HeroDefine;
         if(Boolean(d0))
         {
            Gaming.PG.da.heroData.def = d0;
            return "设成英雄定义：" + str0;
         }
         return "没找到英雄单位：" + str0;
      }
      
      public function toP1(str0:String, v0:int) : String
      {
         var bb0:Boolean = false;
         var heroD0:NormalBodyDefine = Gaming.defineGroup.body.getCnDefine(str0);
         if(Boolean(heroD0))
         {
            bb0 = Gaming.PG.da.setNewP1NameUI(heroD0.name);
            if(bb0)
            {
               return "设为P1：" + str0;
            }
            return "设置失败";
         }
         return "找不到角色：" + str0;
      }
   }
}

