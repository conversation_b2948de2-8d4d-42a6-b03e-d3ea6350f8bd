package UI.api.exchange
{
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   
   public class KuangExchange_API
   {
      public var yesFun:Function;
      
      public var noFun:Function;
      
      internal var loader:URLLoader = new URLLoader();
      
      internal var url:URLRequest = new URLRequest("https://huodong2.4399.com/2015gq_kuanghuan/manage/validate_code.php");
      
      public function KuangExchange_API()
      {
         super();
         this.loader.addEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this.loader.addEventListener(Event.COMPLETE,this.loaderCompleteHandler);
      }
      
      public function startExchange(code0:String, _yesFun:Function = null, _noFun:Function = null) : *
      {
         this.yesFun = _yesFun;
         this.noFun = _noFun;
         if(this.noFun == null)
         {
            this.noFun = this.noFunFun;
         }
         var data0:URLVariables = new URLVariables();
         data0.typeid = 3;
         data0.code = code0;
         this.url.data = data0;
         this.url.method = URLRequestMethod.GET;
         this.loader.load(this.url);
      }
      
      private function loaderCompleteHandler(e:Event) : void
      {
         var str0:String = null;
         if(this.yesFun is Function)
         {
            str0 = this.loader.data;
            if(str0 == "right_code")
            {
               this.yesFun(this.loader.data);
            }
            else if(this.noFun is Function)
            {
               this.noFun("兑换码不存在或已兑换。");
            }
         }
      }
      
      private function errorHandler(e:IOErrorEvent) : void
      {
         if(this.noFun is Function)
         {
            this.noFun("服务器连接错误！");
         }
      }
      
      public function noFunFun(str0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError(str0);
      }
   }
}

