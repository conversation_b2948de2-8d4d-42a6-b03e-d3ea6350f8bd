package w_test
{
   import UI.base.tip.OneTextGather;
   
   public class TestCtrl_Tip extends TestCtrl_Normal
   {
      public function TestCtrl_Tip()
      {
         super();
      }
      
      public function test() : *
      {
         var tg0:OneTextGather = new OneTextGather();
         var str111:String = "";
         str111 += "<i1>|<orange 属性：/>";
         str111 += "\n<i1>|<orange 属性：/>|手枪";
         str111 += "\n<orange 属性：/>";
         str111 += "\n<orange 属性：/>|<i3>";
         str111 += "\n<red 武器类型/>|<red 手枪/>";
         str111 += "\n<red 武器类型/>|<red 0980/>|<i4>";
         tg0.setText(str111);
         Gaming.gameSprite.addChild(tg0);
      }
   }
}

