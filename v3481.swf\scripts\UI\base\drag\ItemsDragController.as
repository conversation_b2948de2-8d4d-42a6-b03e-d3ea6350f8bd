package UI.base.drag
{
   import UI.base.event.ClickEvent;
   import UI.base.grid.NormalGrid;
   import com.sounto.math.Maths;
   import com.sounto.oldUtils.ComMethod;
   import flash.display.Bitmap;
   import flash.display.Sprite;
   import flash.geom.Point;
   
   public class ItemsDragController extends Sprite
   {
      public var dragB:Boolean = false;
      
      public var clickEvent:ClickEvent = null;
      
      public var dragChild:NormalGrid = null;
      
      private var dragBmp:Bitmap = new Bitmap();
      
      private var dragPoint:Point = new Point();
      
      private var iconOverB:Boolean = false;
      
      public function ItemsDragController()
      {
         super();
         this.mouseChildren = false;
         this.mouseEnabled = false;
         addChild(this.dragBmp);
      }
      
      public function startDraging(clickEvent0:ClickEvent) : *
      {
         if(!this.dragB)
         {
            this.dragB = true;
            this.clickEvent = clickEvent0;
            this.dragChild = this.clickEvent.child as NormalGrid;
            if(Boolean(this.dragBmp.bitmapData))
            {
               this.dragBmp.bitmapData.dispose();
            }
            this.dragBmp.bitmapData = ComMethod.getBmp(this.dragChild.getIcon());
            this.dragPoint.x = this.mouseX;
            this.dragPoint.y = this.mouseY;
            this.visible = false;
         }
      }
      
      public function stopDraging() : *
      {
         if(this.dragB)
         {
            this.dragChild.iconReturn();
            this.dragB = false;
            if(Boolean(this.dragBmp.bitmapData))
            {
               this.dragBmp.bitmapData.dispose();
            }
            this.visible = false;
         }
      }
      
      public function FTimer() : *
      {
         var len:* = undefined;
         if(this.dragB)
         {
            len = Maths.Long(this.dragPoint.x - mouseX,this.dragPoint.y - mouseY);
            if(len > 10)
            {
               this.visible = true;
               this.dragChild.iconLeave();
               this.dragBmp.x = this.mouseX - this.dragBmp.width / 2;
               this.dragBmp.y = this.mouseY - this.dragBmp.height / 2 - 10;
            }
         }
      }
      
      public function clear() : *
      {
         this.dragChild = null;
         this.clickEvent = null;
      }
   }
}

