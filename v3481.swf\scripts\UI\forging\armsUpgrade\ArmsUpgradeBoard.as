package UI.forging.armsUpgrade
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.must.NormalMustBox;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.arms.ArmsData;
   import dataAll.arms.ArmsDataGroup;
   import dataAll.arms.creator.ArmsUpgradeCtrl;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class ArmsUpgradeBoard extends NormalUI
   {
      private var beforeTxt:TextField;
      
      private var afterTxt:TextField;
      
      private var mustSp:Sprite;
      
      private var itemsGripSp:MovieClip;
      
      private var btnSp:MovieClip;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var itemsGrip:ItemsGrid = new ItemsGrid();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      public var nowData:ArmsData = null;
      
      public function ArmsUpgradeBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["beforeTxt","afterTxt","mustSp","btnSp","itemsGripSp"];
         super.setImg(img0);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("升级");
         addChild(this.itemsGrip);
         this.itemsGrip.setImg(this.itemsGripSp);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.itemsGrip);
         addChild(this.mustBox);
         this.mustBox.setImg(this.mustSp);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,"arms");
         this.showOneArmsDataAndPan(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      public function armsGripClick(da0:ArmsData) : void
      {
         if(visible)
         {
            this.showOneArmsDataAndPan(da0);
         }
      }
      
      private function showOneArmsDataAndPan(da0:ArmsData) : void
      {
         var dg0:ArmsDataGroup = null;
         this.showNone();
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择要升级的武器。");
         if(Boolean(da0))
         {
            dg0 = Gaming.PG.da.findArmsData(da0);
            if(dg0 is ArmsDataGroup)
            {
               this.showOneArmsData(da0);
            }
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      private function showOneArmsData(da0:ArmsData) : void
      {
         this.nowData = da0;
         var after_da0:ArmsData = ArmsUpgradeCtrl.getAfterData(da0);
         this.itemsGrip.inData_arms(da0);
         this.beforeTxt.htmlText = ArmsUpgradeCtrl.getStateStr(da0);
         this.afterTxt.htmlText = ArmsUpgradeCtrl.getStateStr(after_da0,false);
         var bodyLv0:int = 0;
         if(this.nowData.normalPlayerData is NormalPlayerData)
         {
            bodyLv0 = this.nowData.normalPlayerData.level;
         }
         var must_d0:MustDefine = ArmsUpgradeCtrl.getMust(da0);
         var mustB0:Boolean = this.mustBox.inData(must_d0,bodyLv0);
         var canB0:Boolean = da0.canUpgradeB();
         this.btn.actived = canB0 && mustB0;
      }
      
      private function showNone() : void
      {
         this.nowData = null;
         this.itemsGrip.clearData();
         this.beforeTxt.htmlText = "";
         this.afterTxt.htmlText = "";
         this.mustBox.setShowState(false);
         this.btn.actived = false;
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var must_d0:MustDefine = null;
         if(Boolean(this.nowData))
         {
            must_d0 = ArmsUpgradeCtrl.getMust(this.nowData);
            PlayerMustCtrl.deductMust(must_d0,this.afterUpgrade);
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("武器数据不存在！");
         }
      }
      
      private function afterUpgrade() : void
      {
         ArmsUpgradeCtrl.upgradeOne(this.nowData);
         this.showOneArmsDataAndPan(this.nowData);
         Gaming.uiGroup.allBagUI.fleshAllBox();
         Gaming.uiGroup.alertBox.showSuccess("升级成功！");
      }
   }
}

