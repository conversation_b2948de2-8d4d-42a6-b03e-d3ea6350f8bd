package UI.space
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGripBox;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.grid.NormalGrid;
   import UI.base.grid.NormalGridIcon;
   import UI.base.label.LabelBox;
   import UI.base.loadBar.LoadBar;
   import UI.peak.PeakProBar;
   import com.sounto.utils.DisplayMethod;
   import dataAll._app.peak.PeakProDefine;
   import dataAll._app.space.craft.CraftData;
   import dataAll._app.space.craft.CraftDataGroup;
   import dataAll.body.define.NormalBodyDefine;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class SpaceCraftBoard extends AutoNormalUI
   {
      private var gripBox:ItemsGripBox = new ItemsGripBox();
      
      private var gripTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var imgTag:Sprite;
      
      private var iconCon:NormalGridIcon = new NormalGridIcon();
      
      private var titleTxt:TextField;
      
      private var cnTxt:TextField;
      
      private var expTxt:TextField;
      
      private var lvTxt:TextField;
      
      private var loadBar:LoadBar;
      
      private var pointTxt:TextField;
      
      private var resetBtn:NormalBtn;
      
      private var pointTag:Sprite;
      
      private var barArr:Array = [];
      
      private var pointLabelBox:LabelBox;
      
      private var nowData:CraftData = null;
      
      public function SpaceCraftBoard()
      {
         super();
         mcTypeArr = ["btnSp","txt","tag","loadBarSp"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         FontDeal.dealOne(this.titleTxt);
         FontDeal.dealOne(this.cnTxt);
         FontDeal.dealOne(this.expTxt);
         FontDeal.dealOne(this.lvTxt);
         FontDeal.dealOne(this.pointTxt);
         this.resetBtn.setName("重置");
         this.imgTag.addChild(this.iconCon);
         this.titleTxt.htmlText = "";
         this.gripBox.setIconPro("SpaceUI/craftGrip");
         this.gripBox.arg.init(1,6,0,0,false);
         this.gripBox.evt.setWant(true);
         addChild(this.gripBox);
         NormalUICtrl.setTag(this.gripBox,this.gripTag);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.gripBox.pageBox.setToSmall();
         this.gripBox.setPagePos(this.pageTag);
         this.loadBar.addEventListener(MouseEvent.MOUSE_OVER,this.loadBarOver);
         this.loadBar.addEventListener(MouseEvent.MOUSE_OUT,Gaming.uiGroup.tipBox.hide);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function get dataG() : CraftDataGroup
      {
         return Gaming.PG.da.space.craft;
      }
      
      private function isGaimgB() : Boolean
      {
         return Gaming.LG.isGaming();
      }
      
      public function outLoginEvent() : void
      {
         this.nowData = null;
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      private function clearShow() : void
      {
         this.iconCon.clearData();
         this.resetBtn.visible = false;
         this.pointTag.visible = false;
         this.lvTxt.visible = false;
      }
      
      private function fleshData() : void
      {
         var arr0:Array = this.dataG.getUIArr();
         this.gripBox.inData_byArr(arr0,this.gripFun);
         if(Boolean(this.nowData) && arr0.indexOf(this.nowData) == -1)
         {
            this.nowData = null;
         }
         if(!this.nowData && arr0.length >= 1)
         {
            this.nowData = arr0[0];
         }
         this.fleshChooseData();
      }
      
      private function gripFun(grip0:NormalGrid, da0:CraftData) : void
      {
         grip0.itemsData = da0;
         var bodyD0:NormalBodyDefine = da0.getBodyDef();
         var nowB0:Boolean = this.dataG.getNowData() == da0;
         grip0.setName(da0.getCnName());
         grip0.setIconName(bodyD0.headIconUrl);
         grip0.setNumText("Lv." + da0.level);
         var small0:String = "no";
         if(nowB0)
         {
            small0 = "main";
         }
         grip0.setSmallIcon(small0);
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         this.nowData = e.childData as CraftData;
         this.fleshChooseData();
      }
      
      private function fleshChooseData() : void
      {
         var da0:CraftData = this.nowData;
         if(da0 == null)
         {
            this.gripBox.setChoose_byIndex(-1);
            this.clearShow();
         }
         else
         {
            this.gripBox.setChooseByItemsData(da0);
         }
         this.fleshPoint();
         this.fleshInfo();
      }
      
      private function fleshInfo() : void
      {
         var bd0:NormalBodyDefine = null;
         var tip0:String = null;
         if(Boolean(this.nowData))
         {
            bd0 = this.nowData.getBodyDef();
            this.iconCon.setIconName(bd0.bmpUrl);
            this.cnTxt.text = bd0.cnName;
            this.lvTxt.text = "Lv." + this.nowData.level;
            this.loadBar.setLife(this.nowData.exp,this.nowData.getNowMaxExp(),"",false);
            tip0 = this.nowData.getUITipAndShow();
            if(tip0 != "")
            {
               Gaming.uiGroup.alertBox.showSuccess(tip0);
            }
         }
         else
         {
            this.cnTxt.text = "";
            this.iconCon.clearData();
            this.lvTxt.text = "";
            this.loadBar.setLife(0,1);
         }
      }
      
      private function loadBarOver(e0:MouseEvent) : void
      {
         var s0:String = null;
         if(Boolean(this.nowData))
         {
            s0 = "等级上限：<yellow " + CraftData.MAX_LV + "/>级";
            s0 += "\n飞船耐久：<green " + this.nowData.getLife() + "/>";
            s0 += "\n飞船每升1级，就会提高飞船耐久，同时获得1点技能点数，用于提升飞船技能。";
            Gaming.uiGroup.tipBox.showText(s0);
         }
      }
      
      private function fleshPoint() : void
      {
         var surplus0:int = 0;
         var all0:int = 0;
         if(Boolean(this.nowData))
         {
            surplus0 = this.nowData.getSurplusPoint();
            all0 = this.nowData.getAllPoint();
            this.pointTxt.htmlText = "技能点数 " + surplus0 + "/" + all0;
            this.resetBtn.actived = surplus0 < all0 && !this.isGaimgB();
            this.pointTag.visible = true;
            this.pointTag.mouseEnabled = false;
            this.pointTag.mouseChildren = !this.isGaimgB();
            this.fleshPointBar(this.nowData);
         }
         else
         {
            this.pointTag.visible = false;
         }
      }
      
      private function fleshPointBar(da0:CraftData) : void
      {
         var i:int = 0;
         var bar0:PeakProBar = null;
         var url0:String = null;
         var d0:PeakProDefine = null;
         var defArr0:Array = da0.getPointDefArr();
         var clen0:int = defArr0.length - this.barArr.length;
         if(clen0 > 0)
         {
            for(i = 0; i < clen0; i++)
            {
               bar0 = new PeakProBar();
               url0 = "SpaceUI/skillBar";
               bar0.setImgUrl(url0);
               this.barArr.push(bar0);
            }
         }
         for(var j:int = 0; j < this.barArr.length; j++)
         {
            bar0 = this.barArr[j];
            if(j > defArr0.length - 1)
            {
               if(Boolean(bar0.parent))
               {
                  bar0.parent.removeChild(bar0);
               }
            }
            else
            {
               d0 = defArr0[j];
               bar0.inDefine(d0,da0,this.fleshPoint);
               bar0.fleshData(da0.getSurplusPoint());
               this.pointTag.addChild(bar0);
            }
         }
         DisplayMethod.arrange(this.barArr,0,4,1,5);
      }
      
      private function resetBtnClick(e:MouseEvent) : void
      {
         if(Boolean(this.nowData))
         {
            this.nowData.resetPoint();
            this.fleshPoint();
         }
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var funName0:String = btn0.label + "BtnClick";
         this[funName0](e);
      }
   }
}

