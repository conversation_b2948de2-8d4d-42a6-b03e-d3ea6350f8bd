package UI.union.building
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.union.building.geology.UnionGeologyData;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import gameAll.level._diy.GeologyLevelDiy;
   
   public class UnionGeologyBox extends AutoNormalUI
   {
      private var gripTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var gotoBtn:NormalBtn;
      
      private var timeTxt:TextField;
      
      private var box:ItemsGripBox = new ItemsGripBox();
      
      public function UnionGeologyBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         FontDeal.dealOne(this.timeTxt);
         this.box.setIconPro("UnionUI/watchmenGrip");
         this.box.arg.init(1,9,0,-1);
         this.box.x = this.gripTag.x;
         this.box.y = this.gripTag.y;
         this.box.evt.setWantEvent(false,false,false,false,false);
         addChild(this.box);
         this.box.pageBox.setToSmall();
         this.box.setPagePos(this.pageTag);
         this.gotoBtn.setName("开始挖矿");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      private function fleshData() : void
      {
         var n:* = undefined;
         var overB0:Boolean = false;
         var timeOverB0:Boolean = false;
         var name0:String = null;
         var grip0:ItemsGrid = null;
         var unlockB0:Boolean = false;
         var data0:UnionGeologyData = Gaming.PG.da.union.building.geology;
         var nameArr0:Array = data0.def.getLevelStrArr();
         if(this.box.gripArr.length < nameArr0.length)
         {
            this.box.setNowGripNum(nameArr0.length);
         }
         for(n in nameArr0)
         {
            name0 = nameArr0[n];
            grip0 = this.box.gripArr[n];
            unlockB0 = data0.buildData.save.lv >= n + 1;
            grip0.setLevelText(n + 1);
            grip0.setName(unlockB0 ? ComMethod.color(name0,"#00FF00") : name0);
            grip0.setSmallIcon(unlockB0 ? "have" : "");
         }
         this.box.fleshPageBox();
         overB0 = data0.isOverB();
         timeOverB0 = data0.isTimeOverB();
         this.gotoBtn.setName(overB0 ? "今日已挖完" : "开始挖矿");
         this.gotoBtn.actived = !overB0 && !timeOverB0;
         this.timeTxt.text = "今日剩余时间：" + data0.getSurplusTimeMin();
      }
      
      public function gotoBtnClick(e:MouseEvent) : void
      {
         if(this.gotoBtn.actived)
         {
            if(Gaming.PG.da.isDoubleB())
            {
               Gaming.uiGroup.alertBox.showNormal("只有在单人模式下才能进行挖矿，\n是否设定成单人模式？","yesAndNo",this.yesGotoMap);
            }
            else
            {
               GeologyLevelDiy.chooseLevel();
            }
         }
      }
      
      private function yesGotoMap(e:* = null) : void
      {
         Gaming.PG.da.moreWay.gotoSingle();
         GeologyLevelDiy.chooseLevel();
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var funName0:String = btn0.label + "BtnClick";
         if(this.hasOwnProperty(funName0))
         {
            this[funName0](e);
         }
      }
   }
}

