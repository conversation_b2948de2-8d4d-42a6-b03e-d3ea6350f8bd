package
{
   import com.sounto.oldUtils.StringDate;
   import dataAll._player.PlayerData;
   
   public class Bug
   {
      public static const nameNoChangeB:Boolean = false;
      
      public function Bug()
      {
         super();
      }
      
      public static function TRACE(v:*) : void
      {
      }
      
      public static function panParty(tipB0:Boolean = true) : Boolean
      {
         var pd0:PlayerData = Gaming.PG.da;
         if(<PERSON><PERSON>an(pd0))
         {
            if(panPartyTime())
            {
               if(tipB0)
               {
                  Gaming.uiGroup.alertBox.showError("很抱歉，该功能临时维护中。");
               }
               return true;
            }
         }
         return false;
      }
      
      private static function panPartyTime() : Boolean
      {
         var now0:StringDate = Gaming.api.save.getNowServerDate();
         if(now0.betweenIn("2022-10-12 00:00:00","2022-10-25 18:00:00") == 0)
         {
            return true;
         }
         return false;
      }
   }
}

