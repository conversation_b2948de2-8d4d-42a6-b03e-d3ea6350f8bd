package UI.love
{
   import UI.base.NormalUI;
   import dataAll._app.love.LoveData;
   import dataAll._player.more.MorePlayerData;
   
   public class LoveChildBoard extends NormalUI
   {
      public function LoveChildBoard()
      {
         super();
      }
      
      protected function get loveData() : LoveData
      {
         var pd0:MorePlayerData = Gaming.PG.DATA as MorePlayerData;
         if(<PERSON><PERSON><PERSON>(pd0))
         {
            return pd0.love;
         }
         return null;
      }
   }
}

