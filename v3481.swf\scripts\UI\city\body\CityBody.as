package UI.city.body
{
   import UI.base.button.NormalBtn;
   import com.sounto.math.RectMethod;
   import dataAll._app.city.define.CityBodyDefine;
   import dataAll._app.city.define.CityHouseDefine;
   import dataAll._app.city.dress.CityDressData;
   import dataAll._app.city.dress.CityDressMould;
   import dataAll._app.city.dress.CityDressType;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.filters.GlowFilter;
   import flash.geom.ColorTransform;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class CityBody
   {
      protected static var SET_FILTER:GlowFilter = new GlowFilter(65280,1,4,4,20);
      
      protected static var over_CF:ColorTransform = new ColorTransform(1,1,1,1,90,90,90);
      
      protected var def:CityBodyDefine;
      
      protected var dressData:CityDressData;
      
      protected var img:DisplayObject;
      
      protected var shadow:Sprite = null;
      
      private var imgRect:Rectangle = null;
      
      private var downB:Boolean = false;
      
      private var _x:Number = 0;
      
      private var _y:Number = 0;
      
      public function CityBody()
      {
         super();
      }
      
      public static function getNewImg(d0:CityBodyDefine, mould0:CityDressMould = null) : DisplayObject
      {
         var r0:Rectangle = null;
         var sp0:Sprite = null;
         var url0:String = d0.imgUrl;
         if(Boolean(mould0))
         {
            url0 = mould0.getImgUrl();
         }
         var img0:DisplayObject = null;
         if(url0 != "")
         {
            img0 = Gaming.swfLoaderManager.getResourceFull(url0) as DisplayObject;
            if(Boolean(mould0))
            {
               if(mould0.getDressType() == CityDressType.vehicle)
               {
                  r0 = img0.getRect(img0);
                  sp0 = new Sprite();
                  sp0.addChild(img0);
                  img0.y = -r0.y - r0.height;
                  img0 = sp0;
               }
            }
         }
         else
         {
            img0 = new Sprite();
         }
         return img0;
      }
      
      public function init(d0:CityBodyDefine, dressDa0:CityDressData = null) : void
      {
         var shadowUrl0:String = null;
         this.def = d0;
         this.dressData = dressDa0;
         if(!this.img)
         {
            this.img = getNewImg(d0,Boolean(dressDa0) ? dressDa0.getMould() : null);
            this.imgRect = this.img.getRect(this.img).clone();
            RectMethod.scaleRect(this.imgRect,0.8);
         }
         if(Boolean(dressDa0))
         {
            shadowUrl0 = dressDa0.getMould().getShadowUrl();
            if(shadowUrl0 != "")
            {
               this.shadow = Gaming.swfLoaderManager.getResourceFull(shadowUrl0) as Sprite;
            }
         }
         if(Boolean(this.dressData))
         {
            this.x = this.dressData.getSave().x;
            this.y = this.dressData.getSave().y;
         }
      }
      
      public function getSprite() : DisplayObject
      {
         return this.img;
      }
      
      public function getShadow() : DisplayObject
      {
         return this.shadow;
      }
      
      public function getDressData() : CityDressData
      {
         return this.dressData;
      }
      
      public function getImgRect() : Rectangle
      {
         return this.imgRect;
      }
      
      public function getMouseB(dressB0:Boolean) : Boolean
      {
         if(dressB0)
         {
            return this.dressData;
         }
         return !this.dressData;
      }
      
      public function getLink() : String
      {
         var houseD0:CityHouseDefine = this.def as CityHouseDefine;
         if(Boolean(houseD0))
         {
            return houseD0.link;
         }
         return "";
      }
      
      public function mouseOver() : void
      {
         this.img.transform.colorTransform = over_CF;
      }
      
      public function mouseOut() : void
      {
         this.img.transform.colorTransform = NormalBtn.normal_CF;
         if(this.downB)
         {
            this.mouseUp();
         }
      }
      
      public function mouseDown() : void
      {
         this.img.transform.colorTransform = NormalBtn.down_CF;
         this.downB = true;
      }
      
      public function mouseUp() : void
      {
         this.img.transform.colorTransform = NormalBtn.over_CF;
         this.downB = false;
      }
      
      public function toSetGlow() : void
      {
         this.img.filters = [SET_FILTER];
         this.img.parent.addChild(this.img);
      }
      
      public function clearSetGlow() : void
      {
         if(this.img.filters.length > 0)
         {
            this.img.filters = [];
         }
      }
      
      public function set x(v0:Number) : void
      {
         this._x = v0;
         this.img.x = v0;
         if(Boolean(this.shadow))
         {
            this.shadow.x = v0;
         }
         if(Boolean(this.dressData))
         {
            this.dressData.getSave().x = v0;
         }
      }
      
      public function set y(v0:Number) : void
      {
         this._y = v0;
         this.img.y = v0;
         if(Boolean(this.shadow))
         {
            this.shadow.y = v0;
         }
         if(Boolean(this.dressData))
         {
            this.dressData.getSave().y = v0;
         }
      }
      
      public function get x() : Number
      {
         return this._x;
      }
      
      public function get y() : Number
      {
         return this._y;
      }
      
      public function moveByMouse(dragPoint0:Point) : void
      {
         this.x = this.img.parent.mouseX - dragPoint0.x;
         this.y = this.img.parent.mouseY - dragPoint0.y;
      }
      
      public function hitImg(x0:Number, y0:Number) : Boolean
      {
         if(Boolean(this.imgRect))
         {
            return this.imgRect.contains(x0 - this._x,y0 - this._y);
         }
         return false;
      }
   }
}

