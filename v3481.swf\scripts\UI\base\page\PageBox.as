package UI.base.page
{
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class PageBox extends MovieClip
   {
      private var haveDataB:Boolean = false;
      
      private var prevBtn:NormalBtn = null;
      
      private var nextBtn:NormalBtn = null;
      
      private var pageUrl:String = null;
      
      private var pageArr:Array = [];
      
      private var pageTxt:TextField = null;
      
      private var totalPage:int = 0;
      
      private var nowPage:int = 0;
      
      private var maxPage:int = 5;
      
      private var firstPage:int = 0;
      
      private var table:IO_PageTable = null;
      
      private var t:Number = -1;
      
      private var timeEventB:Boolean = false;
      
      public function PageBox()
      {
         super();
      }
      
      public function setXY_bySp(sp0:DisplayObject, fatherSp0:DisplayObject) : void
      {
         x = sp0.x - fatherSp0.x;
         y = sp0.y - fatherSp0.y;
      }
      
      public function setImgUrl(url0:String) : void
      {
         this.setImg(Gaming.swfLoaderManager.getResourceFull(url0));
      }
      
      public function setImg(img0:DisplayObjectContainer) : void
      {
         if(this.haveDataB)
         {
            INIT.showError("不能设置第二次外观");
         }
         this.haveDataB = true;
         this.prevBtn = new NormalBtn();
         this.nextBtn = new NormalBtn();
         var prev0:MovieClip = img0["prevBtn"];
         if(Boolean(prev0))
         {
            this.prevBtn.setImg(prev0);
         }
         var next0:MovieClip = img0["nextBtn"];
         if(Boolean(next0))
         {
            this.nextBtn.setImg(next0);
         }
         this.initBtn();
         this.pageTxt = img0["pageTxt"];
         if(Boolean(this.pageTxt))
         {
            addChild(this.pageTxt);
            FontDeal.dealOne(this.pageTxt);
         }
      }
      
      public function setBtnUrl(prevUrl0:String, pageUrl0:String, autoCnB0:Boolean = true) : void
      {
         if(this.haveDataB)
         {
            INIT.showError("不能设置第二次外观");
         }
         this.haveDataB = true;
         this.prevBtn = new NormalBtn();
         this.prevBtn.setImgUrl(prevUrl0);
         this.nextBtn = new NormalBtn();
         this.nextBtn.setImgUrl(prevUrl0);
         this.pageUrl = pageUrl0;
         if(autoCnB0)
         {
            this.prevBtn.setName("上一页");
            this.nextBtn.setName("下一页");
         }
         this.initBtn();
      }
      
      private function initBtn() : void
      {
         addChild(this.prevBtn);
         addChild(this.nextBtn);
         this.prevBtn.addEventListener(MouseEvent.CLICK,this.prev);
         this.nextBtn.addEventListener(MouseEvent.CLICK,this.next);
      }
      
      public function setToNormalBtn() : void
      {
         this.setBtnUrl("BasicUI/pagePrevBtn","BasicUI/pageBtn");
      }
      
      public function setToSmall() : void
      {
         this.setImgUrl("BasicUI/smallPageBtn");
         this.prevBtn.setName("<",false);
         this.nextBtn.setName(">",false);
      }
      
      public function setOnlyBtn(prevBtn0:NormalBtn, nextBtn0:NormalBtn) : void
      {
         if(this.haveDataB)
         {
            INIT.showError("不能设置第二次外观");
         }
         this.haveDataB = true;
         this.prevBtn = prevBtn0;
         this.nextBtn = nextBtn0;
         this.prevBtn.addEventListener(MouseEvent.CLICK,this.prev);
         this.nextBtn.addEventListener(MouseEvent.CLICK,this.next);
         this.pageUrl = null;
      }
      
      private function setPageText(str0:String) : void
      {
         if(Boolean(this.pageTxt))
         {
            this.pageTxt.htmlText = str0;
         }
      }
      
      private function setPageValue(v0:Number) : void
      {
         this.nowPage = v0;
         this.setPageText(v0 + 1 + "/" + this.totalPage);
      }
      
      public function inData_byTable(table0:IO_PageTable, maxPage0:int = 5, showPage0:int = 0) : void
      {
         if(!this.haveDataB)
         {
            return;
         }
         var totalPage0:int = int(table0.getTotalPage());
         if(totalPage0 != this.totalPage)
         {
            this.clearAllPage();
         }
         this.totalPage = totalPage0;
         this.maxPage = maxPage0;
         this.table = table0;
         this.setPageNum(this.totalPage);
         this.showPage(showPage0);
         this.t = -1;
      }
      
      public function fleshByTable() : void
      {
         if(Boolean(this.table))
         {
            if(this.totalPage != this.table.getTotalPage())
            {
               this.inData_byTable(this.table,this.maxPage,this.nowPage);
            }
         }
      }
      
      public function setPageNumOut(_totalPage:int) : void
      {
         this.totalPage = _totalPage;
         this.setPageNum(this.totalPage);
      }
      
      public function setMaxPageShow(num0:int) : void
      {
         this.maxPage = num0;
      }
      
      public function getTotalPage() : int
      {
         return this.totalPage;
      }
      
      private function setPageNum(_totalPage:int) : *
      {
         var n:int = 0;
         var pb0:NormalBtn = null;
         var nowpage0:int = this.nowPage;
         this.clearAllPage(false);
         this.setPageValue(nowpage0);
         this.totalPage = _totalPage;
         if(Boolean(this.pageUrl))
         {
            for(n = 0; n < this.totalPage; n++)
            {
               pb0 = new NormalBtn();
               pb0.setImgUrl(this.pageUrl);
               pb0.setName(n + 1 + "");
               pb0.index = n;
               addChild(pb0);
               pb0.addEventListener(MouseEvent.CLICK,this.pageClick);
               pb0.addEventListener(MouseEvent.MOUSE_OVER,this.pageOver);
               this.pageArr[n] = pb0;
            }
         }
      }
      
      public function clearAllPage(clearTableB0:Boolean = true) : void
      {
         var n:* = undefined;
         var pb0:NormalBtn = null;
         for(n in this.pageArr)
         {
            pb0 = this.pageArr[n];
            pb0.removeEventListener(MouseEvent.CLICK,this.pageClick);
            pb0.removeEventListener(MouseEvent.MOUSE_OVER,this.pageOver);
            this.removeChild(pb0);
            pb0.clear();
         }
         this.pageArr.length = 0;
         this.totalPage = 0;
         this.firstPage = 0;
         this.setPageValue(0);
         if(clearTableB0)
         {
            this.table = null;
         }
      }
      
      public function fleshPagePosition() : *
      {
         var n:* = undefined;
         var pb0:NormalBtn = null;
         var tlen:int = 0;
         var first0:int = this.nowPage - int((this.maxPage + 1) / 2) + 1;
         var last0:int = 0;
         if(first0 < 0)
         {
            first0 = 0;
         }
         else if(this.maxPage > this.totalPage)
         {
            first0 = 0;
         }
         else if(first0 > this.totalPage - this.maxPage)
         {
            first0 = this.totalPage - this.maxPage;
         }
         last0 = first0 + this.maxPage - 1;
         if(last0 > this.totalPage - 1)
         {
            last0 = this.totalPage - 1;
         }
         for(n in this.pageArr)
         {
            pb0 = this.pageArr[n];
            pb0.actived = true;
            pb0.isChosen = false;
            if(n >= first0 && n <= last0)
            {
               tlen = 0;
               if(this.maxPage < this.totalPage)
               {
                  tlen = this.maxPage * pb0.width - 2;
               }
               else
               {
                  tlen = this.totalPage * pb0.width - 2;
               }
               pb0.x = -tlen / 2 + pb0.width * (n - first0);
               pb0.visible = true;
               if(n == this.nowPage)
               {
                  pb0.actived = false;
                  pb0.isChosen = true;
               }
            }
            else
            {
               pb0.visible = false;
            }
         }
         if(this.pageArr.length > 0)
         {
            this.prevBtn.x = this.pageArr[first0].x - this.prevBtn.width - 5;
            this.nextBtn.x = this.pageArr[last0].x + this.pageArr[last0].width + 5;
         }
         this.prevBtn.actived = this.nowPage > 0;
         this.nextBtn.actived = this.nowPage < this.totalPage - 1;
      }
      
      public function showPage(num0:int, eventB0:Boolean = true) : void
      {
         var evt0:ClickEvent = null;
         if(num0 < 0)
         {
            num0 = 0;
         }
         else if(num0 > this.totalPage - 1)
         {
            num0 = this.totalPage - 1;
         }
         this.setPageValue(num0);
         this.fleshPagePosition();
         if(Boolean(this.table))
         {
            this.table.showPage(this.nowPage);
         }
         if(eventB0)
         {
            evt0 = new ClickEvent(ClickEvent.ON_SHOW_PAGE);
            evt0.index = this.nowPage;
            dispatchEvent(evt0);
         }
      }
      
      public function getNowPage() : int
      {
         return this.nowPage;
      }
      
      private function prev(event:MouseEvent = null) : *
      {
         --this.nowPage;
         this.showPage(this.nowPage);
      }
      
      private function next(event:MouseEvent = null) : *
      {
         ++this.nowPage;
         this.showPage(this.nowPage);
      }
      
      private function pageClick(event:MouseEvent) : *
      {
         var pb1:NormalBtn = event.target as NormalBtn;
         var cindex:int = pb1.index;
         if(cindex != this.nowPage)
         {
            this.nowPage = cindex;
            this.showPage(this.nowPage);
         }
      }
      
      private function pageOver(event:MouseEvent) : *
      {
         if(Gaming.uiGroup.dragCtrl.dragB)
         {
            if(this.t <= 0)
            {
               this.pageClick(event);
               if(this.totalPage > this.maxPage)
               {
                  this.t = 0.043333333333333335;
               }
            }
         }
      }
      
      public function FTimer() : void
      {
         if(visible == false)
         {
            return;
         }
         if(this.t > -1)
         {
            this.t -= 0.03333333333333333;
            if(this.t <= 0)
            {
               this.t = -1;
            }
         }
      }
   }
}

