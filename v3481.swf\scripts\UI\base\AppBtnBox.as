package UI.base
{
   import UI.base.button.NormalBtn;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class AppBtnBox extends AppNormalUI
   {
      public var btnObj:Object = {};
      
      protected var btnArr:Array = [];
      
      private var btnMidY:int = 0;
      
      private var btnGap:int = 0;
      
      private var btnHeight:int = 0;
      
      public function AppBtnBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var mc0:DisplayObject = null;
         var index0:int = 0;
         var name0:String = null;
         var btn0:NormalBtn = null;
         super.setImg(img0);
         var num0:int = img0.numChildren;
         for(var i:int = 0; i < num0; i++)
         {
            mc0 = img0.getChildAt(i);
            index0 = int(mc0.name.indexOf("BtnSp"));
            if(index0 > 0)
            {
               name0 = mc0.name.substr(0,index0);
               btn0 = new NormalBtn();
               btn0.setImg(mc0 as MovieClip);
               btn0.label = name0;
               addChild(btn0);
               btn0.addEventListener(MouseEvent.CLICK,this.btnClick);
               this.btnObj[name0] = btn0;
               this.btnArr.push(btn0);
               i--;
               num0--;
            }
         }
         this.btnArr.sort(this.sortBtnArrayFun);
         this.countBtnData();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function getBtn(name0:String) : NormalBtn
      {
         return this.btnObj[name0];
      }
      
      private function sortBtnArrayFun(btn0:NormalBtn, btn2:NormalBtn) : int
      {
         if(btn0.y < btn2.y)
         {
            return -1;
         }
         return 1;
      }
      
      private function countBtnData() : void
      {
         var n:* = undefined;
         var btn0:NormalBtn = null;
         var first_y0:int = -10000;
         for(n in this.btnArr)
         {
            btn0 = this.btnArr[n];
            if(n == 0)
            {
               first_y0 = btn0.y;
               this.btnHeight = btn0.height;
            }
            else if(n == 1)
            {
               this.btnGap = btn0.y - first_y0 - this.btnHeight;
            }
            if(n == this.btnArr.length - 1)
            {
               this.btnMidY = first_y0 + (btn0.y + btn0.height - first_y0) / 2;
            }
         }
      }
      
      protected function showBtnArr(arr0:Array) : void
      {
         var n:* = undefined;
         var all_h0:int = 0;
         var y0:int = 0;
         var btn0:NormalBtn = null;
         var btn2:NormalBtn = null;
         for(n in this.btnArr)
         {
            btn0 = this.btnArr[n];
            btn0.visible = arr0.indexOf(btn0.label) >= 0;
         }
         all_h0 = arr0.length * this.btnHeight + (arr0.length - 1) * this.btnGap;
         y0 = this.btnMidY - all_h0 / 2;
         for(n in arr0)
         {
            btn2 = this.btnObj[arr0[n]];
            btn2.y = y0 + n * (this.btnGap + this.btnHeight);
         }
      }
      
      protected function setAllProperty(arr0:Array, pro0:String, value0:*) : void
      {
         var n:* = undefined;
         var btn2:NormalBtn = null;
         for(n in arr0)
         {
            btn2 = this.btnObj[arr0[n]];
            btn2[pro0] = value0;
         }
      }
      
      protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
      }
   }
}

