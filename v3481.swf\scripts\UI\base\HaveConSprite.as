package UI.base
{
   import flash.display.DisplayObjectContainer;
   import flash.display.Sprite;
   
   public class HaveConSprite extends Sprite
   {
      private var con:DisplayObjectContainer = null;
      
      public function HaveConSprite()
      {
         super();
      }
      
      public function setCon(con0:DisplayObjectContainer) : void
      {
         if(this.parent != con0)
         {
            if(<PERSON><PERSON><PERSON>(this.parent))
            {
               this.parent.removeChild(this);
            }
            this.con = con0;
         }
      }
      
      protected function outCon() : void
      {
         if(<PERSON><PERSON><PERSON>(this.con) && Boolean(this.parent))
         {
            this.parent.removeChild(this);
         }
      }
      
      protected function inCon() : void
      {
         if(<PERSON><PERSON><PERSON>(this.con) && this.parent != this.con)
         {
            this.con.addChild(this);
         }
      }
   }
}

