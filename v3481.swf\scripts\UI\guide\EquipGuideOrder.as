package UI.guide
{
   import UI.bag.ItemsGrid;
   import dataAll.equip.EquipData;
   
   public class EquipGuideOrder extends NormalGuideOrder
   {
      public function EquipGuideOrder()
      {
         super();
         arr = [this.mainBtn,this.equipBtn,this.loadBtn];
      }
      
      private function mainBtn() : OneGuideData
      {
         if(Gaming.PG.da.equipBag.dataArr.length >= 1 && Gaming.PG.da.equip.dataArr.length == 0)
         {
            return new OneGuideData(Gaming.uiGroup.mainUI.getBtn("wear"),"你有一个新装备");
         }
         return null;
      }
      
      private function equipBtn() : OneGuideData
      {
         var s0:int = 0;
         var grip0:ItemsGrid = null;
         var da0:EquipData = Gaming.PG.da.equipBag.dataArr[0];
         if(Boolean(da0))
         {
            Gaming.PG.da.equipBag.swapByOther(Gaming.PG.da.equipBag,da0.save.site,0);
            Gaming.uiGroup.bagUI.showBox("equip");
            Gaming.uiGroup.bagUI.equipBox.pageBox.showPage(0);
            s0 = da0.save.site;
            grip0 = Gaming.uiGroup.bagUI.equipBox.gripArr[s0];
            return new OneGuideData(grip0,"点击新装备");
         }
         return null;
      }
      
      private function loadBtn() : OneGuideData
      {
         return new OneGuideData(Gaming.uiGroup.btnList.gripArr[0],"点击“装上”\n即可穿上装备");
      }
   }
}

