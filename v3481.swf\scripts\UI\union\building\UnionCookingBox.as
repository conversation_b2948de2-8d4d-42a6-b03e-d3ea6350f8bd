package UI.union.building
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.union.building.UnionBuildingDataGroup;
   import dataAll._app.union.building.cooking.UnionCookingData;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class UnionCookingBox extends AutoNormalUI
   {
      private var numTxt:TextField;
      
      private var gripTag:Sprite;
      
      private var box:ItemsGripBox = new ItemsGripBox();
      
      private var nowData:UnionCookingData;
      
      public function UnionCookingBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         FontDeal.dealOne(this.numTxt);
         this.box.setIconPro("UnionUI/foodGrip");
         this.box.arg.init(1,5,0,2);
         this.box.x = this.gripTag.x;
         this.box.y = this.gripTag.y;
         this.box.evt.setWantEvent(true,false,false,true,true);
         this.box.addEventListener(ClickEvent.ON_CLICK,this.barClick);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.box);
         addChild(this.box);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      private function fleshData() : void
      {
         var n:* = undefined;
         var da0:UnionCookingData = null;
         var grip0:ItemsGrid = null;
         var state0:String = null;
         var activedB0:Boolean = false;
         var smallIconStr0:String = null;
         var num0:int = Gaming.PG.da.union.building.getTodayCanEatNum();
         var numB0:Boolean = num0 > 0;
         var daArr0:Array = Gaming.PG.da.union.building.getCookingDataArr();
         if(this.box.gripArr.length < daArr0.length)
         {
            this.box.setNowGripNum(daArr0.length);
         }
         for(n in daArr0)
         {
            da0 = daArr0[n];
            grip0 = this.box.gripArr[n];
            grip0.activedAndEnabled = false;
            state0 = da0.getState();
            activedB0 = false;
            smallIconStr0 = state0;
            if(state0 == "no")
            {
               if(!numB0)
               {
                  smallIconStr0 = "gray";
               }
               else
               {
                  activedB0 = true;
               }
            }
            if(grip0.getSmallIconStr() != smallIconStr0 || grip0.itemsData != da0)
            {
               grip0.itemsData = da0;
               grip0.setName(da0.def.cnName);
               grip0.setIconName(da0.def.iconUrl);
               grip0.setSmallIcon(smallIconStr0);
            }
            if(grip0.actived != activedB0)
            {
               grip0.actived = activedB0;
            }
            grip0.setNumText(da0.getTimeStr());
         }
         this.numTxt.htmlText = "今日剩余进食次数：" + ComMethod.colorEnoughNum(num0) + "次";
      }
      
      private function barClick(e:ClickEvent) : void
      {
         var buildingDg0:UnionBuildingDataGroup = null;
         var da0:UnionCookingData = null;
         var tipStr0:String = null;
         var btn0:NormalBtn = e.child as NormalBtn;
         if(btn0.actived)
         {
            buildingDg0 = Gaming.PG.da.union.building;
            da0 = e.childData as UnionCookingData;
            tipStr0 = buildingDg0.getEatCookingDataTip(da0);
            this.nowData = da0;
            if(tipStr0 == "")
            {
               this.after_barClick();
            }
            else
            {
               Gaming.uiGroup.alertBox.showNormal(tipStr0,"yesAndNo",this.after_barClick);
            }
         }
      }
      
      private function after_barClick(e:* = null) : void
      {
         Gaming.PG.da.union.building.eatOne(this.nowData);
         this.fleshData();
         Gaming.soundGroup.playSound("uiSound","getLottery");
      }
      
      public function stopAll() : void
      {
         this.box.stopAll();
      }
      
      public function playAll() : void
      {
         this.box.playAll();
      }
      
      public function FTimerSecond() : void
      {
         if(visible)
         {
         }
      }
   }
}

