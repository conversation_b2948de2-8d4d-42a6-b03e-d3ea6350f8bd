package w_test
{
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll.level.define.LevelDefine;
   import dataAll.level.define.mapRect.MapRect;
   import dataAll.level.define.unit.OneUnitOrderDefine;
   import dataAll.level.define.unit.UnitOrderDefine;
   import flash.events.KeyboardEvent;
   import flash.ui.Keyboard;
   import gameAll.body.IO_NormalBody;
   import gameAll.hero.HeroBody;
   import gameAll.level.unit.UnitCreator;
   
   public class TestCtrl_Level extends TestCtrl_Normal
   {
      public function TestCtrl_Level()
      {
         super();
      }
      
      override public function keyDown(e:KeyboardEvent) : void
      {
         if(e.keyCode != Keyboard.M)
         {
            if(e.keyCode != Keyboard.N)
            {
               if(e.keyCode != Keyboard.J)
               {
                  if(e.keyCode != Keyboard.L)
                  {
                     if(e.keyCode == Keyboard.K)
                     {
                     }
                  }
               }
            }
         }
      }
      
      public function testRepeat() : void
      {
         var n:* = undefined;
         var d0:WorldMapDefine = null;
         var lv_d9:LevelDefine = null;
         var obj0:Object = Gaming.defineGroup.worldMap.obj;
         for(n in obj0)
         {
            d0 = obj0[n];
            lv_d9 = Gaming.defineGroup.level.getDefine(d0.getRepeatLevelName());
         }
      }
      
      public function testLevelRepeat() : void
      {
         var father0:Object = null;
         var d0:LevelDefine = null;
         var bb0:Boolean = false;
         var obj0:Object = Gaming.defineGroup.level.obj;
         for each(father0 in obj0)
         {
            for each(d0 in father0)
            {
               bb0 = LevelDefine.checkRepeatByName(d0.name);
            }
         }
      }
      
      public function enemyBmp(ud0:UnitOrderDefine, rect0:MapRect) : void
      {
         var n:* = undefined;
         var d0:OneUnitOrderDefine = null;
         var b0:IO_NormalBody = null;
         var hero0:HeroBody = null;
         for(n in ud0.arr)
         {
            d0 = ud0.arr[n];
            b0 = UnitCreator.addUnitInBG(d0,rect0,rect0.width / ud0.arr.length * n);
            b0.getAi().enabled = false;
            b0.getAction().toStop();
            hero0 = b0 as HeroBody;
            if(Boolean(hero0))
            {
               hero0.img.setMXY(-10000,4000);
            }
         }
      }
   }
}

