package UI.pet.upgrade
{
   import UI.NormalUICtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import UI.base.must.NormalMustBox;
   import UI.base.tip.OneTextGather;
   import UI.pet.PetUI;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.pet.PetData;
   import dataAll.pet.base.PetBaseData;
   import dataAll.pet.gene.creator.GeneDataUpgradeCtrl;
   import dataAll.pet.gene.save.GeneSave;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   import flash.text.TextField;
   
   public class PetUpgradeBoard extends NormalUI
   {
      private var imgTag:Sprite;
      
      private var baseTxt:TextField;
      
      private var baseValueTxt:TextField;
      
      private var beforeTag:Sprite;
      
      private var afterTag:Sprite;
      
      private var beforeTxt:OneTextGather = new OneTextGather();
      
      private var afterTxt:OneTextGather = new OneTextGather();
      
      private var mustTag:Sprite;
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var btnSp:MovieClip;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var imgMc:MovieClip;
      
      public function PetUpgradeBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["beforeTag","afterTag","mustTag","btnSp","imgTag","baseTxt","baseValueTxt"];
         super.setImg(img0);
         FontDeal.dealLine(this.baseTxt);
         FontDeal.dealLine(this.baseValueTxt);
         addChild(this.beforeTxt);
         NormalUICtrl.setTag(this.beforeTxt,this.beforeTag);
         this.beforeTxt.init();
         this.beforeTxt.LAST_ICON_GAP = 5;
         addChild(this.afterTxt);
         NormalUICtrl.setTag(this.afterTxt,this.afterTag);
         this.afterTxt.init();
         this.afterTxt.LAST_ICON_GAP = 5;
         addChild(this.mustBox);
         this.mustBox.setNormalImg();
         NormalUICtrl.setTag(this.mustBox,this.mustTag);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("升级");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         if(Boolean(PetUI.nowGrip))
         {
            this.showAll(PetUI.getNowData());
         }
      }
      
      private function clearAll() : void
      {
         this.beforeTxt.setText("");
         this.afterTxt.setText("");
         this.mustBox.setShowState(false);
         this.btn.actived = false;
      }
      
      public function showAll(da0:PetData) : void
      {
         this.clearAll();
         this.showInfo(da0);
         this.showBodyImg(da0);
         this.showData(da0);
      }
      
      private function showBodyImg(da0:PetData) : void
      {
         this.clearBodyImg();
         var d0:NormalBodyDefine = da0.getBodyDefine();
         var mc0:MovieClip = Gaming.swfLoaderManager.getResource(d0.name,"stand");
         this.imgMc = mc0;
         this.imgTag.addChild(this.imgMc);
         var rect0:Rectangle = this.imgMc.getRect(this.imgMc);
         this.imgMc.y -= rect0.y + rect0.height / 2;
      }
      
      private function clearBodyImg() : void
      {
         if(this.imgMc is MovieClip)
         {
            if(Boolean(this.imgMc.parent))
            {
               this.imgMc.parent.removeChild(this.imgMc);
            }
            this.imgMc = null;
         }
      }
      
      private function showInfo(da0:PetData) : void
      {
         var base0:PetBaseData = da0.base;
         var s0:String = "";
         var v0:String = "";
         s0 += "名称";
         v0 += base0.save.playerName;
         s0 += "\n等级";
         v0 += "\n" + base0.save.level;
         s0 += "\n战斗力";
         v0 += "\n" + base0.getShowDps();
         s0 += "\n生命值";
         v0 += "\n" + base0.getMaxLife();
         s0 += "\n头部防御";
         v0 += "\n" + base0.getHeadDefence();
         this.baseTxt.text = s0;
         this.baseValueTxt.text = v0;
      }
      
      private function showData(da0:PetData) : void
      {
         var bb0:Boolean = false;
         var before_s0:GeneSave = da0.gene.save;
         var after_s0:GeneSave = GeneDataUpgradeCtrl.getAfterSave(before_s0);
         var must_d0:MustDefine = GeneDataUpgradeCtrl.getMust(da0.gene.save);
         var levelMaxB:Boolean = must_d0.lv >= 100;
         var before_str0:String = "<i1>|<green <b>当前" + before_s0.getTrueLevel() + "级</b>/>";
         before_str0 += "\n" + Gaming.defineGroup.geneCreator.getGather_byObj(before_s0.obj,after_s0.obj);
         var after_str0:String = "";
         if(levelMaxB)
         {
            after_str0 = "<i1>|<green <b>已升至最高等级</b>/>";
            this.mustBox.setShowState(false);
            this.btn.actived = false;
         }
         else
         {
            after_str0 = "<i1>|<green <b>升级后" + after_s0.getTrueLevel() + "级</b>/>";
            after_str0 += "\n" + Gaming.defineGroup.geneCreator.getGather_byObj(after_s0.obj,before_s0.obj);
            bb0 = this.mustBox.inData(must_d0,da0.base.save.level,"尸宠");
            this.btn.actived = bb0;
         }
         this.beforeTxt.setText(before_str0);
         this.afterTxt.setText(after_str0);
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var must_d0:MustDefine = null;
         var da0:PetData = PetUI.getNowData();
         if(Boolean(da0))
         {
            must_d0 = GeneDataUpgradeCtrl.getMust(da0.gene.save);
            PlayerMustCtrl.deductMust(must_d0,this.afterUpgrade);
         }
      }
      
      private function afterUpgrade() : void
      {
         var da0:PetData = PetUI.getNowData();
         GeneDataUpgradeCtrl.upgradeOne(da0);
         this.showAll(da0);
         Gaming.uiGroup.alertBox.showSuccess("升级成功！");
      }
      
      override public function set visible(bb0:Boolean) : void
      {
         super.visible = bb0;
         if(Boolean(this.imgMc))
         {
            if(bb0)
            {
               this.imgMc.play();
            }
            else
            {
               this.imgMc.stop();
            }
         }
      }
   }
}

