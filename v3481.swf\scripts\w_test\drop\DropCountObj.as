package w_test.drop
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll.arms.define.ArmsChargerDefine;
   import dataAll.arms.define.ArmsType;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipType;
   import dataAll.items.save.ItemsSave;
   
   public class DropCountObj
   {
      public var index:int = 0;
      
      public var childType:String = "";
      
      public var name:String = "";
      
      public var cn:String = "";
      
      public var obj:Object = {};
      
      public var num:Number = 0;
      
      public function DropCountObj()
      {
         super();
      }
      
      public static function getArmsObj() : DropCountObj
      {
         var color0:* = null;
         var c0:DropCountObj = null;
         var pi0:int = 0;
         var part0:* = null;
         var p0:DropCountObj = null;
         var d0:ArmsChargerDefine = null;
         var partTypeArr0:Array = ArmsType.TYPE_ARR;
         var all0:DropCountObj = new DropCountObj();
         all0.name = "arms";
         all0.cn = "武器";
         all0.childType = "color";
         var ci0:int = 0;
         for each(color0 in EquipColor.TYPE_ARR)
         {
            ci0++;
            c0 = new DropCountObj();
            c0.childType = "partType";
            c0.name = color0;
            c0.cn = EquipColor.getCn(color0);
            c0.index = ci0;
            pi0 = 0;
            for each(part0 in partTypeArr0)
            {
               pi0++;
               p0 = new DropCountObj();
               d0 = Gaming.defineGroup.armsCharger.getDefine(part0);
               p0.childType = "level";
               p0.name = part0;
               p0.cn = d0.cnName;
               p0.index = pi0;
               c0.addObj(p0);
            }
            all0.addObj(c0);
         }
         all0.addObj(getRareObj());
         return all0;
      }
      
      public static function getEquipObj() : DropCountObj
      {
         var color0:* = null;
         var c0:DropCountObj = null;
         var pi0:int = 0;
         var part0:* = null;
         var p0:DropCountObj = null;
         var partTypeArr0:Array = EquipType.TYPE_ARR;
         var all0:DropCountObj = new DropCountObj();
         all0.name = "equip";
         all0.cn = "装备";
         all0.childType = "color";
         var ci0:int = 0;
         for each(color0 in EquipColor.TYPE_ARR)
         {
            ci0++;
            c0 = new DropCountObj();
            c0.childType = "partType";
            c0.name = color0;
            c0.cn = EquipColor.getCn(color0);
            c0.index = ci0;
            pi0 = 0;
            for each(part0 in partTypeArr0)
            {
               pi0++;
               p0 = new DropCountObj();
               p0.childType = "level";
               p0.name = part0;
               p0.cn = EquipType.getCnName(part0);
               p0.index = pi0;
               c0.addObj(p0);
            }
            all0.addObj(c0);
         }
         all0.addObj(getRareObj());
         return all0;
      }
      
      public static function getRareObj() : DropCountObj
      {
         var r0:DropCountObj = new DropCountObj();
         r0.name = "rare";
         r0.cn = "稀有";
         r0.childType = "name";
         r0.index = 999;
         return r0;
      }
      
      public function addObj(obj0:DropCountObj) : void
      {
         if(!this.obj.hasOwnProperty(obj0.name))
         {
            this.obj[obj0.name] = obj0;
         }
      }
      
      public function addNormalObj(name0:String, childType0:String = "", pointB0:Boolean = false) : DropCountObj
      {
         var xx:int = 0;
         if(name0.indexOf("仲裁者") >= 0)
         {
            xx = 0;
         }
         if(pointB0)
         {
            name0 = "【" + name0 + "】";
         }
         ++this.num;
         var obj0:DropCountObj = this.obj[name0];
         if(!this.obj.hasOwnProperty(name0))
         {
            obj0 = new DropCountObj();
            this.obj[name0] = obj0;
            obj0.name = name0;
            obj0.cn = name0;
            obj0.childType = childType0;
            if(!isNaN(Number(name0)))
            {
               this.index = Number(name0);
            }
         }
         if(childType0 == "")
         {
            ++obj0.num;
         }
         return obj0;
      }
      
      public function addItemsSave(s0:ItemsSave) : void
      {
         var obj0:DropCountObj = null;
         if(this.childType == "color")
         {
            if(s0.isMoreRedB())
            {
               obj0 = this.obj["rare"];
            }
            else
            {
               obj0 = this.obj[s0.color];
            }
            ++this.num;
         }
         else if(this.childType == "partType")
         {
            obj0 = this.obj[s0.getChildType()];
            ++this.num;
         }
         else if(this.childType == "level")
         {
            this.addNormalObj(s0.getTrueLevel() + "","");
         }
         else if(this.childType == "name")
         {
            this.addNormalObj(s0.cnName);
         }
         if(Boolean(obj0))
         {
            obj0.addItemsSave(s0);
         }
      }
      
      public function getString(firstStr0:String) : String
      {
         var arr0:Array = null;
         var obj0:DropCountObj = null;
         var fs2:String = null;
         var s2:String = null;
         var cn0:String = this.cn;
         var str0:String = firstStr0 + cn0 + (this.childType == "" ? "：" + this.num : "【" + this.num + "】");
         if(this.childType != "")
         {
            str0 += "--------------------";
            if(ComMethod.getObjElementNum(this.obj) == 0)
            {
               str0 = "";
            }
            else
            {
               arr0 = this.getSortArray();
               for each(obj0 in arr0)
               {
                  fs2 = firstStr0 + "\t";
                  s2 = obj0.getString(fs2);
                  if(s2 != "")
                  {
                     str0 += "\n" + fs2 + obj0.getString(fs2);
                  }
               }
            }
         }
         return str0;
      }
      
      private function getSortArray() : Array
      {
         var arr0:Array = ComMethod.objToArr(this.obj);
         arr0.sort(this.sortFun);
         return arr0;
      }
      
      private function sortFun(b0:DropCountObj, b1:DropCountObj) : int
      {
         if(b0.index < b1.index)
         {
            return -1;
         }
         if(b0.index > b1.index)
         {
            return 1;
         }
         return 0;
      }
   }
}

