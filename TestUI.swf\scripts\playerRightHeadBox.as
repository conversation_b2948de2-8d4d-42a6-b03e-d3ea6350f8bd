package
{
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol660")]
   public dynamic class playerRightHeadBox extends MovieClip
   {
      public var iconTag:MovieClip;
      
      public var stateTag:MovieClip;
      
      public var lifeSp:MovieClip;
      
      public var levelTxt:TextField;
      
      public var nameTxt:TextField;
      
      public function playerRightHeadBox()
      {
         super();
      }
   }
}

