package UI.skill
{
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.must.NormalMustBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.skill.define.HeroSkillDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class SkillExploreUI extends NormalUI
   {
      private var btn:NormalBtn = new NormalBtn();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var mustBoxSp:Sprite;
      
      private var btnSp:MovieClip;
      
      public function SkillExploreUI()
      {
         super();
         addChild(this.btn);
         addChild(this.mustBox);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["mustBoxSp","btnSp"];
         super.setImg(img0);
         this.mustBox.setImg(this.mustBoxSp);
         this.btn.setImg(this.btnSp);
         this.btn.setName("探索技能");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function fleshData() : void
      {
         var mustLv0:int = 0;
         var must_d0:MustDefine = null;
         var bb0:Boolean = false;
         var pd0:NormalPlayerData = Gaming.PG.DATA;
         var num0:int = int(pd0.skill.dataArr.length + pd0.skillBag.dataArr.length);
         var max0:int = int(Gaming.defineGroup.skill.heroOriginalArr.length);
         if(num0 < max0)
         {
            mustLv0 = pd0.getStudyMustLv();
            must_d0 = HeroSkillDefine.getStudyMust(mustLv0);
            bb0 = this.mustBox.inData(must_d0);
            this.btn.actived = bb0;
         }
         else
         {
            Gaming.uiGroup.skillUI.setCoverText("没有技能可供探索");
         }
      }
      
      public function btnClick(e:MouseEvent) : void
      {
         var pd0:NormalPlayerData = Gaming.PG.DATA;
         var mustLv0:int = pd0.getStudyMustLv();
         var must_d0:MustDefine = HeroSkillDefine.getStudyMust(mustLv0);
         PlayerMustCtrl.deductMust(must_d0,this.affter_click);
      }
      
      private function affter_click() : void
      {
         var pd0:NormalPlayerData = Gaming.PG.DATA;
         var arr1:Array = pd0.getNoStudyOriginalSkillDefineArr();
         var d0:HeroSkillDefine = arr1[int(Math.random() * arr1.length)];
         if(!d0)
         {
            INIT.showError("探索失败");
         }
         pd0.skillBag.addSkillByLabel(d0.baseLabel,pd0.level);
         Gaming.soundGroup.playSound("uiSound","success");
         var tipText0:String = "获得新技能：" + ComMethod.color(d0.cnName,"#FF6600") + "。";
         Gaming.uiGroup.alertBox.showCheck(tipText0,"yes",0,null,null,d0.iconUrl,"equip");
         this.fleshData();
         Gaming.uiGroup.skillUI.wearBox.fleshData();
         Gaming.uiGroup.mainUI.fleshCoin();
      }
   }
}

