package UI.forging.equipCompose
{
   import UI.bag.BagUI;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.grid.NormalGrid;
   import UI.base.must.NormalMustBox;
   import dataAll.equip.EquipData;
   import dataAll.equip.creator.EquipComposeAgent;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.save.EquipSave;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class EquipComposeBoard extends NormalUI
   {
      private var gripTag:Sprite;
      
      private var btnSp:MovieClip;
      
      private var pointer:Sprite;
      
      private var mustSp:Sprite;
      
      private var icon1Sp:Sprite;
      
      private var icon2Sp:Sprite;
      
      private var icon9Sp:Sprite;
      
      private var icon1:EquipComposeIcon = new EquipComposeIcon();
      
      private var icon2:EquipComposeIcon = new EquipComposeIcon();
      
      private var icon9:EquipComposeIcon = new EquipComposeIcon();
      
      private var iconArr:Array = [this.icon1,this.icon2];
      
      private var allIconArr:Array = this.iconArr.concat(this.icon9);
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var gripBox:ItemsGripBox = new ItemsGripBox();
      
      private var nowAgent:EquipComposeAgent = null;
      
      public function EquipComposeBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var icon0:EquipComposeIcon = null;
         elementNameArr = ["gripTag","btnSp","pointer","mustSp","icon1Sp","icon2Sp","icon9Sp"];
         super.setImg(img0);
         this.addChild(this.gripTag);
         this.gripTag.addChild(this.gripBox);
         this.gripBox.imgType = "equipGrip";
         this.gripBox.arg.init(7,1,4,4);
         this.gripBox.evt.setWantEvent(true,false,false,true,true);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.gripBox);
         this.addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("合成");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.addChild(this.mustBox);
         this.mustBox.setImg(this.mustSp);
         this.addChild(this.icon1);
         this.icon1.setImg(this.icon1Sp);
         this.addChild(this.icon2);
         this.icon2.setImg(this.icon2Sp);
         this.addChild(this.icon9);
         this.icon9.setImg(this.icon9Sp);
         for each(icon0 in this.iconArr)
         {
            icon0.addDelBtn();
            icon0.addEventListener(ClickEvent.ON_UP,this.gripUp);
            icon0.addEventListener(ClickEvent.ON_CTRL_CLICK,this.delClick);
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function outLoginEvent() : void
      {
         this.nowAgent = null;
      }
      
      override public function show() : void
      {
         super.show();
         var bag0:BagUI = Gaming.uiGroup.bagUI;
         bag0.showAndLabel("equip",true);
         this.fleshGrip();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         if(Gaming.uiGroup.bagUI.visible)
         {
            Gaming.uiGroup.bagUI.hide();
         }
         super.hide();
      }
      
      private function fleshGrip() : void
      {
         if(this.gripBox.gripArr.length > 0)
         {
            return;
         }
         var arr0:Array = EquipComposeAgent.getComposeDefineArr();
         this.gripBox.inData_byArr(arr0,"inData_singleEquipDefine");
      }
      
      private function fleshData() : void
      {
         var grip0:NormalBtn = null;
         if(!this.nowAgent)
         {
            grip0 = this.gripBox.gripArr[0] as NormalBtn;
            if(Boolean(grip0))
            {
               this.chooseDefine(grip0.itemsData as EquipDefine);
            }
            else
            {
               this.chooseDefine(null);
            }
         }
         else
         {
            this.chooseDefine(this.nowAgent.def);
         }
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         this.chooseDefine(e.childData as EquipDefine);
      }
      
      private function chooseDefine(d0:EquipDefine) : void
      {
         var grip0:NormalBtn = null;
         var a0:EquipComposeAgent = null;
         var mustD0:MustDefine = null;
         var mustB0:Boolean = false;
         var iconB0:Boolean = false;
         if(Boolean(d0))
         {
            grip0 = this.gripBox.setChooseByItemsData(d0);
            this.pointer.x = grip0.x + grip0.width / 2 + this.gripTag.x;
            a0 = new EquipComposeAgent();
            a0.inDefine(d0);
            this.nowAgent = a0;
            mustD0 = a0.getMust();
            mustB0 = this.mustBox.inData(mustD0);
            iconB0 = this.fleshPanIcon();
            this.btn.actived = mustB0 && iconB0;
         }
         else
         {
            this.gripBox.setChoose_byIndex(-1);
            this.iconClearData();
            this.mustBox.setShowState(false);
            this.btn.actived = false;
            this.nowAgent = null;
         }
      }
      
      private function fleshPanIcon() : Boolean
      {
         var defArr0:Array = null;
         var daArr0:Array = null;
         var i:int = 0;
         var icon0:EquipComposeIcon = null;
         var da0:EquipData = null;
         var mustB0:Boolean = false;
         var composeDa0:EquipData = null;
         var bb0:Boolean = true;
         if(Boolean(this.nowAgent))
         {
            defArr0 = this.nowAgent.getMustDefArr();
            daArr0 = [];
            for(i = 0; i < defArr0.length; i++)
            {
               icon0 = this.iconArr[i];
               if(Boolean(icon0))
               {
                  da0 = icon0.getItemsData() as EquipData;
                  mustB0 = false;
                  if(Boolean(da0))
                  {
                     if(this.nowAgent.panMustDataStr(da0,i) == "")
                     {
                        if(Gaming.PG.da.equipBag.dataArr.indexOf(da0) >= 0)
                        {
                           mustB0 = true;
                        }
                     }
                     daArr0.push(da0);
                  }
                  if(!mustB0)
                  {
                     icon0.clearEquipData();
                     bb0 = false;
                  }
                  else
                  {
                     icon0.inEquipData(da0);
                  }
                  icon0.setTxt(this.nowAgent.getIconText(i,da0));
               }
               else
               {
                  bb0 = false;
               }
            }
            if(bb0)
            {
               composeDa0 = this.nowAgent.getComposeData(daArr0);
               this.icon9.inEquipData(composeDa0);
            }
            else
            {
               this.icon9.clearEquipData();
            }
            this.icon9.setTxt(this.nowAgent.getComposeIconText(composeDa0));
         }
         else
         {
            this.iconClearData();
         }
         return bb0;
      }
      
      private function iconClearData() : void
      {
         var icon0:EquipComposeIcon = null;
         for each(icon0 in this.allIconArr)
         {
            icon0.clearData();
         }
      }
      
      private function findIcon(da0:EquipData) : EquipComposeIcon
      {
         var icon0:EquipComposeIcon = null;
         for each(icon0 in this.iconArr)
         {
            if(icon0.getItemsData() == da0)
            {
               return icon0;
            }
         }
         return null;
      }
      
      private function gripUp(e:ClickEvent) : void
      {
         var tgrip0:NormalGrid = null;
         var targetDa0:EquipData = null;
         var icon0:EquipComposeIcon = null;
         var index0:int = 0;
         var mustStr0:String = null;
         if(Boolean(this.nowAgent))
         {
            tgrip0 = Gaming.uiGroup.dragCtrl.dragChild;
            if(Boolean(tgrip0))
            {
               targetDa0 = tgrip0.itemsData as EquipData;
               if(Boolean(targetDa0))
               {
                  if(!this.findIcon(targetDa0))
                  {
                     icon0 = e.target as EquipComposeIcon;
                     index0 = int(this.iconArr.indexOf(icon0));
                     mustStr0 = this.nowAgent.panMustDataStr(targetDa0,index0);
                     if(mustStr0 == "")
                     {
                        icon0.setItmesData(targetDa0);
                        this.fleshData();
                     }
                     else
                     {
                        Gaming.uiGroup.alertBox.showError(mustStr0);
                     }
                  }
               }
            }
         }
      }
      
      private function delClick(e:ClickEvent) : void
      {
         var icon0:EquipComposeIcon = e.target as EquipComposeIcon;
         icon0.clearEquipData();
         this.fleshData();
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var must_d0:MustDefine = null;
         if(Boolean(this.nowAgent))
         {
            if(this.btn.actived)
            {
               if(this.fleshPanIcon())
               {
                  must_d0 = this.nowAgent.getMust();
                  PlayerMustCtrl.deductMust(must_d0,this.afterDeduct);
               }
            }
         }
      }
      
      private function afterDeduct() : void
      {
         var icon0:EquipComposeIcon = null;
         var composeSave0:EquipSave = null;
         var delDataArr0:Array = [];
         for each(icon0 in this.iconArr)
         {
            delDataArr0.push(icon0.getItemsData() as EquipData);
         }
         composeSave0 = this.nowAgent.getComposeSave(delDataArr0);
         Gaming.PG.da.equipBag.removeDataArr(delDataArr0);
         Gaming.PG.da.equipBag.addSave(composeSave0);
         this.iconClearData();
         this.show();
         Gaming.uiGroup.alertBox.showSuccess("合成成功！");
      }
   }
}

