package UI.main
{
   import UI.base.grid.NormalGrid;
   import dataAll._app.task.TaskData;
   import dataAll._app.worldMap.save.WorldMapSave;
   import flash.display.Graphics;
   
   public class WorldMapGrip extends NormalGrid
   {
      public var save:WorldMapSave;
      
      public var taskData:TaskData;
      
      public function WorldMapGrip()
      {
         super();
      }
      
      public function lineTo(x0:int, y0:int) : void
      {
         var gh0:Graphics = this.graphics;
         gh0.lineStyle(0.1,16777215);
         x0 -= x;
         y0 -= y;
         var ra0:Number = Math.atan2(y0 - 0,x0 + 25);
         x0 -= Math.cos(ra0) * 20;
         y0 -= Math.sin(ra0) * 20;
         gh0.moveTo(0,-25);
         gh0.lineTo(x0,y0);
      }
   }
}

