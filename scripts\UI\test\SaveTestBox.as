package UI.test
{
   import UI.base.NormalUI;
   import com.adobe.serialization.json.JSON2;
   import com.common.data.Base64;
   import com.common.text.TextWay;
   import com.sounto.cf.ObjectToXml;
   import dataAll._app.login.SaveData4399;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipType;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFieldType;
   
   public class SaveTestBox extends NormalUI
   {
      public static var ME:SaveTestBox;
      
      public var haveDataB:Boolean = false;
      
      public var txt:TextField;
      
      public var inputBtn:SimpleButton;
      
      public var inputPCGBtn:SimpleButton;
      
      public var inputBase64Btn:SimpleButton;
      
      public var saveUidTxt:TextField;
      
      public var getByUidBtn:SimpleButton;
      
      public var setArenaGiftBtn:SimpleButton;
      
      public function SaveTestBox()
      {
         super();
         ME = this;
      }
      
      public static function addText(s0:String) : void
      {
         if(Boolean(ME))
         {
            if(ME.haveDataB)
            {
               if(Gaming.testCtrl.cheating.enabled)
               {
                  if(Boolean(ME.txt))
                  {
                     ME.txt.appendText("\n" + s0);
                  }
               }
            }
         }
      }
      
      public static function mustText(s0:String) : void
      {
         if(Boolean(ME))
         {
            if(ME.haveDataB)
            {
               if(Boolean(ME.txt))
               {
                  ME.txt.appendText("\n" + s0);
               }
            }
         }
      }
      
      public function initImg() : void
      {
         var img0:Sprite = Gaming.swfLoaderManager.getResource("TestUI","saveTestBox");
         if(Boolean(img0))
         {
            // 使用SWF资源
            addChildAt(img0,0);
            this.inputBtn = img0["inputBtn"];
            this.inputPCGBtn = img0["inputPCGBtn"];
            this.inputBase64Btn = img0["inputBase64Btn"];
            this.txt = img0["txt"];
            this.getByUidBtn = img0["getByUidBtn"];
            this.saveUidTxt = img0["saveUidTxt"];
            this.setArenaGiftBtn = img0["setArenaGiftBtn"];
         }
         else
         {
            // 创建内嵌界面
            this.createEmbeddedSaveTestUI();
         }

         // 通用初始化
         this.txt.text = "";
         this.saveUidTxt.text = "123456_0";
         this.inputBtn.addEventListener(MouseEvent.CLICK,this.inputSave);
         this.inputPCGBtn.addEventListener(MouseEvent.CLICK,this.inputPCGSave);
         this.getByUidBtn.addEventListener(MouseEvent.CLICK,this.getSaveByUid);
         this.setArenaGiftBtn.addEventListener(MouseEvent.CLICK,this.setArenaGiftBtnClick);
         this.haveDataB = true;
      }

      private function createEmbeddedSaveTestUI() : void
      {
         var bg:Sprite = new Sprite();
         bg.graphics.beginFill(0x222222, 1);
         bg.graphics.drawRect(0, 0, 580, 360);
         bg.graphics.endFill();
         addChildAt(bg, 0);

         // 创建文本区域
         this.txt = new TextField();
         this.txt.x = 10;
         this.txt.y = 10;
         this.txt.width = 560;
         this.txt.height = 200;
         this.txt.multiline = true;
         this.txt.wordWrap = true;
         this.txt.border = true;
         this.txt.borderColor = 0x666666;
         this.txt.background = true;
         this.txt.backgroundColor = 0x000000;
         this.txt.textColor = 0xFFFFFF;
         bg.addChild(this.txt);

         // 创建UID输入框
         this.saveUidTxt = new TextField();
         this.saveUidTxt.x = 10;
         this.saveUidTxt.y = 220;
         this.saveUidTxt.width = 100;
         this.saveUidTxt.height = 20;
         this.saveUidTxt.type = TextFieldType.INPUT;
         this.saveUidTxt.border = true;
         this.saveUidTxt.borderColor = 0x666666;
         this.saveUidTxt.background = true;
         this.saveUidTxt.backgroundColor = 0x000000;
         this.saveUidTxt.textColor = 0xFFFFFF;
         bg.addChild(this.saveUidTxt);

         // 创建按钮
         this.inputBtn = this.createButton("导入存档", 10, 250);
         this.inputPCGBtn = this.createButton("导入PCG", 120, 250);
         this.inputBase64Btn = this.createButton("Base64", 230, 250);
         this.getByUidBtn = this.createButton("获取UID", 120, 220);
         this.setArenaGiftBtn = this.createButton("竞技场", 340, 250);

         bg.addChild(this.inputBtn);
         bg.addChild(this.inputPCGBtn);
         bg.addChild(this.inputBase64Btn);
         bg.addChild(this.getByUidBtn);
         bg.addChild(this.setArenaGiftBtn);
      }

      private function createButton(label:String, x:Number, y:Number) : SimpleButton
      {
         var upState:Sprite = new Sprite();
         upState.graphics.beginFill(0x666666, 1);
         upState.graphics.drawRect(0, 0, 100, 25);
         upState.graphics.endFill();

         var overState:Sprite = new Sprite();
         overState.graphics.beginFill(0x888888, 1);
         overState.graphics.drawRect(0, 0, 100, 25);
         overState.graphics.endFill();

         var downState:Sprite = new Sprite();
         downState.graphics.beginFill(0x444444, 1);
         downState.graphics.drawRect(0, 0, 100, 25);
         downState.graphics.endFill();

         var hitArea:Sprite = new Sprite();
         hitArea.graphics.beginFill(0x000000, 0);
         hitArea.graphics.drawRect(0, 0, 100, 25);
         hitArea.graphics.endFill();

         var btn:SimpleButton = new SimpleButton(upState, overState, downState, hitArea);
         btn.x = x;
         btn.y = y;

         return btn;
      }
      
      public function setUidText(s0:String) : void
      {
         if(Boolean(this.saveUidTxt))
         {
            this.saveUidTxt.text = s0;
         }
      }
      
      private function setArenaGiftBtnClick(e:MouseEvent) : void
      {
         var n:* = undefined;
         var str0:String = null;
         var name0:String = null;
         var type0:String = null;
         var da0:EquipData = null;
         var arr0:Array = this.txt.text.split("\r");
         var typeArr0:Array = EquipType.SUIT_TYPE_ARR;
         for(n in arr0)
         {
            str0 = arr0[n];
            str0 = TextWay.toHan2(str0);
            name0 = Gaming.defineGroup.skill.getEquipSkillNameByCn(str0);
            if(name0 != "")
            {
               type0 = typeArr0[n];
               da0 = Gaming.PG.da.equip.getOneDataByType(type0);
               da0.save.skillArr = [name0];
            }
         }
      }
      
      private function inputSave(e:MouseEvent) : void
      {
         var str0:String = this.txt.text;
         var obj0:Object = JSON2.decode(str0);
         Gaming.uiGroup.loginUI.outLoginEvent();
         Gaming.PG.loginData.newSave(0);
         Gaming.uiGroup.loginUI.yes_read(obj0);
      }
      
      private function inputBase64Save(e:MouseEvent) : void
      {
         var str0:String = this.txt.text;
         var obj0:Object = Base64.decodeObject(str0);
      }
      
      public function inputPCGSave(e:MouseEvent = null) : void
      {
         var xml0:XML = XML(this.txt.text);
         var obj0:Object = ObjectToXml.encode(xml0.s[0]);
         var xml2:XML = ObjectToXml.decode(obj0);
         this.txt.text = xml2.toString();
      }
      
      private function getSaveByUid(e:MouseEvent) : void
      {
         var str_arr0:Array = this.saveUidTxt.text.split("_");
         var index0:int = int(String(str_arr0[1]));
         var uid0:String = String(str_arr0[0]);
         this.getUserData(uid0,index0);
      }
      
      public function getUserData(uid0:String, index0:int) : void
      {
         Gaming.uiGroup.connectUI.show("加载存档uid:" + uid0 + "  index:" + index0);
         Gaming.api.top.getUserData(uid0,index0,this.yes_getSaveByUid,this.no_getSaveByUid);
      }
      
      public function setUid(uid0:String, index0:int) : void
      {
         if(Boolean(this.saveUidTxt))
         {
            this.saveUidTxt.text = uid0 + "_" + index0;
         }
      }
      
      private function yes_getSaveByUid(ud0:SaveData4399) : void
      {
         var str0:String = null;
         Gaming.uiGroup.connectUI.hide();
         if(ud0.data == "")
         {
            Gaming.uiGroup.alertBox.showError("没有找到存档。");
         }
         else
         {
            str0 = JSON2.encode(ud0.data);
            this.txt.text = str0;
            Gaming.uiGroup.alertBox.showNormal("复制存档成功！","yes");
         }
      }
      
      private function no_getSaveByUid(str0:String = "") : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showNormal(str0,"yes",null,null,"no");
      }
   }
}

