package UI.test
{
   import UI.base.NormalUI;
   import com.adobe.serialization.json.JSON2;
   import com.common.data.Base64;
   import com.common.text.TextWay;
   import com.sounto.cf.ObjectToXml;
   import dataAll._app.login.SaveData4399;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipType;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFieldType;
   
   public class SaveTestBox extends NormalUI
   {
      private var inputBtn:SimpleButton;
      
      private var inputPCGBtn:SimpleButton;
      
      private var inputBase64Btn:SimpleButton;
      
      private var txt:TextField;
      
      private var getByUidBtn:SimpleButton;
      
      private var saveUidTxt:TextField;
      
      private var setArenaGiftBtn:SimpleButton;
      
      private var haveDataB:Boolean = false;
      
      public function SaveTestBox()
      {
         super();
      }
      
      public static function addText(str0:String) : void
      {
         if(Boolean(Gaming.uiGroup.testUI.saveBox.txt))
         {
            Gaming.uiGroup.testUI.saveBox.txt.appendText(str0 + "\n");
         }
      }
      
      public static function clearText() : void
      {
         if(Boolean(Gaming.uiGroup.testUI.saveBox.txt))
         {
            Gaming.uiGroup.testUI.saveBox.txt.text = "";
         }
      }
      
      public static function setText(str0:String) : void
      {
         if(Boolean(Gaming.uiGroup.testUI.saveBox.txt))
         {
            Gaming.uiGroup.testUI.saveBox.txt.text = str0;
         }
      }
      
      public static function getText() : String
      {
         if(Boolean(Gaming.uiGroup.testUI.saveBox.txt))
         {
            return Gaming.uiGroup.testUI.saveBox.txt.text;
         }
         return "";
      }
      
      public static function getTextArr() : Array
      {
         var str0:String = getText();
         if(str0 == "")
         {
            return [];
         }
         return str0.split("\n");
      }
      
      public static function addTextArr(arr0:Array) : void
      {
         var str0:* = null;
         for each(str0 in arr0)
         {
            addText(str0);
         }
      }
      
      public function initImg() : void
      {
         try {
            // 直接创建内嵌界面
            this.createEmbeddedSaveTestUI();

            // 通用初始化
            if(this.txt) {
               this.txt.text = "";
            }
            if(this.saveUidTxt) {
               this.saveUidTxt.text = "123456_0";
            }
            if(this.inputBtn) {
               this.inputBtn.addEventListener(MouseEvent.CLICK,this.inputSave);
            }
            if(this.inputPCGBtn) {
               this.inputPCGBtn.addEventListener(MouseEvent.CLICK,this.inputPCGSave);
            }
            if(this.getByUidBtn) {
               this.getByUidBtn.addEventListener(MouseEvent.CLICK,this.getSaveByUid);
            }
            if(this.setArenaGiftBtn) {
               this.setArenaGiftBtn.addEventListener(MouseEvent.CLICK,this.setArenaGiftBtnClick);
            }
            this.haveDataB = true;
         } catch(e:Error) {
            // 确保haveDataB被设置，即使初始化失败
            this.haveDataB = true;
         }
      }
      
      private function createEmbeddedSaveTestUI() : void
      {
         var bg:Sprite = new Sprite();
         bg.graphics.beginFill(0x222222, 1);
         bg.graphics.drawRect(0, 0, 580, 360);
         bg.graphics.endFill();
         addChildAt(bg, 0);
         
         // 创建文本区域
         this.txt = new TextField();
         this.txt.x = 10;
         this.txt.y = 10;
         this.txt.width = 560;
         this.txt.height = 200;
         this.txt.multiline = true;
         this.txt.wordWrap = true;
         this.txt.border = true;
         this.txt.borderColor = 0x666666;
         this.txt.background = true;
         this.txt.backgroundColor = 0x000000;
         this.txt.textColor = 0xFFFFFF;
         bg.addChild(this.txt);
         
         // 创建UID输入框
         this.saveUidTxt = new TextField();
         this.saveUidTxt.x = 10;
         this.saveUidTxt.y = 220;
         this.saveUidTxt.width = 100;
         this.saveUidTxt.height = 20;
         this.saveUidTxt.type = TextFieldType.INPUT;
         this.saveUidTxt.border = true;
         this.saveUidTxt.borderColor = 0x666666;
         this.saveUidTxt.background = true;
         this.saveUidTxt.backgroundColor = 0x000000;
         this.saveUidTxt.textColor = 0xFFFFFF;
         bg.addChild(this.saveUidTxt);
         
         // 创建按钮
         this.inputBtn = this.createButton("导入存档", 10, 250);
         this.inputPCGBtn = this.createButton("导入PCG", 120, 250);
         this.inputBase64Btn = this.createButton("Base64", 230, 250);
         this.getByUidBtn = this.createButton("获取UID", 120, 220);
         this.setArenaGiftBtn = this.createButton("竞技场", 340, 250);
         
         bg.addChild(this.inputBtn);
         bg.addChild(this.inputPCGBtn);
         bg.addChild(this.inputBase64Btn);
         bg.addChild(this.getByUidBtn);
         bg.addChild(this.setArenaGiftBtn);
      }
      
      private function createButton(label:String, x:Number, y:Number) : SimpleButton
      {
         var upState:Sprite = new Sprite();
         upState.graphics.beginFill(0x666666, 1);
         upState.graphics.drawRect(0, 0, 100, 25);
         upState.graphics.endFill();
         
         var overState:Sprite = new Sprite();
         overState.graphics.beginFill(0x888888, 1);
         overState.graphics.drawRect(0, 0, 100, 25);
         overState.graphics.endFill();
         
         var downState:Sprite = new Sprite();
         downState.graphics.beginFill(0x444444, 1);
         downState.graphics.drawRect(0, 0, 100, 25);
         downState.graphics.endFill();
         
         var hitArea:Sprite = new Sprite();
         hitArea.graphics.beginFill(0x000000, 0);
         hitArea.graphics.drawRect(0, 0, 100, 25);
         hitArea.graphics.endFill();
         
         var btn:SimpleButton = new SimpleButton(upState, overState, downState, hitArea);
         btn.x = x;
         btn.y = y;

         return btn;
      }

      private function inputSave(e:MouseEvent) : void
      {
         var str0:String = this.txt.text;
         if(str0 != "")
         {
            Gaming.PG.inputSave(str0);
         }
      }

      private function inputPCGSave(e:MouseEvent) : void
      {
         var str0:String = this.txt.text;
         if(str0 != "")
         {
            Gaming.PCG.inputSave(str0);
         }
      }

      private function getSaveByUid(e:MouseEvent) : void
      {
         var uid0:String = this.saveUidTxt.text;
         Gaming.api.save.getSaveByUid(uid0,this.yes_getSaveByUid,this.no_getSaveByUid);
      }

      private function yes_getSaveByUid(obj0:Object) : void
      {
         this.txt.text = JSON2.encode(obj0);
      }

      private function no_getSaveByUid(obj0:Object) : void
      {
         this.txt.text = "获取失败：" + JSON2.encode(obj0);
      }

      private function setArenaGiftBtnClick(e:MouseEvent) : void
      {
         Gaming.PG.da.arena.setGift();
         addText("设置竞技场礼品完毕");
      }
   }
}
