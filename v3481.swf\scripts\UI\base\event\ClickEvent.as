package UI.base.event
{
   import flash.events.Event;
   
   public class ClickEvent extends Event
   {
      public static var ON_CLICK:String = "onClick";
      
      public static var ON_DOUBLE_CLICK:String = "onDoubleClick";
      
      public static var ON_DOWN:String = "onDown";
      
      public static var ON_UP:String = "onUp";
      
      public static var ON_OVER:String = "onOver";
      
      public static var ON_OUT:String = "onOut";
      
      public static var ON_MOVE:String = "onMove";
      
      public static var ON_CTRL_CLICK:String = "onCtrlClick";
      
      public static var ON_CTRL_OVER:String = "onCtrlOver";
      
      public static var ON_CTRL_OUT:String = "onCtrlOut";
      
      public static var ON_SHOW_PAGE:String = "onShowPage";
      
      public static var ON_FLESH:String = "onFlesh";
      
      public static var TOP_TITLE_CLICK:String = "topTitleClick";
      
      public var child:Object = null;
      
      public var childData:Object = null;
      
      public var index:int = -1;
      
      public var label:String = "";
      
      public var fatherUrl:String = "";
      
      public var father:Object = null;
      
      public var fatherData:Object = null;
      
      public function ClickEvent(_type:String = "onClick")
      {
         super(_type);
      }
      
      override public function clone() : Event
      {
         var newEvent:ClickEvent = new ClickEvent();
         newEvent.child = this.child;
         newEvent.fatherData = this.fatherData;
         newEvent.childData = this.childData;
         newEvent.index = this.index;
         newEvent.label = this.label;
         return newEvent;
      }
   }
}

