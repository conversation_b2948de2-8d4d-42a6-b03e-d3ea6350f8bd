package w_test
{
   import flash.display.Sprite;
   import flash.system.System;
   import flash.utils.getTimer;
   
   public class TestCtrl_UI extends TestCtrl_Normal
   {
      public function TestCtrl_UI()
      {
         super();
      }
      
      public function test() : void
      {
         var mc0:Sprite = null;
         var m0:Number = System.totalMemory * 9.54e-7;
         var tt0:Number = getTimer();
         for(var i:int = 0; i < 1000; i++)
         {
            mc0 = Gaming.swfLoaderManager.getResource("BasicUI","equipGrip");
         }
         trace("新建小图标1000ci耗时：" + (getTimer() - tt0));
         trace("内存增加：" + (System.totalMemory * 9.54e-7 - m0));
      }
   }
}

