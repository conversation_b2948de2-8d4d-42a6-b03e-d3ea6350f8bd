package w_test.count
{
   import UI.test.SaveTestBox;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll.pay.OnePlayerPayData;
   import flash.events.Event;
   
   public class TestCount_pay
   {
      private static var playerArr:Array = [];
      
      private static var playerObj:Object = {};
      
      private static var propsNumObj:Object = {};
      
      private static var payNum:int = 0;
      
      public function TestCount_pay()
      {
         super();
      }
      
      public static function start() : void
      {
         Gaming.textLoaderManager.addEventListener(Event.COMPLETE,complete);
         Gaming.textLoaderManager.addTextLoader("xml/3-6.xml","pay");
         Gaming.textLoaderManager.startLoad();
      }
      
      private static function complete(e:Event) : void
      {
         var n:* = undefined;
         var xx:int = 0;
         var d0:OnePlayerPayData = null;
         var xml0:XML = XML(Gaming.textLoaderManager.getResource("pay").data);
         var xl0:XMLList = xml0.player;
         for(n in xl0)
         {
            d0 = new OnePlayerPayData();
            d0.inData_byXML(xl0[n]);
            d0.index = n;
            playerArr.push(d0);
            playerObj[d0.uid] = d0;
            dealProps(d0);
         }
         xx = 0;
      }
      
      private static function dealProps(d0:OnePlayerPayData) : void
      {
         var n:* = undefined;
         var obj0:Object = d0.obj;
         for(n in obj0)
         {
            if(int(n) != 0)
            {
               if(propsNumObj.hasOwnProperty(n))
               {
                  propsNumObj[n] += obj0[n];
               }
               else
               {
                  propsNumObj[n] = obj0[n];
               }
               ++payNum;
            }
         }
      }
      
      private static function getGoodsDefineByPropsId(id0:String) : GoodsDefine
      {
         var d0:GoodsDefine = Gaming.defineGroup.goods.getByPropsId(id0);
         if(!d0)
         {
            d0 = Gaming.defineGroup.must.getGoodsDefineByPropsId(id0);
         }
         return d0;
      }
      
      private static function propsCount() : void
      {
         var n:* = undefined;
         var obj2:Object = propsNumObj;
         var obj0:Object = ClassProperty.copyObj(obj2);
         for(n in obj0)
         {
            obj0[n] /= payNum;
         }
         SaveTestBox.addText(showOnePropsCountObj(obj0));
         SaveTestBox.addText("\n");
         SaveTestBox.addText(showOnePropsCountObj(obj2));
      }
      
      private static function changeToCn(obj0:Object) : Object
      {
         var n:* = undefined;
         var d0:GoodsDefine = null;
         var obj2:Object = {};
         for(n in obj0)
         {
            d0 = getGoodsDefineByPropsId(n);
            if(Boolean(d0))
            {
               obj2[d0.cnName] = obj0[n];
            }
         }
         return obj2;
      }
      
      public static function showOnePropsCountObj(obj0:Object) : String
      {
         var n:* = undefined;
         var i:* = undefined;
         var num0:Number = NaN;
         var d0:GoodsDefine = null;
         var str0:String = "";
         var arr0:Array = [];
         var all0:Number = 0;
         for(n in obj0)
         {
            num0 = Number(obj0[n]);
            d0 = getGoodsDefineByPropsId(n);
            if(!d0)
            {
               arr0.push(n + ":未找到该商品");
            }
            else
            {
               arr0.push(d0.cnName + "\t\t\t" + ComMethod.toFixed(num0,5));
               all0 += d0.price * num0;
            }
         }
         arr0.sort(sort_OnePropsCountObj);
         for(i in arr0)
         {
            str0 += arr0[i] + "\n";
         }
         return str0 + ("\n【总和：" + ComMethod.toFixed(all0,4) + "】");
      }
      
      private static function sort_OnePropsCountObj(str0:String, str1:String) : int
      {
         var n0:Number = Number(str0.split(":")[1]);
         var n1:Number = Number(str1.split(":")[1]);
         if(n0 > n1)
         {
            return -1;
         }
         if(n0 < n1)
         {
            return 1;
         }
         return 0;
      }
      
      public static function getTop() : void
      {
         Gaming.uiGroup.connectUI.show("获取排行榜数据中……");
         Gaming.api.top.getRankListsData(887,1000,1,yes_getTop,no_getTop);
      }
      
      private static function yes_getTop(arr0:Array) : void
      {
         var n:* = undefined;
         var obj0:Object = null;
         var d0:OnePlayerPayData = null;
         Gaming.uiGroup.connectUI.hide();
         for(n in arr0)
         {
            obj0 = arr0[n];
            if(n > 10)
            {
               d0 = new OnePlayerPayData();
               d0.inData_obj(obj0);
               d0.index = n;
               playerArr.push(d0);
               playerObj[d0.uid] = d0;
               dealProps(d0);
            }
         }
         propsCount();
      }
      
      private static function no_getTop(str0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError(str0);
      }
   }
}

