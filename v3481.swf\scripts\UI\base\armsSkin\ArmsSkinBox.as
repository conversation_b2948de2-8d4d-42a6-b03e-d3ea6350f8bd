package UI.base.armsSkin
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.grid.NormalGrid;
   import dataAll._app.goods.GoodsData;
   import dataAll._player.PlayerData;
   import dataAll.arms.skin.ArmsSkinDefine;
   import dataAll.arms.skin.IO_FashionSkinData;
   import dataAll.arms.skin.IO_HaveSkinData;
   import dataAll.equip.define.EquipColor;
   import dataAll.items.define.IO_ItemsDefine;
   import dataAll.pro.ProType;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class ArmsSkinBox extends AutoNormalUI
   {
      private var titleTxt:TextField;
      
      private var closeBtn:NormalBtn;
      
      private var coverBtn:NormalBtn;
      
      private var gripBox:ItemsGripBox = new ItemsGripBox();
      
      private var gripTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var nowData:IO_HaveSkinData = null;
      
      private var nowGrip:NormalGrid;
      
      private var baseSkin:String = "";
      
      public function ArmsSkinBox()
      {
         super();
         mcTypeArr = ["btnSp","tag","txt"];
      }
      
      override protected function firstLoad() : void
      {
         setImgUrl("OtherUI/armsSkinBox");
         this.gripBox.setIconPro("OtherUI/armsSkinGrip");
         this.gripBox.arg.init(3,3,35,30);
         this.gripBox.evt.setWant(true,true);
         addChild(this.gripBox);
         NormalUICtrl.setTag(this.gripBox,this.gripTag);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.gripBox.addEventListener(ClickEvent.ON_OVER,this.gripOver);
         this.gripBox.addEventListener(ClickEvent.ON_OUT,Gaming.uiGroup.tipBox.hide);
         this.gripBox.pageBox.setToSmall();
         this.gripBox.setPagePos(this.pageTag);
         x = 70;
         y = 15;
         this.coverBtn.setName("覆盖时装图像");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function get PD() : PlayerData
      {
         return Gaming.PG.da;
      }
      
      private function isGaimgB() : Boolean
      {
         return Gaming.LG.isGaming();
      }
      
      public function outLoginEvent() : void
      {
         this.nowData = null;
         this.nowGrip = null;
      }
      
      public function showData(da0:IO_HaveSkinData, grip0:NormalGrid) : void
      {
         show();
         this.nowData = da0;
         this.nowGrip = grip0;
         this.fleshData();
      }
      
      override public function hide() : void
      {
         this.nowData = null;
         this.nowGrip = null;
         super.hide();
      }
      
      private function fleshData() : void
      {
         var defArr0:Array = null;
         var nowSkin0:String = null;
         var da0:IO_HaveSkinData = this.nowData;
         if(Boolean(da0))
         {
            this.titleTxt.htmlText = "为 " + da0.getCnName() + " 更换皮肤";
            this.baseSkin = da0.getBaseSkin();
            defArr0 = da0.getSkinDefArr();
            if(Boolean(defArr0))
            {
               this.gripBox.inData_byArr(defArr0,this.gripFun);
               nowSkin0 = da0.getNowSkin();
               this.gripBox.setChoose(nowSkin0);
            }
            else
            {
               this.gripBox.clearAllData();
            }
         }
         else
         {
            this.baseSkin = "";
            this.titleTxt.htmlText = "";
            this.gripBox.clearAllData();
         }
         this.fleshCoverBtn();
      }
      
      private function fleshCoverBtn() : void
      {
         var da0:IO_FashionSkinData = this.nowData as IO_FashionSkinData;
         if(Boolean(da0))
         {
            this.coverBtn.visible = true;
            this.coverBtn.isChosen = da0.coverFashionSkinB();
         }
         else
         {
            this.coverBtn.visible = false;
         }
      }
      
      private function gripFun(grip0:ItemsGrid, d0:ArmsSkinDefine) : void
      {
         var goodDa0:GoodsData = null;
         grip0.itemsData = d0;
         grip0.label = d0.name;
         grip0.setIconName(d0.getBigIconUrl());
         grip0.getIcon().rotation = d0.getBigIconRotation();
         var tip0:String = "";
         var authorStr0:String = "";
         var cn0:String = d0.cnName;
         var evoCn0:String = d0.getEvoCn();
         if(this.baseSkin == d0.name)
         {
            cn0 = "默认";
         }
         else if(cn0 == "" && evoCn0 != "")
         {
            cn0 = evoCn0;
         }
         grip0.setLevelText(cn0);
         var vip0:int = d0.vip;
         var p0:int = d0.p;
         var evo0:int = d0.evoLv;
         var stren0:int = d0.stren;
         var itemsD0:IO_ItemsDefine = d0.getMustItemsDefine();
         var vipB0:Boolean = this.PD.vip.def.getTrueLevel() >= vip0;
         var pB0:Boolean = this.PD.main.totalRecharged >= p0 * 10000;
         var evoB0:Boolean = this.nowData.getEvoLv() >= evo0;
         var colorB0:Boolean = EquipColor.moreColorPan(this.nowData.getColor(),d0.color);
         var normalB0:Boolean = evoB0 && colorB0;
         var strenB0:Boolean = this.nowData.getStrengthenLv() >= stren0;
         var itemsB0:Boolean = Boolean(itemsD0) && this.PD.findItemsDataByName(itemsD0.getName()).length > 0;
         var haveB0:Boolean = this.PD.main.haveArmsSkinB(d0.name);
         var activedB0:Boolean = false;
         var lockB0:Boolean = false;
         if(evo0 > 0)
         {
            tip0 += "\n进阶等级：<yellow " + evoCn0 + "/> " + ProType.booleanToUIRed(evoB0);
         }
         else if(d0.color != "")
         {
            tip0 += "\n品质成色：" + EquipColor.getColorCn(d0.color) + ProType.booleanToUIRed(colorB0);
         }
         if(vip0 > 0)
         {
            grip0.setNumText("VIP." + vip0);
            grip0.setSmallIcon("vip");
            tip0 += "\nVIP等级：<yellow " + vip0 + "级/> " + ProType.booleanToUIRed(vipB0);
            activedB0 = vipB0;
            lockB0 = !activedB0;
         }
         else if(p0 > 0)
         {
            grip0.setSmallIcon("p");
            grip0.setNumText("充" + d0.p + "千");
            tip0 += "\n累计充值：<yellow " + p0 + "万黄金/> " + ProType.booleanToUIRed(pB0);
            activedB0 = pB0;
            lockB0 = !activedB0;
         }
         else if(d0.goods != "")
         {
            goodDa0 = this.PD.goods.getData(d0.goods);
            grip0.setSmallIcon("money");
            grip0.setNumText(goodDa0.getPrice() + "");
            tip0 += "\n消耗黄金：<yellow " + goodDa0.getPrice() + "/> " + ProType.booleanToUIRed(haveB0);
            activedB0 = true;
            lockB0 = !haveB0;
         }
         else if(Boolean(itemsD0))
         {
            grip0.setSmallIcon("black");
            grip0.setNumText(itemsD0.getCnName() + "指定");
            tip0 += "\n拥有" + itemsD0.getTypeCn() + "：<yellow " + itemsD0.getCnName() + "/> " + ProType.booleanToUIRed(itemsB0);
            activedB0 = itemsB0;
            lockB0 = !activedB0;
         }
         else if(stren0 > 0)
         {
            grip0.setNumText("强化" + stren0 + "级");
            grip0.setSmallIcon("black");
            tip0 += "\n强化等级：<yellow " + stren0 + "级/> " + ProType.booleanToUIRed(strenB0);
            activedB0 = strenB0;
            lockB0 = !activedB0;
         }
         else if(d0.other != "")
         {
            grip0.setSmallIcon("other");
            grip0.setNumText("");
            tip0 += "\n<purple " + d0.other + "/> " + ProType.booleanToUIRed(haveB0);
            activedB0 = haveB0;
            lockB0 = !activedB0;
         }
         else if(d0.author != "")
         {
            grip0.setSmallIcon("player");
            grip0.setNumText("");
            if(d0.isPlayerOriginal())
            {
               authorStr0 = "\n\n该皮肤是玩家 <purple " + d0.author + "/> 提供的原创手绘。";
            }
            else
            {
               authorStr0 = "\n\n该皮肤由玩家 <purple " + d0.author + "/> 投稿。";
            }
            activedB0 = normalB0;
            lockB0 = !activedB0;
         }
         else
         {
            grip0.setNumText("");
            grip0.setSmallVisible(false);
            activedB0 = true;
            lockB0 = !activedB0;
         }
         if(d0.au != "")
         {
            grip0.setSmallIcon(d0.au);
         }
         grip0.activedAndEnabled = false;
         grip0.actived = activedB0 && normalB0;
         grip0.setLockVisible(lockB0 || !normalB0);
         if(tip0 != "")
         {
            tip0 = "<blue 解锁条件：/>" + tip0;
         }
         tip0 += authorStr0;
         if(d0.info != "")
         {
            tip0 += "\n\n<green 简介：/>" + d0.info;
         }
         if(d0.name == this.nowData.getNowSkin())
         {
            tip0 = "<b><orange 再次点击可恢复原始皮肤。/></b>\n" + tip0;
         }
         grip0.tipString = tip0;
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var d0:ArmsSkinDefine = null;
         var nowSkin0:String = null;
         var canB0:Boolean = false;
         var grip0:NormalGrid = e.child as NormalGrid;
         if(grip0.actived && Boolean(this.nowData))
         {
            d0 = e.childData as ArmsSkinDefine;
            nowSkin0 = this.nowData.getNowSkin();
            if(d0.name == nowSkin0)
            {
               this.nowData.setSkin("");
               this.nowGrip.setIconName(this.nowData.getIconImgUrl(this.nowGrip.iconMaxWidth,this.nowGrip.iconMaxHeight));
               this.fleshData();
            }
            else
            {
               canB0 = true;
               if(d0.goods != "" || d0.other != "")
               {
                  canB0 = this.PD.main.haveArmsSkinB(d0.name);
               }
               if(canB0)
               {
                  this.nowData.setSkin(d0.name);
                  this.nowGrip.setIconName(d0.getIconUrl());
                  this.fleshData();
               }
               else if(d0.goods != "")
               {
                  if(this.nowData.getEvoLv() >= d0.evoLv)
                  {
                     Gaming.uiGroup.shopUI.buyDefineName(d0.goods,this.yesBuy);
                  }
                  else
                  {
                     UIOrder.alertError("武器进阶等级不足，无法购买。");
                  }
               }
            }
         }
      }
      
      private function yesBuy(da0:GoodsData) : void
      {
         this.fleshData();
      }
      
      private function gripOver(e:ClickEvent) : void
      {
         var grip0:NormalGrid = e.child as NormalGrid;
         var d0:ArmsSkinDefine = grip0.itemsData as ArmsSkinDefine;
         Gaming.uiGroup.tipBox.showText(grip0.tipString);
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var da0:IO_FashionSkinData = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0 == this.closeBtn)
         {
            this.hide();
         }
         else if(btn0 == this.coverBtn)
         {
            da0 = this.nowData as IO_FashionSkinData;
            if(Boolean(da0))
            {
               da0.setCoverFashionSkinB(!da0.coverFashionSkinB());
               this.fleshCoverBtn();
            }
         }
      }
   }
}

