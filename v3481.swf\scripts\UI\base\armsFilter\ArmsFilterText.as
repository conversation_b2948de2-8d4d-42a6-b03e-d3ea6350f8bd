package UI.base.armsFilter
{
   import UI.UIOrder;
   import UI.base.font.FontDeal;
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._app.edit._def.EditProDefine;
   import dataAll.arms.save.ArmsFilterSave;
   import dataAll.arms.save.FilterSave;
   import dataAll.skill.define.SkillDescrip;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class ArmsFilterText extends EventDispatcher
   {
      private var txt:TextField;
      
      private var save:ArmsFilterSave = null;
      
      private var tempPro:String = "";
      
      private var moveLink:String = "";
      
      public function ArmsFilterText()
      {
         super();
      }
      
      public function init(txt0:TextField) : void
      {
         this.txt = txt0;
         FontDeal.dealLine(this.txt);
         this.txt.styleSheet = TextMethod.getLinkCss();
         this.txt.addEventListener(TextEvent.LINK,this.linkClick);
         this.txt.addEventListener(MouseEvent.MOUSE_MOVE,this.txtMove);
         this.txt.htmlText = "";
      }
      
      public function fleshData() : void
      {
         if(Boolean(this.save))
         {
            this.showSave(this.save);
         }
         else
         {
            this.clearSave();
         }
      }
      
      public function showSave(s0:ArmsFilterSave) : void
      {
         this.save = s0;
         var str0:String = FilterSave.getTextAll(s0);
         this.txt.htmlText = FontDeal.getDealLeadingStr(this.txt,str0);
      }
      
      public function clearSave() : void
      {
         this.save = null;
         this.txt.htmlText = "";
      }
      
      public function outLoginEvent() : void
      {
         this.save = null;
      }
      
      private function changeEvent() : void
      {
         this.fleshData();
         dispatchEvent(new Event(Event.CHANGE));
      }
      
      private function linkClick(e:TextEvent) : void
      {
         var fleshB0:Boolean = false;
         var strArr0:Array = null;
         var pro0:String = null;
         var value0:String = null;
         if(Boolean(this.save))
         {
            fleshB0 = this.save.linkClick(e.text);
            if(fleshB0)
            {
               this.changeEvent();
            }
            else
            {
               strArr0 = e.text.split(":");
               pro0 = strArr0[0];
               value0 = strArr0[1];
               if(value0 == "min")
               {
                  this.minClick(pro0);
               }
               else if(value0 == "max")
               {
                  this.maxClick(pro0);
               }
            }
         }
      }
      
      private function txtMove(e:MouseEvent) : void
      {
         var format:TextFormat = null;
         var link0:String = "";
         var charIndex:Number = this.txt.getCharIndexAtPoint(e.localX,e.localY);
         if(charIndex >= 0)
         {
            format = this.txt.getTextFormat(charIndex,charIndex + 1);
            if(format.url != "")
            {
               link0 = format.url;
            }
         }
         if(link0 != this.moveLink)
         {
            this.txtLinkOver(link0);
            this.moveLink = link0;
         }
      }
      
      private function txtLinkOver(link0:String) : void
      {
         var sarr0:Array = null;
         var skillName0:String = null;
         var tip0:String = "";
         if(link0 != "")
         {
            sarr0 = link0.split(":");
            if(sarr0.length >= 3)
            {
               skillName0 = sarr0[2];
               tip0 = SkillDescrip.getSkillGather(skillName0);
            }
         }
         UIOrder.showTip(tip0);
      }
      
      private function minClick(pro0:String) : void
      {
         var tip0:String = null;
         var d0:EditProDefine = this.save.getProDef(pro0);
         var min0:Number = this.save.getDefineProMin(pro0);
         var now0:Number = this.save.getProMin(pro0);
         var max0:Number = this.save.getProMax(pro0);
         if(min0 != FilterSave.NO_NUM)
         {
            this.tempPro = pro0;
            tip0 = d0.cnName + " 的最小值（" + NumberMethod.toFixed(min0,d0.fixed) + "~" + NumberMethod.toFixed(max0,d0.fixed) + "）";
            Gaming.uiGroup.alertBox.showNumChoose(tip0,NumberMethod.toFixed(now0,d0.fixed),max0,min0,NumberMethod.getFixedPoint(d0.fixed),this.yes_minClick,"yesAndNo",null,null,false,d0.fixed);
         }
      }
      
      private function yes_minClick(v0:Number) : void
      {
         this.save.setProMin(this.tempPro,v0);
         this.changeEvent();
      }
      
      private function maxClick(pro0:String) : void
      {
         var tip0:String = null;
         var d0:EditProDefine = this.save.getProDef(pro0);
         var min0:Number = this.save.getProMin(pro0);
         var now0:Number = this.save.getProMax(pro0);
         var max0:Number = this.save.getDefineProMax(pro0);
         if(min0 != FilterSave.NO_NUM)
         {
            this.tempPro = pro0;
            tip0 = d0.cnName + " 的最大值（" + NumberMethod.toFixed(min0,d0.fixed) + "~" + NumberMethod.toFixed(max0,d0.fixed) + "）";
            Gaming.uiGroup.alertBox.showNumChoose(tip0,NumberMethod.toFixed(now0,d0.fixed),max0,min0,NumberMethod.getFixedPoint(d0.fixed),this.yes_maxClick,"yesAndNo",null,null,false,d0.fixed);
         }
      }
      
      private function yes_maxClick(v0:Number) : void
      {
         this.save.setProMax(this.tempPro,v0);
         this.changeEvent();
      }
   }
}

