package UI.gameWorld.vehicle
{
   public class GWVehicleAgent
   {
      public var visible:Boolean = false;
      
      public var iconLabel:String = "";
      
      public var lifePer:Number = 0;
      
      public var timePer:Number = 0;
      
      public var timeStr:String = "";
      
      public var cdPer:Number = 0;
      
      public var keyName:String = "";
      
      public var numStr:String = "";
      
      public function GWVehicleAgent()
      {
         super();
      }
   }
}

