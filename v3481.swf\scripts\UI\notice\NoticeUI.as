package UI.notice
{
   import UI.UIShow;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base._hide.HideNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import UI.base.scroll.NormalScrollBar;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.StringMethod;
   import dataAll._app.task.TaskData;
   import dataAll._data.ConstantDefine;
   import dataAll._player.PlayerData;
   import dataAll._player.supple.PlayerDataSupple;
   import dataAll.arms.creator.ArmsDataCreator;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.gift.secGene.FirstGeneCode;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import flash.text.TextField;
   
   public class NoticeUI extends HideNormalUI
   {
      private var closeBtn:SimpleButton;
      
      private var titleTxt:TextField;
      
      private var linkTxt:TextField;
      
      private var contextSp:Sprite;
      
      private var giftBtnSp:MovieClip;
      
      private var giftBtn:NormalBtn = new NormalBtn();
      
      private var oneBtnSp:MovieClip;
      
      private var oneBtn:NormalBtn = new NormalBtn();
      
      private var twoBtnSp:MovieClip;
      
      private var twoBtn:NormalBtn = new NormalBtn();
      
      private var scrollBar:NormalScrollBar;
      
      public function NoticeUI()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["closeBtn","titleTxt","contextSp","giftBtnSp","oneBtnSp","twoBtnSp"];
         super.setImg(img0);
         FontDeal.dealOne(this.titleTxt);
         this.linkTxt = this.contextSp["linkTxt"];
         this.giftBtnSp = this.contextSp["giftBtnSp"];
         this.oneBtnSp = this.contextSp["oneBtnSp"];
         this.twoBtnSp = this.contextSp["twoBtnSp"];
         this.scrollBar = new NormalScrollBar(this.contextSp,img0["maskTargetSp"],img0["scrollBarSp"],img0["scrollLineSp"],5,false,true,true);
         this.scrollBar.speed = 18;
         this.scrollBar.refresh();
         this.contextSp.addChild(this.giftBtn);
         this.giftBtn.setImg(this.giftBtnSp);
         this.giftBtn.setName("领取补偿礼包");
         this.giftBtn.addEventListener(MouseEvent.CLICK,this.giftBtnClick);
         this.giftBtn.activedAndEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.giftBtn);
         this.giftBtn.visible = false;
         this.contextSp.addChild(this.oneBtn);
         this.oneBtn.setImg(this.oneBtnSp);
         this.oneBtn.setName("修复武器伤害");
         this.oneBtn.addEventListener(MouseEvent.CLICK,this.oneBtnClick);
         this.contextSp.addChild(this.twoBtn);
         this.twoBtn.setImg(this.twoBtnSp);
         this.twoBtn.setName("补偿礼包");
         this.twoBtn.addEventListener(MouseEvent.CLICK,this.twoBtnClick);
         this.twoBtn.visible = false;
         this.oneBtn.visible = false;
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         this.linkTxt.addEventListener(TextEvent.LINK,this.linkClick);
         this.linkTxt.styleSheet = ComMethod.getLinkCss("#00FFFF","#FFFFFF");
         this.linkTxt.htmlText = ComMethod.link("详情点击这里>>","bbs");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.titleTxt.text = "《" + ConstantDefine.cnName + "》" + ConstantDefine.getVersionText() + "公告";
         if(Gaming.uiGroup.loginUI.visible)
         {
            this.y = 125;
         }
         else
         {
            this.y = 80;
         }
         this.fleshGiftBtn();
      }
      
      public function outLoginEvent() : void
      {
         this.fleshGiftBtn();
      }
      
      public function fleshGiftBtn() : void
      {
         if(Boolean(this.PD))
         {
            if(this.PD.main.save.arms283 > 0)
            {
               this.oneBtn.actived = false;
            }
            else
            {
               this.oneBtn.actived = true;
            }
         }
         else
         {
            this.oneBtn.actived = true;
         }
      }
      
      private function giftBtnClick(e:MouseEvent) : void
      {
         var bb0:Boolean = false;
         var num0:int = 0;
         var g0:GiftAddDefineGroup = null;
         if(this.giftBtn.actived)
         {
            bb0 = this.loginPan("登录后才能领取此礼包。");
            if(bb0)
            {
               if(this.PD.city.save.giftB)
               {
                  Gaming.uiGroup.alertBox.showError("该补偿礼包已领取。");
               }
               else if(this.PD.main.save.isZuobiB)
               {
                  Gaming.uiGroup.alertBox.showError("存档有问题，无法领取礼包。");
               }
               else
               {
                  num0 = 50;
                  if(PlayerDataSupple.getArmsGap() > 1500 || PlayerDataSupple.getEquipGap() > 1500)
                  {
                     num0 = 10;
                  }
                  g0 = new GiftAddDefineGroup();
                  g0.addGiftByStr("things;allBlackEquipCash;" + num0);
                  g0.addGiftByStr("things;allBlackCash;" + num0);
                  g0.addGiftByStr("things;normalChest;10");
                  g0.addGiftByStr("things;dragonChest;10");
                  g0.addGiftByStr("things;magicChest;10");
                  bb0 = GiftAddit.addAndAutoBagSpacePan(g0,"获得礼包：\n" + g0.getDescription());
                  if(bb0)
                  {
                     this.PD.city.save.giftB = true;
                     this.fleshGiftBtn();
                  }
               }
            }
         }
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         hide();
      }
      
      private function linkClick(e:TextEvent) : void
      {
         if(e.text == "arena")
         {
            UIShow.showByLabel("arenaSeason");
         }
         else if(e.text == "union")
         {
            navigateToURL(new URLRequest("https://my.4399.com/forums-thread-tagid-81949-id-48804401.html"),"blank");
         }
         else if(e.text == "bbs")
         {
            navigateToURL(new URLRequest(ConstantDefine.versionUrl),"blank");
         }
         else if(e.text != "one")
         {
            if(e.text == "two")
            {
            }
         }
      }
      
      private function oneBtnClick(e:MouseEvent) : void
      {
         var taskDa0:TaskData = null;
         var tip0:String = null;
         var cnArr0:Array = null;
         var bb0:Boolean = this.loginPan("请先登录游戏账号");
         if(bb0)
         {
            taskDa0 = this.PD.task.getTaskDataByName("madSame");
            tip0 = "";
            if(Boolean(taskDa0))
            {
               cnArr0 = ArmsDataCreator.dealAllArms();
               if(cnArr0.length == 0)
               {
                  tip0 = "你的武器伤害没问题，无需修复。\n如果人物总战力降低，请查找其他原因。";
               }
               else
               {
                  tip0 = "已修复武器：" + StringMethod.concatStringArr(cnArr0,99);
               }
            }
            else
            {
               tip0 = "你还没打过外传最后一个任务，无需修复。\n如果人物总战力降低，请查找其他原因。";
            }
            ++this.PD.main.save.arms283;
            Gaming.uiGroup.alertBox.showInfo(tip0);
            this.fleshGiftBtn();
         }
      }
      
      private function testOneYes(code0:String) : void
      {
         var uid0:String = Gaming.PG.loginData.uid;
         var lv0:int = FirstGeneCode.testGetGiftLv(uid0,code0);
         Gaming.uiGroup.alertBox.showSuccess("兑换码等级：" + lv0);
      }
      
      private function twoBtnClick(e:MouseEvent) : void
      {
      }
      
      private function get PD() : PlayerData
      {
         return Gaming.PG.da;
      }
      
      private function loginPan(tip0:String) : Boolean
      {
         if(Boolean(Gaming.PG.da))
         {
            return true;
         }
         Gaming.uiGroup.alertBox.showError(tip0);
         return false;
      }
   }
}

