package UI.pet
{
   import UI.UIShow;
   import UI.api.shop.ShopBuyObject;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.base.AppNormalUI;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.label.LabelBox;
   import UI.pet.book.PetBookBoard;
   import UI.pet.dispatch.PetDispatchBoard;
   import UI.pet.evo.PetEvoBoard;
   import UI.pet.grow.PetGrowBoard;
   import UI.pet.info.PetInfoBoard;
   import UI.pet.move.PetMoveBoard;
   import UI.pet.skill.PetSkillBoard;
   import UI.pet.strengthen.PetStrengthenBoard;
   import UI.pet.transfer.PetTransferBoard;
   import UI.pet.upgrade.PetUpgradeBoard;
   import com.sounto.net.SWFLoaderManager;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll.pet.PetData;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class PetUI extends AppNormalUI
   {
      public static var nowGrip:ItemsGrid;
      
      private static var yes_fun:Function = null;
      
      private static var no_fun:Function = null;
      
      private static var addLoadSwfEventB:Boolean = false;
      
      public var labelBox:LabelBox;
      
      private var labelTag:Sprite = null;
      
      private var barTag:Sprite = null;
      
      private var pageTag:Sprite = null;
      
      private var tipBtn:SimpleButton;
      
      private var coverSp:Sprite;
      
      private var coverTxt:TextField;
      
      private var listTxt:TextField;
      
      private var closeBtn:SimpleButton;
      
      private var addBagBtnSp:MovieClip;
      
      private var addBagBtn:NormalBtn;
      
      private var bookBtnSp:MovieClip;
      
      private var bookBtn:NormalBtn;
      
      private var dispatchBtnSp:MovieClip;
      
      private var dispatchBtn:NormalBtn;
      
      public var itemsBox:ItemsGripBox;
      
      public var bookBoard:PetBookBoard;
      
      public var dispatchBoard:PetDispatchBoard;
      
      public var infoBoard:PetInfoBoard;
      
      public var skillBoard:PetSkillBoard;
      
      public var strengthenBoard:PetStrengthenBoard;
      
      public var upgradeBoard:PetUpgradeBoard;
      
      public var moveBoard:PetMoveBoard;
      
      public var growBoard:PetGrowBoard;
      
      public var transferBoard:PetTransferBoard;
      
      public var evoBoard:PetEvoBoard;
      
      private var labelArr:Array;
      
      private var boxArr:Array;
      
      private var afterChildLabel:String = "";
      
      public function PetUI()
      {
         var label0:* = null;
         var box0:NormalUI = null;
         this.labelBox = new LabelBox();
         this.addBagBtn = new NormalBtn();
         this.bookBtn = new NormalBtn();
         this.dispatchBtn = new NormalBtn();
         this.itemsBox = new ItemsGripBox();
         this.bookBoard = new PetBookBoard();
         this.dispatchBoard = new PetDispatchBoard();
         this.infoBoard = new PetInfoBoard();
         this.skillBoard = new PetSkillBoard();
         this.strengthenBoard = new PetStrengthenBoard();
         this.upgradeBoard = new PetUpgradeBoard();
         this.moveBoard = new PetMoveBoard();
         this.growBoard = new PetGrowBoard();
         this.transferBoard = new PetTransferBoard();
         this.evoBoard = new PetEvoBoard();
         this.labelArr = ["info","skill","strengthen","upgrade","move","grow","evo"];
         this.boxArr = [];
         super();
         UICn = "宠物";
         for each(label0 in this.labelArr)
         {
            if(this.hasOwnProperty(label0 + "Board"))
            {
               box0 = this[label0 + "Board"];
               this.boxArr.push(box0);
               box0.UILabel = label0;
            }
         }
      }
      
      public static function getNowData() : PetData
      {
         if(Boolean(nowGrip))
         {
            return nowGrip.itemsData as PetData;
         }
         return null;
      }
      
      public static function loadPetSwf(yesFun0:Function = null, noFun0:Function = null) : void
      {
         yes_fun = yesFun0;
         no_fun = noFun0;
         var arr0:Array = Gaming.PG.da.pet.getSWFLoaderUrlArr();
         var swfM0:SWFLoaderManager = Gaming.swfLoaderManager;
         if(swfM0.panNoLoad_byURLArr(arr0) == 0)
         {
            yes_LoadPetSwf();
         }
         else
         {
            Gaming.uiGroup.connectUI.show("加载文件中……");
            if(!addLoadSwfEventB)
            {
               addLoadSwfEventB = true;
               swfM0.addEventListener(Event.COMPLETE,yes_LoadPetSwf);
               swfM0.addEventListener(IOErrorEvent.IO_ERROR,no_LoadPetSwf);
            }
            swfM0.addSWFList_byURLArr(arr0);
            swfM0.startLoad();
         }
      }
      
      private static function removeLoaderEvent() : void
      {
         var swfM0:SWFLoaderManager = null;
         if(addLoadSwfEventB)
         {
            swfM0 = Gaming.swfLoaderManager;
            swfM0.removeEventListener(Event.COMPLETE,yes_LoadPetSwf);
            addLoadSwfEventB = false;
            swfM0.removeEventListener(IOErrorEvent.IO_ERROR,no_LoadPetSwf);
         }
         yes_fun = null;
         no_fun = null;
      }
      
      private static function yes_LoadPetSwf(e:Event = null) : void
      {
         Gaming.uiGroup.connectUI.hide();
         var fun0:Function = yes_fun;
         yes_fun = null;
         if(fun0 is Function)
         {
            fun0();
         }
         removeLoaderEvent();
      }
      
      private static function no_LoadPetSwf(e:IOErrorEvent = null) : void
      {
         Gaming.uiGroup.connectUI.hide();
         var fun0:Function = no_fun;
         yes_fun = null;
         if(fun0 is Function)
         {
            fun0();
         }
         removeLoaderEvent();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var label0:String = null;
         var normalUI0:NormalUI = null;
         elementNameArr = ["dispatchBtnSp","addBagBtnSp","listTxt","bookBtnSp","labelTag","barTag","pageTag","coverSp","tipBtn","closeBtn"];
         super.setImg(img0);
         FontDeal.dealOne(this.listTxt);
         addChild(this.bookBtn);
         this.bookBtn.setImg(this.bookBtnSp);
         this.bookBtn.addEventListener(MouseEvent.CLICK,this.bookBtnClick);
         addChild(this.dispatchBtn);
         this.dispatchBtn.setImg(this.dispatchBtnSp);
         this.dispatchBtn.addEventListener(MouseEvent.CLICK,this.dispatchBtnClick);
         addChild(this.addBagBtn);
         this.addBagBtn.setImg(this.addBagBtnSp);
         this.addBagBtn.setName("扩容");
         this.addBagBtn.addEventListener(MouseEvent.CLICK,this.addBagBtnClick);
         this.labelBox.arg.init(9,1,-6,0);
         addChild(this.labelBox);
         this.labelBox.inData("smallFontLabelBtn",this.labelArr,["信息","技能","强化","资质升级","资质替换","资质培养","进化"]);
         this.labelBox.setChoose_byIndex(0);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.labelBox.x = this.labelTag.x;
         this.labelBox.y = this.labelTag.y;
         addChild(this.itemsBox);
         this.itemsBox.setIconPro("PetUI/barIcon",50,50);
         this.itemsBox.arg.init(1,6,0,0);
         this.itemsBox.evt.setWantEvent(true,false,false,true,true);
         this.itemsBox.x = this.barTag.x;
         this.itemsBox.y = this.barTag.y;
         this.itemsBox.pageBox.setToNormalBtn();
         this.itemsBox.pageBox.setXY_bySp(this.pageTag,this.itemsBox);
         this.itemsBox.addEventListener(ClickEvent.ON_CLICK,this.barClick);
         var boardArr0:Array = this.labelArr.concat("book","dispatch");
         for each(label0 in boardArr0)
         {
            label0 += "Board";
            if(this.hasOwnProperty(label0))
            {
               normalUI0 = this[label0];
               addChild(normalUI0);
               normalUI0.visible = false;
               normalUI0.setImg(Gaming.swfLoaderManager.getResource("PetUI",label0));
            }
         }
         addChild(this.coverSp);
         this.coverTxt = this.coverSp["txt"];
         this.setCoverText("");
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         addChild(this.bookBoard);
         addChild(this.dispatchBoard);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         loadPetSwf(this.afterLoad,this.hide);
      }
      
      public function showAndLabel(label0:String) : void
      {
         this.afterChildLabel = label0;
         UIShow.showByLabel("pet");
      }
      
      private function afterLoad() : void
      {
         var label0:String = null;
         this.fleshData();
         if(this.afterChildLabel != "")
         {
            label0 = this.afterChildLabel;
            this.afterChildLabel = "";
            if(label0 == "dispatch")
            {
               this.dispatchBoard.show();
            }
            else
            {
               this.showBox(label0);
            }
         }
      }
      
      override public function hide() : void
      {
         super.hide();
         this.infoBoard.hide();
         this.bookBoard.hide();
         this.dispatchBoard.hide();
         this.upgradeBoard.hide();
      }
      
      public function outLoginEvent() : void
      {
         nowGrip = null;
         this.moveBoard.outLoginEvent();
         this.infoBoard.outLoginEvent();
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      public function showBox(label0:String) : void
      {
         var n:* = undefined;
         var ui0:NormalUI = null;
         if(label0 == "")
         {
            label0 = "info";
         }
         this.labelBox.setChoose(label0);
         for(n in this.boxArr)
         {
            this.boxArr[n].hide();
         }
         ui0 = this[label0 + "Board"];
         if(Boolean(ui0))
         {
            ui0.show();
         }
      }
      
      public function setCoverText(str0:String) : void
      {
         this.coverSp.visible = str0 != "";
         this.coverTxt.htmlText = str0;
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.hide();
      }
      
      private function bookBtnClick(e:MouseEvent) : void
      {
         this.bookBoard.show();
         this.dispatchBoard.hide();
      }
      
      private function dispatchBtnClick(e:MouseEvent) : void
      {
         this.bookBoard.hide();
         this.dispatchBoard.show();
      }
      
      private function addBagBtnClick(e:MouseEvent) : void
      {
         var da0:GoodsData = this.getAddBagGoodsData();
         Gaming.uiGroup.alertBox.shop.showCheck(da0,this.yes_addBagBtnClick);
      }
      
      private function yes_addBagBtnClick() : void
      {
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         var price0:Number = da0.getPrice();
         var shopObj0:ShopBuyObject = da0.getShopObj();
         Gaming.uiGroup.connectUI.show();
         Gaming.api.shop.buyPropNd(shopObj0,this.do_addBagBtnClick);
      }
      
      private function do_addBagBtnClick() : void
      {
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         Gaming.PG.da.pet.addBagNum(da0.nowNum);
         Gaming.soundGroup.playSound("uiSound","changeLabel");
         this.fleshBar();
      }
      
      private function getAddBagGoodsData() : GoodsData
      {
         var da0:GoodsData = new GoodsData();
         var d0:GoodsDefine = Gaming.defineGroup.goods.getDefine("addPetBag");
         da0.def = d0;
         da0.playerData = Gaming.PG.da;
         da0.showTextType = "addPetBag";
         return da0;
      }
      
      public function fleshData() : void
      {
         this.fleshBar();
         this.chooseGrip(nowGrip);
      }
      
      public function fleshBar() : void
      {
         var arr0:Array = Gaming.PG.da.pet.arr;
         this.itemsBox.inData_byArr(arr0,"inData_pet");
         this.listTxt.htmlText = "尸宠背包" + Gaming.PG.da.pet.getBagSizeText();
      }
      
      public function refleshBar() : void
      {
         this.itemsBox.refleshnDataNow();
      }
      
      public function chooseData(da0:PetData) : void
      {
         nowGrip = this.itemsBox.getBtnByItemsData(da0) as ItemsGrid;
         this.chooseGrip(nowGrip);
      }
      
      private function chooseGrip(grip0:ItemsGrid) : void
      {
         nowGrip = null;
         if(this.itemsBox.gripArr.indexOf(grip0) == -1)
         {
            grip0 = null;
         }
         this.setCoverText(this.itemsBox.gripArr.length == 0 ? "你还没有尸宠。\n消灭指定僵尸可掉落基因体，基因体可直接被培育成为尸宠。" : "请在左边选择尸宠。");
         if(Boolean(grip0))
         {
            if(Gaming.PG.da.pet.arr.indexOf(grip0.itemsData) >= 0)
            {
               this.setCoverText("");
               nowGrip = grip0;
               this.itemsBox.setChoose_byIndex(nowGrip.index);
            }
         }
         this.showBox(this.labelBox.nowLabel);
      }
      
      private function barClick(e:ClickEvent) : void
      {
         this.chooseGrip(e.child as ItemsGrid);
      }
      
      private function tipOver(e:MouseEvent) : void
      {
         var str0:String = "";
         if(this.infoBoard.visible)
         {
            str0 += "1、消灭40级以上指定僵尸 才能掉落基因体。";
            str0 += "\n2、掉落的基因体等级为僵尸等级-15。";
            str0 += "\n3、尸宠升级速度为人物的5倍。";
            str0 += "\n4、尸宠等级不会超过人物等级。";
            str0 += "\n5、通过尸宠图鉴可以查看基因体掉落概率等信息。";
         }
         else if(!this.skillBoard.visible)
         {
            if(this.strengthenBoard.visible)
            {
               str0 += "1、强化的最高等级为当前尸宠开放的最大等级。";
               str0 += "\n2、玩家可以强化尸宠的3种基本属性。";
            }
            else if(this.upgradeBoard.visible)
            {
               str0 += "资质升级每5级为一个间隔。";
            }
            else if(this.moveBoard.visible)
            {
               str0 += "用于替换的基因体的品质必须好过或者与当前尸宠品质相同。";
            }
            else if(this.growBoard.visible)
            {
               str0 += "1、人物等级达到50级解锁尸宠培养系统。";
               str0 += "\n2、培养资质有一定几率成功，每次成功培养都会随机提高宠物资质百分比（原先尸宠没有的资质将不会得到提高），并消耗培养材料“血石”。";
               str0 += "\n3、每条资质培养都有上限，到达上限后，该条资质在培养中将不再提升。系统还有一个总百分比上限，即为所有资质培养百分比之和的上限，超过这个上限，尸宠资质将无法继续培养，当前上限值开放到80%。";
               str0 += "\n4、培养到一定阶段，尸宠会发生变异，获得特殊称号。";
               str0 += "\n5、每日签到和50级以上地图都能获得一定数量的“血石”，每天消灭怪物掉落血石超过15个之后，掉落将降至原先的10%。";
            }
            else if(this.evoBoard.visible)
            {
               str0 += "进化后的尸宠将拥有全新的外观，攻击力和防御力也将得到提升，同时还会额外随机一项技能。";
            }
         }
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.setText(str0);
            Gaming.uiGroup.tipBox.textTip.show();
            Gaming.uiGroup.tipBox.followMouseB = true;
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      private function tipOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
   }
}

