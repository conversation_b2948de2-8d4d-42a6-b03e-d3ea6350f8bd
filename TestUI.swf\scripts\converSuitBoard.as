package
{
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol455")]
   public dynamic class converSuitBoard extends MovieClip
   {
      public var beltGrip:equipGrip;
      
      public var nextBtnSp:MovieClip;
      
      public var btnSp:MovieClip;
      
      public var mustTag:MovieClip;
      
      public var pantsGrip:equipGrip;
      
      public var heroImgTag:MovieClip;
      
      public var coatGrip:equipGrip;
      
      public var headGrip:equipGrip;
      
      public var closeBtn:SimpleButton;
      
      public var suitTxt:TextField;
      
      public function converSuitBoard()
      {
         super();
      }
   }
}

