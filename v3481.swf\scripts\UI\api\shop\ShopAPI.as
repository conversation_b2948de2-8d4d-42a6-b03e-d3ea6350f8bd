package UI.api.shop
{
   import UI.UIOrder;
   import dataAll._app.login.LoginData4399;
   import dataAll._player.base.PlayerMainData;
   import flash.display.Stage;
   import unit4399.events.PayEvent;
   import unit4399.events.ShopEvent;
   
   public class ShopAPI
   {
      private var payMoneyVar:PayMoneyVar = PayMoneyVar.getInstance();
      
      private var serviceHold:*;
      
      private var yesFun:Function = null;
      
      private var noFun:Function = null;
      
      private var balance_yesFun:Function = null;
      
      private var balance_noFun:Function = null;
      
      private var totalRecharged_yesFun:Function = null;
      
      private var totalRecharged_noFun:Function = null;
      
      private var payMoney_yesFun:Function = null;
      
      private var payMoney_noFun:Function = null;
      
      private var nowObj:ShopBuyObject = null;
      
      public var test_balance:Number = 0;
      
      public var test_totalRecharged:Number = 0;
      
      public function ShopAPI()
      {
         super();
      }
      
      private function get mainData() : PlayerMainData
      {
         return Gaming.PG.da.main;
      }
      
      public function addListener(stage0:Stage) : *
      {
         this.serviceHold = Gaming.serviceHold;
         stage0.addEventListener("usePayApi",this.onPayEventHandler);
         stage0.addEventListener(PayEvent.GET_MONEY,this.onPayEventHandler);
         stage0.addEventListener(PayEvent.PAY_MONEY,this.onPayEventHandler);
         stage0.addEventListener(PayEvent.PAIED_MONEY,this.onPayEventHandler);
         stage0.addEventListener(PayEvent.RECHARGED_MONEY,this.onPayEventHandler);
         stage0.addEventListener(PayEvent.PAY_ERROR,this.onPayEventHandler);
         stage0.addEventListener(ShopEvent.SHOP_ERROR_ND,this.onShopEventHandler);
         stage0.addEventListener(ShopEvent.SHOP_BUY_ND,this.onShopEventHandler);
         stage0.addEventListener(ShopEvent.SHOP_GET_LIST,this.onShopEventHandler);
      }
      
      public function outLoginEvent() : void
      {
      }
      
      private function getDefaultObj() : ShopBuyObject
      {
         var obj0:ShopBuyObject = new ShopBuyObject();
         var data0:LoginData4399 = Gaming.PG.loginData;
         if(Boolean(data0.save))
         {
            obj0.idx = data0.save.index;
         }
         return obj0;
      }
      
      public function buyPropNd(obj0:ShopBuyObject, _yesFun:Function = null, _noFun:Function = null) : *
      {
         this.yesFun = _yesFun;
         this.noFun = _noFun;
         if(!(this.noFun is Function))
         {
            this.noFun = this.no_buyPropNd;
         }
         obj0.idx = Gaming.PG.loginData.save.index;
         this.nowObj = obj0;
         var loginB0:Boolean = Gaming.PG.loginData.isLoginByJS();
         if(!loginB0)
         {
            if(this.noFun is Function)
            {
               this.noFun("您的账号已经退出登录，无法进行此操作。");
            }
         }
         else
         {
            Gaming.api.save.getStoreState(this.affter_buyPropNd);
         }
      }
      
      private function affter_buyPropNd(state0:int) : *
      {
         if(state0 == 1 || state0 == -2)
         {
            if(Gaming.api.save.isLocal())
            {
               this.mainData.money -= this.nowObj.count * this.nowObj.price;
               if(this.yesFun is Function)
               {
                  this.yesFun();
               }
               Gaming.api.rzxt.buy(this.nowObj,this.mainData.money);
               Gaming.PG.payShopBuyObject(this.nowObj);
               Gaming.uiGroup.fleshAfterBuy();
               UIOrder.save(true,false,false,null,null,true);
            }
            else
            {
               this.serviceHold.buyPropNd(this.nowObj);
            }
         }
      }
      
      private function no_buyPropNd(str0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError(str0);
      }
      
      private function onShopEventHandler(evt:ShopEvent) : void
      {
         var obj0:Object = evt.data;
         switch(evt.type)
         {
            case ShopEvent.SHOP_ERROR_ND:
               if(this.noFun is Function)
               {
                  this.noFun("eId:" + obj0.eId + "  message:" + obj0.msg);
               }
               break;
            case ShopEvent.SHOP_BUY_ND:
               this.mainData.money = obj0.balance;
               if(this.yesFun is Function)
               {
                  this.yesFun();
               }
               Gaming.api.rzxt.buy(this.nowObj,obj0.balance);
               Gaming.PG.payShopBuyObject(this.nowObj);
               Gaming.uiGroup.fleshAfterBuy();
               UIOrder.save(true,false,false,null,null,true);
               break;
            case ShopEvent.SHOP_GET_LIST:
         }
      }
      
      public function getBalance(_yesFun:Function = null, _noFun:Function = null) : void
      {
         this.balance_yesFun = _yesFun;
         this.balance_noFun = _noFun;
         if(!Gaming.api.save.isLocal())
         {
            this.serviceHold.getBalance();
         }
         else
         {
            this.mainData.money = this.test_balance;
            if(this.balance_yesFun is Function)
            {
               this.balance_yesFun(this.test_balance);
            }
         }
      }
      
      public function getTotalRecharged(_yesFun:Function = null, _noFun:Function = null) : void
      {
         this.totalRecharged_yesFun = _yesFun;
         this.totalRecharged_noFun = _noFun;
         if(!Gaming.api.save.isLocal())
         {
            this.serviceHold.getTotalRechargedFun(this.getDefaultObj());
         }
         else
         {
            this.mainData.totalRecharged = this.test_totalRecharged;
            if(this.totalRecharged_yesFun is Function)
            {
               this.totalRecharged_yesFun(this.test_totalRecharged);
            }
         }
      }
      
      public function payMoney(v0:Number = 10000) : void
      {
         this.payMoneyVar.money = v0;
         if(!Gaming.api.save.isLocal())
         {
            this.serviceHold.payMoney_As3(this.payMoneyVar);
         }
         else
         {
            this.test_balance += v0;
            this.test_totalRecharged += v0;
            Gaming.api.rzxt.goldChange(v0,this.test_balance);
         }
      }
      
      private function onPayEventHandler(e:PayEvent) : void
      {
         var getGold0:int = 0;
         var c0:int = 0;
         switch(e.type)
         {
            case "usePayApi":
               break;
            case "getMoney":
               if(e.data !== null && !(e.data is Boolean))
               {
                  getGold0 = int(e.data.balance);
                  c0 = getGold0 - this.mainData.money;
                  Gaming.api.rzxt.goldChange(c0,getGold0);
                  this.mainData.money = e.data.balance;
                  if(this.balance_yesFun is Function)
                  {
                     this.balance_yesFun(e.data.balance);
                  }
                  break;
               }
               if(this.balance_noFun is Function)
               {
                  this.balance_noFun("获取游戏币余额错误！");
               }
               break;
            case "payMoney":
               break;
            case "paiedMoney":
               if(e.data !== null && !(e.data is Boolean))
               {
               }
               break;
            case "rechargedMoney":
               if(e.data !== null && !(e.data is Boolean))
               {
                  this.mainData.totalRecharged = e.data.balance;
                  if(this.totalRecharged_yesFun is Function)
                  {
                     this.totalRecharged_yesFun(e.data.balance);
                  }
                  break;
               }
               if(this.totalRecharged_noFun is Function)
               {
                  this.totalRecharged_noFun("获取累积充值的游戏币错误！");
               }
               break;
            case "payError":
               if(e.data == null)
               {
                  break;
               }
               if(this.noFun is Function)
               {
                  this.noFun("使用支付接口其他错误----->" + e.data.info);
               }
               if(this.totalRecharged_noFun is Function)
               {
                  this.totalRecharged_noFun("使用支付接口其他错误----->" + e.data.info);
               }
               if(this.balance_noFun is Function)
               {
                  this.balance_noFun("使用支付接口其他错误----->" + e.data.info);
               }
               break;
         }
      }
   }
}

