package UI.gameWorld
{
   import UI.base.NormalUI;
   import UI.base.font.FontDeal;
   import UI.gameWorld.agent.GWArmsAgent;
   import com.sounto.utils.MovieClipMethod;
   import dataAll.body.attack.ElementHurt;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class GameWorldArmsBox extends NormalUI
   {
      private var capacityTxt:TextField = null;
      
      private var chargerTxt:TextField = null;
      
      private var armsNameTxt:TextField = null;
      
      private var ax:Number = 0;
      
      private var eleMc:MovieClip = null;
      
      private var _name:String = "0";
      
      private var _capacity:String = "";
      
      private var _charger:String = "";
      
      public function GameWorldArmsBox()
      {
         super();
         repeatSetImgB = false;
         elementNameArr = ["capacityTxt","chargerTxt","armsNameTxt","eleMc"];
         this.mouseChildren = false;
         this.mouseEnabled = false;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         if(Boolean(this.armsNameTxt))
         {
            FontDeal.dealOne(this.armsNameTxt);
            this.ax = this.armsNameTxt.x;
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function inAgent(a0:GWArmsAgent) : void
      {
         var cn0:String = a0.cn;
         if(a0.ele == "")
         {
            MovieClipMethod.mcGoto(this.eleMc,"other");
            if(Boolean(this.armsNameTxt))
            {
               this.armsNameTxt.x = this.ax;
            }
         }
         else
         {
            MovieClipMethod.mcGoto(this.eleMc,ElementHurt.getBreakShell(a0.ele));
            if(Boolean(this.armsNameTxt))
            {
               this.armsNameTxt.x = this.ax - this.eleMc.width;
            }
         }
         if(cn0 != this._name && Boolean(this.armsNameTxt))
         {
            this.armsNameTxt.text = cn0;
            this._name = cn0;
         }
         if(a0.capacity != this._capacity)
         {
            this.capacityTxt.text = a0.capacity;
            this._capacity = a0.capacity;
         }
         if(a0.charger != this._charger)
         {
            this.chargerTxt.text = a0.charger;
            this._charger = a0.charger;
         }
      }
   }
}

