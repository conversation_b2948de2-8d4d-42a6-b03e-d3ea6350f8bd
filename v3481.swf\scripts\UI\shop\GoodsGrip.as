package UI.shop
{
   import UI.bag.ItemsGrid;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._app.goods.define.PriceType;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class GoodsGrip extends ItemsGrid
   {
      protected var discountTxt:TextField;
      
      protected var lineSp:Sprite;
      
      public function GoodsGrip()
      {
         super();
      }
      
      override public function setImg(img0:MovieClip) : void
      {
         super.setImg(img0);
         this.discountTxt = img0["discountTxt"];
         this.lineSp = img0["lineSp"];
      }
      
      public function fleshGoodsPrice() : void
      {
         var da0:GoodsData = itemsData as GoodsData;
         var price0:Number = da0.getPrice();
         var type0:String = da0.def.priceType;
         var fatherPriceType0:String = type0;
         if(fatherPriceType0 == PriceType.PUMPKIN)
         {
            fatherPriceType0 = PriceType.pumpkinIconMcLabel;
         }
         var now0:Number = Gaming.PG.da.getCrrency(type0);
         setPriceText(ComMethod.color(price0 + "",PriceType.getColor(type0)));
         if(da0.isOverAllBuyB())
         {
            setShopBtnBackMc(4);
            activedAndEnabled = false;
            actived = false;
         }
         else
         {
            activedAndEnabled = true;
            actived = true;
            if(now0 >= price0)
            {
               setShopBtnBackMc(1);
            }
            else if(type0 == "score")
            {
               setShopBtnBackMc(3);
            }
            else
            {
               setShopBtnBackMc(2);
            }
         }
         setSmallIcon(fatherPriceType0);
         this.discountFlesh(da0);
      }
      
      private function setDiscount(da0:GoodsData) : void
      {
         var d0:GoodsDefine = da0.def;
         var price0:Number = da0.getPrice(true);
         var truePrice0:Number = da0.getPrice();
         if(d0.isDiscountB())
         {
            this.setDiscountVisible(true);
            this.discountTxt.text = price0 + "";
         }
         else
         {
            this.setDiscountVisible(false);
         }
         setPriceText(ComMethod.color(truePrice0 + "",PriceType.getColor(d0.priceType)));
      }
      
      private function setDiscountVisible(bb0:Boolean) : void
      {
         if(Boolean(this.discountTxt))
         {
            this.discountTxt.visible = bb0;
            this.lineSp.visible = bb0;
         }
      }
      
      private function discountFlesh(da0:GoodsData) : void
      {
         var bb0:Boolean = false;
         var d0:GoodsDefine = da0.def;
         this.setDiscountVisible(false);
         if(d0.father == "normal")
         {
            bb0 = true;
            if(bb0)
            {
               this.setDiscount(da0);
            }
         }
         if(Boolean(this.discountTxt))
         {
            if(this.discountTxt.visible)
            {
               setNewMc("discount");
            }
         }
      }
   }
}

