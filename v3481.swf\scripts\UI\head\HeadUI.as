package UI.head
{
   import UI.base.AppNormalUI;
   import UI.base.NormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.label.LabelBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.head.HeadData;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class HeadUI extends AppNormalUI
   {
      private var labelArr:Array;
      
      private var boxArr:Array;
      
      private var closeBtn:SimpleButton;
      
      private var tipBtn:SimpleButton;
      
      private var honorTxt:TextField;
      
      private var labelTag:Sprite;
      
      private var labelBox:LabelBox;
      
      public var haveBoard:HeadHaveBoard;
      
      public var noBoard:HeadNoBoard;
      
      public var honorBoard:HeadHonorBoard;
      
      public var infoBox:HeadInfoBox;
      
      public function HeadUI()
      {
         var label0:* = null;
         var box0:NormalUI = null;
         this.labelArr = ["no","have","honor"];
         this.boxArr = [];
         this.labelBox = new LabelBox();
         this.haveBoard = new HeadHaveBoard();
         this.noBoard = new HeadNoBoard();
         this.honorBoard = new HeadHonorBoard();
         this.infoBox = new HeadInfoBox();
         super();
         UICn = "称号";
         for each(label0 in this.labelArr)
         {
            if(this.hasOwnProperty(label0 + "Board"))
            {
               box0 = this[label0 + "Board"];
               this.boxArr.push(box0);
               box0.UILabel = label0;
            }
         }
         this.infoBox.headUI = this;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["labelTag","honorTxt","tipBtn","closeBtn"];
         super.setImg(img0);
         FontDeal.dealOne(this.honorTxt);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
         this.haveBoard.setImg(Gaming.swfLoaderManager.getResource("HeadUI","noBoard"));
         addChild(this.haveBoard);
         this.haveBoard.infoBox = this.infoBox;
         this.noBoard.setImg(Gaming.swfLoaderManager.getResource("HeadUI","noBoard"));
         addChild(this.noBoard);
         this.noBoard.infoBox = this.infoBox;
         this.honorBoard.setImg(Gaming.swfLoaderManager.getResource("HeadUI","honorBoard"));
         addChild(this.honorBoard);
         this.honorBoard.infoBox = this.infoBox;
         this.infoBox.setImg(Gaming.swfLoaderManager.getResource("HeadUI","infoBox"));
         addChild(this.infoBox);
         this.labelBox.arg.init(6,1,-6,0);
         addChild(this.labelBox);
         this.labelBox.inData("HeadUI/midLabelBtn",this.labelArr,["未获得称号","已获得称号","荣誉属性"]);
         this.labelBox.setChoose_byIndex(0);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.labelBox.x = this.labelTag.x;
         this.labelBox.y = this.labelTag.y;
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.showBox(this.labelArr[0]);
         this.haveBoard.playAll();
         this.noBoard.playAll();
         this.infoBox.playAll();
      }
      
      public function fleshData() : void
      {
         this.showBox(this.labelBox.nowLabel);
      }
      
      override public function hide() : void
      {
         super.hide();
         this.haveBoard.stopAll();
         this.noBoard.stopAll();
         this.infoBox.stopAll();
      }
      
      public function outLoginEvent() : void
      {
         this.infoBox.outLoginEvent();
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      private function fleshHonor() : void
      {
         var da0:HeadData = Gaming.PG.da.head;
         this.honorTxt.htmlText = "荣誉值 " + ComMethod.color(da0.getNowHonor() + "","#00FF00");
      }
      
      public function showBox(label0:String) : void
      {
         var n:* = undefined;
         var ui0:NormalUI = null;
         if(label0 == "")
         {
            label0 = this.labelArr[0];
         }
         this.labelBox.setChoose(label0);
         for(n in this.boxArr)
         {
            this.boxArr[n].hide();
         }
         ui0 = this[label0 + "Board"];
         if(Boolean(ui0))
         {
            ui0.show();
         }
         this.fleshHonor();
      }
      
      private function tipOver(e:MouseEvent) : void
      {
         var str0:String = "";
         str0 += "1、获得方式：完成指定任务可获得指定称号。";
         str0 += "\n2、称号属性：每个称号拥有若干项属性，使用该称号后才能获得相应属性的加成。";
         str0 += "\n3、称号寿命：有些称号有寿命限制，在寿命结束后将消失，重新回归“未获得称号”的列表中。";
         str0 += "\n4、荣誉属性：每个称号都有一个荣誉值，玩家的荣誉值为所有已获得称号荣誉值之和。玩家荣誉值到达一定值，将打开相应的属性加成。";
         str0 += "\n5、显示：玩家称号将显示在背包界面以及排行榜界面中。";
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.setText(str0);
            Gaming.uiGroup.tipBox.textTip.show();
            Gaming.uiGroup.tipBox.followMouseB = true;
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      private function tipOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.hide();
      }
   }
}

