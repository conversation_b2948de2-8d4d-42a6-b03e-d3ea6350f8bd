package UI.bag
{
   import UI.UIShow;
   import UI.base.alert.AlertBox;
   import UI.base.event.ClickEvent;
   import UI.base.grid.NormalGrid;
   import UI.base.tip.TipBox;
   import com.sounto.cf.NiuBiCF;
   import dataAll._player.more.MorePlayerData;
   import dataAll.arms.ArmsDataGroup;
   import dataAll.items.ItemsDataGroup;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.must.define.MustDefineGroup;
   import dataAll.must.define.MustSimpleThingsDefine;
   import dataAll.skill.HeroSkillDataGroup;
   
   public class ItemsGripUnlockCtrl
   {
      private static var nowDefine:MustDefine;
      
      private static const CF:NiuBiCF = new NiuBiCF();
      
      private static var dataGroup:ItemsDataGroup = null;
      
      private static var type:String = "";
      
      private static var nowSite:int = 0;
      
      private static var tipB:Boolean = false;
      
      public function ItemsGripUnlockCtrl()
      {
         super();
      }
      
      public static function get unlockNum() : Number
      {
         return CF.getAttribute("unlockNum");
      }
      
      public static function set unlockNum(v0:Number) : void
      {
         CF.setAttribute("unlockNum",v0);
      }
      
      public static function addEvent_byItemsGripBox(box0:ItemsGripBox) : void
      {
         box0.addEventListener(ClickEvent.ON_CLICK,gripClick);
         box0.addEventListener(ClickEvent.ON_OVER,gripOver);
         box0.addEventListener(ClickEvent.ON_OUT,gripOut);
      }
      
      private static function gripClick(e:ClickEvent) : void
      {
         var grip0:NormalGrid = e.child as NormalGrid;
         if(grip0.state != "lock")
         {
            return;
         }
         var dg0:ItemsDataGroup = e.fatherData as ItemsDataGroup;
         dataGroup = dg0;
         var site0:int = e.index;
         nowSite = site0;
         if(dg0.placeType == ItemsDataGroup.PLACE_WEAR)
         {
            type = dg0.dataType;
         }
         else
         {
            type = dg0.placeType;
         }
         normalTip();
      }
      
      private static function gripOver(e:ClickEvent) : void
      {
         var d0:MustDefine = null;
         var tipBox0:TipBox = null;
         var str0:String = null;
         var grip0:NormalGrid = e.child as NormalGrid;
         if(grip0.state != "lock")
         {
            return;
         }
         var MG0:MustDefineGroup = Gaming.defineGroup.must;
         var dg0:ItemsDataGroup = e.fatherData as ItemsDataGroup;
         var site0:int = e.index;
         var unlockNum0:int = site0 + 1 - dg0.getSaveGroup().lockLen;
         var firstStr0:String = "解锁所需：";
         if(dg0.placeType == ItemsDataGroup.PLACE_BAG)
         {
            d0 = MG0.getBagNum(unlockNum0);
            firstStr0 = "解锁" + unlockNum0 + "个位置需要：";
         }
         else if(dg0.placeType == ItemsDataGroup.PLACE_HOUSE)
         {
            d0 = MG0.getHouseNum(unlockNum0);
            firstStr0 = "解锁" + unlockNum0 + "个位置需要：";
         }
         else if(dg0.dataType == ItemsDataGroup.TYPE_ARMS)
         {
            d0 = MG0.getArms(site0 + 1,Gaming.PG.DATA is MorePlayerData);
         }
         else if(dg0.dataType == ItemsDataGroup.TYPE_SKILL)
         {
            d0 = MG0.getSkill(site0 + 1,Gaming.PG.DATA.heroData.def.sex);
         }
         else if(dg0.dataType == ItemsDataGroup.TYPE_PARTS)
         {
            d0 = MG0.getParts(site0 + 1);
         }
         if(Boolean(d0))
         {
            tipBox0 = Gaming.uiGroup.tipBox;
            str0 = firstStr0 + "\n" + d0.getText();
            tipBox0.textTip.showFollowText(str0);
         }
      }
      
      private static function gripOut(e:ClickEvent = null) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private static function normalTip() : void
      {
         var d0:MustDefine = null;
         var mustB0:Boolean = false;
         var str0:String = "";
         var site0:int = nowSite;
         var tipB0:Boolean = tipB;
         var MG0:MustDefineGroup = Gaming.defineGroup.must;
         var tipBox0:TipBox = Gaming.uiGroup.tipBox;
         var alertBox0:AlertBox = Gaming.uiGroup.alertBox;
         var unlockNum0:int = 0;
         var tempNum0:int = site0 + 1 - dataGroup.getSaveGroup().lockLen;
         var error0:String = "";
         var firstStr0:String = "解锁所需：";
         if(type == "arms")
         {
            d0 = MG0.getArms(site0 + 1,Gaming.PG.DATA is MorePlayerData);
         }
         else if(type == "skill")
         {
            d0 = MG0.getSkill(site0 + 1,Gaming.PG.DATA.heroData.def.sex);
         }
         else if(type == "parts")
         {
            d0 = MG0.getParts(site0 + 1);
         }
         else if(type == "bag")
         {
            unlockNum0 = tempNum0;
            d0 = MG0.getBagNum(unlockNum0);
         }
         else if(type == "house")
         {
            unlockNum0 = tempNum0;
            d0 = MG0.getHouseNum(unlockNum0);
         }
         if(Boolean(d0))
         {
            if(unlockNum0 > 0)
            {
               firstStr0 = "解锁" + unlockNum0 + "个位置需要：";
               if(dataGroup.getSaveGroup().getUnlockSurplus() < unlockNum0)
               {
                  error0 = "可解锁位置不足" + unlockNum0 + "，无法解锁。";
               }
            }
            unlockNum = unlockNum0;
            if(tipB0)
            {
               str0 += firstStr0 + "\n" + d0.getText();
               tipBox0.textTip.setText(str0);
               tipBox0.textTip.show();
               tipBox0.followMouseB = true;
            }
            else
            {
               if(d0.text != "")
               {
                  return;
               }
               if(error0 == "")
               {
                  str0 += firstStr0 + "\n" + d0.getHorText();
                  alertBox0.showNormal(str0,"yesAndNo",yes_unlock);
                  mustB0 = d0.panCondition();
                  alertBox0.setYesActived(mustB0);
                  if(type == "bag")
                  {
                     if(mustB0 == false)
                     {
                        alertBox0.setBtnText("购买位卡","取消");
                        alertBox0.setYesActived(true);
                     }
                  }
               }
               else
               {
                  alertBox0.showError(error0);
               }
            }
         }
         nowDefine = d0;
      }
      
      private static function yes_unlock() : void
      {
         var md0:MustSimpleThingsDefine = null;
         if(!nowDefine)
         {
            INIT.showErrorMust("找不到定义MustDefine");
         }
         if(nowDefine.panCondition())
         {
            PlayerMustCtrl.deductMust(nowDefine,affter_unlock);
         }
         else if(type == "bag")
         {
            md0 = nowDefine.getFirstThingsDef();
            if(Boolean(md0))
            {
               Gaming.uiGroup.shopUI.buyDefineName(md0.name,null,md0.num);
            }
         }
      }
      
      private static function affter_unlock() : void
      {
         var armsDg0:ArmsDataGroup = null;
         var skillDg0:HeroSkillDataGroup = null;
         if(type == "bag")
         {
            dataGroup.getSaveGroup().unlockNextSiteNum(unlockNum);
            Gaming.uiGroup.bagUI.fleshAllBox();
         }
         else if(type == "house")
         {
            dataGroup.getSaveGroup().unlockNextSiteNum(unlockNum);
            Gaming.uiGroup.houseUI.fleshData();
         }
         else if(type == "arms")
         {
            armsDg0 = dataGroup as ArmsDataGroup;
            armsDg0.saveGroup.unlockSite(nowSite);
            Gaming.uiGroup.wearUI.fleshAllBox();
         }
         else if(type == "parts")
         {
            dataGroup.getSaveGroup().unlockSite(nowSite);
            Gaming.uiGroup.partsUI.assemblyBox.fleshData();
         }
         else if(type == "skill")
         {
            skillDg0 = dataGroup as HeroSkillDataGroup;
            skillDg0.saveGroup.unlockSite(nowSite);
            Gaming.uiGroup.skillUI.wearBox.fleshData();
         }
         UIShow.flesh_coinChange();
         Gaming.soundGroup.playSound("uiSound","changeLabel");
      }
   }
}

