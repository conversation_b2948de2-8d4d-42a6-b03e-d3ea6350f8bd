package UI.base.must
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.font.FontDeal;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.must.define.MustDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class NormalMustBox extends NormalUI
   {
      private var thingsBox:ItemsGripBox = new ItemsGripBox();
      
      private var lvTitleTxt:TextField;
      
      private var lvTxt:TextField;
      
      private var coinTxt:TextField;
      
      private var coinNameTxt:TextField;
      
      private var lvHook:MovieClip;
      
      private var thingsHook:MovieClip;
      
      private var coinHook:MovieClip;
      
      private var thingsGripTag:Sprite;
      
      private var coinVisible:Boolean = true;
      
      private var meMoreDef:Object;
      
      public function NormalMustBox()
      {
         super();
         addChild(this.thingsBox);
      }
      
      public function setNormalImg() : void
      {
         this.setImg(Gaming.swfLoaderManager.getResource("SkillUI","mustBox"));
      }
      
      public function setLongImg() : void
      {
         this.setImg(Gaming.swfLoaderManager.getResource("VehicleUI","mustBox"));
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["lvTitleTxt","thingsGripTag","lvTxt","coinTxt","lvHook","coinNameTxt","thingsHook","coinHook"];
         super.setImg(img0);
         this.setLvHook(false);
         this.setThingsHook(false);
         this.setCoinHook(false);
         if(Boolean(this.lvTitleTxt))
         {
            FontDeal.dealOne(this.lvTitleTxt);
         }
         if(Boolean(this.lvTxt))
         {
            FontDeal.dealOne(this.lvTxt);
         }
         if(Boolean(this.coinTxt))
         {
            FontDeal.dealOne(this.coinTxt);
         }
         if(Boolean(this.coinNameTxt))
         {
            FontDeal.dealOne(this.coinNameTxt);
         }
         this.thingsBox.imgType = "equipGrip";
         this.thingsBox.arg.init(6,1,2,0);
         this.thingsBox.evt.setWantEvent(true,false,false,true,true);
         NormalUICtrl.setTag(this.thingsBox,this.thingsGripTag);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.thingsBox,true);
         addChild(this.thingsHook);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function setMeMoreDef(d0:Object) : void
      {
         this.meMoreDef = d0;
      }
      
      public function inData(d0:MustDefine, body_lv0:int = 0, lvTitleStr0:String = "", thingsGripMidB0:Boolean = true) : Boolean
      {
         var len0:int = 0;
         this.setShowState(true);
         var lv0:int = d0.lv;
         if(lv0 == 0)
         {
            lv0 = 1;
         }
         var coin0:Number = d0.coin;
         var money0:Number = d0.money;
         if(lvTitleStr0 == "")
         {
            this.setLvTitleTxt("所需人物等级");
         }
         else
         {
            this.setLvTitleTxt("所需" + lvTitleStr0 + "等级");
         }
         if(body_lv0 == 0)
         {
            body_lv0 = Gaming.PG.DATA.level;
         }
         var lvB0:Boolean = lv0 <= body_lv0;
         this.setLvTxt(lv0 + "");
         this.setLvHook(lvB0);
         var nowCoin0:Number = Gaming.PG.save.main.coin;
         if(money0 > 0)
         {
            nowCoin0 = Gaming.PG.da.main.money;
            coin0 = money0;
            this.setCoinNameTxt("所需黄金","#FFCC00");
         }
         else
         {
            this.setCoinNameTxt("所需银币");
         }
         var coinB0:Boolean = nowCoin0 >= coin0;
         this.setCoinTxt(ComMethod.numberToSmall(coin0));
         this.setCoinHook(coinB0);
         var thingsB0:Boolean = this.thingsBox.inMustDefine(d0,this.meMoreDef);
         var gripNum0:int = int(this.thingsBox.gripArr.length);
         if(thingsGripMidB0)
         {
            len0 = 56;
            this.thingsBox.x = this.thingsGripTag.x - (gripNum0 * len0 + (gripNum0 - 1) * 2) / 2 + len0 / 2;
         }
         this.setThingsHook(thingsB0);
         return lvB0 && thingsB0 && coinB0;
      }
      
      public function setLvTitleTxt(str0:String) : void
      {
         if(Boolean(this.lvTitleTxt))
         {
            this.lvTitleTxt.text = str0;
         }
      }
      
      public function setLvTxt(str0:String) : void
      {
         if(Boolean(this.lvTxt))
         {
            this.lvTxt.text = str0;
         }
      }
      
      public function setLvHook(bb0:Boolean) : void
      {
         if(Boolean(this.lvHook))
         {
            this.lvHook.gotoAndStop(bb0 ? 1 : 2);
         }
      }
      
      public function setCoinHook(bb0:Boolean) : void
      {
         if(Boolean(this.coinHook))
         {
            this.coinHook.gotoAndStop(bb0 ? 1 : 2);
         }
      }
      
      public function setThingsHook(bb0:Boolean) : void
      {
         if(Boolean(this.thingsHook))
         {
            this.thingsHook.gotoAndStop(bb0 ? 1 : 2);
         }
      }
      
      public function setCoinNameTxt(str0:String, color0:String = "#CCCCCC") : void
      {
         if(Boolean(this.coinNameTxt))
         {
            this.coinNameTxt.htmlText = ComMethod.color(str0,color0);
         }
      }
      
      public function setCoinTxt(str0:String) : void
      {
         if(Boolean(this.coinTxt))
         {
            this.coinTxt.htmlText = str0;
         }
      }
      
      public function getThingsBox() : ItemsGripBox
      {
         return this.thingsBox;
      }
      
      public function setCoinVisible(bb0:Boolean) : void
      {
         this.coinVisible = bb0;
         if(Boolean(this.coinTxt))
         {
            this.coinTxt.visible = bb0;
         }
         if(Boolean(this.coinNameTxt))
         {
            this.coinNameTxt.visible = bb0;
         }
         if(Boolean(this.coinHook))
         {
            this.coinHook.visible = bb0;
         }
      }
      
      public function setShowState(bb0:Boolean) : void
      {
         if(bb0)
         {
            alpha = 1;
            this.thingsBox.visible = true;
         }
         else
         {
            alpha = 0.5;
            this.setLvTxt("0");
            this.setCoinHook("0");
            this.thingsBox.visible = false;
         }
         if(Boolean(this.lvHook))
         {
            this.lvHook.visible = bb0;
         }
         if(Boolean(this.thingsHook))
         {
            this.thingsHook.visible = bb0;
         }
         if(Boolean(this.coinHook))
         {
            this.coinHook.visible = bb0 && this.coinVisible;
         }
      }
   }
}

