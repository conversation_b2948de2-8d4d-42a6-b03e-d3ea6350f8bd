package UI.pay
{
   import UI.UIShow;
   import com.adobe.serialization.json.JSON2;
   import flash.events.MouseEvent;
   import flash.external.ExternalInterface;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import utils.BrowserInfo;
   
   public class PayCtrl
   {
      private static var airB:Boolean = false;
      
      public function PayCtrl()
      {
         super();
      }
      
      public static function staticInit() : void
      {
         BrowserInfo.getBrowserInfo();
         var barr:Array = "".split("|");
         if(barr[barr.length - 1] == "4399.bqtj.air")
         {
            airB = true;
         }
      }
      
      private static function airPay() : void
      {
         var param:Object = {};
         param.pay_union = 10029;
         param.username = Gaming.api.save.getUserNameByCookie();
         param.role = Gaming.PG.getNickName();
         param.pay_money = 50;
         param.is_wpay = 2;
         param.random_num = Math.ceil(Math.random() * 10000000);
         openPay(JSON2.encode(param));
      }
      
      private static function openUrl(url:String) : void
      {
         ExternalInterface.call("openUrl",url);
      }
      
      private static function openPay(url:String) : void
      {
         ExternalInterface.call("openPay",url);
      }
      
      private static function goBack() : void
      {
         ExternalInterface.call("goBack");
      }
      
      public static function gotoPay(e:MouseEvent = null) : void
      {
         Gaming.uiGroup.alertBox.showNormal("充值完毕后，请点击“确定”来刷新黄金数值。","yesAndNo",yes_gotoPay,no_gotoPay);
         Gaming.uiGroup.alertBox.noBtn.setName("遇到问题");
         if(airB)
         {
            airPay();
         }
         else if(Gaming.isLocal())
         {
            Gaming.api.shop.payMoney();
         }
         else
         {
            Gaming.api.shop.payMoney(100);
         }
      }
      
      private static function yes_gotoPay() : void
      {
         getBalance();
      }
      
      private static function no_gotoPay() : void
      {
         getBalance();
         navigateToURL(new URLRequest("https://my.4399.com/forums/thread-30307652"),"blank");
      }
      
      public static function getBalance() : void
      {
         Gaming.uiGroup.connectUI.show("获取黄金余额中……");
         Gaming.api.shop.getBalance(yes_getBalance,no_getBalance);
      }
      
      private static function no_getBalance(str0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showNormal("服务器连接失败！无法获取黄金余额！\n是否重新连接服务器？","yesAndNo",getBalance,getTotalRecharged,"no");
      }
      
      private static function yes_getBalance(v0:Number) : void
      {
         Gaming.uiGroup.connectUI.hide();
         UIShow.flesh_coinChange();
         getTotalRecharged();
      }
      
      private static function getTotalRecharged() : void
      {
         Gaming.uiGroup.connectUI.show("获取累计充值数量中……");
         Gaming.api.shop.getTotalRecharged(yes_getTotalRecharged,no_getTotalRecharged);
      }
      
      private static function no_getTotalRecharged(str0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showNormal("服务器连接失败！无法获取累计充值数量！\n是否重新连接服务器？","yesAndNo",getTotalRecharged,null,"no");
      }
      
      private static function yes_getTotalRecharged(v0:Number) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.PG.da.vip.fleshByTotalRecharged(v0);
         if(UIShow.nowApp == Gaming.uiGroup.vipUI)
         {
            Gaming.uiGroup.vipUI.fleshData();
         }
         else
         {
            UIShow.reShowNowApp();
         }
      }
   }
}

