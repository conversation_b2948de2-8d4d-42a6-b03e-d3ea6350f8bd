package UI.vehicle
{
   import UI.UIShow;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.grid.NormalGridIcon;
   import UI.base.must.NormalMustBox;
   import dataAll.equip.vehicle.VehicleData;
   import dataAll.equip.vehicle.VehicleDataCreator;
   import dataAll.equip.vehicle.VehicleDefine;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class VehicleComposeBoard extends NormalUI
   {
      private var gripTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var imgTag:Sprite;
      
      private var titleTxt:TextField;
      
      private var proTxt:TextField;
      
      private var valueTxt:TextField;
      
      private var mustTag:Sprite;
      
      private var btnSp:MovieClip;
      
      private var gotoShopBtnSp:MovieClip;
      
      private var gotoArenaBtnSp:MovieClip;
      
      private var itemsBox:ItemsGripBox = new ItemsGripBox();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var gotoShopBtn:NormalBtn = new NormalBtn();
      
      private var gotoArenaBtn:NormalBtn = new NormalBtn();
      
      private var coverSp:Sprite;
      
      private var imgIcon:NormalGridIcon = new NormalGridIcon();
      
      private var nowDefine:VehicleDefine;
      
      private var nowData:VehicleData;
      
      public var vehicleUI:VehicleUI;
      
      public function VehicleComposeBoard()
      {
         super();
         this.mouseEnabled = false;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["coverSp","gripTag","pageTag","imgTag","titleTxt","proTxt","valueTxt","mustTag","btnSp","gotoShopBtnSp","gotoArenaBtnSp"];
         super.setImg(img0);
         FontDeal.dealOne(this.titleTxt);
         FontDeal.dealLine(this.proTxt);
         FontDeal.dealLine(this.valueTxt);
         img0["imgBtn"].visible = false;
         this.imgTag.scaleX = -1;
         this.imgTag.mouseChildren = false;
         this.imgTag.mouseEnabled = false;
         addChild(this.itemsBox);
         this.itemsBox.setIconPro("equipGrip",50,50);
         this.itemsBox.arg.init(2,5,8,8);
         this.itemsBox.evt.setWantEvent(true,false,false,true,true);
         this.itemsBox.x = this.gripTag.x;
         this.itemsBox.y = this.gripTag.y;
         this.itemsBox.pageBox.setToNormalBtn();
         this.itemsBox.pageBox.setXY_bySp(this.pageTag,this.itemsBox);
         this.itemsBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.itemsBox);
         addChild(this.mustBox);
         this.mustBox.setNormalImg();
         this.mustBox.x = this.mustTag.x;
         this.mustBox.y = this.mustTag.y;
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.btn.setName("合成");
         addChild(this.gotoShopBtn);
         this.gotoShopBtn.setImg(this.gotoShopBtnSp);
         this.gotoShopBtn.addEventListener(MouseEvent.CLICK,this.gotoShopBtnClick);
         this.gotoShopBtn.setName("前往商店购买");
         addChild(this.gotoArenaBtn);
         this.gotoArenaBtn.setImg(this.gotoArenaBtnSp);
         this.gotoArenaBtn.addEventListener(MouseEvent.CLICK,this.gotoArenaBtnClick);
         this.gotoArenaBtn.setName("前往竞技场兑换");
         this.mustBox.visible = false;
         this.btn.visible = false;
         this.gotoShopBtn.visible = false;
         this.gotoArenaBtn.visible = false;
         this.btn.actived = false;
         this.imgTag.addChild(this.imgIcon);
         addChild(this.coverSp);
         this.setCoverText("");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      protected function setCoverText(str0:String) : void
      {
         if(str0 == "")
         {
            this.coverSp.visible = false;
         }
         else
         {
            this.coverSp.visible = true;
            (this.coverSp["txt"] as TextField).htmlText = str0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.vehicleUI.itemsBox.setChoose_byIndex(-1);
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      private function fleshData() : void
      {
         this.showVehicleList();
         this.showDefine(this.nowDefine);
      }
      
      public function outLoginEvent() : void
      {
         this.nowDefine = null;
         this.nowData = null;
      }
      
      private function showVehicleList() : void
      {
         var defineArr0:Array = null;
         if(this.itemsBox.gripArr.length == 0)
         {
            defineArr0 = Gaming.defineGroup.vehicle.getCanComposeArr();
            this.itemsBox.inData_byArr(defineArr0,"inData_vehicleDefine");
         }
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         this.showDefine(e.childData as VehicleDefine);
      }
      
      private function showDefine(d0:VehicleDefine) : void
      {
         var grip0:ItemsGrid = null;
         var da0:VehicleData = null;
         this.nowDefine = d0;
         if(!(d0 is VehicleDefine))
         {
            this.nowData = null;
            this.setCoverText("请在左边选择你要合成的载具");
         }
         else
         {
            this.setCoverText("");
            grip0 = this.itemsBox.findGripByData(d0);
            this.itemsBox.setChoose_byIndex(grip0.index);
            da0 = VehicleDataCreator.getTempData(d0,Gaming.PG.da);
            this.showData(da0);
         }
      }
      
      private function showData(da0:VehicleData) : void
      {
         this.nowData = da0;
         var d0:VehicleDefine = da0.getVehicleSave().getVehicleDefine();
         this.titleTxt.text = d0.cnName;
         var proStr0:String = VehicleDataCreator.getComposeProString(da0);
         var valueStr0:String = VehicleDataCreator.getComposeValueString(da0);
         this.proTxt.text = proStr0;
         this.valueTxt.text = valueStr0;
         this.imgIcon.setIconName(d0.getBodyDefine().bmpUrl);
         this.showMust(da0);
      }
      
      private function showMust(da0:VehicleData) : void
      {
         var onceBB0:Boolean = false;
         var must_d0:MustDefine = null;
         var bb0:Boolean = false;
         var d0:VehicleDefine = da0.getVehicleSave().getVehicleDefine();
         this.mustBox.visible = false;
         this.btn.visible = false;
         this.gotoShopBtn.visible = false;
         this.gotoArenaBtn.visible = false;
         if(d0.shopB)
         {
            if(Boolean(Gaming.defineGroup.goods.getDefine(d0.name)))
            {
               this.gotoShopBtn.visible = true;
            }
            if(Boolean(Gaming.defineGroup.goods.getDefine(d0.getArenaGoodsName())))
            {
               this.gotoArenaBtn.visible = true;
            }
         }
         else
         {
            this.mustBox.visible = true;
            this.btn.visible = true;
            onceBB0 = true;
            if(d0.onceB)
            {
               if(Boolean(Gaming.PG.da.findEquipDataByName(d0.name,true)))
               {
                  onceBB0 = false;
               }
            }
            must_d0 = VehicleDataCreator.getComposeMust(da0);
            bb0 = this.mustBox.inData(must_d0,Gaming.PG.da.level);
            this.btn.actived = bb0 && onceBB0;
            this.btn.setName(onceBB0 ? "合成" : "只能合成1个");
         }
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var must_d0:MustDefine = null;
         var surplus0:int = Gaming.PG.da.equipBag.getSpaceSiteNum();
         if(surplus0 >= 1)
         {
            must_d0 = VehicleDataCreator.getComposeMust(this.nowData);
            PlayerMustCtrl.deductMust(must_d0,this.affterBtnClick);
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("装备背包空位不足，无法合成载具。");
         }
      }
      
      private function affterBtnClick() : void
      {
         var da0:VehicleData = VehicleDataCreator.getTempData(this.nowDefine,Gaming.PG.da);
         Gaming.PG.da.equipBag.addData(da0);
         Gaming.uiGroup.alertBox.showSuccess("合成成功！");
         this.fleshData();
         this.vehicleUI.fleshBag();
      }
      
      private function gotoShopBtnClick(e:MouseEvent) : void
      {
         UIShow.showByLabel("shop");
         Gaming.uiGroup.shopUI.showBox("normal",this.nowDefine.name,"vehicle");
      }
      
      private function gotoArenaBtnClick(e:MouseEvent) : void
      {
         UIShow.showByLabel("arena");
      }
   }
}

