package
{
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol447")]
   public dynamic class smallThingsGrip extends MovieClip
   {
      public var numTxt:TextField;
      
      public var smallIconMc:MovieClip;
      
      public var annotationTxt:TextField;
      
      public var lockSp:MovieClip;
      
      public var levelTxt:TextField;
      
      public var backMc:MovieClip;
      
      public var iconCon:MovieClip;
      
      public var btnMc:MovieClip;
      
      public function smallThingsGrip()
      {
         super();
      }
   }
}

