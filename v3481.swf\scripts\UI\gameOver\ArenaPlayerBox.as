package UI.gameOver
{
   import UI.base.NormalUI;
   import UI.base.heroImg.HeroEquipImgBox;
   import dataAll._player.PlayerData;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class ArenaPlayerBox extends NormalUI
   {
      private var nameTxt:TextField;
      
      private var imgTag:Sprite;
      
      private var imgBox:HeroEquipImgBox = new HeroEquipImgBox();
      
      private var img_x:int = 0;
      
      public function ArenaPlayerBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["nameTxt","imgTag"];
         super.setImg(img0);
         this.imgTag.addChild(this.imgBox);
         this.imgBox.imgInit();
         this.img_x = this.imgBox.x;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function inData(pd0:PlayerData, winB0:Boolean) : void
      {
         this.imgBox.x = this.img_x + (winB0 ? 0 : 30);
         this.imgTag.scaleX = winB0 ? -1 : 1;
         this.nameTxt.text = pd0.base.save.playerName;
         this.nameTxt.textColor = pd0 != Gaming.PG.da ? 16737792 : 65280;
         this.imgBox.setEquip_byPlayerData(pd0);
         if(!winB0)
         {
            this.imgBox.playLabel("stru");
         }
      }
   }
}

