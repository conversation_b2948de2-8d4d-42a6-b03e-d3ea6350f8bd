package UI.api.exchange
{
   import com.adobe.crypto.MD5;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   
   public class Summer2017_API
   {
      public var yesFun:Function;
      
      public var noFun:Function;
      
      protected var loader:URLLoader = new URLLoader();
      
      protected var url:URLRequest = new URLRequest("https://huodong.4399.com/2017/ndyx/api.php");
      
      public function Summer2017_API()
      {
         super();
         this.loader.addEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this.loader.addEventListener(Event.COMPLETE,this.loaderCompleteHandler);
      }
      
      public function startExchange(code0:String, _yesFun:Function = null, _noFun:Function = null) : *
      {
         this.yesFun = _yesFun;
         this.noFun = _noFun;
         var uid0:String = Gaming.PG.getUid();
         var key0:String = MD5.hash(MD5.hash("asdakjapsdfajslkjasdkWPUls" + uid0 + "7" + code0));
         var data0:URLVariables = new URLVariables();
         data0.uid = uid0;
         data0.type = "7";
         data0.code = code0;
         data0.key = key0;
         this.url.data = data0;
         this.url.method = URLRequestMethod.POST;
         this.loader.load(this.url);
      }
      
      private function loaderCompleteHandler(e:Event) : void
      {
         var msg0:String = null;
         var s0:String = this.loader.data;
         if(s0 == "20000")
         {
            if(this.yesFun is Function)
            {
               this.yesFun();
            }
         }
         else
         {
            msg0 = "未知错误";
            if(s0 == "10000")
            {
               msg0 = "参数填写不完整";
            }
            else if(s0 == "10001")
            {
               msg0 = "礼包id不正确";
            }
            else if(s0 == "10002")
            {
               msg0 = "校验码不正确";
            }
            else if(s0 == "10003")
            {
               msg0 = "激活码有误";
            }
            if(this.noFun is Function)
            {
               this.noFun(msg0);
            }
         }
      }
      
      private function errorHandler(e:IOErrorEvent) : void
      {
         if(this.noFun is Function)
         {
            this.noFun("网络连接错误");
         }
      }
   }
}

