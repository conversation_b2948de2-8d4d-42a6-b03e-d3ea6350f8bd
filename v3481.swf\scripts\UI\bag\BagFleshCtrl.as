package UI.bag
{
   import dataAll.items.ItemsDataGroup;
   
   public class BagFleshCtrl
   {
      private var fleshFunObj:Object = {};
      
      public function BagFleshCtrl()
      {
         super();
      }
      
      public function flesh(nowBox:ItemsGripBox, label0:String, bagUI:BagUI) : void
      {
         var arr0:Array = null;
         var dg0:ItemsDataGroup = Gaming.PG.da[label0 + "Bag"];
         var fun0:Function = this.getFleshFun(label0);
         bagUI.showInfoText("");
         if(fun0 is Function)
         {
            arr0 = fun0(dg0);
            nowBox.inData_byDataGroupAndArr(dg0,arr0);
            if(arr0.length == 0)
            {
               bagUI.showInfoText("背包里没有找到满足条件的" + dg0.getCnName());
            }
         }
         else
         {
            nowBox.inData_byDataGroup(dg0);
         }
      }
      
      public function setFleshFun(type0:String, fun0:Function) : void
      {
         this.fleshFunObj[type0] = fun0;
      }
      
      public function getFleshFun(type0:String) : Function
      {
         return this.fleshFunObj[type0];
      }
      
      public function clearFleshFun() : void
      {
         this.fleshFunObj = {};
      }
   }
}

