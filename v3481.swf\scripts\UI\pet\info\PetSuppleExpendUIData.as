package UI.pet.info
{
   import UI.base.button.NormalBtn;
   import UI.base.must.ExpendUIData;
   import UI.base.must.NormalMustBox;
   import dataAll.must.define.MustDefine;
   import dataAll.pet.PetData;
   import flash.text.TextField;
   
   public class PetSuppleExpendUIData extends ExpendUIData
   {
      public var fleshFun:Function = null;
      
      public var petData:PetData = null;
      
      public function PetSuppleExpendUIData()
      {
         super();
         successReSetDataB = false;
      }
      
      public static function getMustNum() : int
      {
         return 7;
      }
      
      public static function getMustBackNum() : int
      {
         return getMustNum() / 3;
      }
      
      public function setData(da0:PetData) : void
      {
         this.petData = da0;
      }
      
      override public function showDataEvent(okBtn0:NormalBtn, infoTxt0:TextField, mustBox0:NormalMustBox) : void
      {
         var must0:MustDefine = null;
         var mustB0:Boolean = false;
         okBtn0.setName("获得功能");
         if(this.petData.save.base.suppleFunB)
         {
            infoTxt0.htmlText = "该宠物已经获得替补功能";
            mustBox0.setShowState(false);
            okBtn0.actived = false;
         }
         else
         {
            infoTxt0.htmlText = "获得替补功能需要消耗：";
            must0 = this.getMustDefine();
            mustB0 = mustBox0.inData(must0);
            okBtn0.actived = mustB0;
         }
      }
      
      override public function getMustDefine() : MustDefine
      {
         var d0:MustDefine = new MustDefine();
         d0.inThingsDataByArr(["petSuppleCard;10"]);
         return d0;
      }
      
      override public function successEvent() : void
      {
         this.petData.save.base.suppleFunB = true;
         Gaming.uiGroup.alertBox.showSuccess("获取功能成功！");
         if(this.fleshFun is Function)
         {
            this.fleshFun();
         }
      }
   }
}

