package UI.wilder
{
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.oldUtils.StringDate;
   import dataAll._app.wilder.WilderDataGroup;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class WilderLootBoard extends AutoNormalUI
   {
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      private var hourSp:Sprite;
      
      private var minSp:Sprite;
      
      private var secSp:Sprite;
      
      private var timeSp:Sprite;
      
      private var txt:TextField;
      
      private var btn:NormalBtn;
      
      private var closeBtn:NormalBtn;
      
      private var state:int = -1;
      
      public function WilderLootBoard()
      {
         super();
         mcTypeArr = ["btnSp","txt"];
         this.cTime = 99999;
      }
      
      public function get cTime() : Number
      {
         return this.CF.getAttribute("cTime");
      }
      
      public function set cTime(v0:Number) : void
      {
         this.CF.setAttribute("cTime",v0);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         FontDeal.dealOne(this.txt);
         this.btn.setName("抢夺");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      protected function get DATA() : WilderDataGroup
      {
         return Gaming.PG.da.wilder;
      }
      
      override public function show() : void
      {
         Gaming.uiGroup.connectUI.show();
         Gaming.api.save.getServerTime(this.yesGetTime,this.noGetTime);
      }
      
      public function outLoginEvent() : void
      {
         this.cTime = 999999;
      }
      
      private function yesGetTime(timeStr0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         super.show();
         this.fleshData();
      }
      
      private function noGetTime(v0:* = null) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError("连接服务器失败！");
         hide();
      }
      
      private function fleshData() : void
      {
         this.countTime();
         if(visible)
         {
            this.clockFlesh();
            this.fleshUI();
         }
      }
      
      private function fleshUI() : void
      {
         var surplusTime0:Number = NaN;
         var lootTime0:StringDate = this.DATA.getLootTime(Gaming.api.save.nowSeverTime);
         var btnActived0:Boolean = false;
         var timeStr0:String = "";
         this.btn.setName("抢夺");
         if(this.DATA.saveGroup.lootB)
         {
            this.btn.setName("今日已抢");
            timeStr0 = ComMethod.color("今日已经抢到了钥匙。","#00FF00",14);
         }
         else if(this.state == -1)
         {
            timeStr0 = "开抢倒计时：" + ComMethod.getTimeStr(Math.ceil(this.cTime / 1000));
            timeStr0 += "\n开抢时间：" + lootTime0.getTimeStr();
         }
         else if(this.state == 1)
         {
            timeStr0 = ComMethod.color("很遗憾，今天已经过了抢钥匙的时间。","#FF0000",14);
         }
         else
         {
            btnActived0 = true;
            surplusTime0 = Math.ceil((this.DATA.getLootLong() + this.cTime) / 1000);
            timeStr0 = ComMethod.color("秘境钥匙正式开抢！","#00FFFF",14);
            timeStr0 += "\n剩余时间：" + ComMethod.getTimeStrTwo(surplusTime0);
         }
         if(this.btn.actived != btnActived0)
         {
            this.btn.actived = btnActived0;
         }
         this.txt.htmlText = FontDeal.getDealLeadingStr(this.txt,timeStr0);
      }
      
      private function clockFlesh() : void
      {
         var now0:StringDate = Gaming.api.save.getForecastServerDate();
         var lootTime0:StringDate = this.DATA.getLootTime(Gaming.api.save.nowSeverTime);
         this.hourSp.rotation = 360 * ((now0.hours + now0.minutes / 60) / 12) - 90;
         this.minSp.rotation = 360 * (now0.minutes / 60) - 90;
         this.secSp.rotation = 360 * (now0.seconds / 60) - 90;
         this.timeSp.rotation = 360 * (lootTime0.hours + lootTime0.minutes / 60) / 12 - 90;
      }
      
      private function countTime() : void
      {
         var long0:Number = NaN;
         var now0:StringDate = Gaming.api.save.getForecastServerDate();
         var lootTime0:StringDate = this.DATA.getLootTime(Gaming.api.save.getNowServerDate().getStr());
         var c0:Number = lootTime0.getDateClass().time - now0.getDateClass().time;
         this.cTime = c0;
         if(c0 > 0)
         {
            this.state = -1;
         }
         else
         {
            long0 = this.DATA.getLootLong();
            if(-c0 > long0)
            {
               this.state = 1;
            }
            else
            {
               this.state = 0;
            }
         }
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var funName0:String = btn0.label + "BtnClick";
         if(this.hasOwnProperty(funName0))
         {
            this[funName0](e);
         }
      }
      
      public function closeBtnClick(e:MouseEvent) : void
      {
         hide();
      }
      
      public function BtnClick(e:MouseEvent) : void
      {
         Gaming.uiGroup.connectUI.show();
         Gaming.api.save.getServerTime(this.yesLoot,this.noGetTime);
      }
      
      private function yesLoot(timeStr0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         this.countTime();
         if(this.state == 0)
         {
            if(!this.DATA.saveGroup.lootB)
            {
               this.DATA.addKeyNum();
               Gaming.uiGroup.wilderUI.fleshTxt();
               Gaming.uiGroup.alertBox.showSuccess("抢夺成功，获得秘境钥匙！");
               this.fleshUI();
            }
         }
         else
         {
            this.fleshData();
         }
      }
      
      public function getLootBtnName() : String
      {
         if(this.state == 0)
         {
            return ComMethod.color("秘境钥匙正式开抢","#FFFF00",14);
         }
         if(this.state == -1)
         {
            return "开抢倒计时：" + ComMethod.getTimeStr(Math.ceil(this.cTime / 1000));
         }
         return "秘境钥匙抢夺已结束";
      }
      
      public function getShowNewB() : Boolean
      {
         if(this.DATA.saveGroup.lootB)
         {
            return false;
         }
         if(this.DATA.saveGroup.todayClickB)
         {
            return false;
         }
         return this.state == 0;
      }
      
      public function FTimerSecond() : void
      {
         this.fleshData();
      }
   }
}

