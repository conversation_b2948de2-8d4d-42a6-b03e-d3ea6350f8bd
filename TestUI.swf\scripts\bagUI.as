package
{
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol500")]
   public dynamic class bagUI extends MovieClip
   {
      public var labelTag:MovieClip;
      
      public var infoTxt:TextField;
      
      public var sortBtnSp2:MovieClip;
      
      public var composeBtnSp:MovieClip;
      
      public var gripTag:MovieClip;
      
      public var moreBtnSp:MovieClip;
      
      public var partsComposeBoxSp:MovieClip;
      
      public var armsPageTag:MovieClip;
      
      public var delBtnSp:MovieClip;
      
      public var titleTxt:TextField;
      
      public var closeBtn:SimpleButton;
      
      public var sortBtnSp:MovieClip;
      
      public function bagUI()
      {
         super();
      }
   }
}

