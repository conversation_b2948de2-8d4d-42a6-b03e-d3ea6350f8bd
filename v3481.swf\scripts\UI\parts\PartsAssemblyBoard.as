package UI.parts
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBtnListCtrl;
   import UI.bag.ItemsGripMoveCtrl;
   import UI.bag.ItemsGripTipCtrl;
   import UI.bag.ItemsGripUnlockCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.tip.EquipTipBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.parts.PartsMethod;
   import dataAll._app.setting.SettingSave;
   import dataAll.arms.ArmsData;
   import dataAll.arms.ArmsDataGroup;
   import dataAll.arms.define.ArmsTipType;
   import dataAll.equip.EquipPropertyData;
   import dataAll.ui.tip.CheckData;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class PartsAssemblyBoard extends NormalUI
   {
      private var armsTag:MovieClip;
      
      private var partsTag:Sprite = null;
      
      private var beforeTag:Sprite;
      
      private var affterTag:Sprite;
      
      private var loadBtnSp:MovieClip;
      
      private var unloadBtnSp:MovieClip;
      
      private var unloadAllBtnSp:MovieClip;
      
      private var beforeBtnSp:MovieClip;
      
      private var armsGrip:ItemsGrid = new ItemsGrid();
      
      private var partsBox:WearPartsGripBox = new WearPartsGripBox();
      
      private var loadBtn:NormalBtn = new NormalBtn();
      
      private var unloadBtn:NormalBtn = new NormalBtn();
      
      private var unloadAllBtn:NormalBtn = new NormalBtn();
      
      private var beforeBtn:NormalBtn = new NormalBtn();
      
      private var beforeTip:EquipTipBox = new EquipTipBox();
      
      private var affterTip:EquipTipBox = new EquipTipBox();
      
      public var nowArmsData:ArmsData = null;
      
      private var beforeData:ArmsData;
      
      private var affterData:ArmsData;
      
      private var temp_equipData:EquipPropertyData = new EquipPropertyData();
      
      public function PartsAssemblyBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["armsTag","partsTag","beforeTag","affterTag","loadBtnSp","unloadBtnSp","unloadAllBtnSp","beforeBtnSp"];
         super.setImg(img0);
         addChild(this.armsGrip);
         this.armsGrip.setImg(this.armsTag);
         this.partsBox.imgType = "equipGrip";
         this.partsBox.arg.init(5,3,0,0);
         this.partsBox.evt.setWantEvent(true,true,true,true,true);
         this.partsBox.createWearPartsGrip();
         addChild(this.partsBox);
         NormalUICtrl.setTag(this.partsBox,this.partsTag);
         addChild(this.beforeTip);
         NormalUICtrl.setTag(this.beforeTip,this.beforeTag);
         this.beforeTip.init();
         addChild(this.affterTip);
         NormalUICtrl.setTag(this.affterTip,this.affterTag);
         this.affterTip.init();
         addChild(this.loadBtn);
         this.loadBtn.setImg(this.loadBtnSp);
         this.loadBtn.setName("自动装配");
         this.loadBtn.addEventListener(MouseEvent.CLICK,this.loadClick);
         this.loadBtn.addEventListener(MouseEvent.MOUSE_OVER,this.loadOver);
         this.loadBtn.addEventListener(MouseEvent.MOUSE_OUT,this.loadOut);
         addChild(this.unloadBtn);
         this.unloadBtn.setImg(this.unloadBtnSp);
         this.unloadBtn.setName("卸下");
         this.unloadBtn.addEventListener(MouseEvent.CLICK,this.unloadClick);
         addChild(this.unloadAllBtn);
         this.unloadAllBtn.setImg(this.unloadAllBtnSp);
         this.unloadAllBtn.setName("全部卸下");
         this.unloadAllBtn.addEventListener(MouseEvent.CLICK,this.unloadAllClick);
         addChild(this.beforeBtn);
         this.beforeBtn.setImg(this.beforeBtnSp);
         this.beforeBtn.setName("统一零件图标");
         this.beforeBtn.addEventListener(MouseEvent.CLICK,this.beforeClick);
         ItemsGripMoveCtrl.addEvent_byItemsGripBox(this.partsBox);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.partsBox);
         ItemsGripUnlockCtrl.addEvent_byItemsGripBox(this.partsBox);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.armsGrip);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function get settingSave() : SettingSave
      {
         return Gaming.PG.save.setting;
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      public function fleshData() : void
      {
         this.fleshBeforeBtn();
         this.chooseArmsData(this.nowArmsData);
      }
      
      public function chooseArmsData(da0:ArmsData) : void
      {
         this.nowArmsData = da0;
         var dg0:ArmsDataGroup = null;
         if(Boolean(this.nowArmsData))
         {
            dg0 = Gaming.PG.da.findArmsData(this.nowArmsData);
         }
         if(!(this.nowArmsData is ArmsData) || !(dg0 is ArmsDataGroup))
         {
            this.nowArmsData = null;
            this.beforeData = null;
            this.affterData = null;
            this.armsGrip.clearData();
            this.partsBox.clearAllData();
            this.beforeTip.setText("","","","");
            this.affterTip.setText("","","","");
            Gaming.uiGroup.partsUI.setCoverText("在任何地方" + ComMethod.color("双击","#00FF00") + "" + ComMethod.color("武器图标","#FFFF00") + "，\n即可对该武器进行装配。");
         }
         else
         {
            Gaming.uiGroup.partsUI.setCoverText("");
            this.armsGrip.inData_arms(da0);
            this.partsBox.inData_byDataGroup(da0.partsData);
            this.partsBox.setLockByArmsDa(da0);
            this.partsBox.setAllFun("setNew",false);
            this.partsBox.setAllFun("setSmallIcon","");
            this.beforeData = this.nowArmsData.shallowCopy(false);
            this.beforeData.fleshData_byEquip(this.temp_equipData,true);
            this.affterData = this.nowArmsData.shallowCopy();
            this.affterData.fleshData_byEquip(this.temp_equipData,true);
            this.beforeTip.setText(this.beforeData.getGatherTip(this.affterData,true,false,ArmsTipType.parts),"","","");
            this.affterTip.setText(this.affterData.getGatherTip(this.beforeData,true,false,ArmsTipType.parts),"","","");
         }
      }
      
      private function unloadClick(e:MouseEvent) : void
      {
         var check0:CheckData = PartsMethod.unloadCanUnload(this.nowArmsData.partsData);
         this.afterUnload(check0);
      }
      
      private function unloadAllClick(e:MouseEvent) : void
      {
         var check0:CheckData = PartsMethod.unloadAll(this.nowArmsData.partsData);
         this.afterUnload(check0);
      }
      
      private function afterUnload(check0:CheckData) : void
      {
         if(check0.bb)
         {
            ItemsGripBtnListCtrl.fleshAllBy(this.nowArmsData.partsData);
            Gaming.soundGroup.playSound("uiSound","swapSuccess");
         }
         else
         {
            Gaming.uiGroup.alertBox.showError(check0.info);
         }
         Gaming.uiGroup.btnList.hide();
      }
      
      private function loadClick(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
         var check0:CheckData = PartsMethod.loadCan(this.nowArmsData);
         if(!check0.bb && check0.info != "")
         {
            Gaming.uiGroup.alertBox.showError(check0.info);
         }
         else
         {
            Gaming.soundGroup.playSound("uiSound","swapSuccess");
         }
         ItemsGripBtnListCtrl.fleshAllBy(this.nowArmsData.partsData);
         Gaming.uiGroup.btnList.hide();
      }
      
      private function loadOver(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.followMouseB = true;
         Gaming.uiGroup.tipBox.textTip.setText("自动装备最高等级的零件");
         Gaming.uiGroup.tipBox.textTip.show();
      }
      
      private function loadOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function fleshBeforeBtn() : void
      {
         this.beforeBtn.isChosen = this.settingSave.partsSaB;
      }
      
      private function beforeClick(e:MouseEvent) : void
      {
         this.settingSave.partsSaB = !this.settingSave.partsSaB;
         this.fleshData();
      }
   }
}

