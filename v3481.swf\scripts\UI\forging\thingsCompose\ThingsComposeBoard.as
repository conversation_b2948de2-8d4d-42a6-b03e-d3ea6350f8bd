package UI.forging.thingsCompose
{
   import UI.UIOrder;
   import UI.bag.BagUI;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.must.NormalMustBox;
   import UI.base.numChoose.NumChooseBox;
   import com.greensock.TweenLite;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.equip.define.EquipColor;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.GiftData;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.gift.level.LevelGiftData;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.things.define.ThingsComposeDefine;
   import fl.transitions.easing.Regular;
   import flash.display.DisplayObjectContainer;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   public class ThingsComposeBoard extends NormalUI
   {
      private var gripTag:Sprite;
      
      private var numSp:Sprite;
      
      private var btnSp:MovieClip;
      
      private var pointer:Sprite;
      
      private var mustSp:Sprite;
      
      private var upBtnSp:MovieClip;
      
      private var moreBtnSp:MovieClip;
      
      private var downBtnSp:MovieClip;
      
      private var coverSp:Sprite;
      
      private var downCoverSp:Sprite;
      
      private var upBtn:NormalBtn = new NormalBtn();
      
      private var moreBtn:NormalBtn = new NormalBtn();
      
      private var downBtn:NormalBtn = new NormalBtn();
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var numBox:NumChooseBox = new NumChooseBox();
      
      private var gripBox:ItemsGripBox = new ItemsGripBox();
      
      private var nowThingsComposeDefine:ThingsComposeDefine = null;
      
      private var nowGrip:NormalBtn;
      
      private var con:Sprite = new Sprite();
      
      private var packUpB:Boolean = true;
      
      public function ThingsComposeBoard()
      {
         super();
         addChild(this.con);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["coverSp","downCoverSp","moreBtnSp","gripTag","numSp","pointer","mustSp","btnSp","upBtnSp","downBtnSp"];
         super.setImg(img0);
         addChild(this.downCoverSp);
         this.con.mask = this.downCoverSp;
         this.con.addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("合成");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.con.addChild(this.downBtn);
         this.downBtn.setImg(this.downBtnSp);
         this.downBtn.addEventListener(MouseEvent.CLICK,this.downBtnClick);
         this.con.addChild(this.upBtn);
         this.upBtn.setImg(this.upBtnSp);
         this.upBtn.addEventListener(MouseEvent.CLICK,this.upBtnClick);
         this.con.addChild(this.mustBox);
         this.mustBox.setImg(this.mustSp);
         this.con.addChild(this.numBox);
         this.numBox.setImg(this.numSp);
         this.numBox.init(9999,1);
         this.numBox.addEventListener(Event.CHANGE,this.fleshData);
         this.addChild(this.moreBtn);
         this.moreBtn.setImg(this.moreBtnSp);
         this.moreBtn.addEventListener(MouseEvent.CLICK,this.moreBtnClick);
         this.addChild(this.gripTag);
         this.addChild(this.coverSp);
         this.gripTag.mask = this.coverSp;
         this.gripTag.addChild(this.gripBox);
         this.gripBox.imgType = "equipGrip";
         this.gripBox.arg.init(7,5,4,4);
         this.gripBox.evt.setWantEvent(true,false,false,true,true);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.gripBox.pageBox.setToNormalBtn();
         this.gripBox.pageBox.x = 217;
         this.gripBox.pageBox.y = 315;
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.gripBox);
         this.setPackUp(true,false);
      }
      
      override protected function getEleCon() : DisplayObjectContainer
      {
         return this.con;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      protected function get giftData() : GiftData
      {
         return Gaming.PG.da.gift;
      }
      
      override public function show() : void
      {
         super.show();
         var bag0:BagUI = Gaming.uiGroup.bagUI;
         bag0.showAndLabel("things");
         this.fleshGrip();
         this.fleshGripNum();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         if(Gaming.uiGroup.bagUI.visible)
         {
            Gaming.uiGroup.bagUI.hide();
         }
         super.hide();
      }
      
      private function fleshGrip() : void
      {
         if(this.gripBox.gripArr.length > 0)
         {
            return;
         }
         var arr0:Array = Gaming.defineGroup.things.getComposeDefineArr();
         this.gripBox.inData_byArr(arr0,this.gripFun);
         this.gripBox.fleshPageBox();
      }
      
      private function gripFun(grip0:ItemsGrid, d0:ThingsComposeDefine) : void
      {
         var gd0:GiftAddDefine = d0.getGift();
         grip0.secData = d0;
         grip0.inData_gift(gd0);
      }
      
      private function fleshGripNum() : void
      {
         var grip0:ItemsGrid = null;
         var num0:Number = NaN;
         var d0:ThingsComposeDefine = null;
         for each(grip0 in this.gripBox.gripArr)
         {
            num0 = 0;
            d0 = grip0.secData as ThingsComposeDefine;
            if(Boolean(d0))
            {
               if(d0.giftType == "")
               {
                  num0 = Gaming.PG.da.thingsBag.getThingsNum(d0.name);
               }
            }
            if(num0 > 0)
            {
               grip0.setNumText(num0 + "");
            }
            else
            {
               grip0.setNumText("");
            }
         }
      }
      
      private function fleshData(v:* = null) : void
      {
         var d0:ThingsComposeDefine = null;
         var btn0:NormalBtn = this.gripBox.getChooseBtn();
         if(!btn0)
         {
            btn0 = this.gripBox.gripArr[0];
         }
         if(Boolean(btn0))
         {
            d0 = btn0.secData as ThingsComposeDefine;
         }
         this.chooseDefine(d0);
         this.setPackUp(true,false);
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         this.numBox.setNum(1);
         var grip0:ItemsGrid = e.child as ItemsGrid;
         this.chooseDefine(grip0.secData as ThingsComposeDefine);
         this.setPackUp(true);
      }
      
      private function getNowIndex() : int
      {
         var grip0:ItemsGrid = this.getNowGrip();
         if(Boolean(grip0))
         {
            if(grip0.data3 == null)
            {
               grip0.data3 = 0;
            }
            return grip0.data3;
         }
         return 0;
      }
      
      private function setNowIndexAdd(add0:int) : void
      {
         var grip0:ItemsGrid = this.getNowGrip();
         if(Boolean(grip0) && Boolean(this.nowThingsComposeDefine))
         {
            if(grip0.data3 == null)
            {
               grip0.data3 = 0;
            }
            grip0.data3 = this.nowThingsComposeDefine.countIndex(grip0.data3,add0);
         }
      }
      
      private function getNowGrip() : ItemsGrid
      {
         return this.nowGrip as ItemsGrid;
      }
      
      public function chooseDefine(cd0:ThingsComposeDefine) : void
      {
         var btn0:NormalBtn = null;
         var weekMax0:Number = NaN;
         var weekLimit0:Number = NaN;
         var weekNow0:Number = NaN;
         var mustDefine0:MustDefine = null;
         var bb0:Boolean = false;
         if(Boolean(cd0))
         {
            this.nowThingsComposeDefine = cd0;
            this.nowGrip = this.gripBox.setChooseBySecData(cd0);
            btn0 = this.gripBox.getChooseBtn();
            this.pointer.x = btn0.x + btn0.width / 2 + this.gripTag.x;
            weekMax0 = cd0.week;
            weekLimit0 = 9999;
            weekNow0 = this.giftData.getComposeWeekNum(cd0.name);
            if(cd0.all > 0)
            {
               weekMax0 = cd0.all;
               weekNow0 = this.giftData.getComposeAllNum(cd0.name);
            }
            if(weekMax0 > 0)
            {
               weekLimit0 = weekMax0 - weekNow0;
               if(weekLimit0 < 0)
               {
                  weekLimit0 = 0;
               }
            }
            this.numBox.setMax(weekLimit0);
            mustDefine0 = cd0.getMustDefine(this.numBox.nowNum,this.getNowIndex());
            bb0 = this.mustBox.inData(mustDefine0);
            if(cd0.isThingsB() == false && this.numBox.nowNum > 1)
            {
               bb0 = false;
            }
            if(weekMax0 > 0)
            {
               this.mustBox.setCoinNameTxt(cd0.all > 0 ? "总上限" : "每周上限");
               this.mustBox.setCoinTxt(ComMethod.dropColor(weekNow0,weekMax0));
               if(weekNow0 + this.numBox.nowNum <= weekMax0)
               {
                  this.mustBox.setCoinHook(true);
               }
               else
               {
                  this.mustBox.setCoinHook(false);
                  bb0 = false;
               }
            }
            this.btn.actived = bb0;
            this.upBtn.visible = cd0.mustArr.length > 1;
            this.downBtn.visible = this.upBtn.visible;
         }
         else
         {
            this.mustBox.setShowState(false);
            this.btn.actived = false;
         }
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var nowNum0:int = 0;
         var g0:GiftAddDefineGroup = null;
         var bagStr0:String = null;
         var mustDefine0:MustDefine = null;
         this.fleshData();
         if(!this.btn.actived)
         {
            return;
         }
         var cd0:ThingsComposeDefine = this.nowThingsComposeDefine;
         if(Boolean(cd0))
         {
            if(this.otherConditionPan(cd0))
            {
               nowNum0 = this.numBox.nowNum;
               g0 = cd0.getComposeGiftAddDefineGroup(nowNum0);
               bagStr0 = GiftAddit.bagSpacePan(g0);
               if(bagStr0 == "")
               {
                  mustDefine0 = cd0.getMustDefine(nowNum0,this.getNowIndex());
                  PlayerMustCtrl.deductMust(mustDefine0,this.after);
               }
               else
               {
                  Gaming.uiGroup.alertBox.showError(bagStr0);
               }
            }
         }
      }
      
      private function after() : void
      {
         var nowNum0:int = 0;
         var g0:GiftAddDefineGroup = null;
         var cd0:ThingsComposeDefine = this.nowThingsComposeDefine;
         if(Boolean(cd0))
         {
            nowNum0 = this.numBox.nowNum;
            g0 = cd0.getComposeGiftAddDefineGroup(nowNum0);
            this.giftData.composeEvent(cd0,nowNum0);
            GiftAddit.add(g0,"获得物品：" + g0.getDescription());
            this.fleshGripNum();
            this.fleshData();
            Gaming.uiGroup.bagUI.fleshNowBox();
         }
      }
      
      private function otherConditionPan(cd0:ThingsComposeDefine) : Boolean
      {
         var now0:int = 0;
         var max0:int = 1;
         var tip0:String = "";
         if(cd0.name == "purgoldCpu_1")
         {
            max0 = 2;
            now0 = LevelGiftData.getEvoArmsNum(Gaming.PG.da,EquipColor.PURGOLD);
            if(now0 >= max0)
            {
               return true;
            }
            tip0 = "必须进阶出" + ComMethod.mustColor(now0,max0,true) + "把无双武器后才能合成该物品。";
            UIOrder.alertError(tip0);
            return false;
         }
         return true;
      }
      
      private function upBtnClick(e:MouseEvent) : void
      {
         this.setNowIndexAdd(-1);
         this.fleshData();
      }
      
      private function downBtnClick(e:MouseEvent) : void
      {
         this.setNowIndexAdd(1);
         this.fleshData();
      }
      
      private function moreBtnClick(e:MouseEvent) : void
      {
         this.setPackUp(!this.packUpB);
      }
      
      private function setPackUp(bb0:Boolean, tweenB0:Boolean = true) : void
      {
         var coverScaleY0:Number = NaN;
         var boxY0:Number = NaN;
         var conY0:Number = NaN;
         var chooseGrip0:NormalBtn = null;
         if(this.packUpB != bb0)
         {
            this.packUpB = bb0;
            this.moreBtn.setName(bb0 ? "更多▼" : "▲收起");
            coverScaleY0 = 1;
            boxY0 = 0;
            conY0 = 0;
            if(this.packUpB)
            {
               coverScaleY0 = 1;
               chooseGrip0 = this.nowGrip;
               if(!chooseGrip0)
               {
                  chooseGrip0 = this.gripBox.gripArr[0];
               }
               boxY0 = -chooseGrip0.y;
               conY0 = this.gripTag.y + 56 - this.pointer.y;
            }
            else
            {
               coverScaleY0 = (this.gripBox.height + 20) / (this.coverSp.height / this.coverSp.scaleY);
               boxY0 = 0;
               conY0 = this.gripTag.y + this.gripBox.height - this.pointer.y;
            }
            if(tweenB0)
            {
               TweenLite.to(this.coverSp,0.4,{
                  "scaleY":coverScaleY0,
                  "ease":Regular.easeOut
               });
               TweenLite.to(this.gripBox,0.4,{
                  "y":boxY0,
                  "ease":Regular.easeOut
               });
               TweenLite.to(this.con,0.4,{
                  "y":conY0,
                  "ease":Regular.easeOut
               });
            }
            else
            {
               this.coverSp.scaleY = coverScaleY0;
               this.gripBox.y = boxY0;
               this.con.y = conY0;
            }
         }
      }
   }
}

