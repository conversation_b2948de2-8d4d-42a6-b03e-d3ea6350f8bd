package UI.api.exchange
{
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   
   public class HomeExchange_API
   {
      public var yesFun:Function;
      
      public var noFun:Function;
      
      internal var loader:URLLoader = new URLLoader();
      
      internal var url:URLRequest = new URLRequest("https://huodong.4399.com/2015/2015chunjie/api.php");
      
      public function HomeExchange_API()
      {
         super();
         this.loader.addEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this.loader.addEventListener(Event.COMPLETE,this.loaderCompleteHandler);
      }
      
      public function startExchange(code0:String, uid0:String, _yesFun:Function = null, _noFun:Function = null) : *
      {
         if(uid0 == "0")
         {
            uid0 = "1056521331";
         }
         this.yesFun = _yesFun;
         this.noFun = _noFun;
         var data0:URLVariables = new URLVariables();
         data0.type = 2;
         data0.code = code0;
         data0.uid = uid0;
         this.url.data = data0;
         this.url.method = URLRequestMethod.GET;
         this.loader.load(this.url);
      }
      
      private function loaderCompleteHandler(e:Event) : void
      {
         if(this.yesFun is Function)
         {
            this.yesFun(this.loader.data);
         }
      }
      
      private function errorHandler(e:IOErrorEvent) : void
      {
         if(this.noFun is Function)
         {
            this.noFun();
         }
      }
   }
}

