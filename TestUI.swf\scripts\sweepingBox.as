package
{
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol37")]
   public dynamic class sweepingBox extends MovieClip
   {
      public var numTxt:TextField;
      
      public var closeBtnSp:MovieClip;
      
      public var scrollBarSp:MovieClip;
      
      public var scrollLineSp:MovieClip;
      
      public var throwBtnSp:MovieClip;
      
      public var maskTargetSp:MovieClip;
      
      public var levelTxt:TextField;
      
      public var sweepingBtnSp:MovieClip;
      
      public var gripSp:MovieClip;
      
      public var giftTag:MovieClip;
      
      public var giftBtnSp:MovieClip;
      
      public function sweepingBox()
      {
         super();
      }
   }
}

