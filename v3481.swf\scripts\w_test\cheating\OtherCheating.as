package w_test.cheating
{
   import UI.test.SaveTestBox;
   import com.adobe.crypto.MD5;
   import com.adobe.serialization.json.JSON2;
   import com.common.data.Base64;
   import com.common.text.TextWay;
   import com.sounto.utils.XmlMethod;
   import dataAll._app.active.ActiveData;
   import dataAll._app.top.TopBarData;
   import dataAll._app.top.TopBarDataGroup;
   import dataAll._app.top.define.TopBarDefineGroup;
   import dataAll._app.top.player.PlayerTopUploadData;
   import dataAll._player.more.MorePlayerData;
   import gameAll.level._diy.Qixi2019LevelDiy;
   
   public class OtherCheating extends OneCheating
   {
      private var topId:uint = 0;
      
      private var timeIndex:int = 0;
      
      private var timeMax:int = 0;
      
      public function OtherCheating()
      {
         super();
      }
      
      public function setQixi2019High(str0:String, v0:int) : String
      {
         var diy0:Qixi2019LevelDiy = Gaming.LG.nowLevel.nowDiy as Qixi2019LevelDiy;
         if(Boolean(diy0))
         {
            return "设置高度：" + v0;
         }
         return "没找到指定关卡";
      }
      
      public function setPartnerExploit(str0:String, v0:int) : String
      {
         var pd0:MorePlayerData = Gaming.PG.DATA as MorePlayerData;
         if(Boolean(pd0))
         {
            pd0.partner.save.exploit = v0;
            return "设置" + pd0.heroData.def.cnName + "功勋值：" + v0;
         }
         return "当前角色并非队友。";
      }
      
      public function setActive(str0:String, v0:int) : String
      {
         ActiveData.TEST_ACTIVE = v0;
         if(v0 > 0)
         {
            return "设置活跃度为" + v0;
         }
         return "清除活跃度设置";
      }
      
      public function showParts90(str0:String, v0:int) : String
      {
         var num0:Number = Gaming.PG.da.zuobiPaner.getPartsNum90Gather(false);
         var num2:Number = Gaming.PG.da.zuobiPaner.getPartsNum90Gather(true);
         return "87级零件套数：" + num0 + " ，可合成套数：" + num2;
      }
      
      public function showSpecialParts(str0:String, v0:int) : String
      {
         var num0:Number = Gaming.PG.da.zuobiPaner.getSpecialPartsNum();
         return "特殊零件1级数量：" + num0;
      }
      
      public function setSweepingNum(str0:String, v0:int) : String
      {
         Gaming.PG.da.worldMap.saveGroup.sweepingNum = v0;
         return "设置扫荡次数：" + v0;
      }
      
      public function testChipBattleGift(str0:String, v0:int) : String
      {
         return "";
      }
      
      public function testBowlIndex(str0:String, v0:int) : String
      {
         return "元宵跳一跳设定编号：" + v0;
      }
      
      public function testUplevelBug(str0:String, v0:int) : String
      {
         Gaming.testCtrl.uplevelBug.start();
         return "";
      }
      
      public function testUplevelBug2(str0:String, v0:int) : String
      {
         Gaming.testCtrl.uplevelBug.test2();
         return "";
      }
      
      public function getTopData(str0:String, v0:int) : String
      {
         var numArr0:Array = str0.split(",");
         var id0:uint = uint(numArr0[0]);
         var pageNum0:uint = uint(numArr0[1]);
         var pageSize0:uint = uint(numArr0[2]);
         Gaming.uiGroup.connectUI.show("获取排行榜数据……");
         Gaming.api.top.getRankListsData(id0,pageSize0,pageNum0,this.yesGetTopData);
         return "";
      }
      
      private function yesGetTopData(dataArr0:Array) : void
      {
         var obj0:Object = null;
         var da0:TopBarData = null;
         Gaming.uiGroup.connectUI.hide();
         var s0:String = "**** 排行榜id：" + this.topId;
         var d0:TopBarDefineGroup = Gaming.defineGroup.top.getDefineById(this.topId);
         if(Boolean(d0))
         {
            s0 += "   " + d0.cnName;
         }
         s0 += " ************";
         for each(obj0 in dataArr0)
         {
            da0 = new TopBarData();
            da0.inData_byObj(obj0,d0);
            s0 += "\n" + da0.toString();
         }
         Gaming.uiGroup.testUI.saveBox.txt.text = s0;
         Gaming.uiGroup.alertBox.showSuccess("获取排行榜数据成功，请在文本框查看。");
      }
      
      public function addAllFood(str0:String, v0:int) : String
      {
         Gaming.PG.da.food.addRawAll(v0);
         return "添加所有食材数量：" + v0;
      }
      
      public function setFoodProfiAll(str0:String, v0:int) : String
      {
         Gaming.PG.da.food.save.profiAll = v0;
         Gaming.PG.da.food.addProfi(0);
         return "设置厨艺值：" + v0;
      }
      
      public function testSeverTime(str0:String, v0:int) : String
      {
         this.timeMax = v0;
         this.timeIndex = 0;
         this.startSeverTime();
         return "";
      }
      
      private function startSeverTime() : void
      {
         if(this.timeIndex >= this.timeMax)
         {
            Gaming.uiGroup.connectUI.hide();
            Gaming.uiGroup.alertBox.showSuccess("测试结束");
         }
         else
         {
            ++this.timeIndex;
            Gaming.uiGroup.connectUI.show();
            Gaming.api.save.s4399.getServerTime(this.yes_testSeverTime,this.no_testSeverTime);
         }
      }
      
      private function yes_testSeverTime(str0:String) : void
      {
         SaveTestBox.addText("第" + this.timeIndex + "次：  " + str0);
         this.startSeverTime();
      }
      
      private function no_testSeverTime(str0:String) : void
      {
         SaveTestBox.addText("第" + this.timeIndex + "次：  获取失败！");
         this.startSeverTime();
      }
      
      public function md5(str0:String, v0:int) : String
      {
         return MD5.hash(str0);
      }
      
      public function testTop(str0:String, v0:int) : String
      {
         var da0:PlayerTopUploadData = null;
         da0 = new PlayerTopUploadData();
         da0.rId = 2067;
         da0.score = v0;
         Gaming.uiGroup.connectUI.show("测试排行榜……");
         Gaming.api.top.submitScoreArray([da0],this.yes_testTop,this.no_testTop);
         return "";
      }
      
      private function yes_testTop(obj0:* = null) : void
      {
         Gaming.uiGroup.alertBox.showSuccess("测试成功：" + obj0);
         Gaming.uiGroup.connectUI.hide();
      }
      
      private function no_testTop(obj0:* = null) : void
      {
         Gaming.uiGroup.alertBox.showError("测试失败：" + obj0);
         Gaming.uiGroup.connectUI.hide();
      }
      
      public function setSimTopScore(str0:String, v0:int) : String
      {
         TopBarDataGroup.TEST_SCORE = v0;
         return "设置本地排行榜模拟分数：" + v0;
      }
      
      public function base64ToJson(str0:String, v0:int) : String
      {
         var obj0:Object = null;
         var json0:String = null;
         if(str0 != "")
         {
            obj0 = Base64.decodeObject(str0);
            json0 = JSON2.encode(obj0);
            SaveTestBox.addText(json0);
            return "json已输入到SaveTestBox";
         }
         return "";
      }
      
      public function jsonToBase64(str0:String, v0:int) : String
      {
         var obj0:Object = null;
         var base0:String = null;
         if(str0 != "")
         {
            obj0 = JSON2.decode(str0);
            base0 = Base64.encodeObject(obj0);
            SaveTestBox.addText(base0);
            return "base64已输入到SaveTestBox";
         }
         return "";
      }
      
      public function base64ToXml(str0:String, v0:int) : String
      {
         var xml0:XML = null;
         if(str0 != "")
         {
            xml0 = XmlMethod.base64ToXml(str0);
            SaveTestBox.addText(xml0.toXMLString());
            return "xml已输入到SaveTestBox";
         }
         return "";
      }
      
      public function decodeText32(str0:String, v0:int) : String
      {
         var s0:* = null;
         if(str0 != "")
         {
            return TextWay.getText32(str0);
         }
         return "";
      }
   }
}

