package UI.pet.book
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.UIShow;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.grid.NormalGridIcon;
   import UI.base.loadBar.LoadBar;
   import UI.main.WorldMapBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._app.goods.define.PriceType;
   import dataAll._app.worldMap.WorldMapData;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.pet.PetCount;
   import dataAll.pet.gene.define.GeneDefine;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.text.TextField;
   
   public class PetBookBoard extends NormalUI
   {
      private var closeBtn:SimpleButton;
      
      private var gripBox:ItemsGripBox = new ItemsGripBox();
      
      private var gripTag:Sprite;
      
      private var imgTag:Sprite;
      
      private var imgIcon:NormalGridIcon = new NormalGridIcon();
      
      private var skillTag:Sprite;
      
      private var infoTxt:TextField;
      
      private var nameTxt:TextField;
      
      private var dpsBarSp:Sprite;
      
      private var lifeBarSp:Sprite;
      
      private var defenceBarSp:Sprite;
      
      private var skillBox:ItemsGripBox = new ItemsGripBox();
      
      private var proTxt:TextField;
      
      private var mapNameTxt:TextField;
      
      private var mapGoTxt:TextField;
      
      private var proBarSp:Sprite;
      
      private var proBar:LoadBar = new LoadBar();
      
      private var tipSp:Sprite;
      
      private var mustBoxSp:Sprite;
      
      private var proBtnSp:MovieClip;
      
      private var shopSp:Sprite;
      
      private var gotoShopBtn:NormalBtn = new NormalBtn();
      
      private var gotoArenaBtn:NormalBtn = new NormalBtn();
      
      private var proBtn:NormalBtn = new NormalBtn();
      
      private var mustItemsGrip:ItemsGrid = new ItemsGrid();
      
      private var itemsHook:MovieClip;
      
      public var nowGeneDefine:GeneDefine;
      
      public function PetBookBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["shopSp","mapNameTxt","mapGoTxt","closeBtn","infoTxt","gripTag","imgTag","skillTag","nameTxt","dpsBarSp","lifeBarSp","defenceBarSp","proTxt","proBtnSp","tipSp","mustBoxSp","proBarSp"];
         super.setImg(img0);
         FontDeal.dealOne(this.nameTxt);
         FontDeal.dealLine(this.tipSp["txt"]);
         this.imgTag.addChild(this.imgIcon);
         addChild(this.gripBox);
         NormalUICtrl.setTag(this.gripBox,this.gripTag);
         this.gripBox.arg.init(3,5,15,15);
         this.gripBox.setIconPro("PetUI/bookGrip",50,50);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         addChild(this.skillBox);
         NormalUICtrl.setTag(this.skillBox,this.skillTag);
         this.skillBox.arg.init(4,1,2,0);
         this.skillBox.setIconPro("BasicUI/smallThingsGripNoScale",40,40);
         this.skillBox.evt.setWantEvent(true,false,false,true,true);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.skillBox);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         this.mapGoTxt.styleSheet = ComMethod.getLinkCss("#00FF00","#FFFFFF");
         this.mapGoTxt.addEventListener(TextEvent.LINK,this.mapGoLink);
         this.shopSp.addChild(this.gotoShopBtn);
         this.gotoShopBtn.setImg(this.shopSp["gotoShopBtnSp"]);
         this.gotoShopBtn.setName("前往商城购买");
         this.gotoShopBtn.addEventListener(MouseEvent.CLICK,this.shopBtnClick);
         this.gotoShopBtn.visible = false;
         this.shopSp.addChild(this.gotoArenaBtn);
         this.gotoArenaBtn.setImg(this.shopSp["gotoArenaBtnSp"]);
         this.gotoArenaBtn.setName("前往竞技场兑换");
         this.gotoArenaBtn.addEventListener(MouseEvent.CLICK,this.shopBtnClick);
         this.gotoArenaBtn.visible = false;
         this.initPro();
         addChild(this.shopSp);
      }
      
      private function initPro() : void
      {
         addChild(this.proBar);
         this.proBar.setImg(this.proBarSp);
         this.mustBoxSp.addChild(this.mustItemsGrip);
         NormalUICtrl.setTag(this.mustItemsGrip,this.mustBoxSp["itemsTag"]);
         this.itemsHook = this.mustBoxSp["itemsHook"];
         addChild(this.proBtn);
         this.proBtn.setImg(this.proBtnSp);
         this.proBtn.activedAndEnabled = false;
         this.proBtn.addEventListener(MouseEvent.CLICK,this.proBtnClick);
         this.proBtn.addEventListener(MouseEvent.MOUSE_OVER,this.proBtnOver);
         this.itemsHook.stop();
         this.showProMust(false);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.hide();
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshList();
         var index0:int = this.gripBox.nowIndex;
         if(index0 == -1)
         {
            index0 = 0;
         }
         this.showByDefine(this.gripBox.gripArr[index0].itemsData);
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      private function fleshList() : void
      {
         var defineArr0:Array = Gaming.defineGroup.gene.getBookArr();
         this.gripBox.inData_byArr(defineArr0,"inData_geneDefine");
         this.fleshListPro();
      }
      
      private function fleshListPro() : void
      {
         var grip0:ItemsGrid = null;
         var d0:GeneDefine = null;
         var per0:Number = NaN;
         for each(grip0 in this.gripBox.gripArr)
         {
            d0 = grip0.itemsData as GeneDefine;
            per0 = Gaming.PG.da.pet.saveGroup.map.getDropPro(d0.name);
            grip0.setSmallIcon(d0.getBarColorName());
            grip0.setSmallIconPer(per0);
         }
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var d0:GeneDefine = e.childData as GeneDefine;
         this.showByDefine(d0);
      }
      
      private function showByDefine(d0:GeneDefine) : void
      {
         this.nowGeneDefine = d0;
         this.gripBox.setChoose_byIndex(this.gripBox.findGripByData(d0).index);
         var bodyDefine0:NormalBodyDefine = d0.getBodyDefine();
         this.nameTxt.htmlText = d0.cnName;
         this.imgIcon.setIconName(bodyDefine0.bmpUrl);
         this.infoTxt.htmlText = d0.getEvoInfo();
         this.showMul(d0);
         this.showSkill(d0);
         this.showPro(d0);
         this.showMap(d0);
      }
      
      private function showSkill(d0:GeneDefine) : void
      {
         var defineArr0:Array = [];
         if(d0.talentSkillArr.length > 0)
         {
            defineArr0 = Gaming.defineGroup.skill.getArrByNameArr(d0.talentSkillArr);
         }
         this.skillBox.inData_byArr(defineArr0,"inData_SkillDefine");
      }
      
      private function showMul(d0:GeneDefine) : void
      {
         var name0:* = null;
         var per0:Number = NaN;
         var nameArr0:Array = GeneDefine.mulNameArr;
         for each(name0 in nameArr0)
         {
            per0 = d0.getMulUIPer(name0);
            this.setBarPer(name0,per0);
         }
      }
      
      private function setBarPer(name0:String, per0:Number) : void
      {
         if(per0 > 1)
         {
            per0 = 1;
         }
         if(per0 < 0)
         {
            per0 = 0;
         }
         var sp0:Sprite = this[name0 + "BarSp"];
         sp0["barSp"].scaleX = per0;
      }
      
      private function showPro(d0:GeneDefine) : void
      {
         var labelArr0:Array = null;
         var goodDefine0:GoodsDefine = null;
         labelArr0 = Gaming.defineGroup.goods.getDefineArrByDefineLabel(d0.name);
         this.shopSp.visible = false;
         this.gotoShopBtn.visible = false;
         this.gotoArenaBtn.visible = false;
         if(d0.superB && labelArr0.length > 0)
         {
            this.shopSp.visible = true;
            for each(goodDefine0 in labelArr0)
            {
               if(goodDefine0.priceType == PriceType.MONEY)
               {
                  this.gotoShopBtn.visible = true;
               }
               if(goodDefine0.priceType == PriceType.ARENA_STAMP)
               {
                  this.gotoArenaBtn.visible = true;
               }
            }
            return;
         }
         var pro0:Number = Gaming.PG.da.pet.saveGroup.map.getDropPro(d0.name);
         this.proBar.setPer(pro0);
         this.proBar.setText(Number(pro0 * 100).toFixed(2) + "%");
         var tipStr0:String = "1、消灭关卡中的" + PetCount.openLevel + "级以上的" + d0.cnName + "可提升基因体掉落概率。";
         tipStr0 += "\n2、一旦掉落该尸宠基因体，概率则会重新归零。";
         this.tipSp["txt"].text = tipStr0;
         this.mustBoxSp.visible = false;
         this.proBtn.visible = false;
      }
      
      private function showProMust(bb0:Boolean) : void
      {
         this.tipSp.visible = !bb0;
         this.mustBoxSp.visible = bb0;
      }
      
      private function proBtnClick(e:MouseEvent) : void
      {
      }
      
      private function proBtnOver(e:MouseEvent) : void
      {
         this.showProMust(true);
      }
      
      private function showMap(d0:GeneDefine) : void
      {
         var da0:WorldMapData = null;
         var bodyName0:String = d0.targetBodyName;
         var dataArr0:Array = Gaming.PG.da.worldMap.getSortDataArrByEnemyName(bodyName0,PetCount.openLevel);
         var name0:String = "";
         var go0:String = "";
         for each(da0 in dataArr0)
         {
            name0 += ComMethod.color(da0.tempLevel + "","#FFFF00") + "·" + da0.getMapCnName() + "\n";
            go0 += ComMethod.link("进入",da0.getName()) + "\n";
         }
         this.mapNameTxt.htmlText = name0;
         this.mapGoTxt.htmlText = go0;
      }
      
      private function mapGoLink(e:TextEvent) : void
      {
         var mapName0:String = e.text;
         WorldMapBox.gotoLevelByWorldMapId(mapName0);
      }
      
      private function shopBtnClick(e:MouseEvent) : void
      {
         if(e.target == this.gotoShopBtn)
         {
            UIShow.showByLabel("shop");
            Gaming.uiGroup.shopUI.showBox("normal",this.nowGeneDefine.name,"gene");
         }
         else if(e.target == this.gotoArenaBtn)
         {
            if(!UIOrder.zuobiPan())
            {
               UIShow.showByLabel("arena");
               Gaming.uiGroup.arenaUI.showBox("exchange");
            }
         }
      }
   }
}

