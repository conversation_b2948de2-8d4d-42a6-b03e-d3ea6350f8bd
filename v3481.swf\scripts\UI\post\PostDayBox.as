package UI.post
{
   import UI.NormalUICtrl;
   import UI.api.shop.ShopBuyObject;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._app.post.PostData;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class PostDayBox extends AutoNormalUI
   {
      private var giftTag:Sprite;
      
      private var giftBox:ItemsGripBox = new ItemsGripBox();
      
      private var bribeBtn:NormalBtn;
      
      private var getBtn:NormalBtn;
      
      private var dropBtn:NormalBtn;
      
      private var txt:TextField;
      
      private var titleTxt:TextField;
      
      private var txtTipString:* = "";
      
      private var nowData:PostData;
      
      public var postUI:PostUI;
      
      public function PostDayBox()
      {
         super();
         mcTypeArr = ["txt","tag","btnSp"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         FontDeal.dealOne(this.titleTxt);
         FontDeal.dealOne(this.txt);
         if(Boolean(this.giftTag))
         {
            this.giftTag.addChild(this.giftBox);
            this.giftBox.arg.init(7,3,14,14);
            this.giftBox.setIconPro("equipGrip");
            this.giftBox.evt.setWantEvent(true,false,false,true,true);
            ItemsGripTipCtrl.addEvent_byItemsGripBox(this.giftBox);
         }
         if(Boolean(this.txt))
         {
            this.txt.addEventListener(MouseEvent.MOUSE_OVER,this.txtOver);
            this.txt.addEventListener(MouseEvent.MOUSE_OUT,this.txtOut);
         }
         if(Boolean(this.bribeBtn))
         {
            ItemsGripTipCtrl.addNormalBtnTip(this.bribeBtn);
         }
         if(Boolean(this.getBtn))
         {
            ItemsGripTipCtrl.addNormalBtnTip(this.getBtn);
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var funName0:String = UILabel + "_" + btn0.label + "BtnClick";
         if(this.hasOwnProperty(funName0))
         {
            this[funName0](e);
         }
      }
      
      public function inData(da0:PostData) : void
      {
         if(da0.havePostB())
         {
            this.nowData = da0;
            this[UILabel + "Data"](da0);
         }
         else
         {
            this.clearData();
         }
      }
      
      private function fleshData() : void
      {
         if(Boolean(this.nowData))
         {
            this.inData(this.nowData);
         }
         else
         {
            this.clearData();
         }
      }
      
      private function clearData() : void
      {
         this.nowData = null;
         this.giftBox.clearAllData();
         if(Boolean(this.bribeBtn))
         {
            this.bribeBtn.actived = false;
         }
         if(Boolean(this.getBtn))
         {
            this.getBtn.actived = false;
         }
         if(Boolean(this.getBtn))
         {
            this.getBtn.actived = false;
         }
         if(Boolean(this.dropBtn))
         {
            this.dropBtn.visible = false;
         }
         if(Boolean(this.txt))
         {
            this.txt.text = "";
         }
      }
      
      private function getNowTimeStr() : String
      {
         return Gaming.api.save.getNowServerDate().getStr();
      }
      
      private function txtOver(e:MouseEvent) : void
      {
         if(this.txtTipString != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(this.txtTipString);
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      private function txtOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function dayData(da0:PostData) : void
      {
         var gift0:GiftAddDefineGroup = da0.save.getGift(this.getNowTimeStr());
         this.giftBox.inData_byArr(gift0.arr,"inData_gift");
         this.giftBox.setAllPro("FTipB",true);
         NormalUICtrl.alignMiddle(this.giftTag,this.titleTxt);
         this.getBtn.actived = gift0.arr.length > 0;
         this.getBtn.setName(gift0.arr.length > 0 ? "领奖" : "已领取");
         this.getBtn.tipString = "奖励未领取可累计，最多累计7天。";
         this.bribeBtn.setName("游说");
         this.bribeBtn.actived = true;
         this.bribeBtn.tipString = "今日已经游说了<green  " + da0.save.bribe + "/> 次";
         this.bribeBtn.tipString += "\n\n游说完毕之后到了第二天，将额外获得1~3个的联邦工资包和1~2个元首礼包（联邦总统专属），如果游说次数>1，则礼包数量会根据游说次数进行翻倍。";
      }
      
      public function day_getBtnClick(e:MouseEvent) : void
      {
         var da0:PostData = this.nowData;
         var gift0:GiftAddDefineGroup = da0.save.getGift(this.getNowTimeStr());
         var bb0:Boolean = GiftAddit.addAndAutoBagSpacePan(gift0);
         if(bb0)
         {
            da0.save.getGiftEvent(this.getNowTimeStr());
            this.postUI.fleshData();
         }
      }
      
      public function day_bribeBtnClick(e:MouseEvent) : void
      {
         var da0:PostData = this.nowData;
         var goodDa0:GoodsData = this.getBribeGoodsData();
         Gaming.uiGroup.alertBox.shop.showCheck(goodDa0,this.yes_bribe);
      }
      
      private function getBribeGoodsData() : GoodsData
      {
         var da0:GoodsData = new GoodsData();
         var d0:GoodsDefine = Gaming.defineGroup.goods.getDefine("postBribe");
         da0.def = d0;
         da0.playerData = Gaming.PG.da;
         da0.showTextType = "postBribe";
         return da0;
      }
      
      private function yes_bribe() : void
      {
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         var shopObj0:ShopBuyObject = da0.getShopObj();
         Gaming.uiGroup.connectUI.show();
         Gaming.api.shop.buyPropNd(shopObj0,this.do_bribe);
      }
      
      private function do_bribe() : void
      {
         Gaming.uiGroup.connectUI.hide();
         var goodDa0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         var da0:PostData = this.nowData;
         Gaming.PG.da.goods.addBuyNum(goodDa0.def.name,goodDa0.nowNum);
         da0.save.addBribe(goodDa0.getTrueNum());
         this.postUI.fleshData();
         Gaming.uiGroup.mainUI.fleshCoin();
      }
      
      private function levelData(da0:PostData) : void
      {
         var exp0:Number = da0.save.totalGiftExp;
         var day0:int = da0.save.expDay;
         var str0:String = "累计可领取经验值：" + exp0;
         str0 += "\n已累计天数：" + day0 + "天";
         this.txtText = str0;
         this.getBtn.actived = exp0 > 0;
         this.txtTipString = "当你未领取经验值时，经验值会不断累计，最多累计7天。";
         this.getBtn.tipString = this.txtTipString;
      }
      
      public function level_getBtnClick(e:MouseEvent) : void
      {
         var da0:PostData = this.nowData;
         var exp0:Number = da0.save.totalGiftExp;
         Gaming.PG.da.addExp(exp0);
         da0.save.getBodyExpEvent(this.getNowTimeStr());
         Gaming.uiGroup.alertBox.showSuccess("领取经验成功！");
         this.postUI.fleshData();
      }
      
      private function privilegeData(da0:PostData) : void
      {
         var tf0:TextFormat = null;
         this.txtText = da0.getNowPostDefine().getPrivilegeStr();
         this.dropBtn.visible = da0.haveDropRightB();
         if(this.dropBtn.visible)
         {
            this.dropBtn.isChosen = da0.save.dropB;
            tf0 = this.txt.defaultTextFormat;
            this.dropBtn.y = this.txt.y + (tf0.leading + int(tf0.size)) * 5 - 4;
         }
      }
      
      public function privilege_dropBtnClick(e:MouseEvent) : void
      {
         var da0:PostData = this.nowData;
         da0.save.dropB = !da0.save.dropB;
         this.fleshData();
      }
      
      private function proData(da0:PostData) : void
      {
         this.txtText = da0.getProAddStr();
         this.txtTipString = "职务等级越高，属性加成就越高。";
      }
      
      private function set txtText(str0:String) : void
      {
         this.txt.htmlText = FontDeal.getDealLeadingStr(this.txt,str0);
      }
   }
}

