package UI.lottery
{
   import com.sounto.utils.ArrayMethod;
   import dataAll._app.parts.PartsCreator;
   import dataAll._app.parts.define.PartsConst;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._data.ConstantDefine;
   import dataAll._player.base.PlayerBaseData;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.level.LevelDiffGetting;
   
   public class NormalLotteryGift
   {
      public function NormalLotteryGift()
      {
         super();
      }
      
      public function getGift(lv0:int, diff0:int, mapMode0:String, mapD0:WorldMapDefine) : GiftAddDefineGroup
      {
         var d0:GiftAddDefineGroup = new GiftAddDefineGroup();
         var heroLv0:int = Gaming.PG.da.level;
         diff0 = int(diff0);
         if(diff0 < 0)
         {
            diff0 = 0;
         }
         if(diff0 > LevelDiffGetting.getDiffLenByMode(mapMode0,mapD0) - 1)
         {
            diff0 = 0;
         }
         d0.addGiftByStr(this.coin(lv0,diff0,mapMode0)).pro = heroLv0 >= PlayerBaseData.MAX_LEVEL ? 600 : 300;
         d0.addGiftByStr(this.exp(lv0,diff0,mapMode0)).pro = heroLv0 >= PlayerBaseData.MAX_LEVEL ? 0 : 300;
         d0.addGiftByStr(this.skillStone(lv0,diff0,mapMode0)).pro = 100;
         d0.addGiftByStr(this.godStone(lv0,diff0,mapMode0)).pro = 30;
         d0.addGiftByStr(this.converStone(lv0,diff0,mapMode0)).pro = 30;
         if(lv0 >= 30)
         {
            d0.addGift(this.taxStampAndDevice(lv0,diff0,mapMode0));
         }
         d0.addGift(this.armsBoxAndWeapon(lv0,diff0,mapMode0));
         d0.addGift(this.equipBoxAndKey(lv0,diff0,mapMode0));
         if(lv0 >= 15)
         {
            d0.addGift(this.parts(lv0,diff0,mapMode0));
            d0.addGift(this.partsAndNormalChest(lv0,diff0,mapMode0));
         }
         d0.addNumMul(5);
         return d0;
      }
      
      private function taxStampAndDevice(lv0:int, diff0:int, mapMode0:String) : GiftAddDefine
      {
         if(lv0 >= 70)
         {
            return this.deviceChest(lv0,diff0,mapMode0);
         }
         return this.taxStamp(lv0,diff0,mapMode0);
      }
      
      private function taxStamp(lv0:int, diff0:int, mapMode0:String) : GiftAddDefine
      {
         var d0:GiftAddDefine = new GiftAddDefine();
         var num0:int = Math.ceil(this.getLevelPro(lv0) * 10) * this.getDiffMul(diff0,mapMode0);
         num0 = Math.ceil(num0);
         d0.inData_byStr("things;taxStamp;" + num0);
         d0.pro = 100;
         return d0;
      }
      
      private function deviceChest(lv0:int, diff0:int, mapMode0:String) : GiftAddDefine
      {
         var d0:GiftAddDefine = new GiftAddDefine();
         d0.inData_byStr("things;deviceChest;3");
         d0.pro = 20 * this.getNewDiffMul(diff0,mapMode0);
         return d0;
      }
      
      private function armsBoxAndWeapon(lv0:int, diff0:int, mapMode0:String) : GiftAddDefine
      {
         if(lv0 >= 70)
         {
            return this.weaponChest(lv0,diff0,mapMode0);
         }
         return this.armsBox(lv0,diff0,mapMode0);
      }
      
      private function armsBox(lv0:int, diff0:int, mapMode0:String) : GiftAddDefine
      {
         var d0:GiftAddDefine = null;
         d0 = new GiftAddDefine();
         var num0:int = Math.ceil(this.getLevelPro(lv0) * 3) * this.getDiffMul(diff0,mapMode0);
         num0 = Math.ceil(num0);
         d0.inData_byStr("things;armsBox;" + num0);
         d0.pro = 250;
         return d0;
      }
      
      private function weaponChest(lv0:int, diff0:int, mapMode0:String) : GiftAddDefine
      {
         var d0:GiftAddDefine = new GiftAddDefine();
         d0.inData_byStr("things;weaponChest;3");
         d0.pro = 20 * this.getNewDiffMul(diff0,mapMode0);
         return d0;
      }
      
      private function equipBoxAndKey(lv0:int, diff0:int, mapMode0:String) : GiftAddDefine
      {
         if(lv0 >= 90)
         {
            return this.blackChip(lv0,diff0,mapMode0);
         }
         if(lv0 >= 76)
         {
            return this.keyChest(lv0,diff0,mapMode0);
         }
         return this.equipBox(lv0,diff0,mapMode0);
      }
      
      private function equipBox(lv0:int, diff0:int, mapMode0:String) : GiftAddDefine
      {
         var d0:GiftAddDefine = new GiftAddDefine();
         var num0:int = Math.ceil(this.getLevelPro(lv0) * 3) * this.getDiffMul(diff0,mapMode0);
         num0 = Math.ceil(num0);
         d0.inData_byStr("things;equipBox;" + num0);
         d0.pro = 250;
         return d0;
      }
      
      private function keyChest(lv0:int, diff0:int, mapMode0:String) : GiftAddDefine
      {
         var d0:GiftAddDefine = new GiftAddDefine();
         d0.inData_byStr("things;keyChest;3");
         d0.pro = 30 * this.getNewDiffMul(diff0,mapMode0);
         return d0;
      }
      
      private function blackChip(lv0:int, diff0:int, mapMode0:String) : GiftAddDefine
      {
         var nameArr0:Array = ["armsTitanium","armsRadium","allBlackCash","allBlackEquipCash"];
         var name0:String = ArrayMethod.getRandomOne(nameArr0);
         var d0:GiftAddDefine = new GiftAddDefine();
         d0.inData_byStr("things;" + name0 + ";" + 3);
         d0.pro = 40 * this.getNewDiffMul(diff0,mapMode0);
         return d0;
      }
      
      private function partsAndNormalChest(lv0:int, diff0:int, mapMode0:String) : GiftAddDefine
      {
         if(lv0 >= 50)
         {
            return this.normalChest(lv0,diff0,mapMode0);
         }
         return this.parts(lv0,diff0,mapMode0);
      }
      
      private function normalChest(lv0:int, diff0:int, mapMode0:String) : GiftAddDefine
      {
         var d0:GiftAddDefine = new GiftAddDefine();
         d0.inData_byStr("things;normalChest;1");
         d0.pro = 3.5 * this.getDiffMul(diff0,mapMode0);
         return d0;
      }
      
      private function parts(enemyLv0:int, diff0:int, mapMode0:String) : GiftAddDefine
      {
         enemyLv0 += 1;
         var lv0:int = PartsCreator.getDecomposeLv("purple",enemyLv0,Gaming.PG.da.level);
         var cLv0:int = PartsConst.cLv;
         var cx0:int = lv0 % cLv0;
         var true_lv0:int = int(lv0 / cLv0) * cLv0;
         var num0:int = Math.pow(2,cx0) * this.getDiffMul(diff0,mapMode0);
         num0 = Math.ceil(num0);
         var nameArr0:Array = Gaming.defineGroup.things.normalPartsNameArr;
         var name0:String = nameArr0[int(Math.random() * nameArr0.length)] + "_" + true_lv0;
         if(num0 > 15)
         {
            num0 = 15;
         }
         var d0:GiftAddDefine = new GiftAddDefine();
         d0.inData_byStr("parts;" + name0 + ";" + num0);
         d0.pro = 30;
         return d0;
      }
      
      private function coin(lv0:int, diff0:int, mapMode0:String) : String
      {
         var mul0:Number = LevelDiffGetting.getCoinMulBy(diff0,mapMode0);
         var num0:Number = Gaming.defineGroup.normal.getLevelCoinIncome(lv0) * 0.5 * mul0;
         num0 = Math.ceil(num0);
         return "base;coin;" + num0;
      }
      
      private function exp(lv0:int, diff0:int, mapMode0:String) : String
      {
         var mul0:Number = LevelDiffGetting.getExpMulBy(diff0,mapMode0,"");
         var num0:Number = Gaming.defineGroup.normal.getLevelEnemyExp(lv0) * 0.5 * mul0;
         num0 = Math.ceil(num0);
         return "base;exp;" + num0;
      }
      
      private function skillStone(lv0:int, diff0:int, mapMode0:String) : String
      {
         var num0:int = Math.ceil(this.getLevelPro(lv0) * 5) * this.getDiffMul(diff0,mapMode0);
         num0 = Math.ceil(num0);
         if(num0 > 10)
         {
            num0 = 10;
         }
         return "things;skillStone;" + num0;
      }
      
      private function godStone(lv0:int, diff0:int, mapMode0:String) : String
      {
         var num0:int = Math.ceil(this.getLevelPro(lv0) * 2) * this.getDiffMul(diff0,mapMode0);
         num0 = Math.ceil(num0);
         if(num0 > 10)
         {
            num0 = 10;
         }
         return "things;godStone;" + num0;
      }
      
      private function converStone(lv0:int, diff0:int, mapMode0:String) : String
      {
         var num0:int = Math.ceil(this.getLevelPro(lv0) * 2) * this.getDiffMul(diff0,mapMode0);
         num0 = Math.ceil(num0);
         if(num0 > 10)
         {
            num0 = 10;
         }
         return "things;converStone;" + num0;
      }
      
      private function getLevelPro(lv0:int) : Number
      {
         return ConstantDefine.getLevelPro(Gaming.PG.da.level,lv0);
      }
      
      private function getDiffMul(diff0:int, mapMode0:String) : Number
      {
         return LevelDiffGetting.getLotteryMul(diff0,mapMode0);
      }
      
      private function getNewDiffMul(diff0:int, mapMode0:String) : Number
      {
         return LevelDiffGetting.getNewLotteryMul(diff0,mapMode0);
      }
   }
}

