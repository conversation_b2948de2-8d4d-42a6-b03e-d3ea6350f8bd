package UI.top
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.base.AppNormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import UI.base.label.MoreLabelBox;
   import UI.base.scroll.NormalScrollBar;
   import dataAll._app.top.define.TopBarDefineGroup;
   import dataAll.ui.label.LabelAddData;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class TopUI extends AppNormalUI
   {
      private var timeEnabled:Boolean = false;
      
      public var labelBox:LabelBox = new LabelBox();
      
      private var topBoard:TopBoard = new TopBoard();
      
      private var labelNameArr:Array = [];
      
      private var nowLabel:String = "";
      
      private var leftTag:Sprite;
      
      private var closeBtn:SimpleButton;
      
      private var scrollBar:NormalScrollBar;
      
      private var leftBox:MoreLabelBox = new MoreLabelBox();
      
      public var nowType:String = "";
      
      public var nowChildType:String = "";
      
      public function TopUI()
      {
         super();
         UICn = "排行榜";
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["leftTag","closeBtn"];
         super.setImg(img0);
         img0.addChild(this.leftBox);
         NormalUICtrl.setTag(this.leftBox,this.leftTag);
         this.addLeftLabel();
         this.scrollBar = new NormalScrollBar(this.leftBox,img0["maskTargetSp"],img0["scrollBarSp"],img0["scrollLineSp"],2,false,true,true);
         this.scrollBar.speed = 60;
         this.scrollBar.refresh();
         addChild(this.topBoard);
         this.topBoard.setImg(Gaming.swfLoaderManager.getResource("TopUI","topBoard"));
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function addLeftLabel() : void
      {
         var da0:LabelAddData = Gaming.defineGroup.top.getNormalTopLabelAddData();
         this.leftBox.inDataByLabelAddData(da0);
         this.leftBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.leftBox.addEventListener(ClickEvent.ON_OVER,this.labelOver);
         this.leftBox.addEventListener(ClickEvent.ON_OUT,this.labelOut);
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         var strArr0:Array = e.fatherUrl.split("/");
         var type0:String = strArr0[strArr0.length - 2];
         var childType0:String = e.label;
         if(e.father != e.target)
         {
            this.showLabel(type0,childType0);
         }
         this.scrollBar.refresh();
      }
      
      private function labelOver(e:ClickEvent) : void
      {
         var d0:TopBarDefineGroup = null;
         Gaming.uiGroup.tipBox.hide();
         var strArr0:Array = e.fatherUrl.split("/");
         var type0:String = strArr0[strArr0.length - 2];
         var childType0:String = e.label;
         if(e.father != e.target)
         {
            d0 = Gaming.defineGroup.top.getDefine(childType0);
            if(d0.info != "")
            {
               Gaming.uiGroup.tipBox.textTip.showFollowText(d0.info);
            }
         }
      }
      
      private function labelOut(e:ClickEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function showLabel(type0:String, childType0:String) : void
      {
         if(type0 == "")
         {
            type0 = "main";
            childType0 = "dps";
            this.leftBox.showChildVisibleByName(type0,childType0);
         }
         this.nowType = type0;
         this.nowChildType = childType0;
         this.topBoard.start(childType0);
      }
      
      override public function show() : void
      {
         UIOrder.alertError("排行榜暂时关闭。");
         this.hide();
      }
      
      override public function hide() : void
      {
         super.hide();
         this.timeEnabled = false;
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.hide();
      }
      
      public function FTimer() : void
      {
         if(this.timeEnabled)
         {
            this.topBoard.FTimer();
         }
      }
   }
}

