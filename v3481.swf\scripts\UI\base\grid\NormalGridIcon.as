package UI.base.grid
{
   import UI.base.box.ArrangeSprite;
   import com.common.net.SaveImage;
   import com.sounto.image.BmpEffectData;
   import com.sounto.utils.BmpMethod;
   import dataAll.arms.creator.GunImage;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.geom.Rectangle;
   
   public class NormalGridIcon extends ArrangeSprite
   {
      private var bitmap:Bitmap = null;
      
      private var icon:Sprite = null;
      
      private var iconName:String = "";
      
      public function NormalGridIcon()
      {
         super();
      }
      
      public function saveToPng(cn0:String) : void
      {
         if(Boolean(this.bitmap))
         {
            SaveImage.SaveBmp(this.bitmap.bitmapData,cn0,true);
         }
         else if(Boolean(this.icon))
         {
            if(this.icon.width > 0 && this.icon.height > 0)
            {
               SaveImage.SaveBmp(BmpMethod.getBmp(this.icon),cn0,true);
            }
         }
      }
      
      public function setIconName(str0:String, middleB0:Boolean = true) : void
      {
         if(str0 == "")
         {
            this.clearData();
         }
         else
         {
            this.showIcon();
            if(this.iconName != str0)
            {
               this.clear();
               this.iconName = str0;
               if(str0.indexOf("$") >= 0)
               {
                  this.showBmp(str0,middleB0);
               }
               else
               {
                  this.showSp(str0,middleB0);
               }
            }
            else if(str0.indexOf("$") >= 0 && !(Gaming.gunImageManager.getImage(str0,false) is BmpEffectData))
            {
               this.clear();
               this.showBmp(str0,middleB0);
            }
         }
      }
      
      public function getIconName() : String
      {
         return this.iconName;
      }
      
      private function showBmp(str0:String, middleB0:Boolean) : void
      {
         if(!this.bitmap)
         {
            this.bitmap = new Bitmap(null,"auto",true);
         }
         var gunImg0:GunImage = Gaming.gunImageManager.getImage(str0,true);
         if(!gunImg0)
         {
            INIT.showError("找不到武器图像：" + str0);
         }
         this.bitmap.bitmapData = gunImg0.iconBmp;
         this.bitmap.smoothing = true;
         this.setIconPosition(this.bitmap,middleB0);
      }
      
      private function showSp(str0:String, middleB0:Boolean) : void
      {
         var sp0:Sprite = null;
         var bitmapData0:BitmapData = null;
         var bmp0:BmpEffectData = Gaming.EG.bmpEffectM.getBmpFull(str0);
         if(Boolean(bmp0))
         {
            bitmapData0 = bmp0.getFirstBmp();
         }
         if(!bitmapData0)
         {
            bitmapData0 = Gaming.swfLoaderManager.getResourceFull(str0) as BitmapData;
         }
         if(Boolean(bitmapData0))
         {
            if(!this.bitmap)
            {
               this.bitmap = new Bitmap(null,"auto",true);
            }
            this.bitmap.bitmapData = bitmapData0;
            this.bitmap.smoothing = true;
            this.setIconPosition(this.bitmap,middleB0);
         }
         else
         {
            sp0 = Gaming.swfLoaderManager.getResourceFull(str0);
            if(Boolean(sp0))
            {
               this.icon = sp0;
               this.setIconPosition(this.icon,middleB0);
            }
            else
            {
               trace("找不到图标：" + str0);
            }
         }
      }
      
      private function showIcon() : void
      {
         if(Boolean(this.bitmap))
         {
            this.bitmap.visible = true;
         }
         if(Boolean(this.icon))
         {
            this.icon.visible = true;
         }
      }
      
      public function clearData() : void
      {
         if(Boolean(this.bitmap))
         {
            this.bitmap.visible = false;
         }
         if(Boolean(this.icon))
         {
            this.icon.visible = false;
         }
      }
      
      public function stopAll() : void
      {
         if(Boolean(this.icon))
         {
            (this.icon as MovieClip).stop();
         }
      }
      
      public function playAll() : void
      {
         if(Boolean(this.icon))
         {
            (this.icon as MovieClip).play();
         }
      }
      
      public function haveDataB() : Boolean
      {
         if(Boolean(this.bitmap))
         {
            if(Boolean(this.bitmap.bitmapData))
            {
               return true;
            }
         }
         if(Boolean(this.icon))
         {
            return true;
         }
         return false;
      }
      
      public function clear() : void
      {
         if(Boolean(this.bitmap))
         {
            this.bitmap.bitmapData = null;
            if(Boolean(this.bitmap.parent))
            {
               this.bitmap.parent.removeChild(this.bitmap);
            }
         }
         if(Boolean(this.icon))
         {
            if(Boolean(this.icon.parent))
            {
               this.icon.parent.removeChild(this.icon);
               this.icon = null;
            }
         }
         this.iconName = "";
      }
      
      public function removeIcon() : Sprite
      {
         var icon0:Sprite = this.icon;
         this.clear();
         return icon0;
      }
      
      private function setIconPosition(mc0:DisplayObject, middleB0:Boolean) : void
      {
         var rect0:Rectangle = null;
         addChild(mc0);
         if(middleB0)
         {
            rect0 = mc0.getRect(mc0);
            mc0.x = -rect0.x - rect0.width / 2;
            mc0.y = -rect0.y - rect0.height / 2;
         }
         else
         {
            mc0.x = 0;
            mc0.y = 0;
         }
      }
   }
}

