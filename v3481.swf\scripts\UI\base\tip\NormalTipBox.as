package UI.base.tip
{
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class NormalTipBox extends Sprite
   {
      public var tipBox:TipBox = null;
      
      protected var valueTxt:OneTextGather = new OneTextGather();
      
      public var MIN_WIDTH:int = 50;
      
      protected var w:int = 0;
      
      protected var h:int = 0;
      
      public function NormalTipBox()
      {
         super();
         this.mouseChildren = false;
         this.mouseEnabled = false;
         this.addChild(this.valueTxt);
         this.valueTxt.x = 20;
         this.valueTxt.y = 15;
         this.valueTxt.TEXT2_MAX_X = 75;
         this.valueTxt.MAX_WIDTH = 250;
      }
      
      public function init() : void
      {
         this.valueTxt.init();
      }
      
      public function setText(str0:String) : void
      {
         this.valueTxt.setText(str0);
         this.fleshSize();
      }
      
      public function showFollowText(str0:String, followB0:Boolean = true) : void
      {
         if(str0 == "")
         {
            this.tipBox.hide();
         }
         else
         {
            this.setText(str0);
            this.show();
            this.tipBox.followMouseB = followB0;
         }
      }
      
      protected function fleshSize() : void
      {
         var w0:int = this.valueTxt.x + this.valueTxt.width;
         var h0:int = this.valueTxt.y + this.valueTxt.height + 10;
         var max0:int = w0;
         if(max0 < this.MIN_WIDTH)
         {
            max0 = this.MIN_WIDTH;
         }
         w0 = max0;
         this.w = w0;
         this.h = h0;
      }
      
      public function show() : void
      {
         var w0:int = this.w;
         var h0:int = this.h;
         this.tipBox.hideAllshowBack("text",w0,h0);
         this.visible = true;
         this.tipBox.show();
      }
      
      public function addOverBody(sp0:Sprite, overFun0:Function) : void
      {
         sp0.addEventListener(MouseEvent.MOUSE_OVER,overFun0);
         sp0.addEventListener(MouseEvent.MOUSE_OUT,this.bodyOut);
      }
      
      private function bodyOut(e:MouseEvent) : void
      {
         this.tipBox.hide();
      }
   }
}

