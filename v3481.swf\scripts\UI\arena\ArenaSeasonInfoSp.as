package UI.arena
{
   import UI.UIShow;
   import UI.base.NormalUI;
   import com.sounto.oldUtils.ComMethod;
   import flash.display.Sprite;
   import flash.events.TextEvent;
   import flash.text.TextField;
   
   public class ArenaSeasonInfoSp extends NormalUI
   {
      private var linkTxt:TextField;
      
      private var giftTxt:TextField;
      
      public function ArenaSeasonInfoSp()
      {
         super();
      }
      
      public function imgInit() : void
      {
         this.setImg(Gaming.swfLoaderManager.getResource("ArenaUI","arenaSeasonInfo"));
         this.giftTxt.visible = false;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["linkTxt","giftTxt"];
         super.setImg(img0);
         this.linkTxt.styleSheet = ComMethod.getLinkCss("#FFFF00","#FFFFFF");
         this.linkTxt.htmlText = "";
         this.linkTxt.addEventListener(TextEvent.LINK,this.linkClick);
         this.giftTxt.styleSheet = ComMethod.getLinkCss("#FFFF00","#FFFFFF");
         this.giftTxt.htmlText = ComMethod.link("领取补偿礼包>>","gift");
         this.giftTxt.addEventListener(TextEvent.LINK,this.linkClick);
         addChild(this.linkTxt);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function linkClick(e:TextEvent) : void
      {
         if(e.text == "arena")
         {
            UIShow.showByLabel("arenaSeason");
         }
         else if(e.text == "gift")
         {
            Gaming.uiGroup.alertBox.hide();
            Gaming.uiGroup.arenaUI.showBox("gift");
         }
      }
   }
}

