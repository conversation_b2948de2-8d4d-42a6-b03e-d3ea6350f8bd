package UI.gameWorld.skill
{
   import UI.base.grid.NormalGrid;
   import dataAll.skill.define.SkillDefine;
   import flash.display.MovieClip;
   import gameAll.body.skill.SkillData;
   
   public class GameWorldSkillGrip extends NormalGrid
   {
      private var sector:MovieClip = null;
      
      private var flashingMc:MovieClip;
      
      public var dat:SkillData = null;
      
      public function GameWorldSkillGrip()
      {
         super();
         iconMaxWidth;
      }
      
      override public function setImg(img0:MovieClip) : void
      {
         super.setImg(img0);
         this.sector = img0["sector"];
         this.flashingMc = img0["flashingMc"];
         if(Boolean(this.sector))
         {
            this.sector.stop();
            this.sector.visible = false;
         }
         if(Bo<PERSON>an(this.flashingMc))
         {
            this.flashingMc.stop();
         }
      }
      
      public function inData(da0:SkillData) : void
      {
         this.dat = da0;
         var d0:SkillDefine = da0.define;
         var url0:String = d0.getIconImgUrl(iconMaxWidth,iconMaxWidth);
         this.setState("fill");
         setIconName(url0);
         if(d0.isActiveB())
         {
            setLevelText(da0.getKeyText());
         }
         else
         {
            setLevelText("");
            this.setCd(1);
         }
         var num0:int = da0.getUINum();
         if(num0 < 9999)
         {
            if(num0 <= 0)
            {
               this.setCd(0);
            }
            else
            {
               this.setCd(1);
            }
            setPriceText(num0 + "");
         }
         else
         {
            setPriceText("");
            this.setCd(1);
         }
      }
      
      override public function setState(str0:String) : void
      {
         super.setState(str0);
         alpha = 0.4;
         if(state == "fill")
         {
            alpha = 1;
         }
      }
      
      public function setSilence(bb0:Boolean) : void
      {
         if(this.dat is SkillData)
         {
            if(!this.dat.define.isActiveB())
            {
               bb0 = false;
            }
         }
         else
         {
            bb0 = false;
         }
         setLockVisible(bb0);
      }
      
      public function playFlashing() : void
      {
         this.flashingMc.gotoAndPlay(2);
      }
      
      public function setCd(per0:Number, suplusCd0:Number = 0) : void
      {
         if(Boolean(this.sector))
         {
            if(per0 >= 1)
            {
               this.sector.visible = false;
               setNumText("");
            }
            else
            {
               if(per0 < 0)
               {
                  per0 = 0;
               }
               this.sector.visible = true;
               this.sector.gotoAndStop(int(per0 * this.sector.totalFrames));
               setNumText(int(suplusCd0) + "");
            }
         }
      }
      
      override public function clearData() : void
      {
         this.dat = null;
         super.clearData();
         this.setCd(1);
      }
      
      public function fleshEach() : void
      {
         var C_T0:Number = NaN;
         if(this.dat is SkillData)
         {
            if(this.dat.define.isActiveB())
            {
               C_T0 = this.dat.getCD_T();
               if(C_T0 > 0)
               {
                  this.setCd(this.dat.cd_t / C_T0,this.dat.getUITrueSurplusCd());
               }
               else
               {
                  this.setCd(1);
               }
            }
            else
            {
               setNumText(this.dat.getPassiveCdText());
            }
            if(this.dat.iconFleshingB)
            {
               this.dat.iconFleshingB = false;
               this.playFlashing();
            }
         }
      }
   }
}

