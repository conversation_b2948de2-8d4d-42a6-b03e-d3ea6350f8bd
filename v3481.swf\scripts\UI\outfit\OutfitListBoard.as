package UI.outfit
{
   import UI.bag.ItemsGripBox;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.grid.NormalGridIcon;
   import dataAll._app.outfit.OutfitData;
   import dataAll._app.outfit.define.OutfitDefine;
   import dataAll.skill.define.SkillDefine;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class OutfitListBoard extends NormalUI
   {
      private var gripTag:Sprite;
      
      private var iconTag:Sprite;
      
      private var nameTxt:TextField;
      
      private var skillNameTxt:TextField;
      
      private var skillTxt:TextField;
      
      private var mustTxt:TextField;
      
      private var mustNameTxt:TextField;
      
      private var icon:NormalGridIcon = new NormalGridIcon();
      
      private var itemsBox:ItemsGripBox = new ItemsGripBox();
      
      public var nowName:String = "";
      
      public function OutfitListBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["gripTag","iconTag","nameTxt","skillNameTxt","skillTxt","mustNameTxt","mustTxt"];
         super.setImg(img0);
         FontDeal.dealOne(this.nameTxt);
         FontDeal.dealOne(this.skillNameTxt);
         FontDeal.dealOne(this.skillTxt);
         FontDeal.dealOne(this.mustTxt);
         FontDeal.dealOne(this.mustNameTxt);
         addChild(this.itemsBox);
         this.itemsBox.setIconPro("OutfitUI/grip");
         this.itemsBox.arg.init(2,6,13,13);
         this.itemsBox.evt.setWantEvent(true,false,false,true,true);
         this.itemsBox.x = this.gripTag.x;
         this.itemsBox.y = this.gripTag.y;
         this.itemsBox.addEventListener(ClickEvent.ON_CLICK,this.barClick);
         this.iconTag.addChild(this.icon);
         this.clearData();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshList();
         this.showDataByName(this.nowName);
      }
      
      public function outLoginEvent() : void
      {
         this.clearData();
      }
      
      public function fleshList() : void
      {
         var dataArr0:Array = Gaming.PG.DATA.outfit.getDataArr();
         this.itemsBox.inData_byArr(dataArr0,"inData_outfitData");
      }
      
      private function showDataByName(name0:String) : void
      {
         var da0:OutfitData = null;
         var i0:NormalBtn = this.itemsBox.getBtnByLabel(name0);
         if(Boolean(i0))
         {
            da0 = i0.itemsData as OutfitData;
            this.showData(da0);
         }
      }
      
      private function showData(da0:OutfitData) : void
      {
         var d0:OutfitDefine = null;
         var skillD0:SkillDefine = null;
         this.clearData();
         if(da0 is OutfitData)
         {
            d0 = da0.define;
            skillD0 = d0.getSkillDefine();
            this.nowName = d0.name;
            this.itemsBox.setChoose(d0.name);
            this.icon.setIconName(d0.iconUrl);
            this.nameTxt.text = d0.cnName;
            this.skillNameTxt.htmlText = "·套件技能：" + skillD0.cnName;
            this.skillTxt.htmlText = FontDeal.getDealLeadingStr(this.skillTxt,skillD0.getDescription());
            this.mustNameTxt.text = "·套件组合所需";
            this.mustTxt.htmlText = FontDeal.getDealLeadingStr(this.mustTxt,da0.mustStr);
         }
      }
      
      private function clearData() : void
      {
         this.nowName = "";
         this.nameTxt.text = "";
         this.skillNameTxt.text = "";
         this.skillTxt.text = "";
         this.mustNameTxt.text = "";
         this.mustTxt.text = "";
         this.icon.clearData();
      }
      
      public function barClick(e:ClickEvent) : void
      {
         this.showData(e.childData as OutfitData);
      }
   }
}

