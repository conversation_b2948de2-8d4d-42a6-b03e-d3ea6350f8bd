package UI.arena.info
{
   import UI.arena.ArenaTopCtrl;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.StringDate;
   import dataAll._app.arena.ArenaData;
   import dataAll._app.arena.ArenaSave;
   import dataAll._app.top.define.TopBarDefineGroup;
   import dataAll._app.top.player.PlayerTopReturnData;
   import dataAll._player.PlayerData;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.system.System;
   import flash.text.TextField;
   
   public class ArenaInfoBox extends NormalUI
   {
      private var topBox:ArenaInfoNoGiftSp = new ArenaInfoNoGiftSp();
      
      private var topTxt:TextField;
      
      private var scoreTxt:TextField;
      
      private var numTxt:TextField;
      
      private var arenaInfoTxt:TextField;
      
      private var leftTxt:TextField;
      
      private var rightTxt:TextField;
      
      private var codeTxt:TextField;
      
      private var beforeTxt:TextField;
      
      private var topBtnSp:MovieClip;
      
      private var addBtnSp:MovieClip;
      
      private var copyBtnSp:MovieClip;
      
      private var topSp:Sprite;
      
      private var addBtn:NormalBtn = new NormalBtn();
      
      private var copyBtn:NormalBtn = new NormalBtn();
      
      private var topBtn:NormalBtn = new NormalBtn();
      
      public function ArenaInfoBox()
      {
         super();
      }
      
      public static function get arenaData() : ArenaData
      {
         return Gaming.PG.da.arena;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["topSp","beforeTxt","addBtnSp","topBtnSp","topTxt","scoreTxt","numTxt","arenaInfoTxt","leftTxt","rightTxt","codeTxt","copyBtnSp"];
         super.setImg(img0);
         FontDeal.dealOne(this.topTxt);
         FontDeal.dealOne(this.scoreTxt);
         FontDeal.dealOne(this.numTxt);
         FontDeal.dealOne(this.arenaInfoTxt);
         FontDeal.dealLine(this.leftTxt);
         FontDeal.dealLine(this.rightTxt);
         addChild(this.addBtn);
         this.addBtn.setImg(this.addBtnSp);
         this.addBtn.visible = false;
         addChild(this.topBtn);
         this.topBtn.setImg(this.topBtnSp);
         this.topBtn.activedAndEnabled = false;
         this.topBtn.addEventListener(MouseEvent.CLICK,this.topClick);
         ItemsGripTipCtrl.addNormalBtnTip(this.topBtn);
         addChild(this.copyBtn);
         this.copyBtn.setImg(this.copyBtnSp);
         this.copyBtn.setName("复制");
         this.copyBtn.addEventListener(MouseEvent.CLICK,this.copyClick);
         addChild(this.topBox);
         this.topBox.setImg(this.topSp);
         this.topBox.hide();
         this.scoreTxt.addEventListener(MouseEvent.MOUSE_OVER,this.scoreTxtOver);
         this.scoreTxt.addEventListener(MouseEvent.MOUSE_OUT,this.addBtnOut);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function outLoginEvent() : void
      {
      }
      
      public function fleshTop() : void
      {
         var d0:TopBarDefineGroup = arenaData.getTopBarDefineGroup();
         var str0:String = d0.cnName;
         if(str0 == "最高等级榜")
         {
            str0 = "最高榜";
         }
         this.topBtn.setName(str0);
         var topArr0:Array = arenaData.getCanChooseTopArr();
         if(topArr0.length <= 1)
         {
            this.topBtn.actived = false;
            this.topBtn.tipString = "";
         }
         else if(arenaData.save.topSetB || ArenaTopCtrl.noAddScoreB())
         {
            this.topBtn.tipString = "今天不能切换等级榜了。";
            this.topBtn.actived = false;
         }
         else
         {
            this.topBtn.tipString = "点击切换等级榜。";
            this.topBtn.actived = true;
         }
         this.topBox.hide();
      }
      
      public function inDataByPlayerReturnData(da0:PlayerTopReturnData = null) : void
      {
         this.topTxt.htmlText = Boolean(da0) ? da0.curRank + "" : "无";
      }
      
      public function fleshPlayerData(timeStr0:String = "") : void
      {
         var pd0:PlayerData = Gaming.PG.da;
         var left0:String = "";
         var right0:String = "";
         left0 += "名称";
         right0 += pd0.base.save.playerName;
         left0 += "\n战斗力";
         right0 += "\n" + pd0.getDps();
         left0 += "\n生命值";
         right0 += "\n" + pd0.base.getMaxLife();
         left0 += "\n头部防御";
         right0 += "\n" + pd0.base.getHeadDefence();
         this.leftTxt.text = left0;
         this.rightTxt.text = right0;
         this.codeTxt.text = "代码隐藏，点击复制后可查看。";
         var arenaDa0:ArenaData = pd0.arena;
         var arenaSave0:ArenaSave = pd0.arena.save;
         this.scoreTxt.text = arenaSave0.score + "";
         var todayNum0:int = arenaSave0.todayNum;
         this.numTxt.htmlText = "今日剩余挑战次数：" + ComMethod.color(todayNum0 + "",todayNum0 > 0 ? "#00FF00" : "#FF0000");
         var top0:String = "今日胜场：" + ComMethod.color(arenaSave0.winNum + "/" + arenaSave0.challengeNum,"#33FFFF");
         top0 += "\n今日连击：" + ComMethod.color(arenaSave0.streakNum + "","#33FFFF");
         this.arenaInfoTxt.htmlText = FontDeal.getDealLeadingStr(this.arenaInfoTxt,top0);
         var timeDate0:StringDate = new StringDate(timeStr0);
         if(timeStr0 == "")
         {
            timeDate0 = Gaming.api.save.getNowServerDate();
         }
      }
      
      private function scoreTxtOver(e:MouseEvent) : void
      {
         var str0:String = "";
         str0 += "1、获得积分可增加同等数量的优胜券。\n2、每个赛季结束后的周六凌晨将清零所有玩家的积分，但不改变优胜券数量。";
         Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
      }
      
      private function copyClick(e:MouseEvent) : void
      {
         this.codeTxt.text = Gaming.PG.loginData.getArenaCode();
         System.setClipboard(this.codeTxt.text);
         Gaming.uiGroup.alertBox.showSuccess("已成功复制到剪贴板！发给你的朋友，\n让他们在" + ComMethod.color("指定对手","#00FF00") + "界面中对你发起挑战。");
      }
      
      private function topClick(e:MouseEvent) : void
      {
         if(this.topBtn.actived)
         {
            this.topBox.show();
         }
      }
      
      private function addBtnOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
   }
}

