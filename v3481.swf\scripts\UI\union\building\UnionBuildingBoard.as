package UI.union.building
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.loadBar.LoadBar;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.union.building.UnionBuildingData;
   import dataAll._app.union.building.define.UnionBuildingDefine;
   import dataAll.pro.PropertyArrayDefine;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class UnionBuildingBoard extends AutoNormalUI
   {
      public static var nowData:UnionBuildingData = null;
      
      private var coverSp:Sprite;
      
      private var gripTag:Sprite;
      
      private var titleTxt:TextField;
      
      private var nameTxt:TextField;
      
      private var lvTxt:TextField;
      
      private var buildingLvTxt:TextField;
      
      private var refuseBtn:NormalBtn;
      
      private var loadBar:LoadBar;
      
      private var box:ItemsGripBox = new ItemsGripBox();
      
      private var watchmenBox:UnionWatchmenBox = new UnionWatchmenBox();
      
      private var cookingBox:UnionCookingBox = new UnionCookingBox();
      
      public var federalBox:UnionFederalBox = new UnionFederalBox();
      
      private var geologyBox:UnionGeologyBox = new UnionGeologyBox();
      
      private var boxObj:Object = {};
      
      private var uplevelBox:UnionBuildingUplevelBox = new UnionBuildingUplevelBox();
      
      public function UnionBuildingBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         FontDeal.dealOne(this.nameTxt);
         FontDeal.dealOne(this.lvTxt);
         FontDeal.dealOne(this.titleTxt);
         FontDeal.dealOne(this.buildingLvTxt);
         this.box.setIconPro("UnionUI/buildingGrip");
         this.box.arg.init(2,2,47,10);
         this.box.x = this.gripTag.x;
         this.box.y = this.gripTag.y;
         this.box.evt.setWantEvent(true,false,false,true,true);
         this.box.addEventListener(ClickEvent.ON_CLICK,this.boxClick);
         addChild(this.box);
         this.buildingLvTxt.addEventListener(MouseEvent.MOUSE_OVER,this.lvOver);
         this.buildingLvTxt.addEventListener(MouseEvent.MOUSE_OUT,this.lvOut);
         this.titleTxt.addEventListener(MouseEvent.MOUSE_OVER,this.lvOver);
         this.titleTxt.addEventListener(MouseEvent.MOUSE_OUT,this.lvOut);
         this.refuseBtn.setName("升级");
         addChild(this.uplevelBox);
         this.uplevelBox.hide();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function addIcon() : void
      {
         var dArr0:Array = null;
         var n:* = undefined;
         var d0:UnionBuildingDefine = null;
         var name0:String = null;
         var box0:AutoNormalUI = null;
         var grip0:ItemsGrid = null;
         if(this.box.gripArr.length == 0)
         {
            dArr0 = Gaming.defineGroup.union.building.arr;
            this.box.setNowGripNum(dArr0.length);
            for(n in dArr0)
            {
               d0 = dArr0[n];
               name0 = d0.name;
               box0 = this[name0 + "Box"];
               box0.setImg(Gaming.swfLoaderManager.getResource("UnionUI",name0 + "Box"));
               addChild(box0);
               this.boxObj[name0] = box0;
               grip0 = this.box.gripArr[n];
               grip0.setName(d0.cnName);
               grip0.setIconName(d0.iconUrl,false);
               grip0.label = name0;
               grip0.mouseIconEffectB = true;
            }
            addChild(this.coverSp);
            this.showBox("");
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.addIcon();
         this.fleshData();
         this.playAll();
      }
      
      public function outLoginEvent() : void
      {
         this.federalBox.outLoginEvent();
      }
      
      public function fleshData() : void
      {
         this.showBox(this.box.nowLabel);
         this.fleshIconData();
      }
      
      private function boxClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
         this.uplevelBox.hide();
      }
      
      private function showBox(label0:String) : void
      {
         var box0:AutoNormalUI = null;
         var nowBox0:AutoNormalUI = null;
         if(label0 == "" || !this.boxObj.hasOwnProperty(label0))
         {
            this.box.setChoose_byIndex(-1);
            this.coverSp.visible = true;
            this.showBoxInfo("");
         }
         else
         {
            this.coverSp.visible = false;
            nowBox0 = this.boxObj[label0];
         }
         for each(box0 in this.boxObj)
         {
            if(box0 == nowBox0)
            {
               box0.show();
               this.box.setChoose(label0);
               this.showBoxInfo(label0);
            }
            else
            {
               box0.hide();
            }
         }
      }
      
      private function fleshIconData() : void
      {
         var d0:UnionBuildingDefine = null;
         var da0:UnionBuildingData = null;
         var grip0:ItemsGrid = null;
         var dArr0:Array = Gaming.defineGroup.union.building.arr;
         for(var i:int = 0; i < dArr0.length; i++)
         {
            d0 = dArr0[i];
            da0 = Gaming.PG.da.union.building.getData(d0.name);
            grip0 = this.box.gripArr[i];
            grip0.activedAndGray = true;
            if(d0.hideB)
            {
               grip0.setLevelText("未开放");
            }
            else
            {
               grip0.setLevelText(ComMethod.color(da0.save.lv + "","#00FF00") + "/" + da0.getBuildingLv());
            }
            grip0.actived = !d0.hideB;
         }
      }
      
      private function lvOver(e:MouseEvent) : void
      {
         var lv0:int = 0;
         var min0:int = 0;
         var uStr0:String = null;
         var uStrLen0:int = 0;
         var max0:int = 0;
         var str0:String = "建筑等级由军队等级决定。\n\n<green 军队等级  建筑等级/>";
         var proD0:PropertyArrayDefine = Gaming.defineGroup.union.building.property.getDefine("levelRelevance");
         var arr0:Array = proD0.dataArr;
         var len0:int = int(arr0.length);
         if(Boolean(nowData))
         {
            len0 = nowData.getNowMaxLv();
         }
         for(var i:int = 0; i < len0; i++)
         {
            lv0 = i + 1;
            min0 = int(arr0[i]);
            if(min0 < 1)
            {
               min0 = 1;
            }
            uStr0 = "";
            uStrLen0 = 0;
            if(i < len0 - 1)
            {
               max0 = arr0[i + 1] - 1;
               uStr0 = min0 + "~" + max0;
               uStrLen0 = 11 - uStr0.length;
            }
            else
            {
               uStr0 = min0 + "以上";
               uStrLen0 = 11 - uStr0.length - 2;
            }
            str0 += "\n  " + uStr0 + TextWay.getRepeartStr(uStrLen0) + lv0;
         }
         Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
      }
      
      private function lvOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function showBoxInfo(label0:String) : void
      {
         var d0:UnionBuildingDefine = null;
         var da0:UnionBuildingData = Gaming.PG.da.union.building.getData(label0);
         nowData = da0;
         if(da0 is UnionBuildingData)
         {
            d0 = da0.def;
            this.titleTxt.text = d0.cnName;
            this.buildingLvTxt.x = this.titleTxt.width / 2 + this.titleTxt.x + d0.cnName.length * 15 / 2 + 5;
            this.buildingLvTxt.text = da0.getBuildingLv() + "级";
            this.nameTxt.text = d0.lvName;
            this.lvTxt.text = da0.save.lv + "";
            this.loadBar.setText(da0.getExpString());
            this.loadBar.setPer(da0.getExpPer());
            this.refuseBtn.actived = true;
         }
         else
         {
            this.titleTxt.text = "";
            this.buildingLvTxt.text = "";
            this.refuseBtn.actived = false;
         }
         this.uplevelBox.showData(da0);
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0 == this.refuseBtn)
         {
            if(this.uplevelBox.visible)
            {
               this.uplevelBox.hide();
            }
            else
            {
               this.uplevelBox.show();
            }
         }
      }
      
      public function stopAll() : void
      {
         this.box.stopAll();
         this.cookingBox.stopAll();
         this.federalBox.stopAll();
      }
      
      public function playAll() : void
      {
         this.box.playAll();
         this.cookingBox.playAll();
         this.federalBox.playAll();
      }
      
      public function FTimerSecond() : void
      {
         if(visible)
         {
            this.cookingBox.FTimerSecond();
            this.federalBox.FTimerSecond();
         }
      }
   }
}

