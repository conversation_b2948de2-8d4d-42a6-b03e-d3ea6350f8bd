package UI.base.backboard
{
   import com.sounto.oldUtils.ComMethod;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.Sprite;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class AutoBackboard extends Sprite
   {
      private var bmpArr:Array = [];
      
      private var minRect:Rectangle = new Rectangle();
      
      private var spRect:Rectangle = null;
      
      public function AutoBackboard()
      {
         super();
      }
      
      public function copy() : AutoBackboard
      {
         var b0:AutoBackboard = new AutoBackboard();
         b0.bmpArr = this.bmpArr;
         b0.minRect = this.minRect;
         b0.spRect = this.spRect;
         return b0;
      }
      
      public function haveImgB() : Boolean
      {
         return Boolean(this.spRect);
      }
      
      public function setImg(sp0:Sprite) : void
      {
         var n:* = undefined;
         var bmp00:Bitmap = null;
         if(this.haveImgB())
         {
            INIT.showError("只能设置一次图像");
         }
         var textRect0:Sprite = sp0["textRect"];
         if(!textRect0)
         {
            INIT.showError("AutoBackboard 输入的素材必须包含textRect作为切割矩形的依据。");
         }
         textRect0.visible = false;
         var rect0:Rectangle = sp0.getRect(sp0);
         this.spRect = rect0;
         var x1:int = textRect0.x - rect0.x;
         var y1:int = textRect0.y - rect0.y;
         var x2:int = textRect0.x + textRect0.width - rect0.x;
         var y2:int = textRect0.y + textRect0.height - rect0.y;
         var x3:int = rect0.width;
         var y3:int = rect0.height;
         this.minRect.x = x1;
         this.minRect.y = y1;
         this.minRect.width = textRect0.width;
         this.minRect.height = textRect0.height;
         var bmp0:BitmapData = ComMethod.getBmp(sp0);
         var arr0:Array = [];
         var p0:Point = new Point();
         arr0[0] = new BitmapData(x1,y1,true,0);
         arr0[0].copyPixels(bmp0,new Rectangle(0,0,x1,y1),p0);
         arr0[1] = new BitmapData(x2 - x1,y1,true,0);
         arr0[1].copyPixels(bmp0,new Rectangle(x1,0,x2 - x1,y1),p0);
         arr0[2] = new BitmapData(x3 - x2,y1,true,0);
         arr0[2].copyPixels(bmp0,new Rectangle(x2,0,x3 - x2,y1),p0);
         arr0[3] = new BitmapData(x1,y2 - y1,true,0);
         arr0[3].copyPixels(bmp0,new Rectangle(0,y1,x1,y2 - y1),p0);
         arr0[4] = new BitmapData(x2 - x1,y2 - y1,true,0);
         arr0[4].copyPixels(bmp0,new Rectangle(x1,y1,x2 - x1,y2 - y1),p0);
         arr0[5] = new BitmapData(x3 - x2,y2 - y1,true,0);
         arr0[5].copyPixels(bmp0,new Rectangle(x2,y1,x3 - x2,y2 - y1),p0);
         arr0[6] = new BitmapData(x1,y3 - y2,true,0);
         arr0[6].copyPixels(bmp0,new Rectangle(0,y2,x1,y3 - y2),p0);
         arr0[7] = new BitmapData(x2 - x1,y3 - y2,true,0);
         arr0[7].copyPixels(bmp0,new Rectangle(x1,y2,x2 - x1,y3 - y2),p0);
         arr0[8] = new BitmapData(x3 - x2,y3 - y2,true,0);
         arr0[8].copyPixels(bmp0,new Rectangle(x2,y2,x3 - x2,y3 - y2),p0);
         for(n in arr0)
         {
            bmp00 = new Bitmap(arr0[n],"auto",false);
            this.bmpArr[n] = bmp00;
            this.addChild(this.bmpArr[n]);
         }
         this.setSize(0,0);
      }
      
      public function setSize(w0:int, h0:int) : Boolean
      {
         var minB:Boolean = false;
         var w1:int = w0 - this.bmpArr[0].width - this.bmpArr[2].width;
         if(w1 < 0)
         {
            w1 = 0;
            minB = true;
         }
         var h1:int = h0 - this.bmpArr[0].height - this.bmpArr[6].height;
         if(h1 < 0)
         {
            h1 = 0;
            minB = true;
         }
         this.bmpArr[0].x = 0;
         this.bmpArr[0].y = 0;
         this.bmpArr[1].x = this.bmpArr[0].width;
         this.bmpArr[1].y = 0;
         this.bmpArr[1].width = w1;
         this.bmpArr[2].x = this.bmpArr[1].x + this.bmpArr[1].width;
         this.bmpArr[2].y = 0;
         this.bmpArr[3].x = 0;
         this.bmpArr[3].y = this.bmpArr[0].height;
         this.bmpArr[3].height = h1;
         this.bmpArr[4].x = this.bmpArr[0].width;
         this.bmpArr[4].y = this.bmpArr[0].height;
         this.bmpArr[4].width = w1;
         this.bmpArr[4].height = h1;
         this.bmpArr[5].x = this.bmpArr[1].x + this.bmpArr[1].width;
         this.bmpArr[5].y = this.bmpArr[0].height;
         this.bmpArr[5].height = h1;
         this.bmpArr[6].x = 0;
         this.bmpArr[6].y = this.bmpArr[3].y + this.bmpArr[3].height;
         this.bmpArr[7].x = this.bmpArr[0].width;
         this.bmpArr[7].y = this.bmpArr[3].y + this.bmpArr[3].height;
         this.bmpArr[7].width = w1;
         this.bmpArr[8].x = this.bmpArr[1].x + this.bmpArr[1].width;
         this.bmpArr[8].y = this.bmpArr[3].y + this.bmpArr[3].height;
         return minB;
      }
   }
}

