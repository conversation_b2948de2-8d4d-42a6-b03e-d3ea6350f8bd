package UI.city
{
   import UI.city.body.CityBody;
   import flash.events.Event;
   
   public class CityEvent extends Event
   {
      public static var ADD_CITY_BODY:String = "addCityBody";
      
      public static var REMOVE_CITY_BODY:String = "removeCityBody";
      
      public var targetBody:CityBody = null;
      
      public function CityEvent(_type:String = "addCityBody")
      {
         super(_type);
      }
      
      override public function clone() : Event
      {
         var newEvent:CityEvent = new CityEvent();
         newEvent.targetBody = this.targetBody;
         return newEvent;
      }
   }
}

