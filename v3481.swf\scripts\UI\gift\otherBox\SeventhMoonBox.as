package UI.gift.otherBox
{
   import UI.UIOrder;
   import UI.base._hide.HideNormalUI;
   import UI.base.button.NormalBtn;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.StringDate;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class SeventhMoonBox extends HideNormalUI
   {
      private var closeBtn:SimpleButton;
      
      private var codeTxt:TextField;
      
      private var infoTxt:TextField;
      
      private var giftBtnSp:MovieClip;
      
      private var giftBtn:NormalBtn = new NormalBtn();
      
      private var tempCount:SeventhMoonCount = null;
      
      public function SeventhMoonBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["closeBtn","giftBtnSp","codeTxt","infoTxt"];
         super.setImg(img0);
         addChild(this.giftBtn);
         this.giftBtn.setImg(this.giftBtnSp);
         this.giftBtn.setName("测试");
         this.giftBtn.addEventListener(MouseEvent.CLICK,this.giftBtnClick);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         this.infoTxt.htmlText = "   每天系统会根据玩家第一次测试的缘分指数来发放奖励，缘分指数越高，奖励越多。活动将于8月17日结束。";
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         var nowDa0:StringDate = Gaming.api.save.getNowServerDate();
         if(nowDa0.betweenIn("","2024-8-13") == 0)
         {
            Gaming.uiGroup.hideAllAppUI();
            super.show();
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("该活动已结束！");
         }
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         hide();
      }
      
      private function giftBtnClick(e:MouseEvent) : void
      {
         var uid0:String = null;
         var s0:SeventhMoonCount = null;
         var code0:String = TextWay.toHan2(this.codeTxt.text);
         var error0:String = "";
         if(code0 == "")
         {
            error0 = "姓名不能为空。";
         }
         else if(code0 == Gaming.PG.da.base.save.playerName)
         {
            error0 = "不能输入自己的昵称。";
         }
         else if(SeventhMoonCount.sevenGiftB() == false && SeventhMoonCount.isUseB(code0))
         {
            error0 = "该昵称已经使用过了。";
         }
         else
         {
            uid0 = Gaming.getUid();
            s0 = SeventhMoonCount.count(uid0,code0);
            this.tempCount = s0;
            if(Boolean(s0.gift))
            {
               Gaming.uiGroup.connectUI.show();
               UIOrder.save(false,true,false,this.yesGift,this.noGift);
            }
            else
            {
               this.yesGift();
            }
         }
         if(error0 != "")
         {
            Gaming.uiGroup.alertBox.showError(error0);
         }
      }
      
      private function yesGift(v0:* = null) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showSuccess(this.tempCount.tip);
      }
      
      private function noGift(v0:* = null) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError(v0);
      }
   }
}

