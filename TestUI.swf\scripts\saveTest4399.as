package
{
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol105")]
   public dynamic class saveTest4399 extends MovieClip
   {
      public var outBtn:SimpleButton;
      
      public var addBtn:SimpleButton;
      
      public var thingsInSaveBtn:SimpleButton;
      
      public var beforeTxt:TextField;
      
      public var converToGiftBtn:SimpleButton;
      
      public var affterTxt:TextField;
      
      public var addTxt:TextField;
      
      public var inputBeforeBtn:SimpleButton;
      
      public var inputAffterBtn:SimpleButton;
      
      public function saveTest4399()
      {
         super();
      }
   }
}

