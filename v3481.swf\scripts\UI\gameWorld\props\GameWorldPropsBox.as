package UI.gameWorld.props
{
   import UI.bag.ItemsGripBox;
   import UI.base.event.ClickEvent;
   import dataAll.things.ThingsData;
   import dataAll.things.ThingsProps;
   import flash.display.Sprite;
   import gameAll.more.DoubleCtrl;
   
   public class GameWorldPropsBox extends Sprite
   {
      private var box:ItemsGripBox = new ItemsGripBox();
      
      public function GameWorldPropsBox()
      {
         super();
         addChild(this.box);
         this.mouseEnabled = false;
      }
      
      public function imgInit() : void
      {
         this.box.arg.init(5,1,2,0);
         this.box.imgType = "GameWorldUI/smallThingsGrip";
         this.box.setNowGripNum(5);
         this.box.addEventListener(ClickEvent.ON_CLICK,this.click);
      }
      
      public function startLevel() : void
      {
         this.fleshData();
      }
      
      public function fleshData() : void
      {
         var dataArr0:Array = ThingsProps.getGameWorldPropsArr(Gaming.PG.da.thingsBag);
         this.box.inData_byArr(dataArr0,"inData_gameWorldProps");
         this.fleshCanUse();
      }
      
      private function fleshCanUse() : void
      {
         var allDieB0:Boolean = DoubleCtrl.isAllDieB();
         if(allDieB0)
         {
            this.box.setAllPro("actived",false);
            this.box.setAllFun("setLockVisible",true);
         }
      }
      
      public function click(e:ClickEvent) : void
      {
         var da0:ThingsData = e.childData as ThingsData;
         if(da0.save.nowNum > 0)
         {
            ThingsProps.gameWorldPropsEvent(Gaming.PG.da.thingsBag,da0.save.name);
         }
      }
   }
}

