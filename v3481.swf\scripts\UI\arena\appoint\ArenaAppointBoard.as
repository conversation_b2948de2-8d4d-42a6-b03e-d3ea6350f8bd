package UI.arena.appoint
{
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import com.common.data.Base64;
   import com.common.text.TextWay;
   import dataAll._app.top.TopBarData;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import gameAll.level.arena.ArenaCtrl;
   
   public class ArenaAppointBoard extends NormalUI
   {
      private var codeTxt:TextField;
      
      private var btnSp:MovieClip;
      
      private var btn:NormalBtn = new NormalBtn();
      
      public function ArenaAppointBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["codeTxt","btnSp"];
         super.setImg(img0);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("开始挑战");
         this.btn.addEventListener(MouseEvent.CLICK,this.click);
         this.codeTxt.text = "";
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function click(e:MouseEvent) : void
      {
         var da0:TopBarData = new TopBarData();
         var str0:String = this.codeTxt.text;
         str0 = TextWay.toHanSpace(TextWay.toHan2(str0));
         var str2:String = Base64.decodeString(str0);
         if(str2.indexOf("_") == -1 || str2.indexOf(",") == -1)
         {
            Gaming.uiGroup.alertBox.showError("玩家代码格式不正确。");
         }
         else
         {
            da0.inDataByArenaCode(str0);
            if(!da0.uid || da0.uid == "" || !da0.uname || da0.uname == "")
            {
               Gaming.uiGroup.alertBox.showError("玩家代码格式不正确。");
            }
            else
            {
               ArenaCtrl.chooseArivalByTopBarData(da0);
            }
         }
      }
   }
}

