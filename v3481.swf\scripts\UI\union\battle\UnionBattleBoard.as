package UI.union.battle
{
   import UI.UIOrder;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import UI.test.SaveTestBox;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.NumberMethod;
   import dataAll._app.union.UnionData;
   import dataAll._app.union.battle.UBattleAgent;
   import dataAll._app.union.battle.UBattleMapAgent;
   import dataAll._app.union.battle.UnionBattleMapDefine;
   import dataAll._app.union.info.MemberInfo;
   import dataAll._app.union.info.MemberListInfo;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class UnionBattleBoard extends AutoNormalUI
   {
      private var CF:NiuBiCF = new NiuBiCF();
      
      private var meTitleTxt:TextField;
      
      private var unionTitleTxt:TextField;
      
      private var proTxt:TextField;
      
      private var valueTxt:TextField;
      
      private var uproTxt:TextField;
      
      private var uvalueTxt:TextField;
      
      private var infoTxt:TextField;
      
      private var pointerTag:Sprite;
      
      private var giftBtn:NormalBtn;
      
      private var giftShowBtn:NormalBtn;
      
      private var mapBtnObj:Object = {};
      
      private var nowTextIndex:int = -1;
      
      private var nowMapAgent:UBattleMapAgent = null;
      
      public function UnionBattleBoard()
      {
         super();
         mcTypeArr = ["btnSp","txt","tag"];
         btnSetB = false;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function get unionData() : UnionData
      {
         return Gaming.PG.da.union;
      }
      
      private function get meMember() : MemberInfo
      {
         return this.unionData.nowMember;
      }
      
      private function get agent() : UBattleAgent
      {
         return this.unionData.battleAgent;
      }
      
      override protected function firstLoad() : void
      {
         FontDeal.dealStaticLine(this.meTitleTxt);
         FontDeal.dealStaticLine(this.unionTitleTxt);
         FontDeal.dealStaticLine(this.infoTxt);
         this.infoTxt.htmlText = FontDeal.getDealLeadingStr(this.infoTxt,"<b>周六8点~周日21点，可攻打战地。\n周一~周五，最佳领奖时间。</b>");
         FontDeal.dealOne(this.proTxt);
         FontDeal.dealOne(this.valueTxt);
         FontDeal.dealOne(this.uproTxt);
         FontDeal.dealOne(this.uvalueTxt);
         this.valueTxt.addEventListener(MouseEvent.MOUSE_MOVE,this.textMove);
         this.valueTxt.addEventListener(MouseEvent.MOUSE_OUT,this.textOut);
         this.uvalueTxt.addEventListener(MouseEvent.MOUSE_MOVE,this.textMove);
         this.uvalueTxt.addEventListener(MouseEvent.MOUSE_OUT,this.textOut);
         this.initMapBtn();
         this.giftBtn = getBtnByName("gift");
         this.giftBtn.setName("领取奖励");
         this.giftBtn.activedAndEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.giftBtn);
         this.giftShowBtn = getBtnByName("giftShow");
         this.giftShowBtn.buttonMode = false;
         this.giftShowBtn.addEventListener(MouseEvent.MOUSE_OVER,this.giftShowBtnOver);
         this.pointerTag.visible = false;
      }
      
      private function initMapBtn() : void
      {
         var d0:UnionBattleMapDefine = null;
         var btn0:NormalBtn = null;
         var arr0:Array = Gaming.defineGroup.union.battle.mapArr;
         for each(d0 in arr0)
         {
            btn0 = getBtnByName(d0.name);
            this.mapBtnObj[d0.name] = btn0;
            btn0.addEventListener(MouseEvent.MOUSE_OVER,this.mapBtnOver);
            btn0.addEventListener(MouseEvent.MOUSE_OUT,this.textOut);
         }
      }
      
      public function outLoginEvent() : void
      {
         this.nowMapAgent = null;
         this.nowTextIndex = -1;
      }
      
      private function errorAndQuit(tip0:String = "发生意外错误！") : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.unionUI.hide();
         UIOrder.alertError(tip0);
      }
      
      override public function show() : void
      {
         super.show();
         this.getSeverTime();
      }
      
      private function getSeverTime() : void
      {
         Gaming.uiGroup.connectUI.show("获取服务器时间……");
         Gaming.api.save.getServerTime(this.yes_getSeverTime,this.no_getSeverTime);
      }
      
      private function yes_getSeverTime(timeStr0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         this.getMemberList();
      }
      
      private function no_getSeverTime(timeStr0:String) : void
      {
         this.errorAndQuit("服务器时间获取失败！");
      }
      
      private function getMemberList() : void
      {
         Gaming.uiGroup.connectUI.show("获取成员数据……");
         Gaming.api.union.member.getUnionMembers(Gaming.getSaveIndex(),this.unionData.nowUnion.unionId,this.yes_getMemberList,this.no_getMemberList);
      }
      
      private function yes_getMemberList(json0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         var u0:MemberListInfo = new MemberListInfo();
         u0.inData_byJson(json0,this.unionData.nowUnion.uId);
         this.fleshData(u0);
         if(this.unionData.canUploadUnionB())
         {
            this.unionData.inMemberListInfo(u0);
            Gaming.uiGroup.unionUI.flesher.fleshUnionInfo();
         }
      }
      
      private function no_getMemberList(str0:String = "") : void
      {
         Gaming.uiGroup.alertBox.showNormal("获取成员数据失败！\n" + str0,"yes",null,null,"no");
         Gaming.uiGroup.connectUI.hide();
      }
      
      private function fleshData(u0:MemberListInfo) : void
      {
         this.agent.inData(u0,Gaming.api.save.nowSeverTime,this.meMember);
         this.fleshMap();
         this.fleshMemberInfo();
         this.fleshUnionInfo();
         this.fleshGiftBtn();
         this.fleshInfoText();
      }
      
      private function fleshMap() : void
      {
         var a0:UBattleMapAgent = null;
         var d0:UnionBattleMapDefine = null;
         var btn0:NormalBtn = null;
         var obj0:Object = this.agent.getObj();
         for each(a0 in obj0)
         {
            d0 = a0.def;
            btn0 = this.mapBtnObj[d0.name];
            btn0.setName(a0.def.cnName);
            btn0.setNumText(a0.getBtnTopText());
            btn0.itemsData = a0;
         }
      }
      
      private function fleshMemberInfo() : void
      {
         var info0:MemberInfo = this.meMember;
         var attackB0:Boolean = info0.isAttackB();
         var rank0:int = info0.getBattleRank();
         var score0:int = info0.getBattleScore();
         var dpsMul0:Number = info0.getBatDps();
         var dpsFirst0:Number = info0.getBatDpsFirst();
         var lifeMul0:Number = info0.getBatLife();
         var p0:String = "";
         p0 += info0.getBattleMapCn("无");
         p0 += "\n" + score0 + (rank0 >= 1 ? ComMethod.color("（第" + rank0 + "名）","#FFFF00") : "");
         p0 += "\n+" + NumberMethod.toPer(dpsMul0);
         p0 += "\n+" + NumberMethod.toPer(lifeMul0);
         if(score0 > 0)
         {
            p0 = ComMethod.color(p0,"#00FFFF");
         }
         this.valueTxt.htmlText = FontDeal.getDealLeadingStr(this.valueTxt,p0);
      }
      
      private function fleshUnionInfo() : void
      {
         var score0:int = this.agent.getUnionScore();
         var dpsMul0:Number = this.agent.getUnionDpsMul();
         var p0:String = "";
         p0 += score0;
         p0 += "\n+" + NumberMethod.toPer(dpsMul0);
         p0 += "\n" + this.agent.getPlayerNum() + "人";
         p0 += "\n" + this.agent.getAllTime() + "秒/" + this.agent.getAllMapNum();
         if(score0 > 0)
         {
            p0 = ComMethod.color(p0,"#00FFFF");
         }
         this.uvalueTxt.htmlText = FontDeal.getDealLeadingStr(this.uvalueTxt,p0);
      }
      
      private function fleshGiftBtn() : void
      {
         var gift0:GiftAddDefineGroup = this.agent.getWeekGift();
         var gettedB0:Boolean = this.unionData.save.bGiftB;
         var timeB0:Boolean = this.agent.canGiftB;
         var attackB0:Boolean = this.meMember.isAttackB();
         if(Boolean(gift0) && gift0.arr.length > 0)
         {
            this.giftBtn.setName(gettedB0 && timeB0 ? "已领奖" : "领取奖励");
            this.giftBtn.actived = !gettedB0 && timeB0 && attackB0;
         }
         else
         {
            this.giftBtn.actived = false;
            this.giftBtn.setName("奖励不足");
         }
         var tip0:String = "";
         tip0 += "\n1、每周奖励只需领取1次。";
         tip0 += "\n2、奖励数量和军队总积分相关。";
         tip0 += "\n3、打过战地的成员才能领取奖励。";
         if(attackB0 == false)
         {
            tip0 = "<red 攻打战地之后才能领取奖励。/>\n" + tip0;
         }
         if(timeB0 == false)
         {
            tip0 = "<red 重新登录后才能领取奖励。/>\n" + tip0;
         }
         this.giftBtn.tipString = tip0;
      }
      
      private function fleshInfoText() : void
      {
         this.pointerTag.visible = true;
         if(this.agent.attackTB)
         {
            this.pointerTag.y = 453;
         }
         else if(this.agent.giftTB)
         {
            this.pointerTag.y = 480;
         }
         else
         {
            this.pointerTag.visible = false;
         }
      }
      
      private function mapBtnClick(btn0:NormalBtn) : void
      {
         var tip0:String = null;
         var attackError0:String = null;
         var canAttackB0:Boolean = false;
         var a0:UBattleMapAgent = btn0.itemsData as UBattleMapAgent;
         var winB0:Boolean = Gaming.PG.da.worldMap.saveGroup.getWinB(a0.name);
         if(winB0)
         {
            this.nowMapAgent = a0;
            tip0 = "";
            attackError0 = this.getAttackError(a0.name);
            canAttackB0 = false;
            if(attackError0 == "")
            {
               tip0 = "是否进攻地图<b>" + ComMethod.color(a0.def.cnName,"#00FF00") + "</b>？";
               tip0 += "\n每周只有1次进攻机会。以下情况只能获得最低分：\n进入关卡后刷新页面、通关失败、上传成绩失败。";
               canAttackB0 = true;
            }
            else
            {
               tip0 = attackError0 + "当前只能进行训练模式。";
            }
            Gaming.uiGroup.alertBox.showChoose(tip0,this.getStoreState,this.gotoTrain,"",true);
            Gaming.uiGroup.alertBox.setBtnText("进攻","训练模式");
            Gaming.uiGroup.alertBox.setYesActived(canAttackB0);
         }
         else
         {
            UIOrder.alertError("该地图未解锁。");
         }
      }
      
      private function gotoTrain() : void
      {
         var a0:UBattleMapAgent = this.nowMapAgent;
         this.unionData.gotoTrain(a0.name);
         this.TRACE("---gotoTrain：" + a0.name);
         Gaming.LG.chooseLevel(a0.name,3);
      }
      
      private function getStoreState() : void
      {
         UIOrder.getStoreState(this.affterGetStoreState);
      }
      
      private function affterGetStoreState(v0:int) : void
      {
         if(v0 == 1 || v0 == -2)
         {
            this.attackTime();
         }
      }
      
      private function attackTime() : void
      {
         Gaming.uiGroup.connectUI.show("获取服务器时间……");
         this.TRACE("attackTime:获取服务器时间……");
         Gaming.api.save.getServerTime(this.yes_attackTime,this.no_getSeverTime);
      }
      
      private function yes_attackTime(timeStr0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         this.TRACE("attackTime:服务器时间成功：" + timeStr0);
         this.agent.fleshTimeState(timeStr0);
         this.fleshInfoText();
         var error0:String = this.getAttackError(this.nowMapAgent.name);
         if(error0 == "")
         {
            this.uploadExtra();
         }
         else
         {
            UIOrder.alertError(error0);
            this.TRACE("alertError：" + error0);
         }
      }
      
      private function uploadExtra() : void
      {
         this.unionData.chooseMapEvent(this.nowMapAgent,Gaming.api.save.nowSeverTime);
         this.TRACE("输入地图数据：" + this.nowMapAgent.name + "，" + Gaming.api.save.nowSeverTime);
         Gaming.uiGroup.unionUI.flesher.fleshMemberInfo(this.attackMap,this.no_uploadExtra);
      }
      
      private function no_uploadExtra(data0:* = null) : void
      {
         this.unionData.clearMapData();
      }
      
      private function attackMap(data0:* = null) : void
      {
         var a0:UBattleMapAgent = this.nowMapAgent;
         this.unionData.chooseLevelEvent(a0.name);
         this.TRACE("attackMap：" + a0.name);
         Gaming.LG.chooseLevel(a0.name,3);
      }
      
      private function getAttackError(map0:String) : String
      {
         var winB0:Boolean = Gaming.PG.da.worldMap.saveGroup.getWinB(map0);
         if(winB0 == false)
         {
            return "地图未通关，无法进攻。";
         }
         if(this.meMember.isAttackB())
         {
            return "本周进攻机会已用完。";
         }
         if(this.agent.attackTB == false)
         {
            return "进攻时间未到。";
         }
         return "";
      }
      
      private function canTrainB(map0:String) : Boolean
      {
         var winB0:Boolean = Gaming.PG.da.worldMap.saveGroup.getWinB(map0);
         if(winB0 == false)
         {
            return false;
         }
         if(this.agent.attackTB == false)
         {
            if(this.agent.giftTB)
            {
               return true;
            }
         }
         return false;
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var label0:String = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0.actived)
         {
            label0 = btn0.label;
            if(this.mapBtnObj.hasOwnProperty(label0))
            {
               this.mapBtnClick(btn0);
            }
            else if(btn0 == this.giftBtn)
            {
               this.giftClick();
            }
         }
      }
      
      private function mapBtnOver(e:*) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var a0:UBattleMapAgent = btn0.itemsData as UBattleMapAgent;
         var tip0:String = a0.getBtnTip();
         var error0:String = this.getAttackError(a0.def.name);
         if(error0 == "")
         {
            tip0 = "<b><green 点击进攻地图/></b>\n\n" + tip0;
         }
         else
         {
            tip0 = "<redness2 " + error0 + "/>\n\n" + tip0;
         }
         UIOrder.showTip(tip0);
      }
      
      private function textMove(e:MouseEvent) : void
      {
         var str0:String = null;
         var t0:TextField = e.target as TextField;
         var size0:int = int(t0.defaultTextFormat.size);
         var leading0:int = int(t0.defaultTextFormat.leading);
         var gap0:Number = size0 + leading0;
         var y0:Number = t0.mouseY;
         var index0:int = y0 / gap0 + (t0 == this.valueTxt ? 0 : 10);
         if(y0 % gap0 > size0)
         {
            index0 = -1;
         }
         if(index0 != this.nowTextIndex)
         {
            str0 = this.vallueTxtTip(index0);
            if(str0 != "")
            {
               Gaming.uiGroup.tipBox.showText(str0,33);
            }
            else
            {
               Gaming.uiGroup.tipBox.hide();
            }
         }
         this.nowTextIndex = index0;
      }
      
      private function textOut(e:MouseEvent) : void
      {
         this.nowTextIndex = -1;
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function vallueTxtTip(index0:int) : String
      {
         var levelTime0:Number = NaN;
         var dpsMax0:Number = NaN;
         var dpsFirst0:Number = NaN;
         var rank0:int = 0;
         var str0:String = "";
         var attackB0:Boolean = this.meMember.isAttackB();
         if(index0 != 0)
         {
            if(index0 == 1)
            {
               str0 = "<gray 战地积分=(100-通关时间)÷2，最高" + UBattleAgent.getScoreOneMax() + "分，最低4分。/>";
               if(attackB0)
               {
                  levelTime0 = this.meMember.extraObj.lt;
                  if(levelTime0 > 0)
                  {
                     str0 = "通关时间：<yellow " + this.meMember.extraObj.lt + "秒/>\n\n" + str0;
                  }
                  else
                  {
                     str0 = "通关失败，或者通关时间过短。";
                  }
               }
            }
            else if(index0 == 2)
            {
               dpsMax0 = this.meMember.getBatDpsMax();
               dpsFirst0 = this.meMember.getBatDpsFirst();
               rank0 = this.meMember.getBattleRank();
               if(dpsFirst0 > 0)
               {
                  str0 = "计算加成：<yellow " + NumberMethod.toPer(dpsFirst0,0) + "/>";
                  str0 += "，上限：<yellow " + NumberMethod.toPer(dpsMax0,0) + "/><blue （第" + rank0 + "名）/>\n\n";
               }
               str0 += "<gray 个人战斗力加成=战地积分*0.008+0.05，且不能超过该战地设置的上限。/>";
            }
            else if(index0 == 3)
            {
               str0 = "<gray 个人生命值加成=战地积分*0.014+0.05。/>";
            }
            else if(index0 == 10)
            {
               str0 = "<gray 总积分=所有战地第一名积分的和。/>";
            }
            else if(index0 == 11)
            {
               str0 = "<gray 军队战斗力加成=总积分*0.0005，最高为46%。/>";
            }
            else if(index0 == 13)
            {
               str0 = "<gray 已通关地图" + this.agent.getAllMapNum() + "个，最快通关时间之和为" + this.agent.getAllTime() + "秒。/>";
            }
         }
         return str0;
      }
      
      private function giftClick() : void
      {
         if(this.giftBtn.actived)
         {
            if(this.agent.attackTB == false)
            {
               this.giftEvent();
            }
            else
            {
               Gaming.uiGroup.alertBox.showChoose("当前争霸还未结束，是否要领取奖励？",this.giftEvent);
            }
         }
      }
      
      private function giftEvent() : void
      {
         var bb0:Boolean = false;
         var uda0:UnionData = null;
         var gift0:GiftAddDefineGroup = this.agent.getWeekGift();
         if(Boolean(gift0) && gift0.arr.length > 0)
         {
            bb0 = GiftAddit.addAndAutoBagSpacePan(gift0);
            if(bb0)
            {
               uda0 = this.unionData;
               uda0.getBattleGiftEvent();
               this.giftBtn.actived = false;
               this.fleshGiftBtn();
            }
         }
      }
      
      private function giftShowBtnOver(e:MouseEvent) : void
      {
         var gift0:GiftAddDefineGroup = this.agent.getWeekGift();
         if(Boolean(gift0) && gift0.arr.length > 0)
         {
            Gaming.uiGroup.giftTip.showTip(gift0,this.giftShowBtn);
         }
      }
      
      public function TRACE(s0:*) : void
      {
         SaveTestBox.addText(s0);
         INIT.tempTrace(s0);
      }
   }
}

