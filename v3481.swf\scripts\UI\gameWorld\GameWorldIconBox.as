package UI.gameWorld
{
   import UI.UIShow;
   import UI.base.NormalUI;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class GameWorldIconBox extends NormalUI
   {
      public static var wear_B:Boolean = true;
      
      public static var setting_B:Boolean = true;
      
      public static var shop_B:Boolean = true;
      
      public static var space_B:Boolean = true;
      
      public var settingBtnSp:MovieClip = null;
      
      public var wearBtnSp:MovieClip = null;
      
      public var shopBtnSp:MovieClip = null;
      
      public var settingBtn:GameWorldIconBtn = new GameWorldIconBtn();
      
      public var wearBtn:GameWorldIconBtn = new GameWorldIconBtn();
      
      public var shopBtn:GameWorldIconBtn = new GameWorldIconBtn();
      
      public function GameWorldIconBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var n:* = undefined;
         var name0:String = null;
         var btn0:GameWorldIconBtn = null;
         elementNameArr = ["settingBtnSp","wearBtnSp","shopBtnSp"];
         super.setImg(img0);
         var nameArr0:Array = ["setting","wear","shop"];
         var cnNameArr0:Array = ["系统","背包","商店"];
         for(n in nameArr0)
         {
            name0 = nameArr0[n];
            btn0 = this[name0 + "Btn"];
            btn0.setImg(this[name0 + "BtnSp"]);
            btn0.setNew(false);
            btn0.setName(cnNameArr0[n]);
            btn0.setIconName("IconGather/" + name0);
            btn0.mouseIconEffectB = true;
            addChild(btn0);
            btn0.label = name0;
            btn0.addEventListener(MouseEvent.CLICK,this.iconClick);
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function startLevel() : void
      {
         wear_B = true;
         setting_B = true;
         shop_B = true;
         var wearLabel0:String = Gaming.LG.getModelDiyDefineZero().getWearLabel();
         this.wearBtn.label = wearLabel0;
         this.wearBtn.setIconName("IconGather/" + wearLabel0);
         this.wearBtn.setName(Gaming.uiGroup.getAppCn(wearLabel0));
      }
      
      private function iconClick(e:MouseEvent) : void
      {
         var btn0:GameWorldIconBtn = e.target as GameWorldIconBtn;
         var label0:String = btn0.label;
         if(Boolean(GameWorldIconBox[label0 + "_B"]))
         {
            UIShow.showApp(label0);
         }
      }
   }
}

