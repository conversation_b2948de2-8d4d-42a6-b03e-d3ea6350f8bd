package UI
{
   import UI.api.AllAPI;
   import UI.base.alert.AlertBox;
   import UI.guide.GuideOrder;
   import UI.loading.ConnectUI;
   import UI.test.SaveTestBox;
   import com.adobe.serialization.json.JSON2;
   import com.sounto.net.SWFLoaderUrl;
   import com.sounto.process.StaticYesAndNoFun;
   import com.sounto.utils.NumberMethod;
   import dataAll._app.login.LoginUpload;
   import dataAll._app.setting.SettingSave;
   import dataAll._app.task.define.TaskDefine;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._data.ConstantDefine;
   import dataAll._player.PlayerData;
   import dataAll._player.base.PlayerMainSave;
   import dataAll.equip.define.EquipDefine;
   import dataAll.skill.define.SkillDefine;
   import dataAll.test.ZuoBiPaner;
   import flash.utils.getTimer;
   import gameAll.image.ShootMouseCursor;
   import gameAll.level.data.LevelData;
   
   public class UIOrder extends StaticYesAndNoFun
   {
      public static var loginUpload:LoginUpload = new LoginUpload();
      
      private static var saveUIShowB:Boolean = true;
      
      private static var stopSaveB:Boolean = false;
      
      private static var afterLockB:Boolean = false;
      
      private static var nowIsFirstSaveB:Boolean = false;
      
      private static var yes_save_fun:Function = null;
      
      private static var no_save_fun:Function = null;
      
      public static var yesSaveTip:String = "";
      
      public function UIOrder()
      {
         super();
      }
      
      public static function outLoginEvent() : void
      {
         clear();
      }
      
      private static function get PD() : PlayerData
      {
         return Gaming.PG.da;
      }
      
      private static function get connectUI() : ConnectUI
      {
         return Gaming.uiGroup.connectUI;
      }
      
      private static function get alertBox() : AlertBox
      {
         return Gaming.uiGroup.alertBox;
      }
      
      private static function get api() : AllAPI
      {
         return Gaming.api;
      }
      
      public static function autoSavePan() : Boolean
      {
         if(LevelData.autoSaved == false)
         {
            if(LevelData.saveTime > LevelData.autoSaveTime)
            {
               if(Gaming.LG.isNormalLevelB() || Gaming.LG.isEndlessB())
               {
                  LevelData.autoSaved = true;
                  saveCanStop();
                  return true;
               }
            }
         }
         return false;
      }
      
      public static function saveCanStop() : void
      {
         save(true,true,true,null,null,true);
      }
      
      public static function save(uiB0:Boolean = true, getStoreStateB:Boolean = true, zuobiPanB0:Boolean = true, yesFun0:Function = null, noFun0:Function = null, stopSaveB0:Boolean = false, afterLockB0:Boolean = false) : void
      {
         var bb0:Boolean = false;
         saveUIShowB = uiB0;
         stopSaveB = stopSaveB0;
         afterLockB = afterLockB0;
         setFun(yesFun0,noFun0,"save");
         if(zuobiPanB0)
         {
            zuobiPan(false,false);
         }
         if(saveUIShowB)
         {
            connectUI.show("存档中……",1);
         }
         if(getStoreStateB)
         {
            bb0 = isLoginByJS();
            if(bb0)
            {
               api.save.getStoreState(affter_save,no_save,true,!stopSaveB);
            }
            else
            {
               connectUI.hide();
            }
         }
         else
         {
            affter_save(1);
         }
      }
      
      private static function affter_save(state0:int) : void
      {
         var stopSaveDelay0:Number = NaN;
         if(state0 == 1 || state0 == -2)
         {
            Gaming.uiGroup.settingUI.gamingBox.saveShow();
            stopSaveDelay0 = stopSaveB ? 60 : 0;
            if(saveUIShowB)
            {
               connectUI.show("存档中……",1,stopSaveDelay0);
            }
            api.save.save(Gaming.PG.getSaveObj(),Gaming.PG.da.getSaveTitle(),yes_save,no_save);
         }
         else
         {
            no_save();
         }
      }
      
      private static function yes_save() : void
      {
         if(Gaming.PG.da.saveYesEvent())
         {
            Gaming.api.rzxt.reg();
         }
         if(afterLockB)
         {
            SaveTestBox.addText("===== afterLockB 判断开始 ======");
            getStoreState(afterLockPanStore,true);
         }
         else
         {
            doYesSave();
         }
      }
      
      private static function afterLockPanStore(state0:int) : void
      {
         SaveTestBox.addText("===== afterLockPanStore：" + state0);
         if(state0 == 1 || state0 == -2)
         {
            doYesSave();
         }
         else
         {
            no_save();
         }
      }
      
      private static function doYesSave() : void
      {
         if(saveUIShowB)
         {
            connectUI.hide();
            LevelData.saveTime = 0;
            if(yesSaveTip == "")
            {
               alertBox.showCheck("存档成功！","",0.8,null,null,"yes","icon");
            }
            else
            {
               alertBox.showSuccess(yesSaveTip);
            }
            yesSaveTip = "";
         }
         doYesFun(true,"save");
      }
      
      public static function outShowSaveTip() : void
      {
         if(yesSaveTip != "")
         {
            alertBox.showSuccess(yesSaveTip);
         }
         yesSaveTip = "";
      }
      
      private static function no_save(str0:String = "") : void
      {
         if(saveUIShowB)
         {
            connectUI.hide();
         }
         alertBox.showError("存档失败！请检查游戏是否多开、账户退出、网络断开等情况。",null,stopSaveB,!stopSaveB);
         doNoFun(true,"save");
      }
      
      public static function affter_readSave(firstB0:Boolean = false) : void
      {
         nowIsFirstSaveB = firstB0;
         getBalance();
      }
      
      private static function getBalance() : void
      {
         connectUI.show("获取黄金余额中……");
         api.shop.getBalance(yes_getBalance,no_getBalance);
      }
      
      private static function yes_getBalance(v0:Number) : void
      {
         connectUI.hide();
         UIShow.flesh_coinChange();
         getTotalRecharged();
      }
      
      private static function no_getBalance(str0:String) : void
      {
         connectUI.hide();
         alertBox.showNormal("服务器连接失败！无法继续游戏！点击确定重新连接服务器。","yes",getBalance,null,"no");
      }
      
      private static function getTotalRecharged() : void
      {
         connectUI.show("获取累计充值数量中……");
         api.shop.getTotalRecharged(yes_getTotalRecharged,no_getTotalRecharged);
      }
      
      private static function yes_getTotalRecharged(v0:Number) : void
      {
         connectUI.hide();
         Gaming.PG.da.vip.fleshByTotalRecharged(v0,false);
         uploadPay();
      }
      
      private static function no_getTotalRecharged(str0:String) : void
      {
         connectUI.hide();
         alertBox.showNormal("服务器连接失败！无法继续游戏！点击确定重新连接服务器。","yes",getTotalRecharged,null,"no");
      }
      
      private static function uploadPay() : void
      {
         connectUI.show("上传基本数据……");
         var daArr0:Array = loginUpload.getUploadArr();
         api.top.submitScoreArray(daArr0,uploadPay2,uploadPay2);
      }
      
      private static function uploadPay2(obj0:* = null) : void
      {
         var daArr0:Array = loginUpload.getUploadArr2();
         api.top.submitScoreArray(daArr0,zuobiSwitch,zuobiSwitch);
      }
      
      private static function zuobiSwitch(obj0:* = null) : void
      {
         connectUI.show("获取公共数据……");
         api.union.variables.getVariables(Gaming.getSaveIndex(),ZuoBiPaner.idArr,yes_zuobiSwitch,no_zuobiSwitch);
      }
      
      private static function yes_zuobiSwitch(jsonStr0:String) : void
      {
         SaveTestBox.addText("变量集合：" + jsonStr0);
         var arr0:Array = JSON2.decode(jsonStr0) as Array;
         var value0:int = int(arr0[0].value);
         ZuoBiPaner.inIdArr(arr0);
         affter_zuobiSwitch(value0);
      }
      
      private static function no_zuobiSwitch(str0:String) : void
      {
         ZuoBiPaner.inIdArr(null);
         affter_zuobiSwitch(0);
      }
      
      private static function affter_zuobiSwitch(v0:int) : void
      {
         connectUI.hide();
         SaveTestBox.addText("防作弊变量：" + v0);
         Gaming.PG.da.zuobiPaner.specialB = v0 % 2 == 0;
         getSeverTime();
      }
      
      private static function getSeverTime(obj0:* = null) : void
      {
         connectUI.show("获取服务器时间……");
         api.save.getServerTime(yes_getSeverTime,no_getSeverTime);
      }
      
      private static function no_getSeverTime(timeStr0:String) : void
      {
         connectUI.hide();
         alertBox.showNormal("服务器连接失败！无法继续游戏！\n点击确定重新连接服务器。","yes",getSeverTime,null,"no");
      }
      
      private static function yes_getSeverTime(timeStr0:String = "") : void
      {
         connectUI.hide();
         fleshSettingReadSave();
         Gaming.PG.da.setNowReadTime(timeStr0);
         Gaming.uiGroup.mainUI.affterReadSave();
         Gaming.uiGroup.taskBox.showOrHideByHave();
         Gaming.uiGroup.unionTaskBox.fleshDataByUnion();
         Gaming.uiGroup.unionTaskBox.showOrHideByHave();
         playMainMusic();
         if(nowIsFirstSaveB)
         {
            save(true,false,false,firstDoubleChoose,firstDoubleChoose,true);
            nowIsFirstSaveB = false;
         }
         else
         {
            zuobiPan(true,false);
         }
         Gaming.api.rzxt.login();
      }
      
      private static function firstDoubleChoose(v0:* = null) : void
      {
         alertBox.double.showModel(chooseDoubleModel);
      }
      
      public static function fleshSettingReadSave() : void
      {
         var s0:SettingSave = Gaming.PG.save.setting;
         Gaming.ME.stage.quality = s0.quality;
         Gaming.ME.stage.frameRate = s0.getFrame();
         NumberMethod.YI_B = s0.simpleNumberB;
         EquipDefine.FASHION_KEEP_ROLE_HEAD = s0.fHead;
         ShootMouseCursor.shootCursor = s0.getCursor();
         if(SkillDefine.UI_SPECIAL != s0.skillSpecialB)
         {
            SkillDefine.UI_SPECIAL = s0.skillSpecialB;
            Gaming.defineGroup.worldMap.clearAllDemTip();
         }
      }
      
      public static function setFrame30() : void
      {
         var s0:SettingSave = Gaming.PG.save.setting;
         if(Boolean(s0))
         {
            s0.setFrame30();
         }
         Gaming.ME.stage.frameRate = 30;
      }
      
      private static function chooseDoubleModel(label0:String) : void
      {
         if(label0 == "single")
         {
            Gaming.PG.da.moreWay.gotoSingle();
         }
         else
         {
            Gaming.PG.da.moreWay.gotoDouble();
         }
         GuideOrder.open("first");
         Gaming.uiGroup.moreBox.fleshData();
      }
      
      public static function zuobiPan(showZuobiB0:Boolean = true, saveB0:Boolean = true) : Boolean
      {
         var beforeZuobiB0:Boolean = false;
         if(Gaming.testCtrl.cheating.closeZuobiPanB)
         {
            return false;
         }
         var alltt0:Number = getTimer();
         var tt0:Number = getTimer();
         var main0:PlayerMainSave = Gaming.PG.save.main;
         var defineGroupStr0:String = Gaming.defineGroup.zuobiPan();
         INIT.TRACE("defineGroup.zuobiPan作弊判断耗时：" + (getTimer() - tt0));
         tt0 = getTimer();
         var playerDataStr0:String = Gaming.PG.da.zuobiPan();
         INIT.TRACE("PG.da.zuobiPan作弊判断耗时：" + (getTimer() - tt0));
         tt0 = getTimer();
         if(!(defineGroupStr0 == "" && playerDataStr0 == ""))
         {
            beforeZuobiB0 = main0.isZuobiB;
            main0.isZuobiB = true;
            main0.zuobiReason = ConstantDefine.versionNumber + "：" + defineGroupStr0 + " | " + playerDataStr0;
            if(!beforeZuobiB0 && saveB0)
            {
               save(false,false,false);
            }
         }
         INIT.TRACE("zuobiPan总耗时：" + (getTimer() - alltt0));
         if(showZuobiB0)
         {
            return zuobiShow();
         }
         return main0.isZuobiB;
      }
      
      public static function zuobiShow() : Boolean
      {
         if(Gaming.PG.save.main.isZuobiB)
         {
            if(Gaming.PG.save.main.zuobiReason.indexOf("商品xml被修改！") >= 0)
            {
               Gaming.uiGroup.loginUI.outLoginEvent();
               alertBox.showNormal("您的存档数据存在异常，无法进入游戏。","");
            }
            else
            {
               alertBox.showNormal("您的存档数据存在异常，暂时无法进入排行榜、竞技场。","yes");
            }
            return true;
         }
         return false;
      }
      
      public static function getStoreState(yesFun0:Function, errorLockB0:Boolean = true) : void
      {
         var bb0:Boolean = isLoginByJS();
         if(bb0)
         {
            connectUI.show("存档中……",1);
            api.save.getStoreState(yesFun0);
         }
      }
      
      public static function isLoginByJS() : Boolean
      {
         var loginB0:Boolean = Gaming.PG.loginData.isLoginByJS();
         if(!loginB0)
         {
            alertBox.showNormal("您的账号已经退出登录，无法进行此操作。","",null,null,"no");
            return false;
         }
         return true;
      }
      
      public static function playSuccessSound() : void
      {
         Gaming.soundGroup.playSound("uiSound","success");
      }
      
      public static function playLoadSound() : void
      {
         Gaming.soundGroup.playSound("uiSound","swapHero");
      }
      
      public static function playUploadSound() : void
      {
         Gaming.soundGroup.playSound("uiSound","success");
      }
      
      public static function playFailSound() : void
      {
         Gaming.soundGroup.playSound("uiSound","fail");
      }
      
      private static function getLoginMusic() : String
      {
         if(Boolean(Gaming.PG.save))
         {
            return Gaming.PG.save.setting.getLoginMusic();
         }
         return SettingSave.getLocalLoginMusic();
      }
      
      public static function playLoginMusic() : void
      {
         var label0:String = getLoginMusic();
         playMusicLabel(label0);
      }
      
      public static function playMainMusic() : SWFLoaderUrl
      {
         if(Boolean(Gaming.PG.save))
         {
            return playMusicLabel(Gaming.PG.save.setting.getMainMusic());
         }
         return null;
      }
      
      public static function playMusicLabel(label0:String, volume0:Number = 1) : SWFLoaderUrl
      {
         var s0:SWFLoaderUrl = Gaming.defineGroup.staticCtrl.getMusic(label0);
         if(Boolean(s0))
         {
            Gaming.soundGroup.playMusicUrlAdd(s0.url,volume0);
         }
         return s0;
      }
      
      public static function alertError(str0:String) : void
      {
         Gaming.uiGroup.alertBox.showError(str0);
      }
      
      public static function showTip(str0:String) : void
      {
         if(str0 == "")
         {
            Gaming.uiGroup.tipBox.hide();
         }
         else
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
      }
      
      public static function taskPan(name0:String) : Boolean
      {
         var d0:TaskDefine = Gaming.defineGroup.task.getOneDefine(name0);
         var bb0:Boolean = Gaming.PG.da.task.isCompleteB(d0.name);
         if(bb0)
         {
            return true;
         }
         alertError("完成" + d0.lv + "级任务 " + d0.getAlertTitleText() + " 后开放。");
         return false;
      }
      
      public static function openPan(mapName0:String) : Boolean
      {
         var mapD0:WorldMapDefine = null;
         var openB0:Boolean = false;
         if(Boolean(Gaming.PG.da))
         {
            if(Gaming.PG.da.worldMap.saveGroup.getWinB(mapName0))
            {
               openB0 = true;
            }
         }
         if(openB0 == false)
         {
            mapD0 = Gaming.defineGroup.worldMap.getDefine(mapName0);
            alertError("通关地图“" + mapD0.cnName + "”后开启。");
         }
         return openB0;
      }
   }
}

