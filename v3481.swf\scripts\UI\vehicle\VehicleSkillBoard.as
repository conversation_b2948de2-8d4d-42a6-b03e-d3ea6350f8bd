package UI.vehicle
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.must.NormalMustBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._player.PlayerData;
   import dataAll.equip.vehicle.VehicleData;
   import dataAll.equip.vehicle.VehicleDataCreator;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.skill.HeroSkillData;
   import dataAll.skill.HeroSkillDataGroup;
   import dataAll.skill.define.HeroSkillDefine;
   import dataAll.skill.save.HeroSkillSave;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.system.System;
   import flash.text.TextField;
   
   public class VehicleSkillBoard extends AutoNormalUI
   {
      private var itemsTag:Sprite;
      
      private var gripTag:Sprite;
      
      private var nameTxt:TextField;
      
      private var beforeTxt:TextField;
      
      private var nextTxt:TextField;
      
      private var infoTxt:TextField;
      
      private var mustTag:Sprite;
      
      private var coverSp:Sprite;
      
      private var btn:NormalBtn;
      
      private var posBtn:NormalBtn;
      
      private var noBtn:NormalBtn;
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var gripBox:ItemsGripBox = new ItemsGripBox();
      
      private var skillGrip:ItemsGrid = new ItemsGrid();
      
      private var nowSkillData:HeroSkillData;
      
      private var nowSkillDefine:HeroSkillDefine;
      
      public var vehicleUI:VehicleUI;
      
      public function VehicleSkillBoard()
      {
         super();
         mcTypeArr = ["tag","btnSp","txt","mustBoxSp"];
      }
      
      public function get nowData() : VehicleData
      {
         return Gaming.uiGroup.vehicleUI.nowData;
      }
      
      public function set nowData(v0:VehicleData) : void
      {
         Gaming.uiGroup.vehicleUI.nowData = v0;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         FontDeal.dealOne(this.nameTxt);
         FontDeal.dealLine(this.infoTxt);
         FontDeal.dealLine(this.nextTxt);
         FontDeal.dealLine(this.beforeTxt);
         this.gripTag.y = 115;
         addChild(this.gripBox);
         NormalUICtrl.setTag(this.gripBox,this.gripTag);
         this.gripBox.arg.init(2,6,12,6);
         this.gripBox.imgType = "equipGrip";
         this.gripBox.evt.setWantEvent(true,false,false,true,true);
         this.gripBox.addEventListener(ClickEvent.ON_CLICK,this.skillGripClick);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.gripBox);
         ItemsGripTipCtrl.addNormalBtnTip(this.posBtn);
         this.posBtn.tipString = "调整技能位置，对应的按键顺序也会被调整。";
         addChild(this.skillGrip);
         this.skillGrip.setImgToEquipGrip();
         NormalUICtrl.setTag(this.skillGrip,this.itemsTag);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.skillGrip);
         addChild(this.mustBox);
         this.mustBox.setNormalImg();
         NormalUICtrl.setTag(this.mustBox,this.mustTag);
         addChild(this.coverSp);
         this.setCoverState("");
         this.coverSp.mouseChildren = false;
         this.coverSp.addEventListener(MouseEvent.CLICK,this.coverClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var funName0:String = btn0.label + "BtnClick";
         if(this.hasOwnProperty(funName0))
         {
            this[funName0](e);
         }
      }
      
      private function get PD() : PlayerData
      {
         return Gaming.PG.da;
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      public function outLoginEvent() : void
      {
         this.clearAll();
         this.gripBox.clearAllData();
      }
      
      public function setCoverState(str0:String, txt0:String = "") : void
      {
         if(str0 == "")
         {
            this.coverSp.visible = false;
            (this.coverSp as MovieClip).stop();
         }
         else
         {
            this.coverSp.visible = true;
            (this.coverSp as MovieClip).gotoAndStop(str0);
         }
         if(txt0 != "")
         {
            this.coverSp["txt"].htmlText = txt0;
         }
      }
      
      private function coverClick(e:MouseEvent) : void
      {
         var da0:VehicleData = this.nowData;
         if(Boolean(da0))
         {
            if(da0.getVehicleSave().isDiggersComposeB())
            {
               Gaming.uiGroup.alertBox.showChoose("是否消耗" + this.getDiggersGoods().price + "个优胜券，来解锁其技能和属性加成？",this.unlockDiggers);
            }
         }
      }
      
      private function unlockDiggers() : void
      {
         var must0:int = 0;
         var now0:int = 0;
         var da0:VehicleData = this.nowData;
         if(Boolean(da0))
         {
            if(da0.getVehicleSave().isDiggersComposeB())
            {
               must0 = this.getDiggersGoods().price;
               now0 = this.PD.thingsBag.getThingsNum("arenaStamp");
               if(now0 >= must0)
               {
                  this.PD.thingsBag.useThings("arenaStamp",must0);
                  da0.save.shopB = true;
                  this.fleshData();
                  Gaming.uiGroup.alertBox.showSuccess("解锁成功！");
               }
               else
               {
                  UIOrder.alertError("优胜券只有" + now0 + "个，数量不足！");
               }
            }
         }
      }
      
      private function set nextHtml(str0:String) : void
      {
         this.nextTxt.htmlText = FontDeal.getDealLeadingStr(this.nextTxt,str0);
      }
      
      private function set beforeHtml(str0:String) : void
      {
         this.beforeTxt.htmlText = FontDeal.getDealLeadingStr(this.beforeTxt,str0);
      }
      
      public function fleshData() : void
      {
         this.showData(this.nowData);
      }
      
      public function gripClick(e:ClickEvent) : void
      {
         if(visible)
         {
            this.showData(e.childData as VehicleData);
         }
      }
      
      private function showData(da0:VehicleData) : void
      {
         var arr0:Array = null;
         var grip0:ItemsGrid = this.vehicleUI.itemsBox.findGripByData(da0);
         if(!(grip0 is ItemsGrid))
         {
            da0 = null;
         }
         if(this.nowData != da0)
         {
            this.nowSkillData = null;
            this.nowSkillDefine = null;
         }
         this.nowData = da0;
         if(Boolean(da0))
         {
            this.vehicleUI.itemsBox.setChooseByItemsData(da0);
            if(da0.getVehicleSave().isDiggersComposeB())
            {
               this.setCoverState("noSkill","点击此处，消耗" + this.getDiggersGoods().price + "个优胜券，解锁其技能和属性。");
               this.gripBox.clearAllData();
            }
            else
            {
               arr0 = da0.getUISkillArr();
               if(arr0.length == 0)
               {
                  this.setCoverState("noSkill","当前载具没有技能");
                  this.gripBox.clearAllData();
               }
               else
               {
                  this.gripBox.inData_byArr(arr0,"inData_UIPetSkill");
                  if(this.showSkillMustLv())
                  {
                     this.setCoverState("");
                  }
                  this.fleshSkillData();
               }
            }
         }
         else
         {
            this.vehicleUI.itemsBox.setChoose_byIndex(-1);
            this.setCoverState("vehicle");
            this.outLoginEvent();
         }
      }
      
      private function getDiggersGoods() : GoodsDefine
      {
         return Gaming.defineGroup.goods.getDefine("Diggers_arena");
      }
      
      private function fleshSkillData() : void
      {
         this.showObj(Boolean(this.nowSkillData) ? this.nowSkillData : this.nowSkillDefine);
      }
      
      private function getNowSkillLv() : int
      {
         if(Boolean(this.nowSkillData))
         {
            return this.nowSkillData.save.getTrueLevel();
         }
         return 0;
      }
      
      private function skillGripClick(e:ClickEvent) : void
      {
         this.showObj(e.childData);
      }
      
      private function showSkillMustLv() : Boolean
      {
         if(Boolean(this.nowData))
         {
            if(!this.nowData.canStudySkillB())
            {
               this.setCoverState("txt","载具必须强化至" + this.nowData.getSkillMustLVStr() + "级，才能学习技能。");
               return false;
            }
         }
         return true;
      }
      
      public function showObj(obj0:Object) : void
      {
         this.nowSkillData = null;
         this.nowSkillDefine = null;
         if(obj0 is HeroSkillData)
         {
            this.setCoverState("");
            this.nowSkillData = obj0 as HeroSkillData;
            this.showUpgradeByData(this.nowSkillData);
            if(Gaming.testCtrl.cheating.enabled)
            {
               System.setClipboard(this.nowSkillData.save.getDefine().getProjectText());
            }
         }
         else if(obj0 is HeroSkillDefine)
         {
            this.setCoverState("");
            this.nowSkillDefine = obj0 as HeroSkillDefine;
            this.showMustByDefine(this.nowSkillDefine);
            if(Gaming.testCtrl.cheating.enabled)
            {
               System.setClipboard(this.nowSkillDefine.getProjectText());
            }
         }
         else
         {
            this.setCoverState("skill");
            this.clearAll();
         }
         this.showSkillMustLv();
      }
      
      public function clearAll() : void
      {
         this.nowSkillData = null;
         this.nowSkillDefine = null;
         this.mustBox.setShowState(false);
         this.btn.actived = false;
         this.beforeTxt.htmlText = "";
         this.nextTxt.htmlText = "";
         this.skillGrip.clearData();
         this.nameTxt.text = "";
         this.infoTxt.text = "";
      }
      
      private function showUpgradeByData(da0:HeroSkillData) : void
      {
         var s0:HeroSkillSave = null;
         var must_d0:MustDefine = null;
         var bb0:Boolean = false;
         this.gripBox.setChooseByItemsData(da0);
         s0 = da0.save;
         var lv_d0:HeroSkillDefine = s0.getDefine();
         var next_d0:HeroSkillDefine = s0.getNextDefine();
         var maxLevelB0:Boolean = !(next_d0 is HeroSkillDefine);
         this.skillGrip.inData_skill(da0);
         this.nameTxt.text = lv_d0.cnName;
         this.infoTxt.text = lv_d0.getDescriptionNoActiveCd();
         this.beforeHtml = lv_d0.getUIInfoText("当前");
         this.posBtn.visible = true;
         this.posBtn.actived = s0.site > 0;
         this.noBtn.visible = lv_d0.isPassiveB() || !s0.effB;
         this.noBtn.setName(s0.effB ? "关闭技能" : "打开技能");
         this.btn.actived = false;
         this.btn.setName("升级");
         if(maxLevelB0)
         {
            this.mustBox.setShowState(false);
            this.nextHtml = ComMethod.color("已升至最高等级了","#FF9900");
         }
         else
         {
            this.nextHtml = next_d0.getUIInfoText("升级后");
            must_d0 = VehicleDataCreator.getSkillMustByDefine(s0.getDefine());
            bb0 = this.mustBox.inData(must_d0,this.nowData.save.getTrueLevel(),"载具");
            this.btn.actived = bb0;
         }
      }
      
      private function showMustByDefine(d0:HeroSkillDefine) : void
      {
         this.gripBox.setChooseByItemsData(d0);
         var lv_d0:HeroSkillDefine = d0;
         this.skillGrip.inData_SkillDefine(d0);
         this.nameTxt.text = lv_d0.cnName;
         this.infoTxt.text = lv_d0.getDescriptionNoActiveCd();
         this.beforeHtml = ComMethod.color("未学习","#FF9900");
         this.nextHtml = lv_d0.getUIInfoText("学习后");
         var must_d0:MustDefine = VehicleDataCreator.getSkillMustByDefine(d0,1);
         var bb0:Boolean = this.mustBox.inData(must_d0,this.nowData.save.getTrueLevel(),"尸宠");
         this.btn.actived = bb0;
         this.btn.setName("学习");
         this.posBtn.visible = false;
         this.noBtn.visible = false;
      }
      
      public function BtnClick(e:MouseEvent) : void
      {
         var must_d0:MustDefine = null;
         if(Boolean(this.nowSkillData))
         {
            must_d0 = VehicleDataCreator.getSkillMustByDefine(this.nowSkillData.save.getDefine());
         }
         else if(Boolean(this.nowSkillDefine))
         {
            must_d0 = VehicleDataCreator.getSkillMustByDefine(this.nowSkillDefine,1);
         }
         if(Boolean(must_d0))
         {
            PlayerMustCtrl.deductMust(must_d0,Boolean(this.nowSkillData) ? this.affter_upgrade : this.affter_study);
         }
      }
      
      private function affter_upgrade() : void
      {
         var da0:HeroSkillData = this.nowSkillData;
         var s0:HeroSkillSave = da0.save;
         s0.upgrade();
         Gaming.soundGroup.playSound("uiSound","success");
         Gaming.uiGroup.alertBox.showSuccess("升级成功！");
         this.fleshData();
      }
      
      private function affter_study() : void
      {
         var d0:HeroSkillDefine = this.nowSkillDefine;
         this.nowSkillData = this.nowData.skill.addSkillByLabel(d0.name,1);
         this.nowSkillDefine = null;
         this.nowData.skill.sortByActive();
         Gaming.soundGroup.playSound("uiSound","success");
         var tipText0:String = "获得新技能：" + ComMethod.color(d0.cnName,"#FF6600") + "。";
         Gaming.uiGroup.alertBox.showCheck(tipText0,"yes",0,null,null,d0.iconUrl,"equip");
         this.fleshData();
         Gaming.uiGroup.mainUI.fleshCoin();
      }
      
      public function posBtnClick(e:MouseEvent) : void
      {
         var site0:int = 0;
         var da0:HeroSkillData = this.nowSkillData;
         if(Boolean(this.nowData) && Boolean(da0))
         {
            site0 = da0.save.site;
            if(site0 >= 1)
            {
               HeroSkillDataGroup.skillSwapTo(this.nowData.skill,this.nowData.skill,site0,site0 - 1);
               this.fleshData();
               UIOrder.playLoadSound();
            }
         }
      }
      
      public function noBtnClick(e:MouseEvent) : void
      {
         var da0:HeroSkillData = this.nowSkillData;
         if(Boolean(this.nowData) && Boolean(da0))
         {
            da0.save.effB = !da0.save.effB;
            this.fleshData();
         }
      }
   }
}

