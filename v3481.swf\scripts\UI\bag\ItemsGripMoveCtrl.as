package UI.bag
{
   import UI.base.box.NormalBox;
   import UI.base.button.NormalBtn;
   import UI.base.drag.ItemsDragController;
   import UI.base.event.ClickEvent;
   import UI.base.grid.NormalGrid;
   import UI.house.HouseItemsGripMoveCtrl;
   import com.sounto.utils.StringMethod;
   import dataAll._app.parts.PartsDataGroup;
   import dataAll._player.more.MoreData;
   import dataAll.arms.ArmsData;
   import dataAll.arms.ArmsDataGroup;
   import dataAll.equip.EquipData;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.EquipDataSwapPan;
   import dataAll.equip.define.EquipType;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.skill.HeroSkillDataGroup;
   import dataAll.things.ThingsData;
   import dataAll.ui.tip.CheckData;
   import flash.events.MouseEvent;
   
   public class ItemsGripMoveCtrl
   {
      public function ItemsGripMoveCtrl()
      {
         super();
      }
      
      public static function init() : void
      {
      }
      
      public static function addEvent_byItemsGripBox(box0:ItemsGripBox) : void
      {
         box0.addEventListener(ClickEvent.ON_DOWN,gripDown);
         box0.addEventListener(ClickEvent.ON_UP,gripUp);
         box0.addEventListener(ClickEvent.ON_DOUBLE_CLICK,gripDoubleClick);
      }
      
      private static function gripDoubleClick(e:ClickEvent) : void
      {
         var btn0:NormalBtn = e.child as NormalBtn;
         if(e.fatherData is ArmsDataGroup)
         {
            Gaming.uiGroup.partsUI.armsGripClick(e.childData as ArmsData,e.fatherData as ArmsDataGroup);
         }
         else if(e.fatherData is PartsDataGroup)
         {
            Gaming.uiGroup.partsUI.partsGripClick(e.childData as ThingsData,e.fatherData as PartsDataGroup);
         }
         ItemsGripBtnListCtrl.hide();
      }
      
      public static function gripDown(e:ClickEvent) : void
      {
         var grip2:NormalGrid = e.child as NormalGrid;
         if(grip2.canDragB())
         {
            Gaming.uiGroup.dragCtrl.startDraging(e);
         }
         ItemsGripBtnListCtrl.hide();
         ItemsGripTipCtrl.gripOut();
      }
      
      public static function gripUp(e:ClickEvent) : void
      {
         var grip2:NormalGrid = null;
         var dg1:ItemsDataGroup = null;
         var dg2:ItemsDataGroup = null;
         var site1:int = 0;
         var site2:int = 0;
         var continueB0:Boolean = false;
         var targetBox0:NormalBox = null;
         var listB0:Boolean = false;
         var dragCtrl:ItemsDragController = Gaming.uiGroup.dragCtrl;
         dragCtrl.stopDraging();
         if(Boolean(dragCtrl.dragChild) && e.child is NormalGrid)
         {
            grip2 = e.child as NormalGrid;
            if(grip2.canSwapItemsB())
            {
               dg1 = dragCtrl.clickEvent.fatherData as ItemsDataGroup;
               dg2 = e.fatherData as ItemsDataGroup;
               site1 = dragCtrl.clickEvent.index;
               site2 = e.index;
               if(dg1.dataType == dg2.dataType)
               {
                  if(dragCtrl.dragChild != grip2)
                  {
                     continueB0 = spcialPan(dg1,dg2,site1,site2);
                     if(continueB0)
                     {
                        if(dg1.dataType == ItemsDataGroup.TYPE_EQUIP)
                        {
                           equipGripSwap(dg1 as EquipDataGroup,dg2 as EquipDataGroup,site1,site2);
                           Gaming.uiGroup.wearUI.showHDFashionFastPan(dragCtrl.dragChild.itemsData as EquipData);
                        }
                        else if(dg1.dataType == ItemsDataGroup.TYPE_ARMS)
                        {
                           armsGripSwap(dg1 as ArmsDataGroup,dg2 as ArmsDataGroup,site1,site2);
                        }
                        else if(dg1.dataType == ItemsDataGroup.TYPE_THINGS)
                        {
                           thingsGripSwap(dg1,dg2,site1,site2);
                        }
                        else if(dg1.dataType == ItemsDataGroup.TYPE_GENE)
                        {
                           thingsGripSwap(dg1,dg2,site1,site2);
                        }
                        else if(dg1.dataType == ItemsDataGroup.TYPE_PARTS)
                        {
                           partsGripSwap(dg1,dg2,site1,site2);
                        }
                        else if(dg1.dataType == ItemsDataGroup.TYPE_SKILL)
                        {
                           skillGripSwap(dg1,dg2,site1,site2);
                           if(dragCtrl.dragChild.isChosen)
                           {
                              dragCtrl.dragChild.isChosen = false;
                              grip2.isChosen = true;
                           }
                        }
                        else if(dg1.dataType == ItemsDataGroup.TYPE_MORE)
                        {
                           thingsGripSwap(dg1,dg2,site1,site2);
                        }
                        ItemsGripBtnListCtrl.fleshAllBy(dg1);
                     }
                  }
                  else
                  {
                     targetBox0 = e.target as NormalBox;
                     listB0 = true;
                     if(Boolean(targetBox0))
                     {
                        listB0 = !targetBox0.moreChooseB;
                     }
                     if(listB0)
                     {
                        ItemsGripBtnListCtrl.show(grip2,dg1);
                     }
                     Gaming.uiGroup.partsUI.partsGripOneClick(e.childData as ThingsData,e.fatherData as PartsDataGroup);
                  }
               }
            }
         }
         dragCtrl.clear();
         ItemsGripTipCtrl.gripOut();
      }
      
      private static function spcialPan(dg1:ItemsDataGroup, dg2:ItemsDataGroup, site1:int, site2:int) : Boolean
      {
         if(dg1.placeType == ItemsDataGroup.PLACE_HOUSE || dg2.placeType == ItemsDataGroup.PLACE_HOUSE)
         {
            return HouseItemsGripMoveCtrl.pan(dg1,dg2,site1,site2);
         }
         return true;
      }
      
      public static function swapAndSpcialPan(dg1:ItemsDataGroup, dg2:ItemsDataGroup, site1:int, site2:int) : void
      {
         var continueB0:Boolean = spcialPan(dg1,dg2,site1,site2);
         if(continueB0)
         {
            swap(dg1,dg2,site1,site2);
         }
      }
      
      public static function swap(dg1:ItemsDataGroup, dg2:ItemsDataGroup, site1:int, site2:int) : void
      {
         if(dg1.dataType == ItemsDataGroup.TYPE_EQUIP)
         {
            equipGripSwap(dg1 as EquipDataGroup,dg2 as EquipDataGroup,site1,site2);
         }
         else if(dg1.dataType == ItemsDataGroup.TYPE_ARMS)
         {
            armsGripSwap(dg1 as ArmsDataGroup,dg2 as ArmsDataGroup,site1,site2);
         }
         else if(dg1.dataType == ItemsDataGroup.TYPE_THINGS)
         {
            thingsGripSwap(dg1,dg2,site1,site2);
         }
         else if(dg1.dataType == ItemsDataGroup.TYPE_PARTS)
         {
            partsGripSwap(dg1,dg2,site1,site2);
         }
         else if(dg1.dataType == ItemsDataGroup.TYPE_SKILL)
         {
            skillGripSwap(dg1,dg2,site1,site2);
         }
         else if(dg1.dataType == ItemsDataGroup.TYPE_MORE)
         {
            thingsGripSwap(dg1,dg2,site1,site2);
         }
      }
      
      public static function swapLimit(dg1:ItemsDataGroup, dg2:ItemsDataGroup, site1:int, site2:int) : Boolean
      {
         var c0:CheckData = null;
         var da1:IO_ItemsData = null;
         var da2:IO_ItemsData = null;
         var equipDa1:EquipData = null;
         var check1:CheckData = null;
         var equipDa2:EquipData = null;
         var check2:CheckData = null;
         var type1:String = dg1.dataType;
         var type2:String = dg2.dataType;
         if(type1 == type2)
         {
            if(dg1 != dg2)
            {
               da1 = dg1.getDataBySite(site1);
               da2 = dg2.getDataBySite(site2);
               if(type1 == ItemsDataGroup.TYPE_ARMS || type1 == ItemsDataGroup.TYPE_EQUIP)
               {
                  if(da1 && dg2.placeType == ItemsDataGroup.PLACE_WEAR && Boolean(dg2.normalPlayerData))
                  {
                     if(da1.getWearLevel() > dg2.normalPlayerData.getWearLevel())
                     {
                        return false;
                     }
                     equipDa1 = da1 as EquipData;
                     if(Boolean(equipDa1))
                     {
                        check1 = EquipDataSwapPan.canLoadPan(dg2.normalPlayerData,equipDa1);
                        if(check1.bb == false)
                        {
                           return false;
                        }
                     }
                  }
                  if(da2 && dg1.placeType == ItemsDataGroup.PLACE_WEAR && Boolean(dg1.normalPlayerData))
                  {
                     if(da2.getWearLevel() > dg1.normalPlayerData.getWearLevel())
                     {
                        return false;
                     }
                     equipDa2 = da2 as EquipData;
                     if(Boolean(equipDa2))
                     {
                        check2 = EquipDataSwapPan.canLoadPan(dg1.normalPlayerData,equipDa2);
                        if(check2.bb == false)
                        {
                           return false;
                        }
                     }
                  }
               }
            }
            c0 = null;
            if(type1 == ItemsDataGroup.TYPE_ARMS)
            {
               c0 = ArmsDataGroup.armsSwapTo(dg1 as ArmsDataGroup,dg2 as ArmsDataGroup,site1,site2);
            }
            else if(type1 == ItemsDataGroup.TYPE_EQUIP)
            {
               c0 = EquipDataGroup.equipSwapTo(dg1 as EquipDataGroup,dg2 as EquipDataGroup,site1,site2);
            }
            else if(type1 == ItemsDataGroup.TYPE_THINGS)
            {
               c0 = ItemsDataGroup.swapTo(dg1,dg2,site1,site2);
            }
            else if(type1 == ItemsDataGroup.TYPE_MORE)
            {
               c0 = ItemsDataGroup.swapTo(dg1,dg2,site1,site2);
            }
            else if(type1 == ItemsDataGroup.TYPE_PARTS)
            {
               c0 = partsGripSwap(dg1,dg2,site1,site2,false);
            }
            else if(type1 == ItemsDataGroup.TYPE_SKILL)
            {
               c0 = HeroSkillDataGroup.skillSwapTo(dg1 as HeroSkillDataGroup,dg2 as HeroSkillDataGroup,site1,site2);
            }
            if(Boolean(c0))
            {
               return c0.bb;
            }
            return false;
         }
         return false;
      }
      
      public static function swapWearAllByRole(role1:String, role2:String, tipB0:Boolean = true) : String
      {
         var m1:MoreData = null;
         var m2:MoreData = null;
         var nameArr0:Array = null;
         var errorArr0:Array = null;
         var name0:* = null;
         var dg1:ItemsDataGroup = null;
         var dg2:ItemsDataGroup = null;
         var error0:int = 0;
         var tip0:String = null;
         var errorTip0:String = "";
         if(role1 != role2)
         {
            m1 = Gaming.PG.da.moreWay.getDataByHeroName(role1);
            m2 = Gaming.PG.da.moreWay.getDataByHeroName(role2);
            if(Boolean(m1) && Boolean(m2))
            {
               nameArr0 = ["arms","equip"];
               errorArr0 = [];
               for each(name0 in nameArr0)
               {
                  dg1 = m1.DATA[name0];
                  dg2 = m2.DATA[name0];
                  error0 = swapWearAll(dg1,dg2);
                  if(error0 > 0)
                  {
                     errorArr0.push(dg1.getCnName() + "：" + error0 + "个");
                  }
               }
               if(errorArr0.length > 0)
               {
                  errorTip0 += "以下物品交换失败：\n";
                  errorTip0 += StringMethod.concatStringArr(errorArr0,2);
               }
               if(tipB0)
               {
                  tip0 = "交换完成！" + errorTip0;
                  Gaming.uiGroup.alertBox.showInfo(tip0);
               }
            }
         }
         return errorTip0;
      }
      
      public static function swapWearAll(dg1:ItemsDataGroup, dg2:ItemsDataGroup) : int
      {
         if(dg1 != dg2 && dg1.placeType == ItemsDataGroup.PLACE_WEAR && dg2.placeType == ItemsDataGroup.PLACE_WEAR)
         {
            if(dg1.dataType == ItemsDataGroup.TYPE_EQUIP)
            {
               return swap_EquipWearAll(dg1 as EquipDataGroup,dg2 as EquipDataGroup);
            }
            return swap_otherWearAll(dg1,dg2);
         }
         return 0;
      }
      
      private static function swap_EquipWearAll(dg1:EquipDataGroup, dg2:EquipDataGroup) : int
      {
         var type0:* = null;
         var site0:int = 0;
         var bb0:Boolean = false;
         var error0:int = 0;
         var typeArr0:Array = EquipType.WEAR_ARR;
         for each(type0 in typeArr0)
         {
            site0 = EquipType.getSite(type0);
            bb0 = swapLimit(dg1,dg2,site0,site0);
            if(bb0 == false)
            {
               error0++;
            }
         }
         return error0;
      }
      
      private static function swap_otherWearAll(dg1:ItemsDataGroup, dg2:ItemsDataGroup) : int
      {
         var bb0:Boolean = false;
         var error0:int = 0;
         var len0:int = Math.min(dg1.getSaveGroup().gripMaxNum,dg2.getSaveGroup().gripMaxNum);
         for(var i:int = 0; i < len0; i++)
         {
            bb0 = swapLimit(dg1,dg2,i,i);
            bb0 = false;
            if(false)
            {
               error0++;
            }
         }
         return error0;
      }
      
      private static function thingsGripSwap(dg1:ItemsDataGroup, dg2:ItemsDataGroup, site1:int, site2:int) : *
      {
         ItemsDataGroup.swapTo(dg1,dg2,site1,site2);
         Gaming.soundGroup.playSound("uiSound","swapSuccess");
      }
      
      private static function partsGripSwap(dg1:ItemsDataGroup, dg2:ItemsDataGroup, site1:int, site2:int, tipB0:Boolean = true) : CheckData
      {
         var pg1:PartsDataGroup = dg1 as PartsDataGroup;
         var pg2:PartsDataGroup = dg2 as PartsDataGroup;
         var mustLv0:int = 999;
         if(Boolean(pg1.nowArmsData))
         {
            mustLv0 = pg1.nowArmsData.save.getTrueLevel();
         }
         if(Boolean(pg2.nowArmsData))
         {
            mustLv0 = pg2.nowArmsData.save.getTrueLevel();
         }
         var check0:CheckData = PartsDataGroup.partsSwapTo(pg1,pg2,site1,site2,mustLv0);
         if(tipB0)
         {
            if(!check0.bb)
            {
               if(check0.info != "")
               {
                  Gaming.uiGroup.alertBox.showError(check0.info);
               }
            }
            else
            {
               Gaming.soundGroup.playSound("uiSound","swapSuccess");
            }
         }
         return check0;
      }
      
      private static function skillGripSwap(dg1:ItemsDataGroup, dg2:ItemsDataGroup, site1:int, site2:int) : *
      {
         var check0:CheckData = HeroSkillDataGroup.skillSwapTo(dg1 as HeroSkillDataGroup,dg2 as HeroSkillDataGroup,site1,site2);
         if(!check0.bb)
         {
            if(check0.info != "")
            {
               Gaming.uiGroup.alertBox.showError(check0.info);
            }
         }
         else
         {
            Gaming.soundGroup.playSound("uiSound","swapSuccess");
         }
      }
      
      private static function armsGripSwap(dg1:ArmsDataGroup, dg2:ArmsDataGroup, site1:int, site2:int) : *
      {
         var check0:CheckData = ArmsDataGroup.armsSwapTo(dg1,dg2,site1,site2,Gaming.PG.DATA.getWearLevel());
         if(!check0.bb)
         {
            if(check0.info != "")
            {
               Gaming.uiGroup.alertBox.showError(check0.info);
            }
         }
         else
         {
            Gaming.soundGroup.playSound("uiSound","swapSuccess");
         }
      }
      
      public static function equipGripSwap(dg1:EquipDataGroup, dg2:EquipDataGroup, site1:int, site2:int) : CheckData
      {
         var check0:CheckData = EquipDataGroup.equipSwapTo(dg1 as EquipDataGroup,dg2 as EquipDataGroup,site1,site2,Gaming.PG.DATA.getWearLevel());
         if(!check0.bb)
         {
            if(check0.info != "")
            {
               Gaming.uiGroup.alertBox.showError(check0.info);
            }
         }
         else
         {
            Gaming.soundGroup.playSound("uiSound","swapSuccess");
         }
         return check0;
      }
      
      private static function heroImgMouseUp(e:MouseEvent) : *
      {
         var dg1:ItemsDataGroup = null;
         var site1:int = 0;
         var e_dg1:EquipDataGroup = null;
         var da1:EquipData = null;
         var type0:String = null;
         var site2:int = 0;
         var dragCtrl:ItemsDragController = Gaming.uiGroup.dragCtrl;
         if(Boolean(dragCtrl.dragChild))
         {
            dg1 = dragCtrl.clickEvent.fatherData as ItemsDataGroup;
            if(dg1.dataType == ItemsDataGroup.TYPE_EQUIP && dg1.placeType == ItemsDataGroup.PLACE_BAG)
            {
               site1 = dragCtrl.clickEvent.index;
               e_dg1 = dg1 as EquipDataGroup;
               da1 = e_dg1.getDataBySite(site1) as EquipData;
               if(Boolean(da1))
               {
                  type0 = da1.save.partType;
                  site2 = EquipType.getSite(type0);
                  equipGripSwap(e_dg1,Gaming.PG.DATA.equip,site1,site2);
                  Gaming.PG.changeEquip();
                  Gaming.uiGroup.bagUI.fleshAllBox();
                  Gaming.uiGroup.wearUI.fleshAllBox();
                  Gaming.uiGroup.wearUI.showHDFashionFastPan(da1);
               }
            }
         }
         dragCtrl.clear();
      }
      
      public static function mouseUp(e:MouseEvent) : *
      {
         Gaming.uiGroup.dragCtrl.stopDraging();
         if(e.target == Gaming.uiGroup.wearUI.heroImgBox)
         {
            heroImgMouseUp(e);
         }
         Gaming.uiGroup.dragCtrl.clear();
         if(!(e.target is ItemsGrid))
         {
            ItemsGripBtnListCtrl.hide();
         }
         Gaming.uiGroup.bulletPathBox.mouseUp(e);
      }
   }
}

