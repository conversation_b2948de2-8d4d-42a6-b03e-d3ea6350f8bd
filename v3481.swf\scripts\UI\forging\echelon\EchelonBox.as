package UI.forging.echelon
{
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.items.creator.ItemsStrengthenCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class EchelonBox extends NormalUI
   {
      private var btnSp:MovieClip;
      
      private var hookMc:MovieClip;
      
      private var thingsTag:Sprite;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var thingsBox:ItemsGripBox = new ItemsGripBox();
      
      private var infoTxt:TextField;
      
      public function EchelonBox()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["infoTxt","thingsTag","btnSp","hookMc"];
         super.setImg(img0);
         this.thingsTag.addChild(this.thingsBox);
         this.thingsBox.arg.init(1,1,3,3);
         this.thingsBox.setIconPro("equipGrip");
         this.thingsBox.evt.setWantEvent(true,false,false,true,true);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.thingsBox);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("开启保级道具");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.btn.addEventListener(MouseEvent.MOUSE_OVER,this.btnOver);
         this.btn.addEventListener(MouseEvent.MOUSE_OUT,this.btnOut);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function inData(d0:MustDefine, openB0:Boolean, overMaxB0:Boolean, isMinB0:Boolean) : Boolean
      {
         var thingsB0:Boolean = false;
         if(openB0)
         {
            this.btn.setName("保级道具已开启");
            this.btn.isChosen = true;
            this.btn.y = 0;
            this.hookMc.visible = true;
            thingsB0 = this.thingsBox.inMustDefine(d0);
            this.setHook(thingsB0);
            if(!d0.getThingsArr())
            {
               if(isMinB0)
               {
                  this.infoTxt.htmlText = ComMethod.color("当前强化失败后不降级，\n不消耗保级道具。","#00FF00");
               }
               else if(overMaxB0)
               {
                  this.infoTxt.htmlText = ComMethod.color("当前强化等级已超出保级范围，\n无法对强化等级进行保级。","#FF9900");
                  this.hookMc.visible = false;
               }
               else
               {
                  this.infoTxt.htmlText = ComMethod.color("当前强化成功率为100%，\n不消耗保级道具。","#00FF00");
               }
               this.thingsBox.visible = false;
               this.infoTxt.visible = true;
            }
            else
            {
               this.thingsBox.visible = true;
               this.infoTxt.visible = false;
            }
            return thingsB0;
         }
         this.btn.setName("开启保级道具");
         this.btn.isChosen = false;
         this.btn.y = 45;
         this.thingsBox.visible = false;
         this.hookMc.visible = false;
         this.infoTxt.visible = false;
         return true;
      }
      
      public function setHook(bb0:Boolean) : void
      {
         if(Boolean(this.hookMc))
         {
            this.hookMc.gotoAndStop(bb0 ? 1 : 2);
         }
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var e0:ClickEvent = new ClickEvent();
         dispatchEvent(e0);
      }
      
      private function btnOver(e:MouseEvent) : void
      {
         var maxLv0:int = ItemsStrengthenCtrl.getEchelonMaxLevel();
         var str0:String = "开启保级道具后，当强化等级小于或等于<green " + maxLv0 + "级/>时，强化失败不降级。";
         Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
      }
      
      private function btnOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
   }
}

