package UI.gift.anniver.gm
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.text.TextField;
   
   public class AnniverGmGiftBox extends AutoNormalUI
   {
      private var nameTxt:TextField;
      
      private var timeTxt:TextField;
      
      private var iconSp:Sprite;
      
      private var icon:ItemsGrid = new ItemsGrid();
      
      public function AnniverGmGiftBox()
      {
         super();
         mcTypeArr = ["txt"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         addChild(this.icon);
         this.icon.setImg(this.iconSp as MovieClip);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.icon);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function inGift(gift0:GiftAddDefineGroup, openName0:String) : void
      {
         var unlockB0:Boolean = gift0.name == openName0;
         var one0:GiftAddDefine = gift0.arr[0];
         this.icon.inData_gift(one0);
         this.icon.setSmallIcon(unlockB0 ? "2" : "1");
         this.nameTxt.htmlText = gift0.cnName + "x" + one0.num;
         var timeStr0:String = "掉落时间：\n";
         if(gift0.name.indexOf("1") >= 0)
         {
            timeStr0 = "周一、周三\n周五、周日";
         }
         else
         {
            timeStr0 = "周二、周四\n周六";
         }
         this.timeTxt.htmlText = timeStr0;
      }
   }
}

