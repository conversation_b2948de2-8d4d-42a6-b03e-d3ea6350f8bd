package UI.gift.exchange
{
   import UI.base.font.FontDeal;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.StringDate;
   import dataAll.gift.define.ExchangeGiftAddDefineGroup;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import flash.text.TextField;
   
   public class ExchangeGiftTitleGrip extends Sprite
   {
      public var nameTxt:TextField;
      
      public var linkTxt:TextField;
      
      public var giftDefine:ExchangeGiftAddDefineGroup;
      
      public var canGetCodeB:Boolean = false;
      
      public function ExchangeGiftTitleGrip()
      {
         super();
      }
      
      public function setToNormalImg(name0:String = "exchangeTitleGrip") : void
      {
         var img0:Sprite = Gaming.swfLoaderManager.getResource("GiftUI",name0);
         addChild(img0);
         img0.mouseChildren = false;
         img0.mouseEnabled = false;
         this.nameTxt = img0["nameTxt"];
         addChild(this.nameTxt);
         this.linkTxt = img0["linkTxt"];
         if(Boolean(this.linkTxt))
         {
            addChild(this.linkTxt);
         }
         FontDeal.dealOne(this.nameTxt);
         FontDeal.dealOne(this.linkTxt);
         if(Boolean(this.linkTxt))
         {
            this.linkTxt.styleSheet = ComMethod.getLinkCss("#00FF00","#FFFFFF");
         }
         if(Boolean(this.linkTxt))
         {
            this.linkTxt.addEventListener(TextEvent.LINK,this.linkClick);
         }
         addEventListener(MouseEvent.MOUSE_OVER,this.linkOver);
         addEventListener(MouseEvent.MOUSE_OUT,this.linkOut);
      }
      
      public function inData(d0:ExchangeGiftAddDefineGroup) : void
      {
         this.giftDefine = d0;
         this.nameTxt.text = d0.cnName;
         if(Boolean(this.linkTxt))
         {
            this.linkTxt.visible = d0.codeUrl != "";
         }
      }
      
      public function setLinkVisible(bb0:Boolean) : void
      {
         if(Boolean(this.linkTxt))
         {
            this.linkTxt.visible = bb0;
         }
      }
      
      public function fleshTime(now0:StringDate) : void
      {
         this.canGetCodeB = this.giftDefine.isStartB(now0);
         if(Boolean(this.linkTxt))
         {
            if(this.giftDefine.codeUrl != "")
            {
               if(this.canGetCodeB)
               {
                  this.linkTxt.htmlText = ComMethod.link("领取兑换码","link");
               }
               else
               {
                  this.linkTxt.htmlText = ComMethod.color("领取兑换码","#999999");
               }
            }
            else
            {
               this.linkTxt.htmlText = "";
            }
         }
      }
      
      public function clear() : void
      {
         if(Boolean(this.linkTxt))
         {
            this.linkTxt.removeEventListener(TextEvent.LINK,this.linkClick);
         }
         removeEventListener(MouseEvent.MOUSE_OVER,this.linkOver);
         removeEventListener(MouseEvent.MOUSE_OUT,this.linkOut);
      }
      
      private function linkClick(e:TextEvent) : void
      {
         if(this.canGetCodeB)
         {
            navigateToURL(new URLRequest(this.giftDefine.codeUrl),"_blank");
         }
      }
      
      private function linkOver(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
         var str0:String = this.giftDefine.getTip(this.canGetCodeB);
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
      }
      
      private function linkOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
   }
}

