package w_test.cheating
{
   import UI.creator.arms.ArmsCreatorBox;
   import dataAll.arms.ArmsData;
   import dataAll.arms.creator.ArmsDataCreator;
   import dataAll.arms.creator.ArmsUpgradeCtrl;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.arms.save.ArmsSave;
   import dataAll.drop.define.ArmsColorDefine;
   import dataAll.drop.define.DropColorDefineGroup;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipFatherDefine;
   import dataAll.equip.device.DeviceDataCreator;
   import dataAll.equip.device.DeviceDefine;
   import dataAll.equip.device.DeviceSave;
   import dataAll.equip.save.EquipSave;
   import dataAll.equip.weapon.WeaponDataCreator;
   import dataAll.equip.weapon.WeaponDefine;
   import dataAll.equip.weapon.WeaponSave;
   import dataAll.items.IO_ItemsData;
   import dataAll.skill.define.SkillFather;
   import gameAll.arms.GameArmsCtrl;
   import gameAll.drop.bodyDrop.ArmsBodyDrop;
   
   public class EquipCheating extends OneCheating
   {
      private var armsCreatorBox:ArmsCreatorBox = null;
      
      public function EquipCheating()
      {
         super();
      }
      
      public function showArmsCreator(str0:String, v0:int) : String
      {
         var box0:ArmsCreatorBox = this.armsCreatorBox;
         if(!box0)
         {
            box0 = new ArmsCreatorBox();
            box0.setImg(Gaming.swfLoaderManager.getResource("ArenaUI","armsCreatorBox"));
            Gaming.gameSprite.L_topUI.addChild(box0);
            box0.x = 70;
            box0.y = 30;
            this.armsCreatorBox = box0;
         }
         box0.addArmsSave(this.yesAddArms);
         return "";
      }
      
      private function yesAddArms(s0:ArmsSave) : ArmsData
      {
         var da0:ArmsData = Gaming.PG.da.armsBag.addSave(s0) as ArmsData;
         GameArmsCtrl.addArmsSaveResoure(s0);
         return da0;
      }
      
      public function addArmsChip(str0:String, v0:int) : String
      {
         var d0:ArmsRangeDefine = null;
         var arr0:Array = Gaming.defineGroup.bullet.blackArmsRangeArr;
         for each(d0 in arr0)
         {
            if(d0.def.dropLevelArr[0] < 100)
            {
               Gaming.PG.da.thingsBag.addDataByName(d0.def.name,v0);
            }
         }
         return "添加所有武器碎片" + v0 + "个";
      }
      
      public function addRareArms(str0:String, v0:int) : String
      {
         var d0:ArmsRangeDefine = null;
         var s0:ArmsSave = null;
         var lv0:int = Gaming.PG.da.level;
         var arr0:Array = Gaming.defineGroup.bullet.rareArmsRangeArr;
         for each(d0 in arr0)
         {
            s0 = Gaming.defineGroup.armsCreator.getSuperSaveByArmsRangeName(lv0,d0.def.name);
            Gaming.PG.da.armsBag.addSave(s0);
         }
         return "添加所有稀有武器";
      }
      
      public function addBlackArms(str0:String, v0:int) : String
      {
         var d0:ArmsRangeDefine = null;
         var s0:ArmsSave = null;
         var lv0:int = Gaming.PG.da.level;
         var arr0:Array = Gaming.defineGroup.bullet.blackArmsRangeArr;
         for each(d0 in arr0)
         {
            s0 = Gaming.defineGroup.armsCreator.getBlackSave(lv0,d0.def);
            Gaming.PG.da.armsBag.addSave(s0);
            GameArmsCtrl.addArmsSaveResoure(s0);
         }
         return "添加所有黑色武器";
      }
      
      public function setStrengthenLv(str0:String, v0:int) : String
      {
         var da0:IO_ItemsData = Gaming.uiGroup.forgingUI.getNowData();
         if(Boolean(da0))
         {
            da0.getSave().setStrengthenLvAndMax(v0);
         }
         return da0.getSave().cnName + "：强化等级=" + v0;
      }
      
      public function setEvoLv(str0:String, v0:int) : String
      {
         var equipDa0:EquipData = null;
         var armsDa0:ArmsData = null;
         var da0:IO_ItemsData = Gaming.uiGroup.forgingUI.getNowData();
         if(Boolean(da0))
         {
            equipDa0 = da0 as EquipData;
            armsDa0 = da0 as ArmsData;
            if(Boolean(equipDa0))
            {
               equipDa0.save.evoLv = v0;
            }
            if(Boolean(armsDa0))
            {
               armsDa0.save.evoLv = v0;
            }
            if(Boolean(equipDa0) || Boolean(armsDa0))
            {
               return da0.getSave().cnName + "：进化等级=" + v0;
            }
         }
         return "没找到数据";
      }
      
      public function sameToFirst(str0:String, v0:int) : String
      {
         var da0:ArmsData = null;
         var first0:ArmsData = PD.arms.getSiteDataArray()[0];
         var arr0:Array = PD.armsBag.dataArr;
         for each(da0 in arr0)
         {
            da0.sameForm(first0,true,true,true,true);
         }
         return "处理的武器数量：" + arr0.length;
      }
      
      public function addEquipChip(str0:String, v0:int) : String
      {
         var f0:EquipFatherDefine = null;
         var d0:EquipDefine = null;
         var arr0:Array = Gaming.defineGroup.equip.blackFatherArr;
         for each(f0 in arr0)
         {
            for each(d0 in f0.partObj)
            {
               Gaming.PG.da.thingsBag.addDataByName(d0.name,v0);
            }
         }
         return "添加所有装备碎片" + v0 + "个";
      }
      
      public function addSuit(str0:String, v0:int) : String
      {
         Gaming.PG.da.equipBag.saveGroup.unlockTo(120);
         Gaming.PG.da.equipBag.clearData();
         Gaming.testCtrl.arms.addAllSuit();
         return "添加所有套装";
      }
      
      public function addAllFashion(str0:String, v0:int) : String
      {
         var d0:EquipDefine = null;
         var s0:EquipSave = null;
         for each(d0 in Gaming.defineGroup.equip.fashionObj)
         {
            s0 = Gaming.defineGroup.equipCreator.getFashionSave(d0.name);
            Gaming.PG.da.equipBag.addSave(s0);
         }
         return "添加所有时装";
      }
      
      public function addAllDevice(str0:String, v0:int) : String
      {
         var d0:DeviceDefine = null;
         var s0:DeviceSave = null;
         if(v0 < 1)
         {
            v0 = 1;
         }
         for each(d0 in Gaming.defineGroup.device.arr)
         {
            s0 = DeviceDataCreator.getSave(d0.name + "_1",v0);
            Gaming.PG.da.equipBag.addSave(s0);
         }
         return "添加所有装置";
      }
      
      public function addAllWeapon(str0:String, v0:int) : String
      {
         var d0:WeaponDefine = null;
         var s0:WeaponSave = null;
         if(v0 < 1)
         {
            v0 = 1;
         }
         for each(d0 in Gaming.defineGroup.weapon.arr)
         {
            if(d0.lv == 1)
            {
               s0 = WeaponDataCreator.getSave(d0.name,v0);
               Gaming.PG.da.equipBag.addSave(s0);
            }
         }
         return "添加所有兵器";
      }
      
      public function dealAllArms(str0:String, v0:int) : String
      {
         ArmsDataCreator.dealAllArms();
         return "";
      }
      
      public function setFirstArmsGodSkill(str0:String, v0:int) : String
      {
         var da0:ArmsData = Gaming.PG.da.arms.getSiteDataArray()[0];
         var cnArr0:Array = str0.split(",");
         var skillArr0:Array = Gaming.defineGroup.skill.getSkillNameArrByCnArr(cnArr0,SkillFather.godArmsSkill);
         da0.save.godSkillArr = skillArr0;
         return "设置武器：" + da0.getCnName() + " 神技：" + skillArr0;
      }
      
      public function bagSameToFirst(str0:String, v0:int) : String
      {
         var da0:ArmsData = null;
         var s0:ArmsSave = null;
         var arr0:Array = Gaming.PG.da.armsBag.dataArr;
         var first0:ArmsData = Gaming.PG.da.arms.getSiteDataArray()[0];
         var f0:ArmsSave = first0.save;
         var num0:int = 0;
         for each(da0 in arr0)
         {
            if(da0.def.isRandomB())
            {
               s0 = da0.save;
               s0.bounceD = f0.bounceD.copy();
               s0.penetrationGap = f0.penetrationGap;
               s0.penetrationNum = f0.penetrationNum;
               s0.critD = f0.critD.copy();
               s0.twoShootPro = f0.twoShootPro;
               s0.skillArr = f0.skillArr.concat();
               s0.godSkillArr = f0.godSkillArr.concat();
               num0++;
            }
         }
         return "处理数量：" + num0;
      }
      
      public function addRedArms(str0:String, v0:int) : String
      {
         var s0:ArmsSave = null;
         var sarr0:Array = str0.split(",");
         var name0:String = sarr0[0];
         var lv0:int = int(sarr0[1]);
         var dropName0:String = sarr0[2];
         v0 = int(sarr0[3]);
         for(var i:int = 0; i < v0; i++)
         {
            s0 = Gaming.defineGroup.armsCreator.getSkill2RedSave(name0,lv0,dropName0);
            this.yesAddArms(s0);
         }
         return "添加" + v0 + "把：" + name0 + " " + lv0 + "级 " + dropName0;
      }
      
      public function addRanBlackArms(str0:String, v0:int) : String
      {
         var s0:ArmsSave = null;
         var sarr0:Array = str0.split(",");
         var lv0:int = int(sarr0[0]);
         var num0:int = int(sarr0[1]);
         for(var i:int = 0; i < num0; i++)
         {
            s0 = Gaming.defineGroup.armsCreator.getRanBlackSaveTest(lv0);
            this.yesAddArms(s0);
         }
         return "添加" + num0 + "把：" + lv0 + "级黑色随机属性的武器";
      }
      
      public function testBlackArms(str0:String, v0:int) : String
      {
         var da0:ArmsData = null;
         var lv0:int = 0;
         var j:int = 0;
         var arr0:Array = Gaming.PG.da.armsBag.dataArr;
         for each(da0 in arr0)
         {
            lv0 = da0.save.getTrueLevel();
            for(j = 0; j < 99 - lv0; j++)
            {
               ArmsUpgradeCtrl.upgradeOne(da0,false);
            }
         }
         return "升级背包所有武器到99";
      }
      
      public function addBlackArmsPro(str0:String, v0:int) : String
      {
         var dg0:DropColorDefineGroup = null;
         var colorD0:ArmsColorDefine = null;
         var arr0:Array = Gaming.defineGroup.dropColor.arr;
         for each(dg0 in arr0)
         {
            colorD0 = dg0.arms;
            colorD0.bossPro = [0,0,0,0,0,1];
         }
         ArmsBodyDrop.blackTestB = true;
         return "大幅增加黑色武器掉率。";
      }
   }
}

