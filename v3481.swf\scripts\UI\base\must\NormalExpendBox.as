package UI.base.must
{
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import dataAll._data.ConstantDefine;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   import flash.text.TextField;
   
   public class NormalExpendBox extends AutoNormalUI
   {
      private var mustBox:NormalMustBox;
      
      private var okBtn:NormalBtn;
      
      private var closeBtn:NormalBtn;
      
      private var infoTxt:TextField;
      
      private var dat:ExpendUIData = null;
      
      public function NormalExpendBox()
      {
         super();
         mcTypeArr = ["btnSp","mustBoxSp","txt"];
      }
      
      override protected function firstLoad() : void
      {
         this.setImg(Gaming.swfLoaderManager.getResourceFull("PetUI/expendBox"));
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      public function setPositionBySp(sp0:DisplayObject) : void
      {
         var rect0:Rectangle = sp0.getRect(Gaming.gameSprite.L_topUI);
         var x0:int = rect0.x + rect0.width / 2;
         var y0:int = rect0.y + rect0.height;
         if(y0 + this.height > ConstantDefine.HEIGHT)
         {
            y0 = rect0.y - this.height;
         }
         this.x = x0;
         this.y = y0;
      }
      
      public function showData(dat0:ExpendUIData, positionSp0:DisplayObject = null) : void
      {
         var btn0:NormalBtn = null;
         show();
         if(Boolean(positionSp0))
         {
            this.setPositionBySp(positionSp0);
         }
         this.dat = dat0;
         for each(btn0 in btnObj)
         {
            btn0.setName(this.dat.getBtnCn(btn0.label));
         }
         this.dat.showDataEvent(this.okBtn,this.infoTxt,this.mustBox);
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var funName0:String = btn0.label + "BtnClick";
         this[funName0](e);
      }
      
      private function okBtnClick(e:MouseEvent) : void
      {
         var mustD0:MustDefine = this.dat.getMustDefine();
         PlayerMustCtrl.deductMust(mustD0,this.affter_btnClick);
      }
      
      private function affter_btnClick() : void
      {
         this.dat.successEvent();
         if(this.dat.getSuccessReSetDataB())
         {
            this.showData(this.dat);
         }
      }
      
      private function closeBtnClick(e:MouseEvent) : void
      {
         hide();
      }
   }
}

