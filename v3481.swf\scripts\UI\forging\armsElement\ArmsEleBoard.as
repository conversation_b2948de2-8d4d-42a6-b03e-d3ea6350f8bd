package UI.forging.armsElement
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.must.NormalMustBox;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.NumberMethod;
   import dataAll.arms.ArmsData;
   import dataAll.arms.ArmsDataGroup;
   import dataAll.arms.creator.ArmsElementCtrl;
   import dataAll.arms.save.ArmsSave;
   import dataAll.body.attack.ElementHurt;
   import dataAll.body.attack.ElementHurtDefine;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class ArmsEleBoard extends AutoNormalUI
   {
      private var beforeTxt:TextField;
      
      private var beforeGrip:ItemsGrid = new ItemsGrid();
      
      private var afterTxt:TextField;
      
      private var afterGrip:ItemsGrid = new ItemsGrid();
      
      private var mustBox:NormalMustBox;
      
      private var btn:NormalBtn;
      
      private var upBtn:NormalBtn;
      
      private var downBtn:NormalBtn;
      
      public var nowData:ArmsData = null;
      
      private var nowElement:ElementHurtDefine = null;
      
      public function ArmsEleBoard()
      {
         super();
         mcTypeArr = ["btnSp","mustBoxSp","txt","Bx"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         this.btn.setName("生成元素伤害");
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.beforeGrip);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.afterGrip);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,"arms");
         this.showOneArmsDataAndPan(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      public function armsGripClick(da0:ArmsData) : void
      {
         if(visible)
         {
            this.showOneArmsDataAndPan(da0);
         }
      }
      
      private function showOneArmsDataAndPan(da0:ArmsData) : void
      {
         var dg0:ArmsDataGroup = null;
         this.showNone();
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择目标武器。");
         if(Boolean(da0))
         {
            dg0 = Gaming.PG.da.findArmsData(da0);
            if(dg0 is ArmsDataGroup)
            {
               this.showOneArmsData(da0);
            }
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      private function showOneArmsData(da0:ArmsData) : void
      {
         this.nowData = da0;
         this.nextElement(0);
         var after_da0:ArmsData = ArmsElementCtrl.getAfterData(da0,this.nowElement);
         this.showOneText(da0,"before");
         this.showOneText(after_da0,"after");
         var btnStr0:String = "";
         if(da0.save.ele != this.nowElement.name)
         {
            if(da0.save.eleLv >= 1)
            {
               btnStr0 = "转化成";
            }
            else
            {
               btnStr0 = "生成";
            }
         }
         else
         {
            btnStr0 = "强化";
         }
         this.btn.setName(btnStr0 + this.nowElement.getHurtCn());
         var must_d0:MustDefine = ArmsElementCtrl.getMust(da0,this.nowElement);
         var mustB0:Boolean = this.mustBox.inData(must_d0);
         this.btn.actived = mustB0 && Boolean(after_da0);
      }
      
      private function showOneText(da0:ArmsData, label0:String) : void
      {
         var s0:ArmsSave = null;
         var eleD0:ElementHurtDefine = null;
         var grip0:ItemsGrid = this[label0 + "Grip"];
         var text0:TextField = this[label0 + "Txt"];
         var str0:String = "";
         if(Boolean(da0))
         {
            grip0.inData_arms(da0);
            s0 = da0.save;
            if(s0.ele != "")
            {
               eleD0 = ElementHurt.getDefine(s0.ele);
               str0 += ComMethod.color(eleD0.getHurtCn() + "：" + NumberMethod.toPer(da0.getEleHurtMul()),eleD0.getColor());
            }
            else
            {
               str0 += "无元素伤害";
            }
         }
         else
         {
            grip0.clearData();
            str0 = "元素伤害已强化至最高";
         }
         text0.htmlText = str0;
      }
      
      private function nextElement(add0:int) : void
      {
         var index0:int = 0;
         var earr0:Array = ElementHurt.uiArr;
         if(Boolean(this.nowElement))
         {
            index0 = int(earr0.indexOf(this.nowElement.name));
         }
         var new0:int = (index0 + add0 + earr0.length) % earr0.length;
         var newName0:String = earr0[new0];
         this.nowElement = ElementHurt.getDefine(newName0);
         this.upBtn.visible = true;
         this.downBtn.visible = true;
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = null;
         var must_d0:MustDefine = null;
         if(Boolean(this.nowData))
         {
            btn0 = e.target as NormalBtn;
            if(btn0.actived)
            {
               if(btn0 == this.btn)
               {
                  must_d0 = ArmsElementCtrl.getMust(this.nowData,this.nowElement);
                  PlayerMustCtrl.deductMust(must_d0,this.afterStrengthen);
               }
               else if(btn0 == this.upBtn)
               {
                  this.nextElement(-1);
                  this.showOneArmsData(this.nowData);
               }
               else if(btn0 == this.downBtn)
               {
                  this.nextElement(1);
                  this.showOneArmsData(this.nowData);
               }
            }
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("武器数据不存在！");
         }
      }
      
      private function afterStrengthen() : void
      {
         ArmsElementCtrl.deal(this.nowData,this.nowElement);
         this.yes_save();
      }
      
      private function yes_save(v:* = null) : void
      {
         this.showOneArmsDataAndPan(this.nowData);
         Gaming.uiGroup.allBagUI.fleshAllBox();
         Gaming.uiGroup.alertBox.showSuccess("生成元素伤害成功！");
      }
      
      private function showNone() : void
      {
         this.nowData = null;
         this.beforeGrip.clearData();
         this.beforeTxt.text = "";
         this.afterGrip.clearData();
         this.afterTxt.text = "";
         this.mustBox.setShowState(false);
         this.btn.actived = false;
         this.upBtn.visible = false;
         this.downBtn.visible = false;
      }
   }
}

