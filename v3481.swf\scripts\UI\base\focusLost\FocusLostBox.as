package UI.base.focusLost
{
   import UI.base.HaveConSprite;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class FocusLostBox extends HaveConSprite
   {
      private var img:Sprite;
      
      public function FocusLostBox()
      {
         super();
      }
      
      public function imgInit() : void
      {
         if(!this.img)
         {
            this.img = Gaming.uiGroup.getBasicMovieClip("focusLostSp");
            this.img.mouseChildren = false;
            this.img.mouseEnabled = true;
            this.img.buttonMode = true;
            this.img.addEventListener(MouseEvent.CLICK,this.click);
         }
      }
      
      public function showBreak() : void
      {
         inCon();
         if(<PERSON><PERSON>an(this.img))
         {
            this.addChild(this.img);
         }
      }
      
      public function hideBreak() : void
      {
         outCon();
         if(<PERSON>olean(this.img) && Boolean(this.img.parent))
         {
            this.img.parent.removeChild(this.img);
         }
      }
      
      private function click(e:MouseEvent) : void
      {
         this.hideBreak();
         Gaming.LG.resumeLevel();
      }
   }
}

