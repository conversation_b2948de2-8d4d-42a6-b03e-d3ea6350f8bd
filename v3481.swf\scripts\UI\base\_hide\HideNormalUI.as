package UI.base._hide
{
   import UI.base.NormalUI;
   
   public class HideNormalUI extends NormalUI
   {
      protected var visibleX:int = 0;
      
      protected var showX:int = 0;
      
      public function HideNormalUI()
      {
         super();
      }
      
      override public function get x() : Number
      {
         return this.showX;
      }
      
      override public function set x(v0:Number) : void
      {
         this.showX = v0;
         super.x = this.showX + this.visibleX;
      }
      
      override public function get visible() : Boolean
      {
         return this.visibleX == 0;
      }
      
      public function get trueVisible() : Boolean
      {
         return super.visible;
      }
      
      override public function set visible(bb0:Bo<PERSON>an) : void
      {
         super.visible = bb0;
         this.visibleX = bb0 ? 0 : 1;
         super.x = this.showX + this.visibleX;
      }
   }
}

