package UI.api.audit
{
   import UI.test.SaveTestBox;
   import com.adobe.crypto.MD5;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.goods.define.GoodsDefine;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   
   public class ShopItemLog
   {
      private var loader:URLLoader = new URLLoader();
      
      private var url:URLRequest = new URLRequest("https://stat.api.4399.com/audit-log-api/itemLog");
      
      public function ShopItemLog()
      {
         super();
         this.loader.addEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this.loader.addEventListener(Event.COMPLETE,this.loaderCompleteHandler);
      }
      
      public function start(da0:GoodsData, buyB0:Boolean = true) : *
      {
         var d0:GoodsDefine = da0.def;
         var data0:URLVariables = new URLVariables();
         data0.game_id = AuditHeartbeat.game_id;
         data0.item_id = d0.name;
         data0.uid = Gaming.getUid();
         data0.type = buyB0 ? 1 : 2;
         data0.money = "黄金";
         data0.price = da0.getPrice();
         data0.category = d0.dataType;
         data0.num = da0.nowNum;
         data0.time = Gaming.api.save.getNowTimeValueSec();
         data0.scene = 1;
         data0.sign = MD5.hash(MD5.hash("342sg6sfg46fghrt864rh4" + data0.time));
         this.url.data = data0;
         this.url.method = URLRequestMethod.GET;
         this.loader.load(this.url);
      }
      
      private function loaderCompleteHandler(e:Event) : void
      {
         SaveTestBox.addText("LogSuccess:" + this.loader.data);
      }
      
      private function errorHandler(e:IOErrorEvent) : void
      {
         SaveTestBox.addText("LogError:" + this.loader.data);
      }
   }
}

