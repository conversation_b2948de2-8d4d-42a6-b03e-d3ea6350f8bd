package UI.base.tip
{
   import com.common.text.TextWay;
   import dataAll.ui.GatherColor;
   
   public class TextGatherAnalyze
   {
      private static var keyObj:Object = {};
      
      public function TextGatherAnalyze()
      {
         super();
      }
      
      public static function color(s0:String, color0:String) : String
      {
         return "<" + color0 + " " + s0 + "/>";
      }
      
      public static function init() : int
      {
         var name0:* = null;
         keyObj["/>"] = "</font>";
         for each(name0 in GatherColor.arr)
         {
            keyObj["<" + name0 + " "] = "<font color=\'" + GatherColor.getColor(name0) + "\'>";
         }
         return 0;
      }
      
      private static function setGatherText(str0:String) : *
      {
         var str111:String = "";
         str111 += "<i1>|<orange 属性：/>";
         str111 += "\n<i1>|<orange 属性：/>|<i3>";
         str111 += "\n<orange 属性：/>";
         str111 += "\n<orange 属性：/>|<i3>";
         str111 += "\n<red 武器类型/>|<red 手枪/>";
         str111 += "\n<red 武器类型/>|<red 0980/>|<i4>";
         str0 = str111;
         var arr0:Array = swapToFourText(str0);
      }
      
      public static function swapToFourText(str0:String) : Array
      {
         var n:* = undefined;
         var j:* = undefined;
         var s0:String = null;
         var s_arr0:Array = null;
         var s1:String = null;
         var s9:String = null;
         var len0:int = 0;
         var i:* = undefined;
         var arr0:Array = str0.split("\n");
         var arr1:Array = [[],"","",[]];
         for(n in arr0)
         {
            s0 = arr0[n];
            s_arr0 = s0.split("|");
            s1 = s_arr0[0];
            s9 = "";
            len0 = int(s_arr0.length);
            if(s_arr0.length > 1)
            {
               s9 = s_arr0[s_arr0.length - 1];
            }
            if(s1.indexOf("<i") != 0)
            {
               s_arr0.unshift("");
            }
            len0 = int(s_arr0.length);
            if(s9.indexOf("<i") != 0)
            {
               if(len0 == 2)
               {
                  s_arr0.push("");
               }
               s_arr0.push("");
            }
            else if(len0 == 3)
            {
               s_arr0.splice(2,0,"");
            }
            if(s_arr0.length > 4)
            {
               INIT.showError("行" + n + "超过3个以上的分隔符|");
            }
            for(i in s_arr0)
            {
               if(i == 1 || i == 2)
               {
                  arr1[i] += "\n" + swapText(s_arr0[i]);
               }
               else
               {
                  arr1[i].push(s_arr0[i]);
               }
            }
         }
         for(j in arr1)
         {
            if(j == 1 || j == 2)
            {
               arr1[j] = String(arr1[j]).substr(1);
            }
         }
         return arr1;
      }
      
      public static function swapText(str0:String) : String
      {
         var n:* = undefined;
         for(n in keyObj)
         {
            str0 = TextWay.replaceStr(str0,n,keyObj[n]);
         }
         return str0;
      }
   }
}

