package UI.gift.anniver.other
{
   import UI.UIOrder;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import com.sounto.utils.NetMethod;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.anniver.HolidaySignGiftSave;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class AnniverEggBoard extends AutoNormalUI
   {
      private var numTxt:TextField;
      
      private var fillBtn:NormalBtn;
      
      private var eggTag:Sprite;
      
      private var eggBox:ItemsGripBox = new ItemsGripBox();
      
      private var tempBtn:ItemsGrid = null;
      
      public function AnniverEggBoard()
      {
         super();
         mcTypeArr = ["btnSp","txt","tag"];
      }
      
      private function get SAVE() : HolidaySignGiftSave
      {
         return Gaming.PG.save.gift.annSign25;
      }
      
      private function get activeNum() : int
      {
         return Gaming.PG.da.active.nowActive;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         this.fillBtn.visible = false;
         this.fillBtn.setName("查看物品概率");
         this.fillBtn.addEventListener(MouseEvent.CLICK,this.fillClick);
         this.eggTag.addChild(this.eggBox);
         this.eggBox.arg.init(5,2,43,108);
         this.eggBox.setIconPro("AnniverUI/eggIcon");
         this.eggBox.evt.setWant(true,true);
         this.eggBox.setNowGripNum(10);
         this.eggBox.addEventListener(ClickEvent.ON_CLICK,this.eggClick);
         this.eggBox.addEventListener(ClickEvent.ON_OVER,this.eggOver);
         this.eggBox.addEventListener(ClickEvent.ON_OUT,this.eggOut);
         FontDeal.dealOne(this.numTxt);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      private function fleshData() : void
      {
         var surplus0:int = this.SAVE.getEggSurplus(this.activeNum);
         this.numTxt.text = "今天还有" + surplus0 + "次拆红包机会";
         this.fleshEgg();
      }
      
      private function fleshEgg() : void
      {
         var btn0:ItemsGrid = null;
         var index0:int = 0;
         var openB0:Boolean = false;
         var g0:GiftAddDefine = null;
         var btnArr0:Array = this.eggBox.gripArr;
         var allNum0:int = this.SAVE.getEggAll(this.activeNum);
         for each(btn0 in btnArr0)
         {
            btn0.iconEffectB = true;
            btn0.setNumText("");
            btn0.setIconName("");
            index0 = btn0.index;
            if(index0 >= allNum0)
            {
               btn0.setSmallIconFrame(1);
               btn0.actived = false;
               btn0.setLessGrayFilter(true);
            }
            else
            {
               btn0.setLessGrayFilter(false);
               openB0 = this.SAVE.getEggB(index0);
               if(openB0)
               {
                  g0 = btn0.secData as GiftAddDefine;
                  if(Boolean(g0))
                  {
                     btn0.inData_gift(g0);
                  }
                  btn0.setSmallIconFrame(2);
                  btn0.actived = false;
                  btn0.transform.colorTransform = NormalBtn.normal_CF;
               }
               else
               {
                  btn0.setSmallIconFrame(1);
                  btn0.actived = true;
               }
            }
         }
      }
      
      public function outLoginEvent() : void
      {
         var btn0:ItemsGrid = null;
         var btnArr0:Array = this.eggBox.gripArr;
         for each(btn0 in btnArr0)
         {
            btn0.secData = null;
         }
      }
      
      private function eggClick(e:ClickEvent) : void
      {
         var btn0:ItemsGrid = e.child as ItemsGrid;
         var index0:int = btn0.index;
         var openB0:Boolean = this.SAVE.getEggB(index0);
         if(!openB0)
         {
            this.tempBtn = btn0;
            UIOrder.getStoreState(this.openEgg);
         }
      }
      
      private function openEgg(state0:int) : void
      {
         var btn0:ItemsGrid = null;
         var index0:int = 0;
         var g0:GiftAddDefine = null;
         if(state0 == 1)
         {
            btn0 = this.tempBtn;
            index0 = btn0.index;
            g0 = this.SAVE.openEggGift();
            btn0.secData = g0;
            this.SAVE.setEggB(index0,true);
            GiftAddit.addByDefine(g0,Gaming.PG.da);
            this.fleshData();
            UIOrder.yesSaveTip = "拆红包获得:\n" + g0.getDescription();
            UIOrder.save(true,false,false,null,null,false,true);
         }
      }
      
      private function eggOver(e:ClickEvent) : void
      {
         var g0:GiftAddDefineGroup = this.SAVE.getEggGiftAll();
         var tip0:String = "拆红包有几率获得以下物品：\n" + g0.getDescriptionAndPro(1);
         Gaming.uiGroup.tipBox.textTip.showFollowText(tip0);
      }
      
      private function eggOut(e:ClickEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function fillClick(e:MouseEvent) : void
      {
         NetMethod.gotoUrl("https://my.4399.com/forums/thread-62667825");
      }
   }
}

