package UI.bag.chargerShow
{
   import dataAll.arms.ArmsChargerData;
   import dataAll.arms.ArmsChargerDataGroup;
   import dataAll.arms.define.ArmsChargerDefine;
   import dataAll.arms.define.ArmsType;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class ChargerShowBox extends Sprite
   {
      private var barObj:Object = {};
      
      private var imgInitB:Boolean = false;
      
      private var dataGroup:ArmsChargerDataGroup = null;
      
      public function ChargerShowBox()
      {
         super();
         this.mouseChildren = false;
      }
      
      public function imgInit() : void
      {
         var n:* = undefined;
         var name0:String = null;
         var d0:ArmsChargerDefine = null;
         var bar0:ChargerShowBar = null;
         if(this.imgInitB)
         {
            INIT.showError("已经初始化过图像");
         }
         this.imgInitB = true;
         var arr0:Array = ArmsType.uiArr;
         for(n in arr0)
         {
            name0 = arr0[n];
            d0 = Gaming.defineGroup.armsCharger.getDefine(name0);
            bar0 = new ChargerShowBar();
            bar0.setToNormalImg();
            bar0.setIcon(d0.name);
            bar0.y = 24 * n;
            addChild(bar0);
            this.barObj[d0.name] = bar0;
         }
         this.addEventListener(MouseEvent.MOUSE_OVER,this.barMove);
         this.addEventListener(MouseEvent.MOUSE_OUT,this.barOut);
      }
      
      public function inData(dg0:ArmsChargerDataGroup) : void
      {
         var obj0:Object = null;
         var n:* = undefined;
         var bar0:ChargerShowBar = null;
         var da0:ArmsChargerData = null;
         if(this.imgInitB)
         {
            this.dataGroup = dg0;
            obj0 = dg0.obj;
            for(n in this.barObj)
            {
               bar0 = this.barObj[n];
               da0 = obj0[n];
               bar0.setNum(da0.getMax());
               bar0.itemsData = da0;
            }
         }
      }
      
      private function barMove(e:MouseEvent) : void
      {
         var arr0:Array = null;
         var tip0:String = null;
         var type0:* = null;
         var da0:ArmsChargerData = null;
         if(Boolean(this.dataGroup))
         {
            arr0 = ArmsType.wearArr;
            tip0 = "<blue 各类武器携弹量/>";
            for each(type0 in arr0)
            {
               da0 = this.dataGroup.getData(type0);
               if(Boolean(da0))
               {
                  tip0 += "\n" + da0.def.cnName + "：" + da0.getMax();
               }
            }
            Gaming.uiGroup.tipBox.showText(tip0);
         }
      }
      
      private function barOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
   }
}

