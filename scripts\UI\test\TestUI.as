package UI.test
{
   import UI.NormalUICtrl;
   import UI.base.HaveConSprite;
   import UI.base.NormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class TestUI extends HaveConSprite
   {
      public var closeBtn:SimpleButton;
      
      public var haveDataB:Boolean = false;
      
      public var saveBox:SaveTestBox = new SaveTestBox();
      
      public var save4399:Save4399Box = new Save4399Box();
      
      public var cheatingBox:CheatingBox = new CheatingBox();
      
      private var labelArr:Array = ["save4399","saveBox","cheatingBox"];
      
      private var labelTag:Sprite;
      
      public var labelBox:LabelBox = new LabelBox();
      
      public function TestUI()
      {
         super();
      }
      
      public function initImg() : void
      {
         // 优先使用内嵌界面，确保总是能正常工作
         var img0:Sprite = null;
         try
         {
            img0 = Gaming.swfLoaderManager.getResource("TestUI","testUI");
         }
         catch(e:Error)
         {
            // 忽略SWF加载错误
         }

         if(Bo<PERSON>an(img0))
         {
            // 使用SWF资源
            this.initFromSWF(img0);
         }
         else
         {
            // 创建内嵌界面
            this.createEmbeddedUI();
         }

         // 通用初始化
         this.initCommon();
      }

      private function initFromSWF(img0:Sprite) : void
      {
         addChildAt(img0,0);
         this.closeBtn = img0["closeBtn"];
         this.labelTag = img0["labelTag"];
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.hide);
      }

      private function createEmbeddedUI() : void
      {
         // 创建主背景
         var bg:Sprite = new Sprite();
         bg.graphics.beginFill(0x000000, 0.8);
         bg.graphics.drawRect(0, 0, 600, 400);
         bg.graphics.endFill();
         addChildAt(bg, 0);

         // 创建标题背景
         var titleBg:Sprite = new Sprite();
         titleBg.graphics.beginFill(0x333333, 1);
         titleBg.graphics.drawRect(0, 0, 600, 30);
         titleBg.graphics.endFill();
         bg.addChild(titleBg);

         // 创建关闭按钮
         this.closeBtn = this.createCloseButton();
         this.closeBtn.x = 570;
         this.closeBtn.y = 5;
         bg.addChild(this.closeBtn);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.hide);

         // 创建标签区域
         this.labelTag = new Sprite();
         this.labelTag.x = 10;
         this.labelTag.y = 35;
         bg.addChild(this.labelTag);
      }

      private function createCloseButton() : SimpleButton
      {
         // 创建关闭按钮的各种状态
         var upState:Sprite = new Sprite();
         upState.graphics.beginFill(0xFF0000, 1);
         upState.graphics.drawRect(0, 0, 20, 20);
         upState.graphics.endFill();

         var overState:Sprite = new Sprite();
         overState.graphics.beginFill(0xFF6666, 1);
         overState.graphics.drawRect(0, 0, 20, 20);
         overState.graphics.endFill();

         var downState:Sprite = new Sprite();
         downState.graphics.beginFill(0xCC0000, 1);
         downState.graphics.drawRect(0, 0, 20, 20);
         downState.graphics.endFill();

         var hitArea:Sprite = new Sprite();
         hitArea.graphics.beginFill(0x000000, 0);
         hitArea.graphics.drawRect(0, 0, 20, 20);
         hitArea.graphics.endFill();

         return new SimpleButton(upState, overState, downState, hitArea);
      }

      private function initCommon() : void
      {
         addChild(this.labelBox);
         NormalUICtrl.setTag(this.labelBox,this.labelTag);
         this.labelBox.arg.init(5,1,-6,0);
         this.labelBox.inData("longLabelBtn",this.labelArr,["4399存档","内部存档","作弊大全"]);
         this.labelBox.setChoose("xml");
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.saveBox.setCon(this);
         this.saveBox.initImg();
         this.save4399.setCon(this);
         this.save4399.initImg();
         this.cheatingBox.setCon(this);
         this.cheatingBox.initImg();
         this.haveDataB = true;
         this.setCon(Gaming.gameSprite.coverUI);
      }
      
      public function show(e:MouseEvent = null) : void
      {
         if(this.haveDataB)
         {
            // 移除outOutBtn限制，直接显示界面
            this.visible = true;
            inCon();
            this.showBox(this.labelBox.nowLabel);
         }
      }
      
      private function hide(e:MouseEvent = null) : void
      {
         this.visible = false;
         outCon();
      }
      
      public function showAndHide() : void
      {
         if(this.visible)
         {
            this.hide();
         }
         else
         {
            this.show();
         }
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      public function showBox(label0:String) : void
      {
         var now0:NormalUI = null;
         var l0:* = null;
         var ui0:NormalUI = null;
         if(this.haveDataB)
         {
            if(label0 == "")
            {
               label0 = this.labelArr[0];
            }
            this.labelBox.setChoose(label0);
            now0 = this[label0];
            for each(l0 in this.labelArr)
            {
               ui0 = this[l0];
               if(now0 != ui0)
               {
                  ui0.hide();
               }
               else
               {
                  ui0.show();
               }
            }
         }
      }
   }
}

