package UI.setting.key
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGripBox;
   import UI.base.NormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.setting.key.KeyActionData;
   import dataAll._app.setting.key.SettingKeySave;
   import flash.display.Sprite;
   import flash.events.KeyboardEvent;
   import flash.events.TimerEvent;
   import flash.text.TextField;
   import flash.utils.Timer;
   
   public class SettingKeySingleBoard extends NormalUI
   {
      protected var txt:TextField;
      
      protected var p1Tag:Sprite;
      
      protected var p1Box:ItemsGripBox = new ItemsGripBox();
      
      protected var pubTxt:TextField;
      
      protected var pubTag:Sprite;
      
      protected var pubBox:ItemsGripBox = new ItemsGripBox();
      
      protected var p1Save:SettingKeySave;
      
      protected var uiType:String = "";
      
      protected var nowSave:SettingKeySave;
      
      protected var nowKeyData:KeyActionData;
      
      protected var nowIndex:int = 0;
      
      protected var clickTimer:Timer = new Timer(200);
      
      public function SettingKeySingleBoard()
      {
         super();
         elementNameArr = ["txt","p1Tag","pubTxt","pubTag","pubBox"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         FontDeal.dealLine(this.txt);
         FontDeal.dealLine(this.pubTxt);
         addChild(this.p1Box);
         this.p1Box.arg.init(1,20,0,8);
         this.p1Box.evt.setWantEvent(true,false,false,true,true);
         this.p1Box.setIconPro("BasicUI/keyBtn");
         this.p1Box.addEventListener(ClickEvent.ON_CLICK,this.btnClick);
         NormalUICtrl.setTag(this.p1Box,this.p1Tag);
         addChild(this.pubBox);
         this.pubBox.arg.init(1,20,0,8);
         this.pubBox.evt.setWantEvent(true,false,false,true,true);
         this.pubBox.setIconPro("BasicUI/keyBtn");
         this.pubBox.addEventListener(ClickEvent.ON_CLICK,this.btnClick);
         NormalUICtrl.setTag(this.pubBox,this.pubTag);
         this.clickTimer.addEventListener(TimerEvent.TIMER,this.clickTimerFun);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function inData(uiType0:String, s1:SettingKeySave, s2:SettingKeySave = null) : void
      {
         this.uiType = uiType0;
         this.p1Save = s1;
         this.inBoxData(this.p1Box,s1,false);
         this.inBoxData(this.pubBox,s1,true);
      }
      
      protected function fleshData() : void
      {
         if(this.p1Save is SettingKeySave)
         {
            this.inData(this.uiType,this.p1Save,null);
         }
      }
      
      protected function inBoxData(box0:ItemsGripBox, s0:SettingKeySave, pubB0:Boolean) : void
      {
         var daArr0:Array = s0.getDataArr(pubB0);
         var cnNameArr0:Array = KeyActionData.getCnNameArrByDataArr(daArr0);
         var str0:String = TextWay.mixedStringArr(cnNameArr0,1,"\n");
         box0.inData_byArr(daArr0,"inData_keyAction");
         if(pubB0)
         {
            this.pubTxt.text = str0;
         }
         else
         {
            this.txt.text = str0;
         }
      }
      
      public function outLoginEvent() : void
      {
         this.p1Save = null;
         this.nowSave = null;
         this.nowKeyData = null;
      }
      
      protected function clickTimerFun(e:TimerEvent) : void
      {
         this.startSetKey(0);
         this.clickTimer.stop();
      }
      
      protected function btnClick(e:ClickEvent) : void
      {
         var da0:KeyActionData = e.childData as KeyActionData;
         this.nowKeyData = da0;
         this.nowSave = this.getSaveByBox(e.target as ItemsGripBox);
         if(!this.clickTimer.running)
         {
            this.clickTimer.start();
         }
         else
         {
            this.startSetKey(1);
            this.clickTimer.stop();
         }
      }
      
      protected function startSetKey(i:int) : void
      {
         this.nowIndex = i;
         var tipStr0:String = ComMethod.color("现在设置该动作的第" + (this.nowIndex + 1) + "个按键，请在键盘上按下指定键","#00FF00");
         if(this.nowIndex == 1)
         {
            tipStr0 += ComMethod.color("\n按下“Esc键”可以删除该按键","#FF6600");
         }
         Gaming.uiGroup.alertBox.showNormal(tipStr0,"no",this.noSetFun,this.noSetFun);
      }
      
      protected function noSetFun() : void
      {
         this.nowSave = null;
         this.nowKeyData = null;
         this.nowIndex = 0;
      }
      
      public function keyUp(e:KeyboardEvent) : void
      {
         var setCode0:int = 0;
         var keyCanB0:Boolean = false;
         var beforeCode0:int = 0;
         var bb0:Boolean = false;
         if(this.nowKeyData is KeyActionData && this.nowSave is SettingKeySave)
         {
            setCode0 = int(e.keyCode);
            keyCanB0 = Gaming.defineGroup.keyAction.isCanB(setCode0);
            if(visible && keyCanB0)
            {
               beforeCode0 = this.nowKeyData.getValue(this.nowIndex);
               bb0 = beforeCode0 != setCode0 && (this.nowIndex == 0 || setCode0 == 27);
               if(bb0)
               {
                  if(this.nowIndex == 1)
                  {
                     if(this.nowKeyData.getOneValue() == setCode0 || setCode0 == 27)
                     {
                        setCode0 = 0;
                     }
                  }
                  this.nowSave.setOneByName(this.nowKeyData.name,this.nowIndex,setCode0);
                  Gaming.PG.save.setting.key.setNewKeyCodeEvent(this.nowSave.type,this.nowKeyData.name,setCode0,beforeCode0);
                  this.fleshData();
                  this.noSetFun();
                  Gaming.soundGroup.playSound("uiSound","changeLabel");
               }
               Gaming.uiGroup.alertBox.hide();
            }
         }
      }
      
      protected function getSaveByBox(box0:ItemsGripBox) : SettingKeySave
      {
         return this.p1Save;
      }
   }
}

