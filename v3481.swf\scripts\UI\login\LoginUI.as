package UI.login
{
   import UI.UIOrder;
   import UI.UIShow;
   import UI.base.AppNormalUI;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import com.greensock.TweenLite;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.login.SaveBaseData4399;
   import dataAll._data.ConstantDefine;
   import dataAll._player.base.PlayerMainData;
   import dataAll._player.supple.PlayerDataSupple;
   import fl.motion.easing.Back;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import gameAll.drop.DropCtrl;
   
   public class LoginUI extends AppNormalUI
   {
      public var startBtn:NormalBtn = new NormalBtn();
      
      public var newBtn:NormalBtn = new NormalBtn();
      
      public var nameBox:LoginNameBox = new LoginNameBox();
      
      public var saveBox:LoginSaveBox = new LoginSaveBox();
      
      private var nowBox:NormalUI = null;
      
      public var verTxt:TextField;
      
      public var inVerTxt:TextField;
      
      public var musicTxt:TextField;
      
      private var noticeBtn:SimpleButton;
      
      private var bbsBtn:SimpleButton;
      
      private var qaBtn:SimpleButton;
      
      private var startBtnSp:MovieClip;
      
      private var newBtnSp:MovieClip;
      
      private var saveBoxSp:MovieClip;
      
      private var nameBoxSp:MovieClip;
      
      private var groupMc:MovieClip;
      
      private var backMc:MovieClip;
      
      private var ageBtn:SimpleButton;
      
      private var ageSp:Sprite;
      
      private var showNoticeB:Boolean = false;
      
      private var readSaveB:Boolean = true;
      
      public function LoginUI()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["ageSp","ageBtn","groupMc","noticeBtn","inVerTxt","bbsBtn","qaBtn","newBtnSp","startBtnSp","saveBoxSp","nameBoxSp","verTxt","musicTxt"];
         super.setImg(img0);
         addChild(this.startBtn);
         this.startBtn.setImg(this.startBtnSp);
         this.startBtn.setName("读取存档");
         addChild(this.newBtn);
         this.newBtn.setImg(this.newBtnSp);
         this.newBtn.setName("新的开始");
         this.ageBtn.addEventListener(MouseEvent.CLICK,this.ageShow);
         this.noticeBtn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.bbsBtn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.qaBtn.addEventListener(MouseEvent.CLICK,this.btnClick);
         addChild(this.nameBox);
         this.nameBox.setImg(this.nameBoxSp);
         this.nameBox.visible = false;
         this.nameBox.x = Gaming.WIDTH / 2;
         this.nameBox.y = Gaming.HEIGHT / 2;
         addChild(this.saveBox);
         this.saveBox.setImg(this.saveBoxSp);
         this.saveBox.visible = false;
         this.saveBox.x = 450;
         this.saveBox.y = 337;
         this.backMc = Gaming.uiGroup.loadingUI.getBack();
         addChildAt(this.backMc,0);
         this.verTxt.htmlText = "版本号：" + ConstantDefine.getVersionText();
         this.inVerTxt.htmlText = "内部版本：" + ConstantDefine.inVersion;
         this.musicTxt.visible = false;
         this.startBtn.addEventListener(MouseEvent.CLICK,this.startBtnClick);
         this.newBtn.addEventListener(MouseEvent.CLICK,this.newBtnClick);
         this.saveBox.box.addEventListener(ClickEvent.ON_CLICK,this.saveListClick);
         this.saveBox.closeBtn.addEventListener(MouseEvent.CLICK,this.saveListCloseEvent);
         Gaming.api.save.setClosePanelEvent(this.closePanelEvent);
         Gaming.api.save.setOutLoginEvent(this.apiOutLoginEvent);
         addChild(this.ageSp);
         this.ageSp.visible = false;
         this.ageSp["closeBtn"].addEventListener(MouseEvent.CLICK,this.ageClose);
         this.addChild(this.groupMc);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         if(Boolean(this.groupMc.parent) && Gaming.testCtrl.enabled)
         {
            this.groupMc.parent.removeChild(this.groupMc);
         }
         if(!this.groupMc.parent)
         {
            this.afterShow();
         }
         else if(!this.groupMc.hasEventListener(Event.ENTER_FRAME))
         {
            this.groupMc.addEventListener(Event.ENTER_FRAME,this.groupTimer);
            this.groupMc.gotoAndPlay(1);
         }
      }
      
      private function afterShow() : void
      {
         if(Boolean(this.backMc))
         {
            this.backMc.gotoAndStop(1);
         }
         if(!this.showNoticeB)
         {
            UIShow.showByLabel("notice");
            this.showNoticeB = true;
         }
         UIOrder.playLoginMusic();
      }
      
      private function groupTimer(e:Event) : void
      {
         if(this.groupMc.currentFrame >= this.groupMc.totalFrames - 1)
         {
            this.groupMc.stop();
            this.groupMc.removeEventListener(Event.ENTER_FRAME,this.groupTimer);
            this.groupMc.parent.removeChild(this.groupMc);
            this.afterShow();
         }
      }
      
      private function showBox(str0:String) : void
      {
         this.saveBox.hideNewBtn();
         if(Boolean(this.nowBox))
         {
            TweenLite.killTweensOf(this.nowBox);
            this.nowBox.hide();
         }
         Gaming.uiGroup.noticeUI.hide();
         if(this.hasOwnProperty(str0 + "Box"))
         {
            this.nowBox = this[str0 + "Box"];
            this.startBtn.actived = false;
            this.newBtn.actived = false;
            this.nowBox.show();
            this.nowBox.alpha = 0;
            this.nowBox.scaleX = 0.7;
            this.nowBox.scaleY = this.nowBox.scaleX;
            TweenLite.to(this.nowBox,0.3,{
               "scaleX":1,
               "scaleY":1,
               "alpha":1,
               "ease":Back.easeOut
            });
         }
         else
         {
            this.startBtn.actived = true;
            this.newBtn.actived = true;
         }
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var name0:String = e.target.name.replace("Btn","");
         Gaming.soundGroup.playSound("uiSound","click");
         UIShow.showByLabel(name0);
      }
      
      private function ageClose(e:MouseEvent) : void
      {
         this.ageSp.visible = false;
      }
      
      private function ageShow(e:MouseEvent) : void
      {
         if(this.ageSp.visible)
         {
            this.ageSp.visible = false;
         }
         else
         {
            this.ageSp.visible = true;
            Gaming.uiGroup.noticeUI.hide();
         }
      }
      
      private function newBtnClick(e:MouseEvent) : void
      {
         this.readSaveB = false;
         this.mainBtnClick(e);
      }
      
      private function startBtnClick(e:MouseEvent) : void
      {
         this.readSaveB = true;
         this.mainBtnClick(e);
      }
      
      private function mainBtnClick(e:MouseEvent) : void
      {
         Gaming.uiGroup.connectUI.show("登录中……");
         if(Gaming.api.save.isLogin())
         {
            if(Gaming.isLocal())
            {
               Gaming.api.save.showLogPanel(this.affter_login);
            }
            else
            {
               this.getSaveList();
            }
         }
         else
         {
            Gaming.api.save.showLogPanel(this.affter_login);
         }
      }
      
      private function affter_login(obj0:Object) : void
      {
         Gaming.PG.inLoginObj(obj0);
         this.getSaveList();
      }
      
      private function getSaveList() : void
      {
         Gaming.uiGroup.connectUI.show("读取存档列表中……");
         Gaming.api.save.getList(this.affter_getList);
      }
      
      private function affter_getList(arr0:Array) : void
      {
         var arr2:Array = null;
         var n:* = undefined;
         var d0:SaveBaseData4399 = null;
         Gaming.uiGroup.connectUI.hide();
         if(Gaming.api.save.isLogin())
         {
            this.showBox("save");
            arr2 = [];
            for(n in arr0)
            {
               d0 = new SaveBaseData4399();
               d0.inData_byObj(arr0[n]);
               arr2[n] = d0;
            }
            Gaming.PG.inLoginList(arr2);
            this.saveBox.inData(arr2,this.readSaveB);
         }
         else
         {
            this.outLoginEvent();
         }
      }
      
      private function saveListClick(e:ClickEvent) : void
      {
         var d0:SaveBaseData4399 = e.childData as SaveBaseData4399;
         if(Boolean(d0))
         {
            if(d0.status == "0")
            {
               if(this.readSaveB)
               {
                  this.readSaveOne(d0);
               }
               else
               {
                  this.newSaveOne(d0);
               }
            }
            else
            {
               Gaming.uiGroup.alertBox.showNormal("该存档被封！\n如有问题请联系客服。","yes",null,null,"no");
            }
         }
         else
         {
            Gaming.api.save.nowIndex = e.index;
            Gaming.PG.loginData.newSave(e.index);
            Gaming.uiGroup.alertBox.showNormal("是否在位置" + (e.index + 1) + "新建存档？","yesAndNo",this.init_save);
         }
      }
      
      private function readSaveOne(da0:SaveBaseData4399) : void
      {
         Gaming.secondLoad(this.secondLoadReadSaveOne,da0);
      }
      
      private function secondLoadReadSaveOne(da0:SaveBaseData4399) : void
      {
         Gaming.PG.loginData.setNowSave(da0);
         Gaming.uiGroup.connectUI.show("读取存档中……");
         Gaming.api.save.read(da0.index,this.yes_read,this.no_read);
      }
      
      private function newSaveOne(da0:SaveBaseData4399) : void
      {
         var str0:String = null;
         this.saveBox.nowSaveListData = da0;
         if(da0 is SaveBaseData4399)
         {
            str0 = "是否要清除存档“" + ComMethod.color(da0.title,"#00FF00") + "”，并在此新建存档？\n剩余提示次数：" + ComMethod.color("2","#00FF00");
            str0 = ComMethod.color(str0,"#FF9900");
            Gaming.uiGroup.alertBox.showNormal(str0,"yesAndNo",this.saveNewTip2);
         }
      }
      
      private function saveNewTip2() : void
      {
         var str0:String = null;
         var da0:SaveBaseData4399 = this.saveBox.nowSaveListData;
         if(da0 is SaveBaseData4399)
         {
            str0 = "是否要清除存档“" + ComMethod.color(da0.title,"#00FF00") + "”，并在此新建存档？\n剩余提示次数：" + ComMethod.color("1","#00FF00");
            str0 = ComMethod.color(str0,"#FF9900");
            Gaming.uiGroup.alertBox.showNormal(str0,"yesAndNo",this.saveNewTip1);
         }
      }
      
      private function saveNewTip1() : void
      {
         var str0:String = null;
         var da0:SaveBaseData4399 = this.saveBox.nowSaveListData;
         if(da0 is SaveBaseData4399)
         {
            str0 = "是否要清除存档“" + ComMethod.color(da0.title,"#00FF00") + "”，并在此新建存档？\n剩余提示次数：" + ComMethod.color("0","#00FF00");
            str0 = ComMethod.color(str0,"#FF9900");
            Gaming.uiGroup.alertBox.showNormal(str0,"yesAndNo",this.saveNewTip0);
         }
      }
      
      private function saveNewTip0() : void
      {
         var da0:SaveBaseData4399 = this.saveBox.nowSaveListData;
         if(da0 is SaveBaseData4399)
         {
            Gaming.api.save.nowIndex = da0.index;
            Gaming.PG.loginData.newSave(da0.index);
            this.init_save();
         }
      }
      
      private function init_save() : void
      {
         Gaming.secondLoad(this.secondLoadInit_save,null);
      }
      
      private function secondLoadInit_save(v:* = null) : void
      {
         Gaming.PG.initSave(Gaming.api.save.s4399.getLogObjNull());
         this.showBox("name");
      }
      
      public function yes_read(obj0:Object, saveObj0:Object = null) : void
      {
         var main0:PlayerMainData = null;
         Gaming.uiGroup.connectUI.hide();
         if(Boolean(obj0))
         {
            Gaming.PG.readSave(obj0,"me",Gaming.api.save.s4399.getLogObjNull(),saveObj0);
            main0 = Gaming.PG.da.main;
            if(PlayerDataSupple.versionTooHighB)
            {
               Gaming.uiGroup.alertBox.showCheck("当前游戏版本太低，无法继续游戏！","");
            }
            else
            {
               UIOrder.affter_readSave();
            }
         }
         else
         {
            this.no_read();
         }
      }
      
      private function no_read(str0:String = "") : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showNormal("读取存档失败！","yes",null,null,"no");
      }
      
      public function apiOutLoginEvent() : void
      {
         Gaming.api.rzxt.unlogin();
         this.outLoginEvent();
      }
      
      public function outLoginEvent(clearLoginB0:Boolean = true) : void
      {
         Gaming.LG.outLoginEvent();
         Gaming.uiGroup.outLoginEvent();
         Gaming.PG.outLoginEvent(clearLoginB0);
         DropCtrl.outLoginEvent();
         Gaming.PCG.outLoginEvent();
         Gaming.defineGroup.outLoginEvent();
         Gaming.api.outLoginEvent();
         UIShow.login();
         this.showBox("");
         Gaming.uiGroup.alertBox.hide();
         Gaming.uiGroup.connectUI.hide();
      }
      
      private function closePanelEvent() : void
      {
         if(!Gaming.api.save.isLogin())
         {
            Gaming.uiGroup.connectUI.hide();
         }
      }
      
      private function saveListCloseEvent(e:MouseEvent) : void
      {
         this.outLoginEvent(false);
      }
   }
}

