package UI.gift.activity
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.StringDate;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.ExchangeGiftAddDefineGroup;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   import flash.net.navigateToURL;
   import flash.text.TextField;
   
   public class WeixinExchangeBoard extends NormalUI
   {
      private var codeTxt:TextField;
      
      private var btnSp:MovieClip;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var getCodeTxt:TextField;
      
      private var giftTag:Sprite;
      
      private var linkTxt:TextField;
      
      private var loader:URLLoader = new URLLoader();
      
      private var url:URLRequest = new URLRequest("https://huodong2.4399.com/comm/zmxy3/api.php");
      
      private var game_id:String = "7";
      
      private var gift_id:String = "weixin2016";
      
      private var giftBox:ItemsGripBox = new ItemsGripBox();
      
      private var nowGiftType:int = 1;
      
      public function WeixinExchangeBoard()
      {
         super();
         this.loader.addEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this.loader.addEventListener(Event.COMPLETE,this.loaderCompleteHandler);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["getCodeTxt","codeTxt","btnSp","giftTag","linkTxt"];
         super.setImg(img0);
         this.linkTxt.styleSheet = ComMethod.getLinkCss("#FF9900","#FFFFFF");
         this.linkTxt.addEventListener(TextEvent.LINK,this.linkClick);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("兑换礼包");
         this.btn.addEventListener(MouseEvent.CLICK,this.click);
         this.codeTxt.text = "";
         addChild(this.giftBox);
         NormalUICtrl.setTag(this.giftBox,this.giftTag);
         this.giftBox.imgType = "equipGrip";
         this.giftBox.evt.setWantEvent(true,false,false,true,true);
         this.giftBox.arg.init(8,1,3,3);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.giftBox);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      private function no_getDate(str:* = null) : *
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.giftUI.hide();
      }
      
      private function affter_getDate(time00:String) : *
      {
         Gaming.uiGroup.connectUI.hide();
         this.fleshData();
      }
      
      private function fleshData() : void
      {
         var gift0:ExchangeGiftAddDefineGroup = null;
         gift0 = Gaming.defineGroup.gift.getOne(this.gift_id) as ExchangeGiftAddDefineGroup;
         this.giftBox.inData_byArr(gift0.arr,"inData_gift");
         this.btn.actived = false;
         var now0:StringDate = Gaming.api.save.getNowServerDate();
         var thisD0:StringDate = new StringDate();
         thisD0.inData_byStr(gift0.startTime);
         var canGetCodeB:Boolean = gift0.startTime == "" ? true : thisD0.compareDateValue(now0) >= 0;
         if(canGetCodeB)
         {
            this.linkTxt.htmlText = "点击进入活动地址：" + ComMethod.link(gift0.codeUrl,"url");
         }
         else
         {
            this.linkTxt.htmlText = "活动开始时间：" + gift0.startTime + ComMethod.color("（日期未到）","#FF0000");
         }
      }
      
      private function linkClick(e:TextEvent) : void
      {
         var gift0:ExchangeGiftAddDefineGroup = null;
         if(e.text == "url")
         {
            gift0 = Gaming.defineGroup.gift.getOne(this.gift_id) as ExchangeGiftAddDefineGroup;
            navigateToURL(new URLRequest(gift0.codeUrl),"blank");
         }
      }
      
      private function click(e:MouseEvent) : void
      {
         var bb0:Boolean = false;
         this.nowGiftType = e.target == this.btn ? 1 : 2;
         var ac0:String = TextWay.toHan2(TextWay.toHanSpace(this.codeTxt.text));
         if(ac0 == "" || !ac0)
         {
            Gaming.uiGroup.alertBox.showError("请输入兑换码");
         }
         else
         {
            bb0 = UIOrder.isLoginByJS();
            if(bb0)
            {
               Gaming.uiGroup.connectUI.show("数据处理中……");
               Gaming.api.save.getStoreState(this.affter_getStoreState);
            }
            else
            {
               Gaming.uiGroup.connectUI.hide();
            }
         }
      }
      
      private function affter_getStoreState(state0:int) : void
      {
         var uid0:String = null;
         var ac0:String = null;
         if(state0 == 1 || state0 == -2)
         {
            uid0 = Gaming.PG.loginData.uid;
            ac0 = TextWay.toHan2(TextWay.toHanSpace(this.codeTxt.text));
            this.startExchange(ac0);
         }
      }
      
      private function yes_code(str0:String) : void
      {
         var result0:String = null;
         var gift0:GiftAddDefineGroup = null;
         var tipStr0:String = null;
         Gaming.uiGroup.connectUI.hide();
         var code0:int = int(str0.split("|")[0]);
         var msg0:String = str0.split("|")[1];
         if(code0 == 1)
         {
            result0 = this.gift_id;
            gift0 = Gaming.defineGroup.gift.getOne(result0);
            if(!gift0)
            {
               INIT.showErrorMust("找不到指定id礼包：" + result0);
            }
            tipStr0 = "兑换成功！";
            GiftAddit.add(gift0,tipStr0,false);
            this.fleshData();
            UIOrder.save(false,false);
         }
         else
         {
            if(msg0 == "01")
            {
               msg0 = "未登录";
            }
            else if(msg0 == "02")
            {
               msg0 = "参数有误";
            }
            else if(msg0 == "03")
            {
               msg0 = "同一帐号连续错误超过20次";
            }
            else if(msg0 == "04")
            {
               msg0 = "兑换码无效（没有这个兑换码）";
            }
            else if(msg0 == "05")
            {
               msg0 = "兑换码无效（兑换码未领过）";
            }
            else if(msg0 == "06")
            {
               msg0 = "兑换码无效（已过期）";
            }
            else if(msg0 == "07")
            {
               msg0 = "兑换码无效（已被其他人领取）";
            }
            else if(msg0 == "08")
            {
               msg0 = "兑换码无效（已过期）";
            }
            else
            {
               msg0 = "未知错误";
            }
            Gaming.uiGroup.alertBox.showError("错误：" + msg0);
         }
      }
      
      private function no_code() : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError("接口无响应！请稍候再试。");
      }
      
      private function startExchange(code0:String) : *
      {
         var data0:URLVariables = new URLVariables();
         data0.cid = 6;
         data0.code = code0;
         this.url.data = data0;
         this.url.method = URLRequestMethod.GET;
         this.loader.load(this.url);
      }
      
      private function loaderCompleteHandler(e:Event) : void
      {
         this.yes_code(this.loader.data);
      }
      
      private function errorHandler(e:IOErrorEvent) : void
      {
         this.no_code();
      }
   }
}

