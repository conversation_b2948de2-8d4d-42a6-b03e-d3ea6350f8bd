package UI.gift.yuanXiao
{
   import UI.base.AppNormalUI;
   import UI.base.event.ClickEvent;
   import com.sounto.oldUtils.StringDate;
   import dataAll.gift.yuanXiao.YuanXiaoSave;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class YuanXiaoUI extends AppNormalUI
   {
      private var initB:Boolean = false;
      
      private var closeBtn:SimpleButton;
      
      private var tipBtn:SimpleButton;
      
      private var infoTxt:TextField;
      
      private var numTxt:TextField;
      
      private var timeTxt:TextField;
      
      private var mouseMc:MovieClip;
      
      private var mouseSp:Sprite;
      
      private var gameSp:Sprite;
      
      private var coverSp:Sprite;
      
      private var overSp:Sprite;
      
      private var overBoard:YuanXiaoOverBoard = new YuanXiaoOverBoard();
      
      private var startSp:Sprite;
      
      private var startBoard:YuanXiaoStartBoard = new YuanXiaoStartBoard();
      
      private var xgame:YuanXiaoGame = new YuanXiaoGame();
      
      public function YuanXiaoUI()
      {
         super();
         UICn = "元宵活动";
      }
      
      public static function getTip() : String
      {
         var s0:String = "1、在游戏中长按鼠标并释放，使元宵跳入前方的碗里。如果落到地面，则游戏失败。";
         s0 += "\n2、每隔几个碗会出现1个物品奖励（可随机到万能生肖碎片），落入该碗时即可获得奖励。";
         return s0 + "\n3、每天玩家有3次挑战机会。";
      }
      
      public function testBowlIndex(v0:int) : void
      {
         this.xgame.testBowlIndex(v0);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["mouseSp","overSp","coverSp","startSp","tipBtn","closeBtn","infoTxt","numTxt","timeTxt","mouseMc","gameSp"];
         super.setImg(img0);
         this.addOverBoard(img0);
         this.addStartBoard(img0);
         addChild(this.closeBtn);
         addChild(this.tipBtn);
         this.mouseSp.mouseChildren = false;
         this.mouseSp.addEventListener(MouseEvent.MOUSE_DOWN,this.mouseDown);
         this.mouseSp.addEventListener(MouseEvent.MOUSE_UP,this.mouseUp);
         this.xgame.addEventListener(ClickEvent.ON_OVER,this.gameOver);
         this.gameSp.mask = this.coverSp;
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function addStartBoard(img0:Sprite) : void
      {
         var index0:int = img0.getChildIndex(this.startSp);
         this.startBoard.setImg(this.startSp);
         img0.addChildAt(this.startBoard,index0);
         this.startBoard.visible = false;
         this.startBoard.addEventListener(ClickEvent.ON_CLICK,this.gameStart);
      }
      
      private function addOverBoard(img0:Sprite) : void
      {
         var index0:int = img0.getChildIndex(this.overSp);
         this.overBoard.setImg(this.overSp);
         img0.addChildAt(this.overBoard,index0);
         this.overBoard.visible = false;
         this.overBoard.addEventListener(ClickEvent.ON_CLICK,this.gameFirst);
      }
      
      public function outLoginEvent() : void
      {
         this.initB = false;
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.connectUI.show();
         Gaming.api.save.getServerTime(this.afterGetTime,this.noGetTime);
      }
      
      private function afterGetTime(timeStr0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         timeStr0 = Gaming.api.save.getNowServerDate().getStr();
         var day0:int = StringDate.compareDateByStr(timeStr0,YuanXiaoSave.getOverTime());
         if(day0 >= 0)
         {
            this.timeTxt.htmlText = "距离活动结束还剩余" + (day0 + 1) + "天";
            this.xgame.init(this.gameSp);
            if(this.initB == false)
            {
               this.overBoard.hide();
               this.gameFirst();
               this.initB = true;
            }
            this.numTxt.visible = true;
         }
         else
         {
            this.overBoard.hide();
            this.startBoard.showData(true);
            this.timeTxt.htmlText = "活动已结束！";
            this.setInfo("");
            this.numTxt.visible = false;
         }
      }
      
      private function noGetTime(v0:* = null) : void
      {
         Gaming.uiGroup.connectUI.hide();
         this.hide();
         Gaming.uiGroup.alertBox.showError("服务器时间获取错误！");
      }
      
      override public function hide() : void
      {
         super.hide();
         this.mouseMc.stop();
      }
      
      private function setInfo(info0:String) : void
      {
         this.infoTxt.htmlText = info0;
         this.infoTxt.visible = info0 != "";
      }
      
      private function get SAVE() : YuanXiaoSave
      {
         return null;
      }
      
      private function gameFirst(e:* = null) : void
      {
         this.overBoard.hide();
         this.fleshNumText();
         this.setInfo("");
         this.xgame.clear();
         var num0:int = this.SAVE.getSurplus();
         if(num0 <= 0)
         {
            this.startBoard.hide();
            this.setInfo("今天已经没有游玩次数了");
         }
         else
         {
            this.startBoard.showData(false);
         }
         this.fleshMouseSp();
      }
      
      private function fleshNumText() : void
      {
         var num0:int = this.SAVE.getSurplus();
         this.numTxt.htmlText = "· 今天还可以玩" + num0 + "次 ·";
      }
      
      private function fleshMouseSp() : void
      {
         this.mouseSp.visible = !this.overBoard.visible && !this.startBoard.visible && !this.infoTxt.visible;
         this.mouseMc.visible = this.mouseSp.visible;
      }
      
      private function gameStart(e:* = null) : void
      {
         this.startBoard.hide();
         this.fleshMouseSp();
         this.SAVE.gameStart();
         this.xgame.start();
      }
      
      private function mouseDown(e:MouseEvent) : void
      {
         this.xgame.mouseDown(e);
      }
      
      private function mouseUp(e:MouseEvent) : void
      {
         this.xgame.mouseUp(e);
      }
      
      private function gameOver(e:ClickEvent) : void
      {
         this.gameOverDo();
      }
      
      private function gameOverDo() : void
      {
         this.overBoard.showData(this.xgame);
         this.fleshMouseSp();
      }
      
      private function gameRestart(e:ClickEvent) : void
      {
         this.overBoard.hide();
         this.gameFirst();
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.hide();
      }
      
      private function tipOver(e:MouseEvent) : void
      {
         var str0:String = getTip();
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      private function tipOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      public function FTimer() : void
      {
         if(visible)
         {
            this.xgame.FTimer();
         }
      }
   }
}

