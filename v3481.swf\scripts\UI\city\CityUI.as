package UI.city
{
   import UI.UIShow;
   import UI.base.AppNormalUI;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.city.body.CityBody;
   import UI.city.body.CityBodyCtrler;
   import UI.city.body.CityBodyGroup;
   import UI.city.dress.CityDressBox;
   import UI.city.smelt.CitySmeltBoard;
   import UI.pet.PetUI;
   import dataAll._app.city.CityData;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.ui.Mouse;
   import flash.ui.MouseCursor;
   
   public class CityUI extends AppNormalUI
   {
      private var closeBtn:SimpleButton;
      
      private var mapSp:Sprite;
      
      private var BG:CityBodyGroup = new CityBodyGroup();
      
      private var smeltBoard:CitySmeltBoard = new CitySmeltBoard();
      
      private var boxArr:Array = [this.smeltBoard];
      
      private var boxObj:Object = {};
      
      private var nowBoard:NormalUI;
      
      private var btnArr:Array = [];
      
      private var btnObj:Object = {};
      
      private var ctrler:CityBodyCtrler = new CityBodyCtrler();
      
      private var dressBox:CityDressBox = new CityDressBox();
      
      public function CityUI()
      {
         super();
         this.smeltBoard.UILabel = "smelt";
         UICn = "战斧高地";
         this.mouseEnabled = false;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var box0:NormalUI = null;
         elementNameArr = ["closeBtn","mapSp"];
         super.setImg(img0);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         this.initBtn(img0);
         this.ctrler.init(this.BG);
         this.dressBox.init(this.ctrler,this.BG);
         addChild(this.dressBox);
         this.dressBox.hide();
         this.BG.getCon().mouse.addEventListener(MouseEvent.MOUSE_DOWN,this.mapDown);
         this.BG.getCon().mouse.addEventListener(MouseEvent.MOUSE_UP,this.mapUp);
         for each(box0 in this.boxArr)
         {
            box0.setImg(Gaming.swfLoaderManager.getResource("CityUI",box0.UILabel + "Board"));
            addChild(box0);
            box0.hide();
            this.boxObj[box0.UILabel] = box0;
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function initBtn(img0:Sprite) : void
      {
         var mc0:DisplayObject = null;
         var index0:int = 0;
         var name0:String = null;
         var btn0:NormalBtn = null;
         var num0:int = img0.numChildren;
         for(var i:int = 0; i < num0; i++)
         {
            mc0 = img0.getChildAt(i);
            index0 = int(mc0.name.indexOf("BtnSp"));
            if(index0 > 0)
            {
               name0 = mc0.name.substr(0,index0);
               btn0 = new NormalBtn();
               btn0.setImg(mc0 as MovieClip);
               btn0.mouseIconEffectB = true;
               btn0.label = name0;
               btn0.setIconName("CityUI/" + name0 + "Icon");
               addChild(btn0);
               btn0.addEventListener(MouseEvent.CLICK,this.mainBtnClick);
               this.btnObj[name0] = btn0;
               this.btnArr.push(btn0);
               i--;
               num0--;
            }
         }
      }
      
      override public function show() : void
      {
         super.show();
         if(!this.mapSp.parent)
         {
            this.addChildAt(this.mapSp,0);
         }
         PetUI.loadPetSwf(this.afterLoad,this.hide);
      }
      
      private function afterLoad() : void
      {
         this.dressBox.clearCtrlState();
         this.cityData.dress.openUI();
         this.BG.fleshBody(this.mapSp);
      }
      
      override public function hide() : void
      {
         super.hide();
         if(Boolean(this.mapSp.parent))
         {
            this.mapSp.parent.removeChild(this.mapSp);
         }
         Mouse.cursor = MouseCursor.AUTO;
         this.showBox("");
      }
      
      private function get cityData() : CityData
      {
         return Gaming.PG.da.city;
      }
      
      override public function bagCloseEvent() : void
      {
         if(visible)
         {
            this.showBox("");
         }
      }
      
      private function mainBtnClick(e:MouseEvent) : void
      {
         var btn0:NormalBtn = e.target as NormalBtn;
         var label0:String = btn0.label;
         if(label0 == "dress")
         {
            this.dressBox.showOrHide();
         }
      }
      
      private function mapDown(e:MouseEvent) : void
      {
         var b0:CityBody = this.BG.getMouse();
         if(Boolean(b0))
         {
            b0.mouseDown();
            this.ctrler.bodyDown(b0);
         }
      }
      
      private function mapUp(e:MouseEvent) : void
      {
         var link0:String = null;
         var b0:CityBody = this.BG.getMouse();
         var linkB0:Boolean = this.dressBox.mouseUp(b0);
         if(Boolean(b0))
         {
            b0.mouseUp();
            if(linkB0)
            {
               link0 = b0.getLink();
               if(link0 != "")
               {
                  this.showBox(link0);
               }
            }
         }
         this.ctrler.clearDrag();
      }
      
      public function showBox(label0:String) : void
      {
         var box0:NormalUI = null;
         this.nowBoard = this.boxObj[label0];
         for each(box0 in this.boxArr)
         {
            if(this.nowBoard != box0)
            {
               box0.hide();
            }
         }
         if(Boolean(this.nowBoard))
         {
            this.nowBoard.show();
            this.BG.setMouse(null);
         }
         else
         {
            Gaming.uiGroup.bagUI.hideWhenVisible();
            if(label0 != "")
            {
               UIShow.showByLabel(label0);
            }
         }
      }
      
      public function outLoginEvent() : void
      {
         var ui0:NormalUI = null;
         for each(ui0 in this.boxArr)
         {
            if(ui0.hasOwnProperty("outLoginEvent"))
            {
               ui0["outLoginEvent"]();
            }
         }
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.hide();
      }
      
      override public function getMoreBoxVisible() : Boolean
      {
         return false;
      }
      
      public function FTimer() : void
      {
         var dressB0:Boolean = false;
         if(visible)
         {
            dressB0 = this.dressBox.visible;
            if(!this.nowBoard)
            {
               this.BG.FTimer(dressB0,this.ctrler.getDragB());
            }
            if(dressB0)
            {
               this.ctrler.FTimer();
               this.dressBox.FTimer();
            }
         }
      }
   }
}

